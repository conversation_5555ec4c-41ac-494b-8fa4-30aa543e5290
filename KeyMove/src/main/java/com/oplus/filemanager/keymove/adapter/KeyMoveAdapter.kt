/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: KeyMoveAdapter
 * * Description: the adapter for a key to move
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         the adapter for a key to move
 ****************************************************************/
package com.oplus.filemanager.keymove.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.oplus.filemanager.keymove.ui.model.ItemChild
import com.oplus.filemanager.keymove.ui.model.ListPrimary
import com.oplus.filemanager.keymove.ui.model.ListSecond
import com.oplus.filemanager.keymove.ui.model.Visitable
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.expandable.COUIExpandableRecyclerView
import com.coui.appcompat.rotateview.COUIRotateView
import com.filemanager.common.MyApplication
import com.filemanager.common.controller.PrivacyPolicyController.Companion.RTL_SYMBOL
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.keymove.R
import java.util.*

class KeyMoveAdapter : COUIExpandableRecyclerView.Adapter() {
    companion object {
        private const val TAG = "KeyMoveAdapter"
    }

    private var mGroupItems: ArrayList<ListPrimary>? = null
    private var mChildItems: ArrayList<ArrayList<out Visitable>>? = null
    private var mParentCOUICheckBoxClickListener: ParentCOUICheckBoxListener? = null
    private var mChildCOUICheckBoxClickListener: ChildCOUICheckBoxListener? = null

    fun setData(groupItems: ArrayList<ListPrimary>, childItems: ArrayList<ArrayList<out Visitable>>) {
        mGroupItems = groupItems
        mChildItems = childItems
        notifyDataSetChanged()
    }

    fun setGroupCheckBoxClickListener(parentCOUICheckBoxClickListener: ParentCOUICheckBoxListener) {
        mParentCOUICheckBoxClickListener = parentCOUICheckBoxClickListener
    }

    fun setChildCheckBoxClickListener(childCOUICheckBoxClickListener: ChildCOUICheckBoxListener) {
        mChildCOUICheckBoxClickListener = childCOUICheckBoxClickListener
    }

    override fun getGroup(groupPosition: Int): ListPrimary? {
        return mGroupItems?.get(groupPosition)
    }

    override fun onBindGroupView(groupPosition: Int, isExpanded: Boolean, holder: RecyclerView.ViewHolder) {
        (holder as ListPrimaryViewHolder).bindTo(groupPosition, isExpanded)
    }

    override fun onCreateChildView(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.a_key_to_move_child_item, parent, false)
        return ItemChildViewHolder(v)
    }

    override fun getChildrenCount(groupPosition: Int): Int {
        return mChildItems?.get(groupPosition)?.size ?: 0
    }

    override fun onCreateGroupView(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.key_move_primary_item, parent, false)
        return ListPrimaryViewHolder(v)
    }

    override fun onBindChildView(groupPosition: Int, childPosition: Int, isLastChild: Boolean, holder: RecyclerView.ViewHolder?) {
        (holder as ItemChildViewHolder).bindTo(groupPosition, childPosition)
    }

    override fun getChild(groupPosition: Int, childPosition: Int): Visitable? {
        return mChildItems?.get(groupPosition)?.get(childPosition)
    }

    override fun getGroupCount(): Int {
        return mGroupItems?.size ?: 0
    }

    inner class ListPrimaryViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        private val mContext: Context = MyApplication.appContext
        private val mIcon: ImageView = view.findViewById(R.id.group_file_icon)
        private val mName: TextView = view.findViewById(R.id.group_file_name)
        private val mDetail: TextView = view.findViewById(R.id.group_file_detail)
        private val mCOUICheckBox: COUICheckBox = view.findViewById(R.id.group_file_checkbox)
        private var mCOUIRotateView: COUIRotateView = view.findViewById(R.id.group_indicator_image)

        fun bindTo(groupPosition: Int, expanded: Boolean) {
            if (groupPosition >= (mGroupItems?.size ?: 0)) {
                return
            }
            val model = getGroup(groupPosition) ?: return
            if (model.mTitle.isEmpty()) {
                mIcon.visibility = View.INVISIBLE
                mName.visibility = View.INVISIBLE
                mDetail.visibility = View.INVISIBLE
                mCOUICheckBox.visibility = View.INVISIBLE
                mCOUIRotateView.visibility = View.INVISIBLE
                return
            }
            mCOUICheckBox.setOnClickListener { view ->
                mParentCOUICheckBoxClickListener?.onClick(view, groupPosition)
            }
            mCOUICheckBox.state = model.getChosenState()
            val count = model.getCount()
            val countString = mContext.resources.getQuantityString(com.filemanager.common.R.plurals.text_x_items, count, count)
            mDetail.text = if (Utils.isRtl()) {
                (RTL_SYMBOL + Utils.byteCountToDisplaySize(model.getSize()) + RTL_SYMBOL + "  " + countString)
            } else {
                (Utils.byteCountToDisplaySize(model.getSize()) + "  " + countString)
            }
            mCOUICheckBox.visibility = View.VISIBLE
            mDetail.visibility = View.VISIBLE

            setIcon(model)
            mIcon.visibility = View.VISIBLE
            mName.visibility = View.VISIBLE
            mName.text = model.mTitle
            mCOUIRotateView.apply {
                visibility = if (model.mChild.isEmpty()) {
                    View.INVISIBLE
                } else {
                    View.VISIBLE
                }
                isExpanded = expanded
            }
        }

        fun setIcon(model: ListPrimary) {
            when (model.mTitle) {
                mContext.getString(com.filemanager.common.R.string.string_photos) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_image_def_icon)
                }
                mContext.getString(com.filemanager.common.R.string.string_videos) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_video_def_icon)
                }
                mContext.getString(com.filemanager.common.R.string.string_audio) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_audio)
                }
                mContext.getString(com.filemanager.common.R.string.string_documents) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_doc)
                }
                mContext.getString(com.filemanager.common.R.string.string_apk) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_apk_icon)
                }
            }
        }
    }

    inner class ItemChildViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        private val mIcon: ImageView = view.findViewById(R.id.child_file_icon)
        private val mName: TextView = view.findViewById(R.id.child_file_name)
        private val mDetail: TextView = view.findViewById(R.id.child_file_detail)
        private val mPhotoDetail: TextView = view.findViewById(R.id.child_photo_detail)
        private val mAlbumName: TextView = view.findViewById(R.id.child_photo_name)
        private val mSizeDetail: TextView = view.findViewById(R.id.child_file_size)
        private val mJumpImg: ImageView = view.findViewById(R.id.jump_photo_mark)
        private val mCOUICheckBox: COUICheckBox = view.findViewById(R.id.child_file_checkbox)
        private val mContext: Context = MyApplication.sAppContext

        fun bindTo(groupPosition: Int, childPosition: Int) {
            if ((groupPosition >= (mChildItems?.size ?: 0)) ||
                    (childPosition >= (mChildItems?.get(groupPosition)?.size ?: 0))) {
                return
            }
            val model = getChild(groupPosition, childPosition) ?: return
            mCOUICheckBox.setOnClickListener { view ->
                mChildCOUICheckBoxClickListener?.onClick(view, groupPosition, childPosition)
            }
            when (model) {
                is ItemChild -> {
                    mCOUICheckBox.state = model.getChosenState()
                    setIcon(model)
                    mName.text = model.mFile.mDisplayName
                    mDetail.text = if (Utils.isRtl()) {
                        RTL_SYMBOL + Utils.byteCountToDisplaySize(model.mFile.mSize) + RTL_SYMBOL
                    } else {
                        Utils.byteCountToDisplaySize(model.mFile.mSize)
                    }
                    mAlbumName.visibility = View.GONE
                    mSizeDetail.visibility = View.GONE
                    mCOUICheckBox.visibility = View.VISIBLE
                    mIcon.visibility = View.VISIBLE
                    mName.visibility = View.VISIBLE
                    mDetail.visibility = View.VISIBLE
                    mPhotoDetail.visibility = View.INVISIBLE
                    mJumpImg.visibility = View.INVISIBLE
                }
                is ListSecond -> {
                    mCOUICheckBox.state = model.getChosenState()
                    val countString = mContext.resources.getQuantityString(
                        com.filemanager.common.R.plurals.text_x_items,
                        model.mChild.size,
                        model.mChild.size
                    )
                    mSizeDetail.text =
                        if (Utils.isRtl()) {
                            (RTL_SYMBOL + Utils.byteCountToDisplaySize(model.getSize()) + RTL_SYMBOL + "   " + countString)
                        } else {
                            (Utils.byteCountToDisplaySize(model.getSize()) + "   " + countString)
                        }
                    mPhotoDetail.text = if (Utils.isRtl()) {
                        mContext.getString(com.filemanager.common.R.string.akey_already_chosen) + "   " +
                                RTL_SYMBOL + Utils.byteCountToDisplaySize(model.getSelectedSize()) + RTL_SYMBOL
                    } else {
                        mContext.getString(com.filemanager.common.R.string.akey_already_chosen) + "   " +
                                Utils.byteCountToDisplaySize(model.getSelectedSize())
                    }
                    mJumpImg.visibility = if (model.mChild.size == 0) {
                        View.INVISIBLE
                    } else {
                        View.VISIBLE
                    }
                    mCOUICheckBox.visibility = View.VISIBLE
                    mDetail.visibility = View.INVISIBLE
                    mPhotoDetail.visibility = View.VISIBLE
                    mSizeDetail.visibility = View.VISIBLE
                    mAlbumName.text = model.mTitle
                    mAlbumName.visibility = View.VISIBLE
                    mJumpImg.visibility = if (model.mChild.size == 0) {
                        View.INVISIBLE
                    } else {
                        View.VISIBLE
                    }
                    mName.visibility = View.GONE
                    mIcon.visibility = View.GONE
                }
            }
        }

        fun setIcon(model: ItemChild) {
            val file = model.mFile
            FileImageLoader.sInstance.clear(mContext, mIcon)
            FileImageLoader.sInstance.displayDefault(file, mIcon, 0)
        }
    }

    interface ParentCOUICheckBoxListener {
        fun onClick(view: View, groupPosition: Int)
    }

    interface ChildCOUICheckBoxListener {
        fun onClick(view: View, groupPosition: Int, childPosition: Int)
    }

}
