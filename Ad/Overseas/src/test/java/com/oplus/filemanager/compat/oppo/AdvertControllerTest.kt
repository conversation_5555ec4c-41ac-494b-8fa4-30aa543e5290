/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AdvertControllerTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import android.content.Context
import android.view.View
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.compat.brand.oppo.AdvertController
import com.opos.overseas.ad.api.IAdListener
import com.opos.overseas.ad.api.delegate.IMixAdActionTemplateDelegate
import com.opos.overseas.ad.api.template.ITemplateAd
import com.opos.overseas.ad.api.template.TemplateAdViewAttributes
import com.opos.overseas.ad.entry.api.TemplateAdLoader
import com.opos.overseas.ad.strategy.api.response.AppInfo
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.powermock.reflect.Whitebox

@RunWith(JUnit4::class)
class AdvertControllerTest {

    @MockK
    lateinit var context: Context

    private val advertCallback = object : AdvertController.AdvertCallback {
        override fun loadSuccess(templateAd: ITemplateAd?) {
            println("loadSuccess===========")
        }

        override fun closeAdvert() {
            println("closeAdvert===========")
        }
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun assert_value_when_release() {
        val controller = AdvertController(advertCallback)
        val temp = object : ITemplateAd {
            override fun getAdId(): String {
                return ""
            }

            override fun getPosId(): String {
                return ""
            }

            override fun isAdValid(): Boolean {
                return true
            }

            override fun getChannel(): Int {
                return 0
            }

            override fun getCreative(): Int {
                return 0
            }

            override fun getChainId(): String {
                return ""
            }

            override fun getUiType(): Int {
                return 0
            }

            override fun getPkg(): String {
                return ""
            }

            override fun setAdListener(p0: IAdListener?) {
                p0?.apply {
                    onAdExpose()
                    onAdClick()
                    onAdDismissed()
                    onAdClose()
                    onAdError(0, "")
                }
            }

            override fun setMixAdActionTemplateDelegate(p0: IMixAdActionTemplateDelegate) {
            }

            override fun buildTemplateView(p0: Context): View {
                return View(context)
            }

            override fun buildTemplateView(p0: Context, p1: TemplateAdViewAttributes?): View {
                return View(context)
            }

            override fun refreshTemplateView(p0: Context, p1: TemplateAdViewAttributes?) {
            }

            override fun refreshTemplateView(p0: Context) {
            }


            override fun destroy() {
                println("destroy===========")
            }

            override fun switchUiMode(p0: Boolean) {
            }

            override fun getCustomReqId(): String {
                return ""
            }

            override fun getReqId(): String {
                return ""
            }

            override fun getAppInfo(): AppInfo {
                return AppInfo("", "")
            }

            override fun getAdUrl(): String {
                return ""
            }
        }
        Whitebox.setInternalState(controller, "mTemplateAd", temp)
        Assert.assertNotNull(Whitebox.getInternalState(controller, "mTemplateAd"))
        controller.release()
        Assert.assertNull(Whitebox.getInternalState(controller, "mTemplateAd"))
        controller.release()
        Assert.assertNull(Whitebox.getInternalState(controller, "mTemplateAd"))
    }


    @Test
    fun assert_value_when_initAdClick() {
        mockkObject(MyApplication)
        every { context.applicationContext }.returns(context)
        every { MyApplication.appContext }.returns(context)
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }.answers { nothing }
        val temp = object : ITemplateAd {
            override fun getAdId(): String {
                return ""
            }

            override fun getPosId(): String {
                return ""
            }

            override fun isAdValid(): Boolean {
                return true
            }

            override fun getChannel(): Int {
                return 0
            }

            override fun getCreative(): Int {
                return 0
            }

            override fun getChainId(): String {
                return ""
            }

            override fun getUiType(): Int {
                return 0
            }

            override fun getPkg(): String {
                return ""
            }

            override fun setAdListener(p0: IAdListener?) {
                p0?.apply {
                    onAdExpose()
                    onAdClick()
                    onAdDismissed()
                    onAdClose()
                    onAdError(0, "")
                }
            }

            override fun setMixAdActionTemplateDelegate(p0: IMixAdActionTemplateDelegate) {
            }

            override fun buildTemplateView(p0: Context): View {
                return View(context)
            }

            override fun buildTemplateView(p0: Context, p1: TemplateAdViewAttributes?): View {
                return View(context)
            }

            override fun refreshTemplateView(p0: Context, p1: TemplateAdViewAttributes?) {
            }


            override fun refreshTemplateView(p0: Context) {
            }


            override fun destroy() {
                println("destroy===========")
            }

            override fun switchUiMode(p0: Boolean) {
            }

            override fun getCustomReqId(): String {
                return ""
            }

            override fun getReqId(): String {
                return ""
            }

            override fun getAppInfo(): AppInfo {
                return AppInfo("", "")
            }

            override fun getAdUrl(): String {
                return ""
            }
        }

        val controller = AdvertController(advertCallback)
        Whitebox.invokeMethod<Void>(controller, "initAdClick", temp)
    }

    @Test
    fun assert_value_when_startWork() {
        val controller = AdvertController(advertCallback)
        val templateAdLoader = mockk<TemplateAdLoader>()
        Whitebox.setInternalState(controller, "mTemplateAdLoader", templateAdLoader)
        controller.startWork("main")
        Assert.assertFalse(Whitebox.getInternalState(controller, "mRequesting"))
    }
}