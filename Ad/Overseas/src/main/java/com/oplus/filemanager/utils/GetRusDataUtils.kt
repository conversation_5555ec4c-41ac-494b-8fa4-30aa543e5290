/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: GetRusDataUtils.kt
 ** Description: for ad rus util
 ** Version: 4.0
 ** Date : 2020/9/27
 ** Author: HuiHua Teng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * HuiHua.Teng 2020/9/27      4.0    modify
 ****************************************************************/

package com.oplus.filemanager.utils

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.Settings
import com.filemanager.common.utils.Log
import com.oplus.filemanager.compat.brand.oppo.AdvertController
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader

class GetRusDataUtils(private val context: Context) {

    companion object {
        private const val TAG = "GetRusDataUtils"

        const val APPS_SECURITY_CONF: String = "apps_security_analysis_appconf"

        private const val CHECK_FILE_AD_IS_OPEN = "filemanager_ad_status"
        const val FILE_AD_STATUS_OPEN = 1

        fun getRusEnabled(context: Context): Boolean {
            val status: Int = Settings.System.getInt(context.contentResolver, CHECK_FILE_AD_IS_OPEN, FILE_AD_STATUS_OPEN)
            Log.d(AdvertController.AD_TAG, "save_rus_value = $status")
            return status == FILE_AD_STATUS_OPEN
        }
    }

    private val CONTENT_URI = Uri.parse("content://com.nearme.romupdate.provider.db/update_list")
    private val COLUMN_NAME_XML = "xml"
    private val TAG_MUSIC_AD_IS_OPEN = "check_filemanager_ad_is_open"

    fun updateParameterList() {
        val xmlValue = getDataFromProvider(APPS_SECURITY_CONF)
        parserConfigXmlValue(xmlValue)
    }

    private fun getDataFromProvider(filterName: String): String? {
        var cursor: Cursor? = null
        var xmlValue: String? = null
        val projection = arrayOf("xml")
        return try {
            cursor = context.contentResolver?.query(CONTENT_URI,
                projection, "filtername=\"$filterName\"", null, null)
            if (cursor != null && cursor.count > 0) {
                val xmlColumnIndex = cursor.getColumnIndex(COLUMN_NAME_XML)
                cursor.moveToNext()
                xmlValue = cursor.getString(xmlColumnIndex)
            } else {
                Log.d(TAG, "cursor is null !!!  filterName=$filterName")
            }
            xmlValue
        } catch (e: Exception) {
            Log.d(TAG, "We can not get data from provider,because of $e")
            null
        } finally {
            cursor?.close()
        }
    }

    private fun parserConfigXmlValue(xmlValue: String?) {
        if (xmlValue == null || xmlValue.isEmpty()) {
            return
        }
        try {
            val parser: XmlPullParser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(StringReader(xmlValue))
            parser.nextTag()
            var type = -1
            do {
                type = parser.next()
                if (type == XmlPullParser.START_TAG) {
                    val tag: String = parser.name
                    if (TAG_MUSIC_AD_IS_OPEN == tag) {
                        val value: String = parser.nextText()
                        if (value != null) {
                            Log.d(TAG, "RUS security_analysis_result = $value")
                            updateCheckAdIsOpenValue(value)
                        }
                    }
                }
            } while (type != XmlPullParser.END_DOCUMENT)
        } catch (e: Exception) {
            Log.d(TAG, "failed parsing $e")
        }
    }

    private fun updateCheckAdIsOpenValue(value: String) {
        var saveValue: Int
        try {
            saveValue = value.toInt()
        } catch (e: NumberFormatException) {
            saveValue = FILE_AD_STATUS_OPEN
            Log.e(TAG, "failed parsing $e")
        }
        Settings.System.putInt(context.contentResolver, CHECK_FILE_AD_IS_OPEN, saveValue)
    }
}