/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AdvertControlUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/22 15:42
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/22       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.utils

import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.controller.PersonalizedServiceController
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.compat.AdvertSwitchHelp
import com.oplus.filemanager.compat.brand.realme.AdvertEntry.Companion.AD_TAG
import com.oplus.filemanager.utils.AdHelper.allAdSwitch
import com.oplus.filemanager.utils.AdHelper.showAdSwitch
import com.oplus.filemanager.utils.AdHelper.splashAdSwitch
import java.time.ZoneId
import java.time.ZonedDateTime

private const val ONE_DAY = 24 * 60 * 60 * 1000L

const val SP_KEY_CLOSE_TIME = "closeTimeDuringOneDayKey"

const val SP_NAME = "closeTimeDuringOneDay"
const val SP_NAME_OPEN_AD = "OpenShowDays"
const val SP_NAME_MAIN_AD = "MainShowDays"
private const val MACHINE_MODEL_NARZO = "narzo"
private const val APP_ACTIVE_TIME = "filemanager_advert_app_active_time"
private const val APP_ACTIVIE_TIME_YEAR = 2023
private const val APP_ACTIVIE_TIME_MONTH = 11
private const val APP_ACTIVIE_TIME_DAY = 1
private val mPersonalizedServiceController: PersonalizedServiceController by lazy { PersonalizedServiceController() }
/**
 * Check if the time interval for closing the ad satisfies the condition.
 *
 * @param ignore Indicates whether to ignore the check.=
 * @return true means the close time less than [ONE_DAY], don't display the advert.
 */
fun checkIfDuringCloseTime(ignore: Boolean = false): Boolean {
    if (ignore) {
        Log.w(AdConstants.AD_TAG, "ignore one-day check logic")
        return false
    }
    val closeSystemTime = PreferencesUtils.getLong(SP_NAME, SP_KEY_CLOSE_TIME, 0L)
    if (closeSystemTime != 0L && System.currentTimeMillis() - closeSystemTime < ONE_DAY) {
        Log.w(AdConstants.AD_TAG, "Less than a day since last display advert.")
        return true
    }
    return false
}

/**
 * 判断是否超过可曝光天数
 * */
fun checkIfExceedDays(ignore: Boolean = false, days: Int): Boolean {
    if (ignore) {
        Log.w(AdConstants.AD_TAG, "ignore one-day check logic")
        return false
    }
    val closeSystemTime = PreferencesUtils.getLong(SP_NAME_MAIN_AD, SP_KEY_CLOSE_TIME, 0L)
    Log.d(
        AdConstants.AD_TAG,
        "$closeSystemTime,${System.currentTimeMillis() - closeSystemTime},${System.currentTimeMillis()},${ONE_DAY * days}"
    )
    if (closeSystemTime != 0L && System.currentTimeMillis() - closeSystemTime < ONE_DAY * days) {
        Log.w(AdConstants.AD_TAG, "Less than $days days since last display main advert.")
        return true
    }
    return false
}

fun isNewMachineProtection(): Boolean {
    val timeInterval = System.currentTimeMillis() - appActiveTime()
    val protectionDays = if (PropertyCompat.sOplusSeries == MACHINE_MODEL_NARZO) {
        AdHelper.newMachineProtectionNarzo
    } else {
        AdHelper.newMachineProtection
    }

    val protectionInterval = protectionDays * ONE_DAY
    val result = timeInterval in 1 until protectionInterval
    Log.d(AD_TAG, "isNewMachineProtection:$result")
    return result
}
fun appActiveTime(): Long {
    val saveTime = PreferencesUtils.getLong(key = APP_ACTIVE_TIME, default = -1)
    var curTime = -1L
    if (saveTime == -1L) {
        val zonedDateTime = ZonedDateTime.of(
            APP_ACTIVIE_TIME_YEAR,
            APP_ACTIVIE_TIME_MONTH, APP_ACTIVIE_TIME_DAY, 0, 0, 0, 0, ZoneId.systemDefault()
        )
        val standardTime = zonedDateTime.toInstant().toEpochMilli()
        curTime = System.currentTimeMillis()

        if (curTime >= standardTime) {
            PreferencesUtils.put(key = APP_ACTIVE_TIME, value = curTime)
        } else {
            Log.d(AD_TAG, "active time invalid")
        }
    } else {
        curTime = saveTime
        Log.d(AD_TAG, "active time valid")
    }
    return curTime
}

/**
 * 判断云控广告开关和个性化开关是否打开
 * */
fun isSwitchAndCloudConfigEnabled(isSplashAd: Boolean): Boolean {
    val mIsSplashAd: Boolean = if (isSplashAd) splashAdSwitch else true //如果是闪屏页，需要判断splashAdSwitch是否开启，否则为true
    val showAd = AdvertSwitchHelp.isShowAd(appContext)//如果广告开关打开并且是支持机型则为true，否则为false
    val isNewMachineProtection = isNewMachineProtection()//是否新机保护
    val isSwitchAndCloudConfigEnabled = when {
        mIsSplashAd && allAdSwitch && !isNewMachineProtection -> showAdSwitch.takeIf { showAd } ?: false
        else -> false
    }
    val checkIsSupportAd = mPersonalizedServiceController.checkIsSupportAd(appContext)
    Log.d(AD_TAG, "showAd=$showAd, splashAdSwitch=$splashAdSwitch, allAdSwitch=$allAdSwitch, showAdSwitch=$showAdSwitch")
    Log.d(AD_TAG, "isSwitchAndCloudConfigEnabled = $isSwitchAndCloudConfigEnabled,checkIsSupportAd = $checkIsSupportAd")
    return isSwitchAndCloudConfigEnabled && checkIsSupportAd
}