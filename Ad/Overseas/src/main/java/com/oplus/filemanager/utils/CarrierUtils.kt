/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: CarrierUtils
 * * Description: CarrierUtils
 * * Version: 1.0
 * * Date : 2024/11/04
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/11/04      1.0            create
 ****************************************************************/
package com.oplus.filemanager.utils

import android.content.Context
import android.os.Looper
import androidx.annotation.WorkerThread
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.Log
import com.oplus.filemanager.carrier.CarrierEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking

object CarrierUtils {
    private const val TAG = "CarrierUtils"
    private const val CARRIER_JSON = "carrier.json"

    private var isSupport: Boolean? = null

    @JvmStatic
    fun isCarrierSupport(context: Context): Boolean {
        if (isSupport == null) {
            isSupport = if (isMainThread()) {
                runBlocking(Dispatchers.IO) {
                    isCarrierSupportAsync(context)
                }
            } else {
                isCarrierSupportAsync(context)
            }
        }
        Log.d(TAG, "isCarrierSupport -> result:$isSupport")
        return isSupport ?: false
    }

    @WorkerThread
    @JvmStatic
    fun isCarrierSupportAsync(context: Context): Boolean {
        // 读取json文件，转成java bean
        val list = parseJson(context)
        // 读取手机中的属性值
        val channelName = PropertyCompat.channelInfo
        val country = PropertyCompat.channelCountry
        val carrier = PropertyCompat.pipelineCarrier
        val pipelineRegion = PropertyCompat.pipelineRegion
        val isSupport = isCarrierSupport(list, channelName, country, carrier, pipelineRegion)
        Log.d(TAG, "isCarrierSupport -> list:${list.size} channel:$channelName country:$country carrier:$carrier region:$pipelineRegion")
        return isSupport
    }

    @JvmStatic
    fun isCarrierSupport(
        list: List<CarrierEntity>,
        channelName: String?,
        channelCountry: String?,
        carrier: String?,
        pipelineRegion: String?
    ): Boolean {
        if (!channelName.isNullOrEmpty() && !channelCountry.isNullOrEmpty()) {
            return isCOTASupport(list, channelName, channelCountry)
        }
        if (!carrier.isNullOrEmpty()) {
            return isCustomSupport(list, carrier)
        }
        if (!pipelineRegion.isNullOrEmpty()) {
            return isPublicSupport(list, pipelineRegion)
        }
        return true
    }

    /**
     * 解析json文件为java bean对象
     */
    @JvmStatic
    private fun parseJson(context: Context): List<CarrierEntity> {
        val input = context.assets.open(CARRIER_JSON)
        val json = FileUtils.read(input)
        val list = TextUtils.fromJson(json)
        return list
    }

    /**
     * 运营商COTA 版本是否支持
     */
    @JvmStatic
    private fun isCOTASupport(list: List<CarrierEntity>, channelName: String, channelCountry: String): Boolean {
        list.forEach {
            val channelNameSet = it.channelName?.toHashSet()
            val channelCountrySet = it.regionCode?.toHashSet()
            if (channelNameSet?.contains(channelName) == true && channelCountrySet?.contains(channelCountry) == true) {
                return it.isSupport
            }
        }
        return true
    }

    /**
     * 运营商定制版是否支持
     * 通过流水线KEY值 进行判断
     */
    @JvmStatic
    private fun isCustomSupport(list: List<CarrierEntity>, carrier: String?): Boolean {
        list.forEach {
            val keySet = it.pipelineKey?.toHashSet()
            if (keySet?.contains(carrier) == true) {
                return it.isSupport
            }
        }
        return true
    }

    /**
     * 公版是否支持
     */
    @JvmStatic
    private fun isPublicSupport(list: List<CarrierEntity>, pipelineRegion: String): Boolean {
        list.forEach {
            if (it.regionCode?.contains(pipelineRegion) == true) {
                return it.isSupport
            }
        }
        return true
    }

    /**
     * 是否在主线程
     */
    @JvmStatic
    private fun isMainThread(): Boolean {
        return Thread.currentThread() == Looper.getMainLooper().thread
    }
}