/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeHomeMaxAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_CLICK
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_FILL
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_REQUEST
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_SHOW
import com.filemanager.common.utils.StatisticsUtils.AD_REQUEST_FAIL
import com.filemanager.common.utils.StatisticsUtils.BANNER_ADS_REQ_FAIL
import com.filemanager.common.utils.StatisticsUtils.EVENT_AD_LOAD_TAG
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_BANNER_ADS_CLICK
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_BANNER_ADS_DISPALY
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_BANNER_ADS_FILL
import com.filemanager.common.utils.StatisticsUtils.REALME_FILE_EVENT
import com.filemanager.common.R
import com.filemanager.common.constants.Constants
import com.oplus.filemanager.utils.AdHelper

class RealmeHomeMaxAdMgr : BaseHomePageAdMgr(), MaxAdViewAdListener {
    private var adView: MaxAdView? = null
    companion object {
        private const val AD_POS_ID = "f8cf4b043086d423"
    }

    override fun requestMainAd(activity: Activity) {
        if (!AdHelper.homeAdSwitch) {
            // 云控关闭了首页广告
            Log.e(AdvertEntry.AD_TAG, "mdp home ad switch is closed")
            return
        }

        Log.d(AdvertEntry.AD_TAG, "home banner request Ad")
        adView = MaxAdView(AD_POS_ID, activity)
        adView?.setListener(this)

        // Stretch to the width of the screen for banners to be fully functional
        val width = ViewGroup.LayoutParams.MATCH_PARENT

        val height = if (ModelUtils.isTablet()) R.dimen.dimen_90dp else R.dimen.dimen_50dp
        // Banner height on phones and tablets is 50 and 90, respectively
        val heightPx = activity.resources.getDimensionPixelSize(height)

        adView?.layoutParams = FrameLayout.LayoutParams(width, heightPx)

        // Set background or background color for banners to be fully functional
        adView?.setBackgroundColor(Color.BLACK)
        adView?.loadAd()

        val map = HashMap<String, String>()
        map[StatisticsUtils.BANNER_ADS_REQ] = AD_EVENT_REQUEST
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdRequestFlow(appContext, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun eventOnDestroy() {
        Log.d(AdvertEntry.AD_TAG, "home banner eventOnDestroy")
        adView?.destroy()
        mMainFragmentContainer?.removeAllViews()
    }

    override fun onAdLoaded(maxAd: MaxAd) {
        mMainFragmentContainer?.let {
            it.removeAllViews()
            it.visibility = View.VISIBLE
            it.addView(adView)
        }

        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdLoaded")
        val map = HashMap<String, String>()
        map[HOMEPAGE_BANNER_ADS_FILL] = AD_EVENT_FILL
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdReturnFlow(appContext, true, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun onAdDisplayed(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdDisplayed:$maxAd")
        val map = HashMap<String, String>()
        map[HOMEPAGE_BANNER_ADS_DISPALY] = AD_EVENT_SHOW
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdShowFlow(appContext, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun onAdHidden(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdHidden:$maxAd")
    }

    override fun onAdClicked(maxAd: MaxAd) {
        val map = HashMap<String, String>()
        map[HOMEPAGE_BANNER_ADS_CLICK] = AD_EVENT_CLICK
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdClick(appContext, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun onAdLoadFailed(message: String, error: MaxError) {
        adRootView?.visibility = View.GONE
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdLoadFailed:${error.code}  message:${error.message}")
        val map = HashMap<String, String>()
        map[BANNER_ADS_REQ_FAIL] = AD_REQUEST_FAIL
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdReturnFlow(appContext, false, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun onAdDisplayFailed(maxAd: MaxAd, error: MaxError) {
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdDisplayFailed")
    }

    override fun onAdExpanded(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdExpanded")
        StatisticsUtils.statisticsAdLoadFlow(appContext, Constants.PAGE_MAIN, AdHelper.adSource)
    }

    override fun onAdCollapsed(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max binner ad onAdCollapsed")
    }
}