/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: FileUtils
 * * Description: 文件相关的工具类
 * * Version: 1.0
 * * Date : 2024/11/01
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/11/01      1.0            create
 ****************************************************************/
package com.oplus.filemanager.utils

import android.util.Log
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader
import java.io.Reader

object FileUtils {

    private const val TAG = "FileUtils"

    @JvmStatic
    fun write(file: File, content: String) {
        Log.d(TAG, "write start....")
        val time = System.currentTimeMillis()
        // 确保文件存在
        ensureFileExist(file)

        val fileWriter = FileWriter(file)
        val writer = BufferedWriter(fileWriter)
        try {
            writer.append(content)
            writer.flush()
        } catch (e: IOException) {
            Log.e(TAG, "write error, file: ${file.absolutePath}", e)
        } finally {
            fileWriter.close()
            writer.close()
        }
        Log.d(TAG, "write end..., cost ${System.currentTimeMillis() - time} ms!!")
    }

    /**
     * 确保文件是否存在
     */
    @JvmStatic
    private fun ensureFileExist(file: File) {
        try {
            if (!file.exists()) {
                file.createNewFile()
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "ensureFileExist security error, file: ${file.absolutePath}", e)
        } catch (e: IOException) {
            Log.e(TAG, "ensureFileExist io error, file: ${file.absolutePath}", e)
        }
    }

    /**
     * 读取文件
     */
    @JvmStatic
    fun read(file: File): String {
        if (!file.exists()) {
            throw IllegalArgumentException("file not exists:${file.absolutePath}")
        }
        val fileReader = FileReader(file)
        return read(fileReader)
    }

    /**
     * 读取文件
     */
    @JvmStatic
    fun read(input: InputStream): String {
        val inputReader = InputStreamReader(input)
        return read(inputReader)
    }

    /**
     * 读取文件
     */
    @JvmStatic
    private fun read(reader: Reader): String {
        Log.d(TAG, "read start....")
        val time = System.currentTimeMillis()
        val bufferReader = BufferedReader(reader)
        var result = ""
        try {
            result = bufferReader.readText()
        } catch (e: IOException) {
            Log.e(TAG, "read error", e)
        } finally {
            reader.close()
            bufferReader.close()
        }
        Log.d(TAG, "read end..., cost ${System.currentTimeMillis() - time} ms!!")
        return result
    }
}