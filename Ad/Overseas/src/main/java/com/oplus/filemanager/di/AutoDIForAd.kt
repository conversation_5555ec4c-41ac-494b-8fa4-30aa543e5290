/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForArchive
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.di

import androidx.annotation.Keep
import com.oplus.filemanager.AdvertApi
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import org.koin.dsl.module

@Keep
class AutoDIForAd {

    val advertSwitchHelp = module {
        single<IAdvertApi>(createdAtStart = true) {
            AdvertApi
        }
    }
}