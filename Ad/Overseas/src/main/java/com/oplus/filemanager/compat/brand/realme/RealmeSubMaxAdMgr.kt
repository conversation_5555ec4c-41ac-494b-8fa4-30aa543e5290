/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeSubMaxAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_CLICK
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_FILL
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_REQUEST
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_SHOW
import com.filemanager.common.utils.StatisticsUtils.AD_REQUEST_FAIL
import com.filemanager.common.utils.StatisticsUtils.BANNER_ADS_REQ
import com.filemanager.common.utils.StatisticsUtils.BANNER_ADS_REQ_FAIL
import com.filemanager.common.utils.StatisticsUtils.EVENT_AD_LOAD_TAG
import com.filemanager.common.utils.StatisticsUtils.REALME_FILE_EVENT
import com.filemanager.common.utils.StatisticsUtils.SECONDARYPAGE_BANNER_ADS_CLICK
import com.filemanager.common.utils.StatisticsUtils.SECONDARYPAGE_BANNER_ADS_DISPALY
import com.filemanager.common.utils.StatisticsUtils.SECONDARYPAGE_BANNER_ADS_FILL
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.utils.AdHelper

class RealmeSubMaxAdMgr : BaseSubPageAdMgr(), MaxAdViewAdListener {
    private var mCount: Int = 0
    private var adView: MaxAdView? = null
    private var adLayout: RelativeLayout? = null
    private var multiFmAdEntityItem: MultiFmAdEntityItem? = null

    companion object {
        private const val AD_POS_ID = "99d3d27f39c925cd"
    }

    override fun makeName(name: String): String {
        mCount++
        return name + mCount.toString()
    }

    override fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>,
        needHideAd: Boolean
    ) {
        if (!AdHelper.subAdSwitch) {
            // 云控关闭了二级页面广告
            Log.e(AdvertEntry.AD_TAG, "mdp sub ad switch is closed")
            return
        }

        Log.d(AdvertEntry.AD_TAG, "request sub banner ad")
        multiFmAdEntityItem = MultiFmAdEntityItem(null, name, adapter, fileList, needHideAd)

        adLayout = RelativeLayout(activity)
        adLayout?.layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT)
        adView = MaxAdView(AD_POS_ID, activity)
        adView?.setListener(this)

        // Stretch to the width of the screen for banners to be fully functional
        val width = ViewGroup.LayoutParams.MATCH_PARENT

        val height = if (ModelUtils.isTablet()) R.dimen.dimen_90dp else R.dimen.dimen_50dp
        // Banner height on phones and tablets is 50 and 90, respectively
        val heightPx = activity.resources.getDimensionPixelSize(height)

        adView?.layoutParams = FrameLayout.LayoutParams(width, heightPx)

        // Set background or background color for banners to be fully functional
        adView?.setBackgroundColor(Color.BLACK)
        adLayout?.visibility = View.VISIBLE
        adLayout?.addView(adView, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT))

        // Load the ad
        adView?.loadAd()

        val map = HashMap<String, String>()
        map[BANNER_ADS_REQ] = AD_EVENT_REQUEST
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdRequestFlow(appContext, this.name, AdHelper.adSource)
    }

    override fun scanModeChange(name: String, needHideAd: Boolean) {
        if ((adLayout?.visibility == View.VISIBLE) && (adLayout!!.childCount > 0)) {
            multiFmAdEntityItem?.setNeedHideAD(needHideAd)
            multiFmAdEntityItem?.onLoadSuccess()
        }
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return adLayout
    }

    override fun eventDestroy() {
        adLayout?.removeAllViews()
        adView?.destroy()
        adLayout?.visibility = View.GONE
    }

    override fun onAdLoaded(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoaded")
        multiFmAdEntityItem?.onLoadSuccess()

        val map = HashMap<String, String>()
        map[SECONDARYPAGE_BANNER_ADS_FILL] = AD_EVENT_FILL
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdReturnFlow(appContext, true, this.name, AdHelper.adSource)
    }

    override fun onAdDisplayed(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdDisplayed")

        val map = HashMap<String, String>()
        map[SECONDARYPAGE_BANNER_ADS_DISPALY] = AD_EVENT_SHOW
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdShowFlow(appContext, this.name, AdHelper.adSource)
    }

    override fun onAdHidden(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdHidden")
    }

    override fun onAdClicked(maxAd: MaxAd) {
        val map = HashMap<String, String>()
        map[SECONDARYPAGE_BANNER_ADS_CLICK] = AD_EVENT_CLICK
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdClick(appContext, this.name, AdHelper.adSource)
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdClicked")
    }

    override fun onAdLoadFailed(message: String, error: MaxError) {
        adLayout?.removeAllViews()
        adLayout?.visibility = View.GONE
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoadFailed:${error.code}")
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoadFailed:${error.message}")

        val map = HashMap<String, String>()
        map[BANNER_ADS_REQ_FAIL] = AD_REQUEST_FAIL
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdReturnFlow(appContext, false, this.name, AdHelper.adSource)
    }

    override fun onAdDisplayFailed(maxAd: MaxAd, error: MaxError) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdDisplayFailed:${error.message}")
    }

    override fun onAdExpanded(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdExpanded")
    }

    override fun onAdCollapsed(maxAd: MaxAd) {
        Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdCollapsed")
    }
}