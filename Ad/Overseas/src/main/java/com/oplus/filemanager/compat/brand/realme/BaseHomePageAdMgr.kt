/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseHomePageAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : W8083229
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W8083229        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.RelativeLayout
import com.filemanager.common.R
import com.oplus.filemanager.compat.interfaces.IHomePageAdMgr

open class BaseHomePageAdMgr : IHomePageAdMgr {
    var mMainFragmentContainer: ViewGroup? = null
    var adRootView: View? = null

    override fun setContainer(rootView: View, rootViewId: Int) {
        adRootView = (rootView.findViewById(rootViewId) as? ViewStub)?.inflate()
        mMainFragmentContainer = adRootView?.findViewById(R.id.ad_container_top)
        adRootView?.visibility = View.GONE
    }

    override fun requestMainAd(activity: Activity) {}

    override fun getEntryView(name: String): RelativeLayout? {
        return null
    }

    override fun eventOnDestroy() {}
}