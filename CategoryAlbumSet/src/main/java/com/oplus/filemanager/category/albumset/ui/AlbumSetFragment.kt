/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : Fragment of AlbumSet
 * * Version     : 1.0
 * * Date        : 2020/6/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.albumset.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.ALBUM_SCAN_MODE_SP_KEY
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_ALBUM_SET
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.oplus.filemanager.category.albumset.R
import com.oplus.filemanager.category.albumset.adapter.AlbumSetAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting

class AlbumSetFragment : BaseVMFragment<AlbumSetFragmentViewModel>(), OnBackPressed,
    OnRecyclerItemClickListener, OnGetUIInfoListener, IPreviewListFragment {
    companion object {
        const val TAG = "AlbumSetFragment"
    }

    private var mRecyclerView: FileManagerRecyclerView? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mToolbar: COUIToolbar? = null
    private var mViewModel: AlbumSetFragmentViewModel? = null
    private var mTitle: String? = null
    private var mSetAdapter: AlbumSetAdapter? = null
    private var mLayoutManager: GridLayoutManager? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(GRID_ITEM_DECORATION_ALBUM_SET, activity = activity)
    }
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var hasShowEmpty: Boolean = false
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_IMAGE,
            model, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(model))
        }
    }

    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    private var mLoadingController: LoadingController? = null
    private var previewOperate: IPreviewOperate? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.album_set_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            mSetAdapter = AlbumSetAdapter(it, <EMAIL>)
            mSetAdapter!!.setHasStableIds(true)
            val bundle = arguments ?: return
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            val resId = bundle.getInt(Constants.TITLE_RES_ID, -1)
            if (resId != -1) {
                mTitle = getString(resId)
            }
        }
    }

    fun setTitle(title: String?) {
        mTitle = title
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mRecyclerView = view.findViewById(R.id.recycler_view)
        mRecyclerView?.run {
            mGridSpanAnimationHelper = GridSpanAnimationHelper(this)
        }
        scrollHelper = DragScrollHelper(mRecyclerView)
        initToolbar()
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.album_set_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(!isChildDisplay)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    override fun initData(savedInstanceState: Bundle?) {
        if (mViewModel == null) {
            mViewModel = ViewModelProvider(this)[AlbumSetFragmentViewModel::class.java]
        }
        mRecyclerView?.let {
            mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType: Int? = mSetAdapter?.getItemViewType(position)
                        return if (viewType == BaseFileBean.TYPE_FILE_AD) spanCount else 1
                    }
                }
            }
            it.isNestedScrollingEnabled = true
            it.addItemDecoration(mSpacesItemDecoration)
            it.clipToPadding = false
            it.layoutManager = mLayoutManager!!
            it.itemAnimator?.changeDuration = 0
            it.itemAnimator?.addDuration = 0
            it.itemAnimator?.removeDuration = 0
            it.itemAnimator?.moveDuration = 0
            mSetAdapter?.apply {
                it.adapter = this
                setOnRecyclerItemClickListener(this@AlbumSetFragment)
            }
            mToolbar?.post {
                if (isAdded) {
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appBarLayout, 0), it.paddingRight,
                            appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.content_margin_bottom))
                    mNeedSkipAnimation = true
                    mViewModel?.mBrowseModeState?.value = mViewModel?.mBrowseModeState?.value
                }
            }
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
    }

    override fun startObserve() {
        mRecyclerView?.post {
            if (isAdded && (mViewModel != null)) {
                mViewModel!!.mUiState.observe(this) { fileUiModel ->
                    Log.d(TAG, "mUiState =" + fileUiModel.mFileList.size)
                    if (fileUiModel.mFileList.isEmpty()) {
                        showEmptyView()
                    } else {
                        mFileEmptyController.hideFileEmptyView()
                    }
                    mToolbar?.let {
                        refreshScanModeItemIcon(it)
                        refreshToolbarNormalMode(it)
                    }
                    mSetAdapter?.setData(fileUiModel.mFileList)
                    mSetAdapter?.let {
                        if (baseVMActivity is AlbumSetActivity) {
                            (baseVMActivity as AlbumSetActivity).mAdManager?.requestSubAlbumSetAd(
                                baseVMActivity!!, it,
                                fileUiModel.mFileList as ArrayList<AlbumItem>
                            )
                        }
                    }
                }
                startScanModeObserver()
                startObserveLoadState()
            }
        }
    }

    private fun refreshToolbarNormalMode(it: COUIToolbar) {
        it.menu.findItem(R.id.actionbar_edit)?.isVisible = false
        setToolbarMenuVisible(it, !isChildDisplay)
    }

    private fun startScanModeObserver() {
        mViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    mRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            mRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = mViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            updateLeftRightMargin()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = mViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mSetAdapter?.notifyDataSetChanged()
        }
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return null
    }

    override fun setNavigateItemAble() {}

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode, GRID_ITEM_DECORATION_ALBUM_SET)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mSetAdapter?.apply {
            mScanViewModel = scanMode
            notifyDataSetChanged()
            mLastRefreshScanModeTime = SystemClock.elapsedRealtime()
            if (activity is AlbumSetActivity) {
                (activity as? AlbumSetActivity)?.mAdManager?.refreshByScanModeChanged()
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val resources = appContext.resources
            val desc: String
            val resId: Int = if (mViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = resources.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = resources.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            if (needSkipAnimation) {
                it.setIcon(resId)
            } else {
                mRecyclerView?.stopScroll()
                KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(mViewModel?.mDataLoadState) {
                    mViewModel?.let { vm ->
                        vm.mUiState.value?.mFileList.isNullOrEmpty().not()
                    } ?: false
                }
            }
        }
    }

    @Deprecated("ignore")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.album_set_menu, menu)
        mToolbar?.apply {
            setToolbarMenuVisible(this, !isChildDisplay)
        }
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            android.R.id.home -> baseVMActivity?.onBackPressed()
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_IMAGE)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.IMAGE_SET)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_IMAGE_SET)
            }
            R.id.actionbar_scan_mode -> {
                mViewModel?.clickScanModeItem(baseVMActivity)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.IMAGE_SET)
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.IMAGE_SET)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_IMAGE_SET)
            }
        }
        return true
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (mViewModel?.mUiState?.value?.mFileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            previewOperate?.listEmptyFile()
        }
        Log.d(TAG, "showEmptyView")
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        checkShowPermissionEmpty()
        mViewModel?.initLoader(LoaderViewModel.getLoaderController(this))
        //The albumSet page and album page need to have the same browse mode, and the browse mode needs to be updated when the album page changes back to the albumSet page
        val albumScanMode = ConfigSharedPreferenceUtils.getInt(ALBUM_SCAN_MODE_SP_KEY, 0)
        if (albumScanMode != 0 && mViewModel?.mBrowseModeState?.value != albumScanMode) {
            mNeedSkipAnimation = true
            mViewModel?.mBrowseModeState?.value = albumScanMode
            if (albumScanMode == KtConstants.SCAN_MODE_GRID && previewOperate?.isPreviewOpen() == true) {
                //图片页面修改展示方式会同时修改图集页面的展示方式，如果展示方式被切换成了宫格展示，且当前已开启了预览，需要把预览关闭
                previewOperate?.closePreview()
            }
        }
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
        if (mViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun pressBack(): Boolean {
        return mViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return false
    }

    override fun onItemClick(view: View, position: Int) {
        mViewModel?.onItemClick(baseVMActivity, position)
    }

    override fun onItemLongClick(view: View, position: Int) {
        mViewModel?.onItemLongClick(position)
    }

    override fun getRecyclerView() = mRecyclerView

    override fun getViewModel() = mViewModel

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.apply {
            setToolbarMenuVisible(this, !isChildDisplay)
        }
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(!isChildDisplay)
        }
    }

    override fun checkPermission() {
        baseVMActivity?.let {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun backToTop() {
        mRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}
    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return mViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        mViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { fileOperateController.onSelectPathReturn(it, requestCode, paths) }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        fileOperateController.pickResultListener()

    override fun exitSelectionMode() {}

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (mViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            GRID_ITEM_DECORATION_ALBUM_SET
        )
        return true
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_PAGE_ALBUM_SET
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            mRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }
}
