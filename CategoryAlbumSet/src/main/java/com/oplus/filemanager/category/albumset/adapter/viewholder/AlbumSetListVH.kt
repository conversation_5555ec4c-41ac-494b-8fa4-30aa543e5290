/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.albumset.adapter.viewholder

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.marginStart
import androidx.core.view.updateLayoutParams
import com.filemanager.common.MyApplication
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.view.FileThumbView
import com.oplus.filemanager.category.albumset.R

class AlbumSetListVH(convertView: View) : BaseAlbumSetVH(convertView) {
    companion object {
        fun getLayoutId(): Int {
            return R.layout.album_recycler_item
        }
    }

    private val mImg: FileThumbView = convertView.findViewById(R.id.file_list_item_icon)
    private val mTitle: TextView = convertView.findViewById(R.id.album_name)
    private val mDetail: TextView = convertView.findViewById(R.id.album_num)
    private val iconContainer: View = convertView.findViewById(R.id.file_list_item_icon_container)
    private val albumContent: View = convertView.findViewById(R.id.album_content)
    private var mImageRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    override fun updateViewHolder(context: Context, file: AlbumItem, threadManager: ThreadManager) {
        val baseFileBean = covertAlbumItemToFileBean(file, context)
        updateLeftRightMargin()
        mTitle.text = baseFileBean.mDisplayName
        mImg.setBorderStyle(mImageRadius.toFloat())
        FileImageLoader.sInstance.clear(context, mImg)
        FileImageLoader.sInstance.displayDefault(baseFileBean, mImg, file.orientation, mImageRadius)
        if (file.count == 0) {
            mDetail.apply {
                asyncAlbumItemsCount(threadManager, file, this, file.key)
            }
        } else {
            setAlbumCount(mDetail, file.count)
        }
    }

    private fun updateLeftRightMargin() {
        iconContainer.updateLayoutParams<LinearLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        albumContent.updateLayoutParams<LinearLayout.LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}