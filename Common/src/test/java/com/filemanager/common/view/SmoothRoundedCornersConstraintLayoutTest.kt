/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SmoothRoundedCornersConstraintLayout
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/28 16:13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  xuechao         2022/6/28       1.0      create
 ***********************************************************************/
package com.filemanager.common.view

import android.app.Activity
import android.content.Context
import android.os.Build
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.google.android.material.shape.MaterialShapeDrawable
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@Ignore
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class SmoothRoundedCornersConstraintLayoutTest {

    private var context: Context? = null
    private var layout: SmoothRoundedCornersConstraintLayout? = null

    @Before
    fun setup() {
        context = Robolectric.buildActivity(Activity::class.java).get()
        layout = SmoothRoundedCornersConstraintLayout(context!!)
    }

    @After
    fun tearDown() {
        context = null
        layout = null
    }

    @Test
    fun should_when_setRoundCornerBg() {
        layout?.let {
            Whitebox.invokeMethod<Void>(it, "setRoundCornerBg")

            val background = it.background
            Assert.assertNotNull(background)
            Assert.assertTrue(background is MaterialShapeDrawable)
        }
    }

    @Test
    fun should_when_refreshBg() {
        layout?.let {
            it.refreshBg()

            val background = it.background
            Assert.assertNotNull(background)
            Assert.assertTrue(background is MaterialShapeDrawable)
        }
    }


    @Test
    fun should_when_initPressFeedback_with_view() {
        layout?.let {
            it.mIsDefaultBg = false
            Whitebox.invokeMethod<Void>(it, "initPressFeedback", it)

            it.mIsDefaultBg = true
            Whitebox.invokeMethod<Void>(it, "initPressFeedback", it)
        }
    }

    @Test
    fun should_when_initPressFeedback() {
        layout?.let {
            Whitebox.invokeMethod<Void>(it, "initPressFeedback", COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK, it, it)

            Whitebox.invokeMethod<Void>(it, "initPressFeedback", COUIPressFeedbackHelper.UNJUMPABLE_CARD_PRESS_FEEDBACK, it, it)
        }
    }
}