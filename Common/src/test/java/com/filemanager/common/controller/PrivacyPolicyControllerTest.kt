/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : PrivacyPolicyControllerTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/2/19 20:09
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/2/19       1.0      create
 **********************************************************************/
package com.filemanager.common.controller

import com.filemanager.common.compat.compat30.PrivacyPolicyControllerR
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class PrivacyPolicyControllerTest {

    @Test
    fun testHasAgreeAdditionalFunctions() {
        mockkObject(PrivacyPolicyControllerR.Companion)
        // Given
        every { PrivacyPolicyControllerR.hasAgreeAdditionalFunctions() }.returns(false)
        // When
        val disagree = PrivacyPolicyController.hasAgreeAdditionalFunctions()
        // Then
        Assert.assertFalse(disagree)

        // Given
        every { PrivacyPolicyControllerR.hasAgreeAdditionalFunctions() }.returns(true)
        // When
        val agree = PrivacyPolicyController.hasAgreeAdditionalFunctions()
        // Then
        Assert.assertTrue(agree)
        unmockkObject(PrivacyPolicyControllerR.Companion)
    }

    @Test
    fun testSaveAgreeAdditionalFunctions() {
        mockkObject(PrivacyPolicyControllerR.Companion)
        // Given
        justRun { PrivacyPolicyControllerR.saveAgreeAdditionalFunctions(any()) }
        // When
        PrivacyPolicyController.saveAgreeAdditionalFunctions(true)
        // Then
        verify { PrivacyPolicyControllerR.saveAgreeAdditionalFunctions(any()) }
        unmockkObject(PrivacyPolicyControllerR.Companion)
    }
}