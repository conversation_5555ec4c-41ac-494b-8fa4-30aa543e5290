/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SortPopupControllerTest
 ** Description : SortPopupController Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/11/20      1.0        create
 ***********************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import androidx.lifecycle.Lifecycle
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortPopup
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.verify
import org.junit.Test

class SortPopupControllerTest {

    @Test
    fun should_init_showSortPopUp() {
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        every { lifecycle.addObserver(any()) }.returns(Unit)
        val controller = SortPopupController(lifecycle)
        val activity = mockk<Activity>(relaxed = true)
        val selectItemListener = mockk<SelectItemListener>()
        val sortPop = mockk<SortPopup>(relaxed = true)
        controller.mSortPopUp = sortPop
        mockkConstructor(SortPopup::class) {
            every { anyConstructed<SortPopup>().initDate(any()) }.returns(Unit)
            every { anyConstructed<SortPopup>().showPopUp(any()) }.returns(Unit)
        }
        controller.showSortPopUp(activity, 0, null, "test", selectItemListener)
        verify { controller.mSortPopUp?.initDate(any()) }
    }
}