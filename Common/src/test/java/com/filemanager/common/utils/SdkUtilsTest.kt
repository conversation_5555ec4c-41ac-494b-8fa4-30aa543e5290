/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/6/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import com.filemanager.common.compat.OplusBuildCompat
import com.filemanager.common.compat.PropertyCompat
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SdkUtilsTest {
    @Before
    fun setUp() {
        mockkStatic(SdkUtils::class)
        mockkStatic(PropertyCompat::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(SdkUtils::class)
        unmockkStatic(PropertyCompat::class)
    }

    @Test
    fun isAtLeastR() {
        every { SdkUtils.isAtLeastR() } returns true
        Assert.assertEquals(true, SdkUtils.isAtLeastR())
    }

    @Test
    fun isAtLeastS() {
        every { SdkUtils.isAtLeastS() } returns true
        Assert.assertEquals(true, SdkUtils.isAtLeastS())
    }

    @Test
    fun isAtLeastT() {
        every { SdkUtils.isAtLeastT() } returns true
        Assert.assertEquals(true, SdkUtils.isAtLeastT())
    }

    @Test
    fun getSDKVersion() {
        every { SdkUtils.getSDKVersion() } returns 30
        Assert.assertEquals(30, SdkUtils.getSDKVersion())
    }

    @Test
    fun isAtLeastOS13() {
        every { PropertyCompat.sColorOSVersionCode }.answers { OplusBuildCompat.OS_13_0 }
        Assert.assertTrue(SdkUtils.isAtLeastOS13())

        every { PropertyCompat.sColorOSVersionCode }.answers { OplusBuildCompat.OS_12_2 }
        Assert.assertFalse(SdkUtils.isAtLeastOS13())
    }

    @Test
    fun getOSVersionTest() {
        every { SdkUtils.getOSVersion() }.returns(31)
        Assert.assertEquals(31, SdkUtils.getOSVersion())
    }

    @Test
    fun getOSSdkVersionTest() {
        every { SdkUtils.getOSSdkVersion() }.returns(31)
        Assert.assertEquals(31, SdkUtils.getOSSdkVersion())
    }

    @Test
    fun getOSSdkSubVersionTest() {
        every { SdkUtils.getOSSdkSubVersion() }.returns(1)
        Assert.assertEquals(1, SdkUtils.getOSSdkSubVersion())
    }

    @Test
    fun testIsAtLeastU() {
        Assert.assertFalse(SdkUtils.isAtLeastU())
    }
}