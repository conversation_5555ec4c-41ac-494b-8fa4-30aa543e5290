/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils.UtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import io.mockk.*
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.robolectric.RuntimeEnvironment

class UtilTest {
    var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mContext = RuntimeEnvironment.application
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mContext = null
        unmockkAll()
    }

    @Test
    @Throws(Exception::class)
    fun should_return_str_when_get_date_and_time_format_local() {
        val time = System.currentTimeMillis()
        mockkStatic(Utils::class)
        println("getDateAndTimeFormatLocal: ${Utils.getDateAndTimeFormatLocal(mContext, time)}")
        Assert.assertTrue(Utils.getDateAndTimeFormatLocal(mContext, time).contains(" "))
    }
}