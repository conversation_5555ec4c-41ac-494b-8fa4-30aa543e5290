/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ShortCutUtilsTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/19
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  ********      2024/6/19      1.0        create
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.PendingIntent
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.os.Bundle
import com.filemanager.common.base.BaseFileBean
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Test

class ShortCutUtilsTest {

    @Test
    fun `should call right params when call createShortCut if hasSameId is false and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        mockkStatic(PendingIntent::class)
        val file = BaseFileBean()
        file.mDisplayName = "Screenshot_2024-06-18-15-04-45-07_55bef12f624c2b805189a6aa783c400d.jpg"
        file.mData = "/storage/emulated/0/Pictures/Screenshots/Screenshot_2024-06-18-15-04-45-07_55bef12f624c2b805189a6aa783c400d.jpg"
        file.mDateModified = 1718694285000
        file.mLocalType = 4
        file.mSize = 311279

        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        every { shortcutManager.isRequestPinShortcutSupported } returns true
        every { ShortCutUtils.hasSameId(any(), any()) }.returns(false)
        every { shortcutManager.pinnedShortcuts } returns listOf()
        every { context.contentResolver } returns mockk<ContentResolver>()

        val shortcutInfo = mockk<ShortcutInfo>()
        every { shortcutInfo.id } returns file.mData + ""
        val intent = mockk<Intent>()
        every { shortcutInfo.intent } returns intent
        val extras = mockk<Bundle>()
        every { intent.extras } returns extras
        every { intent.data } returns null
        every { ShortCutUtils.getShortCutInfo(context, file) }.returns(shortcutInfo)
        val pinnedShortcutCallbackIntent = mockk<Intent>()
        every { shortcutManager.createShortcutResultIntent(shortcutInfo) }.returns(pinnedShortcutCallbackIntent)
        val callback = mockk<PendingIntent>()
        every { PendingIntent.getBroadcast(context, 0, pinnedShortcutCallbackIntent, PendingIntent.FLAG_IMMUTABLE) }.returns(callback)
        val intentSender = mockk<IntentSender>()
        every { callback.intentSender }.returns(intentSender)
        every { shortcutManager.requestPinShortcut(shortcutInfo, callback.intentSender) }.returns(true)
        //when
        ShortCutUtils.createShortCut(context, file, object : ShortCutUtils.ShortCutCallback {
            override fun result(success: Boolean) {
                //then
                assertEquals(true, success)
            }
        })
        //then
        unmockkStatic(ShortCutUtils::class)
        unmockkStatic(PendingIntent::class)
    }

    @Test
    fun `should call right params when call createShortCut if hasSameId is true and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        val file = BaseFileBean()
        file.mDisplayName = "Screenshot_2024-06-18-15-04-45-07_55bef12f624c2b805189a6aa783c400d.jpg"
        file.mData = "/storage/emulated/0/Pictures/Screenshots/Screenshot_2024-06-18-15-04-45-07_55bef12f624c2b805189a6aa783c400d.jpg"
        file.mDateModified = 1718694285000
        file.mLocalType = 4
        file.mSize = 311279

        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        every { shortcutManager.isRequestPinShortcutSupported } returns true
        every { ShortCutUtils.hasSameId(any(), any()) }.returns(true)
        //when
        ShortCutUtils.createShortCut(context, file, object : ShortCutUtils.ShortCutCallback {
            override fun result(success: Boolean) {
                //then
                assertEquals(false, success)
            }
        })

        //then
        unmockkStatic(ShortCutUtils::class)
    }

    @Test
    fun `should call right params disableShortCuts when call checkDisableShortCut if checkFileIsExist is false and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        val shortcutInfo1 = mockk<ShortcutInfo>()
        val shortcutInfo2 = mockk<ShortcutInfo>()
        val shortcuts = ArrayList<ShortcutInfo>()
        shortcuts.add(shortcutInfo1)
        shortcuts.add(shortcutInfo2)
        every { shortcutInfo1.id } returns "sdcard/path1"
        every { shortcutInfo2.id } returns "sdcard/path2"
        every { shortcutManager.pinnedShortcuts } returns shortcuts
        every { ShortCutUtils.disableShortCuts(any(), any()) } returns true
        every { ShortCutUtils.checkFileIsExist(any()) } returns false // 这个条件变化就作为分支验证，可另写一个测试用例
        val expect = ArrayList<String>()
        expect.add("sdcard/path1")
        expect.add("sdcard/path2")
        val intent1 = mockk<Intent>()
        val intent2 = mockk<Intent>()
        every { shortcutInfo1.intent } returns intent1
        every { shortcutInfo2.intent } returns intent2
        val extras1 = mockk<Bundle>()
        val extras2 = mockk<Bundle>()
        every { intent1.extras } returns extras1
        every { intent2.extras } returns extras2
        every { extras1.getBoolean(ShortCutUtils.KEY_SHORTCUT_FORM_FILM_MANAGER) } returns true
        every { extras2.getBoolean(ShortCutUtils.KEY_SHORTCUT_FORM_FILM_MANAGER) } returns true
        //when
        ShortCutUtils.checkDisableShortCut(context)
        //then
        verify { ShortCutUtils.disableShortCuts(context, expect) }
        unmockkStatic(ShortCutUtils::class)
    }

    @Test
    fun `should call right params disableShortCuts when call checkDisableShortCut if checkFileIsExist is ture and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        val shortcutInfo1 = mockk<ShortcutInfo>()
        val shortcutInfo2 = mockk<ShortcutInfo>()
        val shortcuts = ArrayList<ShortcutInfo>()
        shortcuts.add(shortcutInfo1)
        shortcuts.add(shortcutInfo2)
        every { shortcutInfo1.id } returns "sdcard/path1"
        every { shortcutInfo2.id } returns "sdcard/path2"
        every { shortcutManager.pinnedShortcuts } returns shortcuts
        every { ShortCutUtils.disableShortCuts(any(), any()) } returns true
        every { ShortCutUtils.checkFileIsExist(any()) } returns true
        val expect = ArrayList<String>()
        val intent1 = mockk<Intent>()
        val intent2 = mockk<Intent>()
        every { shortcutInfo1.intent } returns intent1
        every { shortcutInfo2.intent } returns intent2
        val extras1 = mockk<Bundle>()
        val extras2 = mockk<Bundle>()
        every { intent1.extras } returns extras1
        every { intent2.extras } returns extras2
        every { extras1.getBoolean(ShortCutUtils.KEY_SHORTCUT_FORM_FILM_MANAGER) } returns true
        every { extras2.getBoolean(ShortCutUtils.KEY_SHORTCUT_FORM_FILM_MANAGER) } returns true
        //when
        ShortCutUtils.checkDisableShortCut(context)
        //then
        verify { ShortCutUtils.disableShortCuts(context, expect) }
        unmockkStatic(ShortCutUtils::class)
    }

    @Test
    fun `should call system disableShortcuts when call disableShortCuts if has same id and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        val expect = ArrayList<String>()
        expect.add("sdcard/path1")
        expect.add("sdcard/path2")
        every { shortcutManager.disableShortcuts(expect) } just runs
        //when
        val result = ShortCutUtils.disableShortCuts(context, expect)
        //then
        assertEquals(true, result)
        unmockkStatic(ShortCutUtils::class)
    }

    @Test
    fun `should call system disableShortcuts when call disableShortCuts if has exception and from filemanager`() {
        //given
        mockkStatic(ShortCutUtils::class)
        val context = mockk<Context>()
        val shortcutManager = mockk<ShortcutManager>()
        every { context.getSystemService(ShortcutManager::class.java) } returns shortcutManager
        val expect = ArrayList<String>()
        expect.add("sdcard/path1/diff")
        expect.add("sdcard/path2/diff")
        val exception = mockk<Exception>()
        every { shortcutManager.disableShortcuts(expect) } throws exception
        every { exception.message } returns "exception"
        //when
        val result = ShortCutUtils.disableShortCuts(context, expect)
        //then
        assertEquals(false, result)
        unmockkStatic(ShortCutUtils::class)
    }
}