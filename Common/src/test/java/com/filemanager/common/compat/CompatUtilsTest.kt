/***********************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:  CompatUtilsTest
 ** Description: CompatUtils Unit Test
 ** Version: 1.0
 ** Date : 2023/07/07
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  chao.xue    2023/07/07      1.0         create
 ****************************************************************/
package com.filemanager.common.compat

import com.filemanager.common.utils.SdkUtils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.util.function.Supplier

class CompatUtilsTest {

    @Before
    fun setup() {
        mockkStatic(SdkUtils::class)
    }

    @After
    fun teardown() {
        unmockkStatic(SdkUtils::class)
    }

    @Test
    fun should_return_boolean_when_checkOSVersion() {
        every { SdkUtils.getOSVersion() }.returns(25)
        Assert.assertFalse(CompatUtils.checkOSVersion(14, 0))

        every { SdkUtils.getOSVersion() }.returns(30)
        every { SdkUtils.getOSSdkVersion() }.returns(30)
        every { SdkUtils.getOSSdkSubVersion() }.returns(2)
        Assert.assertTrue(CompatUtils.checkOSVersion(29, 1))
        Assert.assertTrue(CompatUtils.checkOSVersion(30, 1))
        Assert.assertTrue(CompatUtils.checkOSVersion(30, 2))
        Assert.assertFalse(CompatUtils.checkOSVersion(31, 0))
    }

    @Test
    fun should_return_boolean_when_checkAndroidUVersion() {
        every { SdkUtils.getSDKVersion() }.returns(32)
        Assert.assertFalse(CompatUtils.checkAndroidUVersion())

        every { SdkUtils.getSDKVersion() }.returns(33)
        Assert.assertFalse(CompatUtils.checkAndroidUVersion())

        every { SdkUtils.getSDKVersion() }.returns(34)
        Assert.assertTrue(CompatUtils.checkAndroidUVersion())

        every { SdkUtils.getSDKVersion() }.returns(35)
        Assert.assertTrue(CompatUtils.checkAndroidUVersion())
    }

    @Test
    fun should_call_when_compactApi_checkOS() {
        every { SdkUtils.getOSVersion() }.returns(30)
        every { SdkUtils.getOSSdkVersion() }.returns(30)
        every { SdkUtils.getOSSdkSubVersion() }.returns(2)
        val newApi = Supplier<Int> { 10 }
        val oldApi = Supplier<Int> { 5 }

        Assert.assertEquals(10, CompatUtils.compactApi(29, 0, newApi, oldApi))
        Assert.assertEquals(10, CompatUtils.compactApi(30, 2, newApi, oldApi))
        Assert.assertEquals(5, CompatUtils.compactApi(30, 3, newApi, oldApi))
    }

    @Test
    fun should_call_when_compactApi_checkAndroidU() {
        val newApi = Supplier<Int> { 10 }
        val oldApi = Supplier<Int> { 5 }

        every { SdkUtils.getSDKVersion() }.returns(33)
        Assert.assertEquals(5, CompatUtils.compactApi(newApi, oldApi))

        every { SdkUtils.getSDKVersion() }.returns(34)
        Assert.assertEquals(10, CompatUtils.compactApi(newApi, oldApi))
    }
}