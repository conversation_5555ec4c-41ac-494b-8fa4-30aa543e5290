/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : PropertyCompatQTest
 * * Description : PropertyCompatQ Util Test
 * * Version     : 1.0
 * * Date        : 2023/02/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   chao.xue                 2023/02/13       1      create
 ***********************************************************************/
package com.filemanager.common.compat.compat29

import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class PropertyCompatQTest {

    @Before
    fun setup() {
        mockkObject(PropertyCompatQ)
    }

    @After
    fun teardown() {
        unmockkObject(PropertyCompatQ)
    }

    @Test
    fun should_return_string_when_getRSAProperty() {
        every { PropertyCompatQ.getRSAProperty() }.answers { callOriginal() }
        every { PropertyCompatQ.getProperty(any(), any()) }.returns("")
        Assert.assertEquals("", PropertyCompatQ.getRSAProperty())
        verify { PropertyCompatQ.getProperty(any(), any()) }

        every { PropertyCompatQ.getProperty(any(), any()) }.returns("123")
        Assert.assertEquals("123", PropertyCompatQ.getRSAProperty())
    }
}