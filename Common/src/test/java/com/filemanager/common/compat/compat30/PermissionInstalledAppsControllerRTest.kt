/*********************************************************************
 * * Copyright (C), 2010-2029 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : PermissionInstalledAppsControllerRTest
 * * Description : PermissionInstalledAppsControllerRTest Util Test
 * * Version     : 1.0
 * * Date        : 2023/12/07
 * * Author      : W9059186
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *   W9059186               2023/12/07       1      create
 ***********************************************************************/
package com.filemanager.common.compat.compat30

import android.app.Activity
import android.content.Context
import com.filemanager.common.MyApplication
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class PermissionInstalledAppsControllerRTest {
    private lateinit var context: Context
    @Before
    fun setup() {
        context = mockk()
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        every { context.packageName }.returns("FileManager")
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
    }
    @Test
    fun should_start_activity_when_jumpToPermissionSetting() {
        val permissionInstalledAppsControllerR =
            mockk<PermissionInstalledAppsControllerR>(relaxed = true)
        val activity = mockk<Activity>(relaxed = true)
        every { permissionInstalledAppsControllerR.jumpToPermissionSetting(activity) }.answers { callOriginal() }
        every { permissionInstalledAppsControllerR.getCanNavigateToAppPermissions(activity) } returns true
        permissionInstalledAppsControllerR.jumpToPermissionSetting(activity)
        verify { activity.startActivity(any()) }
    }

    @Test
    fun should_start_activity_when_jumpToAppDetailSetting() {
        val permissionInstalledAppsControllerR =
            mockk<PermissionInstalledAppsControllerR>(relaxed = true)
        val activity = mockk<Activity>(relaxed = true)
        every { permissionInstalledAppsControllerR.jumpToAppDetailSetting(activity) }.answers { callOriginal() }
        permissionInstalledAppsControllerR.jumpToAppDetailSetting(activity)
        verify { activity.startActivity(any()) }
    }
}