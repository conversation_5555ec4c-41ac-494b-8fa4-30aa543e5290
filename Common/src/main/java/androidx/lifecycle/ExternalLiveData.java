/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : ExternalLiveData
 * * Description : 事件总线中防止数据倒灌的liveData
 * * Version     : 1.0
 * * Date        : 2024/5/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  w9007122                  2020/7/21  1.0        -
 ***********************************************************************/
package androidx.lifecycle;

import android.util.Log;

import androidx.annotation.NonNull;


import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;


public class ExternalLiveData<T> extends MutableLiveData<T> {
    private static final String TAG = "ExternalLiveData";
    private final ConcurrentHashMap<LifecycleObserver, LifecycleOwner> mObserverMap = new ConcurrentHashMap<>();


    public void removeObservers(LifecycleOwner owner) {
        if (mObserverMap.containsValue(owner)) {
            mObserverMap.keySet().forEach(lifecycleObserver -> {
                if (mObserverMap.get(lifecycleObserver) == owner) {
                    owner.getLifecycle().removeObserver(lifecycleObserver);
                    mObserverMap.remove(lifecycleObserver);
                    Log.d(TAG, "removeObserver observer = " + lifecycleObserver + " -- ower = " + owner.getClass().getSimpleName());
                }
            });
        }
    }

    public void clear() {
        mObserverMap.clear();
    }

    @Override
    public void observe(@NonNull LifecycleOwner owner, @NonNull Observer<? super T> observer) {
        ExternalLifecycleBoundObserver wrapper = new ExternalLifecycleBoundObserver(owner, observer);
        observeInternal(owner, observer, wrapper);
        Log.i(TAG, "observe version:" + wrapper.mLastVersion);
    }

    private void observeInternal(@NonNull LifecycleOwner owner, Observer<? super T> realObserver, LifecycleObserver wrapper) {
        if (owner.getLifecycle().getCurrentState() == Lifecycle.State.DESTROYED) {
            // ignore
            return;
        }
        try {
            //use ExternalLifecycleBoundObserver instead of LifecycleBoundObserver
            LifecycleBoundObserver existing = (LifecycleBoundObserver) callMethodPutIfAbsent(realObserver, wrapper);
            if (existing != null && !existing.isAttachedTo(owner)) {
                throw new IllegalArgumentException("Cannot add the same observer"
                        + " with different lifecycles");
            }
            if (existing != null) {
                return;
            }
            owner.getLifecycle().addObserver(wrapper);
            mObserverMap.put(wrapper, owner);
        } catch (Exception e) {
            Log.e(TAG, "observe error:", e);
        }
    }

    /**
     * determine when the observer is active, means the observer can receive message
     * the default value is CREATED, means if the observer's state is above create,
     * for example, the onCreate() of activity is called
     * you can change this value to CREATED/STARTED/RESUMED
     * determine on witch state, you can receive message
     *
     * @return Lifecycle.State
     */
    protected Lifecycle.State observerActiveLevel() {
        return Lifecycle.State.CREATED;
    }

    class ExternalLifecycleBoundObserver extends LifecycleBoundObserver {

        ExternalLifecycleBoundObserver(@NonNull LifecycleOwner owner, Observer<? super T> observer) {
            super(owner, observer);
            mLastVersion = getVersion();
        }

        @Override
        boolean shouldBeActive() {
            return mOwner.getLifecycle().getCurrentState().isAtLeast(observerActiveLevel());
        }
    }

    private Object getFieldObservers() throws Exception {
        Field fieldObservers = LiveData.class.getDeclaredField("mObservers");
        fieldObservers.setAccessible(true);
        return fieldObservers.get(this);
    }

    private Object callMethodPutIfAbsent(Object observer, Object wrapper) throws Exception {
        Object observers = getFieldObservers();
        Class<?> classOfSafeIterableMap = observers.getClass();
        Method putIfAbsent = classOfSafeIterableMap.getDeclaredMethod("putIfAbsent",
                Object.class, Object.class);
        putIfAbsent.setAccessible(true);
        return putIfAbsent.invoke(observers, observer, wrapper);
    }
}