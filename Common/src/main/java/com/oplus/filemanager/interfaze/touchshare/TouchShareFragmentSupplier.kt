/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareFragmentSupplier
 * * Description : 碰一碰分享的Fragment提供文件的抽象类
 * * Version     : 1.0
 * * Date        : 2025/06/03
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.interfaze.touchshare

import android.app.Activity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.filepreview.util.PreviewUtils
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.Log

open class TouchShareFragmentSupplier(
    categoryType: Int,
    var activity: Activity?,
    var viewModel: SelectionViewModel<*, *>?,
    var operator: IFileOperate?
) :
    TouchShareSupplier(categoryType) {

    override fun get(): List<BaseFileBean> {
        if (PreviewUtils.isShowPreview()) { // 显示预览
            val previewFile = viewModel?.previewClickedFileLiveData?.value
            Log.d(TAG, "get previewFile:$previewFile")
            if (previewFile != null) {
                return listOf(previewFile)
            }
        }
        return viewModel?.getSelectItems() ?: emptyList()
    }

    override fun interceptShare(): Boolean {
        return TouchShareDialogInterceptor.intercept(activity, operator)
    }
}