/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareSupplier
 * * Description : 碰一碰分享的提供文件的抽象类
 * * Version     : 1.0
 * * Date        : 2025/05/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.interfaze.touchshare

import androidx.annotation.MainThread
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.thread.ThreadUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import java.util.function.Supplier

abstract class TouchShareSupplier(val categoryType: Int) : DefaultLifecycleObserver, Supplier<List<BaseFileBean>> {

    companion object {
        const val TAG = "TouchShareSupplier"

        fun attach(fragment: Fragment, supplier: TouchShareSupplier) {
            fragment.lifecycle.addObserver(supplier)
        }

        fun attach(fragment: RecyclerSelectionVMFragment<*>, fileOperate: IFileOperate?) {
            val categoryType = fragment.getFragmentCategoryType()
            val supplier = TouchShareFragmentSupplier(categoryType, fragment.activity, fragment.getViewModel(), fileOperate)
            attach(fragment, supplier)
        }
    }

    private val shareApi by lazy {
        Injector.injectFactory<ITouchShareApi>()
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        Log.d(TAG, "onResume $categoryType")
        shareApi?.registerTapShareEvent { found ->
            Log.d(TAG, "onFragmentResume $categoryType share agent found:$found")
            kotlin.runCatching {
                if (found) {
                    val intercept = ThreadUtils.callOnMainThread {
                        interceptShare()
                    }
                    if (intercept) {
                        Log.e(TAG, "onFragmentResume $categoryType share reject!!!")
                        shareApi?.rejectShare(ITouchShareApi.REJECT_REASON_CONDITION_LIMIT)
                        return@registerTapShareEvent
                    }
                    val shareFiles = flatMapShareFiles()
                    if (shareFiles.isEmpty()) {
                        Log.e(TAG, "onFragmentResume $categoryType share file is empty!!!")
                        shareApi?.rejectShare(ITouchShareApi.REJECT_REASON_NO_CONTENT)
                        return@registerTapShareEvent
                    }
                    shareApi?.share(categoryType, shareFiles)
                }
            }.onFailure {
                Log.e(TAG, "share agent found error", it)
            }
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        Log.d(TAG, "onPause $categoryType")
        shareApi?.unregisterTapShareEvent()
    }

    /**
     * 分享中的文件如果有文件夹，将其展开，只显示其中的子文件
     */
    private fun flatMapShareFiles(): List<BaseFileBean> {
        val files = get()
        // 需要将dir的文件展开
        val list = mutableListOf<BaseFileBean>()
        val excludeHideFile = HiddenFileHelper.isNeedShowHiddenFile().not()
        files.forEach { file ->
            val children = flatMapFiles(file, excludeHideFile)
            list.addAll(children)
        }
        Log.d(TAG, "flatMapShareFiles origin:${files.size} result:${list.size}")
        return list
    }

    private fun flatMapFiles(file: BaseFileBean, excludeHideFile: Boolean): List<BaseFileBean> {
        val list = mutableListOf<BaseFileBean>()
        fun innerFlatMapFile(file: BaseFileBean) {
            if (file.mIsDirectory) {
                val children = JavaFileHelper.listFileBeans(file, excludeHideFile)
                children?.forEach {
                    innerFlatMapFile(it)
                }
            } else {
                list.add(file)
            }
        }

        innerFlatMapFile(file)
        return list
    }

    /**
     * 拦截分享
     */
    @MainThread
    open fun interceptShare(): Boolean {
        return false
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        Log.e(TAG, "onDestroy")
        shareApi?.release()
        owner.lifecycle.removeObserver(this)
    }
}