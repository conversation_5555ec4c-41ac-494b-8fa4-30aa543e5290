/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IFileCloudBrowser.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.filecloudbrowser

import android.app.Activity
import android.content.Context
import androidx.activity.ComponentActivity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.oplus.filemanager.interfaze.parentchild.IParentChildApi
import java.util.function.Consumer

interface IFileCloudBrowser : IParentChildApi {

    fun initStd(context: Context)

    fun startCloudDriveFragment(
        activity: Activity,
        itemType: Int,
        clearTask: Boolean = false,
        auth: Boolean = false
    )

    fun startCloudDrive(activity: Activity, itemType: Int, auth: Boolean)

    fun saveAuthorizationResult(code: String, state: String)

    fun initDocsEnvironment()

    fun searchDriveFile(searchKey: String, page: Int): List<BaseFileBean>

    fun getCloudFileIconRes(mimeType: Int): Int

    fun openTencentDocs(context: Context, fileUrl: String, fileType: String)

    fun openKDocsFile(context: Context, fileId: String, fileName: String, fileType: String)

    fun renameCloudFile(activity: ComponentActivity, file: DriveFileWrapper, consumer: Consumer<Pair<Int, String>>)

    fun downloadCloudFile(activity: ComponentActivity, file: DriveFileWrapper, targetPath: String, consumer: Consumer<Int>)

    fun deleteCloudFiles(files: List<DriveFileWrapper>): Int

    fun deleteSameCategoryCloudFile(activity: ComponentActivity, files: List<DriveFileWrapper>, consumer: Consumer<Int>)
}