/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ICollectPrivacyApi
 * * Description: ICollectPrivacy Api
 * * Version: 1.0
 * * Date : 2024/08/28
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/28      1.0            create
 ****************************************************************/
package com.oplus.filemanager.interfaze.privacy

import androidx.annotation.StringDef

interface ICollectPrivacyApi {

    companion object {
        /**
         * 手机号码
         */
        const val PHONE_NUMBER = "category_phone_number"

        /**
         * 邮箱地址
         */
        const val EMAIL_ADDRESS = "category_email_address"

        /**
         * 图像数据
         */
        const val IMAGE_DATA = "category_image_data"

        /**
         * 音频数据
         */
        const val AUDIO_DATA = "category_audio_data"

        /**
         * 视频数据
         */
        const val VIDEO_DATA = "category_video_data"

        /**
         * 文档数据
         */
        const val DOCUMENT_DATA = "category_document_data"

        /**
         * 压缩包数据
         */
        const val COMPRESS_DATA = "category_compress_data"

        /**
         * 安装包数据
         */
        const val APK_DATA = "category_apk_data"

        /**
         * DUID
         */
        const val DUID = "category_duid"

        /**
         * 设备型号
         */
        const val DEVICE_MODEL = "category_device_model"

        /**
         * 设备品牌
         */
        const val DEVICE_BRAND = "category_device_brand"

        /**
         * 软件OS版本
         */
        const val OS_VERSION = "category_os_version"

        /**
         * 配置信息
         */
        const val CONFIG_INFO = "category_config_info"

        /**
         * SIM卡信息
         */
        const val SIM_CARD_INFO = "category_sim_card_info"

        /**
         * IMSI信息
         */
        const val IMSI_INFO = "category_imsi_info"

        /**
         * IMEI
         */
        const val IMEI = "category_imei"

        /**
         * SN码
         */
        const val SN_CODE = "category_sn_code"

        /**
         * Open ID
         */
        const val OPEN_ID = "category_open_id"

        /**
         * Android ID
         */
        const val ANDROID_ID = "category_android_id"

        /**
         * IDFA
         */
        const val IDFA = "category_idfa"

        /**
         * 应用使用记录
         */
        const val APP_USAGE_RECORDS = "category_app_usage_records"

        /**
         * IP地址
         */
        const val IP_ADDRESS = "category_ip_address"

        /**
         * 已安装应用列表
         */
        const val INSTALLED_APP_LIST = "category_installed_app_list"

        /**
         * 本应用软件签名
         */
        const val APP_SIGN = "category_app_sign"

        /**
         * 软件包名
         */
        const val SOFT_PKG = "category_soft_pkg"

        /**
         * 错误日志报告
         */
        const val ERROR_LOG_REPORT = "category_error_log_report"

        /**
         * 埋点信息
         */
        const val BURIED_POINT_INFO = "category_buried_point_info"

        /**
         * 增加反馈内容附件
         */
        const val FEEDBACK_CONTENT_ATTACHMENT = "category_feedback_content_attachment"

        /**
         * 电信运营商
         */
        const val TELECOM_OPERATOR = "category_telecom_operator"

        /**
         * 网络环境
         */
        const val NETWORK_ENVIRONMENT = "category_network_environment"
    }

    @StringDef(
        value = [PHONE_NUMBER, EMAIL_ADDRESS, IMAGE_DATA, AUDIO_DATA, VIDEO_DATA, DOCUMENT_DATA, COMPRESS_DATA,
            APK_DATA, DUID, DEVICE_MODEL, DEVICE_BRAND, OS_VERSION, CONFIG_INFO, SIM_CARD_INFO, IMSI_INFO, IMEI, SN_CODE,
            OPEN_ID, ANDROID_ID, IDFA, APP_USAGE_RECORDS, IP_ADDRESS, INSTALLED_APP_LIST, APP_SIGN, SOFT_PKG, ERROR_LOG_REPORT,
            BURIED_POINT_INFO, FEEDBACK_CONTENT_ATTACHMENT, TELECOM_OPERATOR, NETWORK_ENVIRONMENT]
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class PrivacyCategory


    /**
     * 收集
     * @param category 类型
     * @param content 收集的内容
     */
    fun collect(@PrivacyCategory category: String, content: String? = null)
}