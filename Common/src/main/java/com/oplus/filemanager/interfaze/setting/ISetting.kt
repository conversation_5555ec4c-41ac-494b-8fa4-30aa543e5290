/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ISetting.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.setting

import android.app.Activity
import android.content.res.Configuration
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.toolbar.COUIToolbar

interface ISetting {

    fun startSettingActivity(activity: Activity?)

    fun setBarBackgroundColor(
        toolbar: COUIToolbar,
        appBarLayout: View,
        decorView: View,
        configuration: Configuration,
        activity: AppCompatActivity
    )

    fun setActionCloseFlexibleActivity(decorView: View, activity: Activity)

    fun jumpToSettingFunctionActivity(activity: Activity)
}