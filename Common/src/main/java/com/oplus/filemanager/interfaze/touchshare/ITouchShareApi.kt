/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ITouchShareApi
 * * Description : 碰一碰分享的Api
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.interfaze.touchshare

import android.content.Context
import android.content.Intent
import com.filemanager.common.base.BaseFileBean
import java.util.function.Consumer

interface ITouchShareApi {

    companion object {
        const val REJECT_REASON_UNKNOWN = 0

        /**
         * 无分享内容
         */
        const val REJECT_REASON_NO_CONTENT = 1

        /**
         * 场景受限
         */
        const val REJECT_REASON_CONDITION_LIMIT = 2
        const val REJECT_REASON_USER_REJECT = 3
    }

    fun create(context: Context)

    /**
     * 碰一碰分享是否可用
     */
    fun isAvailable(): Boolean

    /**
     * 注册碰一碰分享，在onResume生命周期调用
     */
    fun registerTapShareEvent(consumer: Consumer<Boolean>)

    /**
     * 解除注册碰一碰分享，在onPause 中调用
     */
    fun unregisterTapShareEvent()

    /**
     * 分享
     * @param from 从哪个页面分享
     * @param files 分享的文件
     */
    fun share(from: Int, files: List<BaseFileBean>): Boolean

    /**
     * 拒绝分享
     * @param code 拒绝分享的理由
     */
    fun rejectShare(code: Int): Boolean

    /**
     * 恢复分享
     */
    fun resumeShare(from: Int, files: List<BaseFileBean>): Boolean

    /**
     * 获取分享的数据
     */
    fun getShareData(intent: Intent): List<BaseFileBean>

    /**
     * 释放资源
     */
    fun release()
}