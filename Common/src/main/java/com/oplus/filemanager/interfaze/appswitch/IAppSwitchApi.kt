/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IDmpSearchApi.kt
 ** Description: 中子搜索API
 ** Version: 1.0
 ** Date: 2024/6/12
 ** Author: huangyuanwang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.appswitch

import android.content.Context
import java.util.function.Consumer

interface IAppSwitchApi {

    /**
     * 判断当前配置给场景智能的三方App监听配置中开关是打开状态还是关闭状态
     */
    fun isCurrentAppSwitchConfigFeatureOn(context: Context): Boolean

    /**
     * 判断获取当前从云控或从asset目录下获取的正则表达式解析配置项中功能是打开状态还是关闭状态
     */
    fun isCurrentThirdAppListenConfigFeatureOn(): Boolean

    /**
     * 更新正则表达是的配置文件，这个函数只有从云控更新的时候调用
     * @param thirdAppConfigJsonString 配置项的Json字符串
     */
    fun updateThirdAppListenConfig(thirdAppConfigJsonString: String)

    /**
     * 初始化AppSwitchManager，加载本地货云控配置，并设置相应AppSwitchConfig配置到场景智能，这里主要是在Application初始化时调用，
     */
    fun initDmpAndAppSwitch(useService: Boolean)

    /**
     * 触发AppSwitch中的db的同步操作
     */
    fun trigDbSync(useWorkerThread: Boolean)

    /**
     * 查看AppSwitchManager中的config是否初始化完成
     */
    fun getAppListConfigReady(): Boolean


    /**
     * 设置AppSwitchManager中的config初始化监听
     */
    fun setAppListConfigReadyCallback(consumer: Consumer<Boolean>?)
}