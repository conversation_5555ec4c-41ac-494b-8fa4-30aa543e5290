/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IFilePreviewApi.kt
 * Description:
 *     The definition for file preview api.
 *
 * Version: 1.0
 * Date: 2024-09-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.interfaze.filepreview

import android.app.Application
import android.content.Context
import com.filemanager.common.base.BaseFileBean

interface IFilePreviewApi {

    /**
     * Init some sdks for file preview
     */
    fun initPreviewConfigs(application: Application)

    /**
     * Get the preview type for [fileBean]
     */
    @FilePreviewType
    fun getPreviewType(context: Context, fileBean: BaseFileBean): Int

    /**
     * Obtain preview fragment with file type.
     */
    fun obtainFilePreviewFragment(@FilePreviewType previewType: Int): IFilePreviewFragment

    /**
     * Obtain preview fragment for [BaseFileBean]
     */
    fun obtainFilePreviewFragment(
        context: Context,
        fileBean: BaseFileBean
    ): IFilePreviewFragment {
        val fragment = obtainFilePreviewFragment(getPreviewType(context, fileBean))
        fragment.setPreviewFile(context, fileBean)
        return fragment
    }

    /**
     * Obtain heap up fragment to preview selections.
     */
    fun obtainFileHeapUpFragment(): IFilesHeapUpFragment

    /**
     * Obtain heap up fragment for [List<BaseFileBean>]
     */
    fun obtainFileHeapUpFragment(selection: List<BaseFileBean>): IFilesHeapUpFragment {
        val fragment = obtainFileHeapUpFragment()
        fragment.updateSelectionFiles(selection)
        return fragment
    }
}