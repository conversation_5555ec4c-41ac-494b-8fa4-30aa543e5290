/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IDmpSearchApi.kt
 ** Description: 中子搜索API（外销）
 ** Version: 1.0
 ** Date: 2024/8/30
 ** Author: muning
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.dmpsearch

import android.app.Activity
import android.content.Context
import android.database.Cursor
import androidx.annotation.WorkerThread

interface IExportDmpSearchApi {

    /**
     * 初始化中子搜索sdk，在Application初始化中调用,方法耗时，需要放在子线程处理
     */
    @WorkerThread
    fun initDmpSdk(context: Context): Boolean

    /**
     * 本地文件搜索需求，本地文件搜索中初始化SearchHelper中的proxy
     */
    fun initClient()

    /**
     * 本地文件搜索需求，搜索本地文件
     */
    fun getCursor(searchKey: String?): Cursor?


    /**
     * 本地文件搜索需求, 检查dmp有没有所有文件权限，没有权限就拉起授权页面
     */
    fun checkState(activity: Activity)

    /**
     * 本地文件搜索需求,判定是否是本地文件结果cursor
     */
    fun isDMPCursor(cursor: Cursor): Boolean

    /**
     * 本地文件搜索需求,判断是否继承本地文件搜索功能
     */
    fun isShouldLoadDMP(): Boolean

    /**
     * 本地文件搜索需求,
     */
    fun buildRewriteQueries(mSearchKey: String?): Collection<String>
}