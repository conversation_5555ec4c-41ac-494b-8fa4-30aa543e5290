/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareNotSupportSupplier
 * * Description : 不支持碰一碰分享的提供文件的抽象类
 * * Version     : 1.0
 * * Date        : 2025/05/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.interfaze.touchshare

import com.filemanager.common.base.BaseFileBean

abstract class TouchShareNotSupportSupplier(categoryType: Int) : TouchShareSupplier(categoryType) {

    override fun get(): List<BaseFileBean> {
        return emptyList()
    }

    override fun interceptShare(): Boolean {
        notSupportTips()
        return true
    }

    /**
     * 不支持的提示
     */
    abstract fun notSupportTips()
}