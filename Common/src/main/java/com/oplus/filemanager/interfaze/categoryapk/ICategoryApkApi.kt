/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ICategoryApkApi.kt
 ** Description: category apk api
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.categoryapk

import android.app.Activity
import android.os.Bundle
import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.parentchild.IParentChildApi

@Keep
interface ICategoryApkApi : IParentChildApi {

    fun startCategoryApkActivity(activity: Activity, name: String?)

    fun startCategoryApkFragment(activity: Activity, name: String?)

    fun getUnInstallApkCount(arg: String?, extras: Bundle?): Bundle?
}