/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareDialogInterceptor
 * * Description : 碰一碰分享的弹窗的拦截逻辑
 * * Version     : 1.0
 * * Date        : 2025/06/03
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.oplus.filemanager.interfaze.touchshare

import android.app.Activity
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier.Companion.TAG

object TouchShareDialogInterceptor {

    /**
     * 拦截
     * @param activity activity
     * @param operateController 底部操作文件
     */
    fun intercept(activity: Activity?, operateController: IFileOperate?): Boolean {
        var showPanel = false
        if (activity is TransformNextFragmentListener) {
            showPanel = activity.hasShowPanel()
        }
        val showDialog = operateController?.isShowDialog() ?: false
        Log.d(TAG, "intercept showPanel:$showPanel showDialog:$showDialog")
        return showPanel || showDialog
    }
}