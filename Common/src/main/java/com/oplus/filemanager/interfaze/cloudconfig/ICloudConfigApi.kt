/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ICloudConfigApi.kt
 ** Description: cloud config api
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.cloudconfig

import android.widget.TextView

interface ICloudConfigApi {

    fun initCloudConfig()

    fun getAlias(path: String?): String?

    fun updateViewByAlias(view: TextView?, path: String?, keyWord: String? = null)

    fun getCategoryListGson(): String?
}