/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ICategoryAlbumApi.kt
 ** Description: category album api
 ** Version: 1.0
 ** Date: 2024/6/5
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.categoryalbum

import android.app.Activity
import android.net.Uri
import com.oplus.filemanager.interfaze.parentchild.IParentChildApi

interface ICategoryAlbumApi : IParentChildApi {

    fun startAlbumActivity(activity: Activity, uri: Uri?, bucketDate: String?, albumSetName: String?, coverPath: String?)
}