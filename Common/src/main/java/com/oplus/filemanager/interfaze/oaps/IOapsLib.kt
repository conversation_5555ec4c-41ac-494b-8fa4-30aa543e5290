/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IOapsLib.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.oaps

import android.app.Activity
import android.content.Context

interface IOapsLib {
    fun initOapsData(activity: Activity, callback: OapsInitResultCallback)

    fun isShowApkEntrance(): Boolean

    fun isShowHomeEntrance(): Boolean

    fun startOaps(activity: Activity)

    fun openAppStoreDetail(context: Context, uninstallTab: Boolean)

    fun checkAppStoreEnabledWithDialog(context: Context): Boolean
}