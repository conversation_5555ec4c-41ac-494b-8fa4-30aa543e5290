/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: IShortFolder
 * * Description: IShortFolder
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.interfaze.shortcutfolder

import android.app.Activity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.filepreview.IPreviewListFragment
import com.oplus.filemanager.interfaze.parentchild.IParentChildApi
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

interface IShortcutFolderApi : IParentChildApi {

    /**
     * 加载所有的快捷文件夹
     */
    suspend fun loadFolders(): MutableList<MainCategoryItemsBean>

    /**
     * 添加快捷文件夹
     * @param paths 路径列表
     * @return 插入到数据库的id
     */
    suspend fun addShortcutFolders(paths: List<String>?): List<Long>

    /**
     * 是否已经添加到快捷文件夹,如果已添加，BaseFileBean.mHasLabel = true
     */
    fun isAddShortcutFolder(list: List<BaseFileBean>)

    suspend fun deleteShortcutFolder(id: Long): Boolean

    suspend fun updateShortcutFolderUseTime(list: List<Long>)

    fun startShortcutFolderActivity(activity: Activity, dbID: Long, title: String, path: String, sideCategoryType: Int)

    /**
     * 当更新了文件路径
     */
    fun updateFilePath(oldPath: String, newPath: String)

    /**
     * 删除了文件路径
     */
    fun deleteFiles(paths: List<String>)

    /**
     * 回收了文件路径
     */
    fun recycleFiles(paths: List<String>)

    /**
     * 恢复了文件路径
     */
    fun restoreFiles(paths: List<String>)

    /**
     * 拷贝了文件
     */
    fun copyFile(sourcePath: String, destPath: String, localType: Int, mimeType: String)

    /**
     * 获取当前页面的路径
     */
    fun getCurrentPath(fragment: IPreviewListFragment): String?

    /**
     * 获取当前页面的路径
     */
    fun getCurrentPath(activity: Activity): String?
}