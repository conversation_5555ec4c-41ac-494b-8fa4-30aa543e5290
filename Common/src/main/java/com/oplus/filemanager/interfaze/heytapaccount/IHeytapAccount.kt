/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IHeytapAccount.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2024/06/05
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.interfaze.heytapaccount

import android.content.Context
import com.filemanager.common.bean.TransitionCloudAccount

interface IHeytapAccount {
    fun initAccountClient(context: Context)

    fun isLogin(context: Context): Boolean

    fun getUserCountry(context: Context): String?

    fun login(context: Context, isShowAcPage: Boolean, callback: (Boolean) -> Unit)

    fun getUserToken(context: Context): String

    fun getUserId(context: Context): String?

    fun getUserName(context: Context): String?

    fun reqSignInAccount(context: Context, callback: (account: TransitionCloudAccount) -> Unit)
}