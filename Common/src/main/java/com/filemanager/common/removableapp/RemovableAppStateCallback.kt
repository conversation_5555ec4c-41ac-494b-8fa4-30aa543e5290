/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppStateCallback
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/29 16:47
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/29       1.0      create
 ***********************************************************************/
package com.filemanager.common.removableapp

interface RemovableAppStateCallback {
    fun onOpen()
    fun onClose()
}