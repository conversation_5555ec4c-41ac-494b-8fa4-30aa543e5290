/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.app.Dialog
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.filemanager.common.compat.compat29.PermissionControllerQ
import com.filemanager.common.compat.compat30.PermissionControllerR
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isInvalid

abstract class PermissionController : LifecycleObserver {
    companion object {
        private const val TAG = "PermissionController"
        fun create(lifecycle: Lifecycle, listener: OnRequestPermissionListener): PermissionController {
            return if (SdkUtils.isAtLeastR()) {
                PermissionControllerR(lifecycle, listener)
            } else {
                PermissionControllerQ(lifecycle, listener)
            }
        }
    }

    protected var mPermissionGuildDialog: Dialog? = null
    protected var mPermissionEmptyView: View? = null
    protected var mPermissionListener: OnRequestPermissionListener? = null
    protected var mLifecycle: Lifecycle? = null
    private var mPrivacyPolicyController: PrivacyPolicyController? = null

    // onResume will be invoked again after system permission dialog dismiss,
    // so use mWaitPermissionGrantResult to make sure checkPermission() not be invoked repetitive
    private var mWaitPermissionGrantState = false
    var isDialogShow = false

    protected constructor(lifecycle: Lifecycle, listener: OnRequestPermissionListener) {
        lifecycle.addObserver(this)
        mLifecycle = lifecycle
        mPermissionListener = object : OnRequestPermissionListener {
            override fun onPermissionSuccess() {
                listener.onPermissionSuccess()
                recycle()
            }

            override fun onPermissionReject(alwaysReject: Boolean) {
                listener.onPermissionReject(alwaysReject)
                recycle()
            }
        }
    }

    fun checkPermission(activity: Activity) {
        Log.d(TAG, "checkPermission: start check")
        if (activity.isInvalid()) {
            return
        }
        // Check privacy policy first, then check is it need to show permission guild dialog
        if (mPrivacyPolicyController == null) {
            mPrivacyPolicyController = PrivacyPolicyController.create()
        }
        mPrivacyPolicyController!!.checkPrivacyPolicyAgreed(activity,
                object : PrivacyPolicyController.OnPrivacyPolicyListener {
                    override fun onAgreeResult(agree: Boolean, noLongerRemind: Boolean) {
                        Log.d(TAG, "onAgreeResult agree: $agree  noLongerRemind: $noLongerRemind ")
                        if (agree) {
                            internalCheckPermission(activity)
                        }
                    }
                })
    }

    abstract fun internalCheckPermission(activity: Activity)

    /**
     * 所有文件弹窗
     */
    abstract fun showSettingGuildDialog(activity: Activity)

    /**
     * 应用安装列表弹窗
     */
    abstract fun checkGetInstalledAppsPermission(activity: Activity, isMainShow: Boolean)

    open fun setWaitPermissionGrantResult(waiting: Boolean) {
        Log.d(TAG, "setWaitPermissionGrantResult: $waiting")
        mWaitPermissionGrantState = waiting
    }

    fun getWaitPermissionGrantResult(): Boolean = mWaitPermissionGrantState

    abstract fun onPermissionsResultReturn(activity: Activity, requestCode: Int = PermissionUtils.REQUEST_MANAGE_ALL_FILES_PERMISSIONS,
                                           permissions: Array<out String>? = null, grantResults: IntArray? = null)

    abstract fun createPermissionEmptyView(activity: Activity): View

    fun showPermissionEmptyView(activity: Activity, rootView: ViewGroup?) {
        Log.d(TAG, "showPermissionEmptyView(): rootView=$rootView")
        if (rootView == null) {
            return
        }
        if (mPermissionEmptyView == null) {
            mPermissionEmptyView = createPermissionEmptyView(activity)
        } else if (rootView.indexOfChild(mPermissionEmptyView) >= 0) {
            Log.d(TAG, "showPermissionEmptyView(): empty view has been added, return directly")
            return
        }
        val layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        if ((WindowUtils.isMiddleAndLargeScreen(activity))) {
            //增加判断activity是否已经设置paddingtop
            if (rootView.paddingTop != COUIPanelMultiWindowUtils.getStatusBarHeight(activity)) {
                layoutParams.topMargin = activity.resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_min_height) +
                            COUIPanelMultiWindowUtils.getStatusBarHeight(activity)
            } else {
                layoutParams.topMargin = activity.resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_min_height)
            }
        } else {
            //增加判断activity是否已经设置paddingtop
            if (rootView.paddingTop != COUIPanelMultiWindowUtils.getStatusBarHeight(activity)) {
                layoutParams.topMargin = StatusBarUtil.getStatusBarHeight()
            }
        }

        //切换到文件来源需要其parent remove view
        val parent = mPermissionEmptyView?.parent
        (parent as? ViewGroup)?.removeView(mPermissionEmptyView)
        rootView.addView(mPermissionEmptyView, layoutParams)
    }

    fun removePermissionEmptyView(rootView: ViewGroup?) {
        if ((rootView != null) && (mPermissionEmptyView != null)) {
            rootView.removeView(mPermissionEmptyView)
        }
    }

    protected open fun recycle() {
        mPrivacyPolicyController?.onDestroy()
        mPrivacyPolicyController = null
        mPermissionGuildDialog?.dismiss()
        mPermissionGuildDialog = null
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        Log.d(TAG, "onDestroy()")
        recycle()
        mLifecycle?.removeObserver(this)
        mLifecycle = null
        mPermissionListener = null
        mPermissionEmptyView = null
    }

    interface OnRequestPermissionListener {
        fun onPermissionSuccess()
        fun onPermissionReject(alwaysReject: Boolean)
    }

    interface OnPermissionGuildListener {
        fun onPermissionGuildResult(grant: Boolean, noLongerRemind: Boolean = true)
    }
}