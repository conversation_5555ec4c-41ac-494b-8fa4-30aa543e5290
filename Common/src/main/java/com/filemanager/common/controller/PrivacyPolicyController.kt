/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PrivacyPolicyController.kt
 ** Description: Check privacy policy
 ** Version: 1.0
 ** Date: 2020/4/17
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.app.Dialog
import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.compat29.PrivacyPolicyControllerQ
import com.filemanager.common.compat.compat30.PrivacyPolicyControllerR
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.SdkUtils

abstract class PrivacyPolicyController protected constructor() {
    companion object {
        private const val TAG = "PrivacyPolicyController"
        const val RTL_SYMBOL = "\u200E"
        private var sAgreeResult: Boolean = false
        var privacyPolicyListener: MutableList<OnPrivacyPolicyListener>? = null
        var activityPrivacyPolicyListener: OnPrivacyPolicyListener? = null

        fun create(): PrivacyPolicyController {
            return if (SdkUtils.isAtLeastR()) {
                PrivacyPolicyControllerR()
            } else {
                PrivacyPolicyControllerQ()
            }
        }

        fun reset() {
            sAgreeResult = false
        }

        /**
         * preload PrivacyPolicy SharePreference
         */
        fun preloadPrivacyPolicySharePreference(context : Context) {
            if (SdkUtils.isAtLeastR()) {
                PrivacyPolicyControllerR.preloadPrivacyPolicySharePreference(context)
            } else {
                PrivacyPolicyControllerQ.preloadPrivacyPolicySharePreference(context)
            }
        }

        /**
         * Check whether the user agrees to the terms of use
         */
        fun hasAgreePrivacy(): Boolean {
            return hasAgreePrivacy(MyApplication.sAppContext)
        }

        fun hasAgreePrivacy(context: Context): Boolean {
            return if (SdkUtils.isAtLeastR()) {
                PrivacyPolicyControllerR.hasAgreePrivacy(context)
            } else {
                PrivacyPolicyControllerQ.hasAgreePrivacy()
            }
        }

        fun saveAgreePrivacy() {
            if (SdkUtils.isAtLeastR()) {
                PrivacyPolicyControllerR.saveAgreePrivacy()
            } else {
                PrivacyPolicyControllerQ.saveAgreePrivacy()
            }
        }

        fun hasAgreeAdditionalFunctions(): Boolean {
            return PrivacyPolicyControllerR.hasAgreeAdditionalFunctions()
        }

        fun hasAgreeUseNet(): Boolean {
            return PrivacyPolicyControllerR.hasAgreeUseNet() || FeatureCompat.sIsExpRom
        }

        fun saveAgreeAdditionalFunctions(agree: Boolean) {
            PrivacyPolicyControllerR.saveAgreeAdditionalFunctions(agree)
        }

        fun isEnableWithDraw(): Boolean {
            return PreferencesUtils.getBoolean(key = CommonConstants.FEED_BACK_FUNCTION_SHOW, default = false) ||
                    PreferencesUtils.getBoolean(key = CommonConstants.THIRD_APP_SEARCH_INTRODUCE_DIALOG_SHOW, default = false) ||
                    hasAgreeAdditionalFunctions()
        }

        fun saveAgreeUseNet(agree: Boolean) {
            PrivacyPolicyControllerR.saveAgreeAgreeUseNet(agree)
        }

        fun saveDriveCloudAgree(agree: Boolean) {
            PreferencesUtils.put(key = CommonConstants.TENCENT_DOCS_FUNCTION_SHOW, value = agree)
            PreferencesUtils.put(key = CommonConstants.K_DOCS_FUNCTION_SHOW, value = agree)
        }

        fun saveFeedBackAgree(agree: Boolean) {
            PreferencesUtils.put(key = CommonConstants.FEED_BACK_FUNCTION_SHOW, value = agree)
        }

        fun bindPrivacyPolicyListener(privacyPolicyListener: OnPrivacyPolicyListener) {
            if (this.privacyPolicyListener == null) {
                this.privacyPolicyListener = mutableListOf()
            }
            Log.d(TAG, "bindPrivacyPolicyListener$privacyPolicyListener")
            this.privacyPolicyListener?.add(privacyPolicyListener)
        }

        fun bindActivityPrivacyPolicyListener(activityPrivacyPolicyListener: OnPrivacyPolicyListener) {
            this.activityPrivacyPolicyListener = activityPrivacyPolicyListener
        }

        fun saveDisagreePrivacy() {
            PrivacyPolicyControllerR.saveAgreePrivacy(true)
            sAgreeResult = false
        }
    }

    private var mPrivacyDialog: Dialog? = null
    private var mCallback: OnPrivacyPolicyListener? = null
    private var isPrivacyPolicyDialogShowing = false

    fun checkPrivacyPolicyAgreed(activity: Activity, callback: OnPrivacyPolicyListener) {
        if (sAgreeResult.not()) {
            if (checkStatementDialogState()) {
                mCallback = callback
                showPolicyDialog(activity)
            } else {
                sAgreeResult = true
            }
        }
        if (sAgreeResult) {
            Log.d(TAG, "checkPrivacyPolicyAgreed: Privacy policy has been agreed")
            callback.onAgreeResult(true)
            onDestroy()
        }
    }

    fun isPrivacyPolicyDialogShowing(): Boolean {
        return isPrivacyPolicyDialogShowing
    }

    private fun checkStatementDialogState() = hasAgreePrivacy().not()

    private fun saveStatementDialogState() {
        saveAgreePrivacy()
    }

    private fun showPolicyDialog(activity: Activity) {
        if (!isActivityValid(activity)) {
            Log.e(TAG, "showPolicyDialog isActivityValid = false")
            onDestroy()
            return
        }
        if (mPrivacyDialog?.isShowing == true) {
            Log.d(TAG, "showPolicyDialog, mPrivacyDialog is showing and return")
            return
        }
        mPrivacyDialog = createPolicyDialog(activity, object : OnPrivacyPolicyListener {
            override fun onAgreeResult(agree: Boolean, noLongerRemind: Boolean) {
                Log.d(TAG, "onAgreeResult: agree=$agree, noLongerRemind=$noLongerRemind mCallback = $mCallback")
                if (agree) {
                    if (noLongerRemind) {
                        saveStatementDialogState()
                    }
                }
                PrivacyPolicyControllerR.saveRequestAdditionalFunctions()
                sAgreeResult = agree
                privacyPolicyListener?.forEach {
                    it.onAgreeResult(agree)
                }
                mCallback?.onAgreeResult(agree)
                activityPrivacyPolicyListener?.onAgreeResult(agree)
                onDestroy()
            }
        })
        try {
            mPrivacyDialog?.show()
        } catch (e: Exception) {
            onDestroy()
        }
    }

    fun processKdocAndTencentSwitchPreference() {
        val hasAgreeAdditionalFunctions = hasAgreeAdditionalFunctions()
        Log.d(TAG, "processKdocAndTencentSwitchPreference hasAgreeAdditionalFunctions $hasAgreeAdditionalFunctions")
        PreferencesUtils.put(key = CommonConstants.K_DOCS_FUNCTION_SHOW, value = hasAgreeAdditionalFunctions)
        PreferencesUtils.put(key = CommonConstants.TENCENT_DOCS_FUNCTION_SHOW, value = hasAgreeAdditionalFunctions)
    }

    protected abstract fun createPolicyDialog(activity: Activity, listener: OnPrivacyPolicyListener): Dialog

    private fun isActivityValid(activity: Activity?): Boolean {
        return (activity != null) && !(activity.isFinishing || activity.isDestroyed)
    }

    fun onDestroy() {
        Log.d(TAG, "onDestroy")
        mPrivacyDialog?.setOnDismissListener(null)
        try {
            mPrivacyDialog?.dismiss()
        } catch (e: Exception) {
        }
        mPrivacyDialog = null
        mCallback = null
        privacyPolicyListener?.clear()
        privacyPolicyListener = null
        activityPrivacyPolicyListener = null
    }

    interface OnPrivacyPolicyListener {
        fun onAgreeResult(agree: Boolean, noLongerRemind: Boolean = true)
    }
}