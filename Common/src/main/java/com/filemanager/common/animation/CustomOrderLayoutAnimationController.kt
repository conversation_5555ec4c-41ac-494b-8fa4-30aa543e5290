/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : CustomOrderLayoutAnimationController.kt
 * * Description : Calculate the delay time of each line
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>        <data>       <version>       <desc>
 * *  ********       2020/9/21       1.0           create
 ***********************************************************************/
package com.filemanager.common.animation

import android.view.View
import android.view.animation.Animation
import android.view.animation.GridLayoutAnimationController
import java.util.*

class CustomOrderLayoutAnimationController : GridLayoutAnimationController {

    companion object {
        const val PRIORITY_LINE = -1
    }

    val mLineDelay: Long

    constructor(animation: Animation, lineDelay: Long) : super(animation) {
        mLineDelay = lineDelay
    }

    override fun willOverlap(): Boolean {
        return mLineDelay > 0
    }

    /**
     * Calculate the delay time of each line
     */
    private fun getDelayForViewByLine(view: View): Long {
        val lp = view.layoutParams
        return if (lp.layoutAnimationParameters is AnimationParameters) {
            val params: AnimationParameters = lp.layoutAnimationParameters as AnimationParameters
            getTransformedRowIndex(params) * mLineDelay
        } else {
            0
        }
    }

    /**
     * Transforms the index stored in
     * {@link android.view.animation.LayoutAnimationController.AnimationParameters}
     * by the order returned by {@link #getOrder()}.
     * This method should be invoked by
     * {@link #getDelayForView(android.view.View)} prior to any computation.
     *
     * @param params the animation parameters containing the index
     * @return a transformed index
     */
    private fun getTransformedRowIndex(params: AnimationParameters): Int {
        val lastItemIndex = params.rowsCount - 1
        var index = when (order) {
            ORDER_REVERSE -> {
                lastItemIndex - params.row
            }
            ORDER_RANDOM -> {
                val random = mRandomizer?.nextFloat() ?: Random().nextFloat()
                (params.rowsCount * random).toInt()
            }
            else -> {
                params.row
            }
        }

        val direction: Int = (direction and DIRECTION_HORIZONTAL_MASK)
        if (direction == DIRECTION_BOTTOM_TO_TOP) {
            index = lastItemIndex - index
        }
        return index
    }

    override fun getDelayForView(view: View): Long {
        return when (directionPriority) {
            PRIORITY_LINE -> {
                getDelayForViewByLine(view)
            }
            else -> {
                super.getDelayForView(view)
            }
        }
    }
}