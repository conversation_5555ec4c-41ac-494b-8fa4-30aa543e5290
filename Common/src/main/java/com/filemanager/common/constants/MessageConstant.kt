/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** File:  - Messager.kt
 ** Description: common messager
 ** Version: 1.0
 ** Date : 2020/10/28
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/10/28    1.0     create
 ****************************************************************/
package com.filemanager.common.constants

object MessageConstant {
    const val MSG_LOAD_DATA = 3
    const val MSG_CREATE_NEW_FOLDER = 4
    const val MSG_EDITOR_COMPRESS = 5
    const val MSG_EDITOR_DECOMPRESS = 10
    const val MSG_SORT = 7
    const val MSG_UPDATE_LIST = 8
    const val MSG_EDITOR_SELECT_ALL = 9
    const val MSG_EDITOR_DESELECT_ALL = 11
    const val MSG_EDITOR_COPY = 12
    const val MSG_EDITOR_CUT = 13
    const val MSG_CLOUD_DRIVE_DOWNLOAD = 14
    const val MSG_ENCRYPT = 15
    const val MSG_ACTIVITY_RESULT = 16
    const val MSG_UPDATE_ACTIVITY_TITLE = 19
    const val MSG_PLAY_MUSIC = 20
    const val MSG_STOP_MUSIC = 21
    const val MSG_MENU_TITLE_UPDATE = 22
    const val MSG_BUTTON_STATE = 23
    const val MSG_SUSPEND_MUSIC = 28
    const val MSG_CREATE_NEW_FOLDER_SORT_POSITION = 29
    const val MSG_DELETE_FILES = 31
    const val MSG_MOVE_FILES = 32
    const val MSG_MEDIA_EJECT = 41
    const val DELAY_EJECT_ACTIVITY_FINISH: Long = 150
    const val MSG_UPDATE_APK_COUNT = 51
    const val MSG_SETTINGS_CHANAGED = 61
    const val MSG_LOAD_ALBUM_COUNT = 64
    const val MSG_COMPRESS_PREVIEW_QUIT = 71
    const val MSG_DETROY_ACTION_MODE = 72
    const val MSG_SAVE_TO_HERE = 73
    const val MSG_SAVE_AND_RENAME = 74 //保存
    const val MSG_SAVE = 75 //分享
    const val MSG_FOLDER_PICKER = 76//移至此处
    const val MSG_ADD_SHORTCUT_FOLDER = 77 //添加快捷文件夹
    const val MSG_DOWNLOAD_REMOTE_FILE = 78 //下载远程设备上面的文件
    const val MSG_OPEN_REMOTE_FILE = 79 //打开远程设备上面的文件
}