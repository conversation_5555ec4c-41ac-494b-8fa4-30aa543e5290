///***********************************************************
// ** Copyright (C), 2008-2017 Oplus. All rights reserved.
/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.constants;

import com.filemanager.common.helper.CategoryHelper;

import java.io.File;

public class Constants {
    public static final String BASE_LOG_TAG = "FileManager";
    public static final String LOG_DEFAULT_TAG = BASE_LOG_TAG + "::";
    public static final String SELECTED_ITEM = "selected_item";
    public static final String FILEMANAGER_RECORD = "record";
    public static final String RECORD_LAST_FILE_SYSTEM_AVAILABLE_SIZE = "last_file_system_size";
    public static final String RECORD_LAST_AUDIO_COUNT = "last_audio_count";
    public static final String RECORD_LAST_AUDIO_SIZE = "last_audio_size";
    public static final String RECORD_LAST_VIDEO_COUNT = "last_video_count";
    public static final String RECORD_LAST_VIDEO_SIZE = "last_video_size";
    public static final String RECORD_LAST_IMAGE_COUNT = "last_image_count";
    public static final String RECORD_LAST_IMAGE_SIZE = "last_image_size";
    public static final String RECORD_LAST_DOC_COUNT = "last_doc_count";
    public static final String RECORD_LAST_DOC_SIZE = "last_doc_size";
    public static final String RECORD_LAST_APK_COUNT = "last_apk_count";
    public static final String RECORD_LAST_APK_SIZE = "last_apk_size";
    public static final String RECORD_LAST_COMPRESS_COUNT = "last_compress_count";
    public static final String RECORD_LAST_COMPRESS_SIZE = "last_compress_size";
    public static final String RECORD_LAST_DOWNLOAD_COUNT = "last_download_count";
    public static final String RECORD_LAST_DOWNLOAD_SIZE = "last_download_size";
    public static final String RECORD_LAST_QQ_COUNT = "last_qq_count";
    public static final String RECORD_LAST_QQ_SIZE = "last_qq_size";
    public static final String RECORD_LAST_MICROMSG_COUNT = "last_micromsg_count";
    public static final String RECORD_LAST_MICROMSG_SIZE = "last_micromsg_size";
    public static final String RECORD_LAST_APK_NINE_COUNT = "last_apk_nine_count";
    public static final String RECORD_LAST_APK_NINE_SIZE = "last_apk_nine_size";
    public static final String RECORD_LAST_APK_TEN_COUNT = "last_apk_ten_count";
    public static final String RECORD_LAST_APK_TEN_SIZE = "last_apk_ten_size";
    public static final String RECORD_LAST_APK_ELEVEN_COUNT = "last_apk_eleven_count";
    public static final String RECORD_LAST_APK_ELEVEN_SIZE = "last_apk_eleven_size";
    public static final String RECORD_LAST_APP_INSTALL_COUNT = "last_install_app_count";
    public static final String RECORD_LAST_APP_INSTALL_SIZE = "last_install_app_size";
    public static final String EXTERNALURI = "EXTERNALURI";
    public static final String SQL = "SQL";
    public static final String BUCKETDATA = "BUCKETDATA";
    public static final String TITLE = "TITLE";
    public static final String TITLE_RES_ID = "TITLE_RES_ID";
    public static final String CATEGORY_TYPE = "CATEGORY_TYPE";
    public static final String TEMP_SORT_TYPE = "TEMP_SORT_TYPE";
    public static final String SIDE_CATEGORY_TYPE = "DIDE_CATEGORY_TYPE";
    public static final String FROM_SAFE = "isFromSafe";
    public static final String ACTION_TITLE_TEXT = "action_title_text";
    public static final String LABEL_ID = "labelId";
    public static final String IS_FILTER_SEARCH_RESULT = "is_filter";
    public static final String IS_FROM_SEARCH = "is_from_search";
    public static final String LABEL_FILTER_MAPPING_CONTENT = "label_filter_mapping_content";
    public static final int LABEL_MAX_COUNT = 1000;
    public static final String KEY_HAS_CLICK_LABEL_NAVIGATION_MENU = "key_has_click_label_navigation_menu";
    public static final String KEY_IS_FROM_LABEL_FILE_LIST = "key_is_from_label_file_list";
    public static final String KEY_IS_FROM_NOTIFICATION = "key_is_from_notification";
    public static final String KEY_IS_FROM_DEVICE = "is_from_device";
    public static final String KEY_REMOTE_DEVICE_ID = "remote_device_id";
    public static final String START_FILE_MANAGER_ACTION = "oplus.intent.action.filemanager.OPEN_FILEMANAGER";
    public static final String LABEL_FILE_LIST_NAME = "label_name_file_list";
    public static final String KEY_IS_FROM_LABEL_CARD = "key_is_from_label_card";
    public static final String TITLE_AND_LABEL_ID = "title_and_label_id";
    public static final String CHANGE_TO_SELECT_MODE = "change_to_select_mode";
    public static final int LABEL_NAME_LEN = 32;
    public static final int LABEL_NAME_BYTES_LEN = 164;
    public static final String SHOW_ADD_FILE_DIALOG = "show_add_file_dialog";
    public static final String FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG = "from_label_card_show_rename_label_dialog";
    public static final String TEMP_SORT_DESC = "TEMP_SORT_DESC";
    public static final String FROM_NEW_FILES_SEEDLING = "new_files_seedling";
    /**
     * snippet language
     */
    public static final String SNIPPET_LANGUAGE = "si";


    public static final String DOCUMENT_FORMAT_ARRAY = "document_format_array";
    public static final String NEED_RELOAD = "need_reload";
    public static final String PROVIDERS_MEDIA_PACKAGE = "com.android.providers.media";
    public static final String PROVIDERS_MEDIA_CLASS = "com.oppo.media.OppoMediaReceiver";

    public static final String[] CATEGORY_PATH_DOWNLOAD = {"Download" + File.separator};
    public static final String[] CATEGORY_PATH_OPPO_SHARE = {"OPPO Share" + File.separator, "Documents" + File.separator + "OPPO Share" + File.separator,
            "Movies" + File.separator + "OPPO Share" + File.separator, "Music" + File.separator + "OPPO Share" + File.separator, "Pictures" + File.separator + "OPPO Share" + File.separator, "Download" + File.separator + "OPPO Share" + File.separator};
    public static final String[] CATEGORY_PATH_REALME_SHARE = {"Realme Share" + File.separator, "Documents" + File.separator + "Realme Share" + File.separator,
            "Movies" + File.separator + "Realme Share" + File.separator, "Music" + File.separator + "Realme Share" + File.separator, "Pictures" + File.separator + "Realme Share" + File.separator, "Download" + File.separator + "Realme Share" + File.separator};
    public static final String[] CATEGORY_PATH_ONEPLUS_SHARE = {"OnePlusShare" + File.separator, "Documents" + File.separator + "OnePlus Share" + File.separator,
            "Movies" + File.separator + "OnePlus Share" + File.separator, "Music" + File.separator + "OnePlus Share" + File.separator, "Pictures" + File.separator + "OnePlus Share" + File.separator, "Download" + File.separator + "OnePlus Share" + File.separator};
    public static final String CATEGORY_PATH_BT[] = {"bluetooth" + File.separator, "Download" + File.separator + "Bluetooth" + File.separator};
    public static final int LOG_LEVEL = android.util.Log.VERBOSE;

    public static final int TAB_ALL = 0;
    public static final int TAB_IMAGE = 1;
    public static final int TAB_VIDEO = 2;
    public static final int TAB_AUDIO = 3;
    public static final int TAB_DOCUMENT = 4;
    public static final int TAB_OTHER = 5;
    public static final int TAB_APK = 5;
    public static final int TAB_COMPRESS = 6;
    public static final int TAB_DFM_OTHER = 7;
    public static final int MAX_PROGRESS = 100;
    public static final long QUICK_CLICK_TIME_GAP = 400L;
    public static final String RECORD_LAST_CLOUD_COUNT = "category_clouddisk_count";
    public static final String RECORD_LAST_CLOUD_SIZE = "category_clouddisk_size";
    public static final String RECORD_LAST_OAPS_COUNT = "category_oaps_count";
    public static final String RECORD_LAST_OAPS_SIZE = "category_oaps_size";
    public static final float NIGHT_MODE_COMMON_ALPHA = 0.5f;
    public static final float NORMAL_MODE_COMMON_ALPHA = 1.0f;
    public static final float DEGREE_0 = 0f;
    public static final float DEGREE_180 = 180f;
    public static final float ALPHA_0 = 0f;
    public static final float ALPHA_1 = 1f;
    public static final float SCALE_100 = 1.0f;
    public static final String PROPERTY_ALPHA = "alpha";

    public static final String WECHAT_PACKAGE_NAME = "com.tencent.mm";
    public static final String WPS_PACKAGE_NAME = "cn.wps.moffice.lite";
    public static final String WPS_PERSONAL_PACKAGE_NAME = "cn.wps.moffice_eng";
    public static final String SYSTEMUI_PACKAGE_NAME = "com.android.systemui";
    public static final String INTENT_FILE_SAFE_ACTION = "com.coloros.filemanager.FILE_SAFE";
    public static final String INTENT_FILE_SAFE_ACTION_OPLUS = "com.oplus.filemanager.FILE_SAFE";
    public static final long SECOND_TO_MILLIS = 1000L;
    public static final int SECOND_ONE_MIN = 60;
    public static final int SECOND_ONE_HOUR = 60 * 60;
    public static final long SECOND_ONE_DAY = 60 * 60 * 24;
    public static final String DEFAULT_OPEN_PACKAGE_NAME = "android";

    public static final String SELF_PACKAGE_NAME = "com.coloros.filemanager";
    public static final String HEYTAP_READER_PACKAGE_NAME = "com.heytap.reader";
    public static final String OPPO_READER_PACKAGE_NAME = "com.oppo.reader";
    public static final String BOOK_PACKAGE_NAME = "com.heytap.book";
    // 随心开包名
    public static final String OPEN_ANY_PKG_NAME = "andes.oplus.documentsreader";
    public static final String OPEN_ANY_CLASS = "andes.oplus.documentsreader.superpreview.ui.SuperFilePreviewActivity";
    /**
     * 打开随心开-创建文件弹窗
     */
    public static final String START_FILE_CREATE_BY_YOZO_SOFT = "oplus.intent.action.FILE_CREATE_BY_YOZO_SOFT";

    public static final String IS_OPEN_PARENT_DOCUMENT_ACTIVITY = "is_open_parent_document_activity";

    public static final String KEY_SOURCE_APP_NAME = "sourceAppName";

    public static final String KEY_CARD_TYPE = "cardType";
    public static final String KEY_ITEM_INFO = "itemInfo";

    public static final String KEY_OPEN_PERMISSION = "openPermission";

    public static final String KEY_IS_FROM_RECENT_WIDGET = "isFromRecentCardWidget";

    public static final String KEY_FILE_DRIVE_TYPE = "file_drive_type";
    public static final String KEY_NEW_FILES_REFRESH_LAST_TIME = "key_new_files_refresh_last_time";
    public static final String KEY_LAUNCHER_PKG = "launcher_pkg";

    public static final String DB_ID = "db_id";

    public static final String REMOTE_PATH_PREFIX = "/remote_mac";
    public static final String REMOTE_RECENT_DIR_PATH = "RECENT_USE_DIR";

    //从 远程控制电脑 跳过来的类型
    public static final String FROM_REMOTE_CONTROL_JUMP_TYPE = "from_remote_control_jump_type";
    public static final String KEY_DEVICE_NAME = "device_name";
    //MAC远程控制电脑 的 远控文件
    public static final int REMOTE_CONTROL_JUMP_TYPE_MAC_FILE = 1;
    //PC(Windows)远程控制电脑 的 远控文件
    public static final int REMOTE_CONTROL_JUMP_TYPE_PC_FILE = 2;
    //远程电脑文件的虚拟包名
    public static final String PKG_REMOTE_MAC_CONTROL = "com.oplus.remote.pc.control";

    /**
     * 跳转到首页来源下面的子页面
     */
    public static final String ACTION_MAIN_CATEGORY_SUPER = "oplus.intent.action.filemanager.superApp";

    /**
     * 页面名称
     */
    public static final String PAGE_MAIN = "main";
    public static final String PAGE_RECYCLE_BIN = "recycleBin";
    public static final String PAGE_CLEAR_UP = "clear_up";
    public static final String PAGE_RECENT = "recent";
    public static final String PAGE_FILE_BROWSER = "fileBrowser";
    public static final String PAGE_OTG = "OTG";
    public static final String PAGE_SD_CARD = "sd_card";
    public static final String PAGE_SUPER_REMOTE_FILE = "super_remote_file";
    public static final String PAGE_SUPER = "super";
    public static final String PAGE_LABEL = "label";
    public static final String PAGE_LABEL_FILE = "label_file";
    public static final String PAGE_REMOTE_FILE_DOWNLOAD = "remote_file_download";
    public static final String PAGE_REMOTE_FILE_LIST = "remote_file_list";
    public static final String PAGE_SEARCH = "search";
    public static final String PAGE_IMAGE_SET = "category_image_set";
    public static final String PAGE_IMAGE = "category_image";
    public static final String PAGE_VIDEO = "category_video";
    public static final String PAGE_AUDIO = "category_audio";
    public static final String PAGE_DOC = "category_doc";
    public static final String PAGE_COMPRESS = "category_compress";
    public static final String PAGE_APK = "category_apk";
    public static final String PAGE_APK_MARKET = "category_apk_market";
    public static final String PAGE_OWORK = "category_owork";
    public static final String PAGE_CLOUD_DISK = "cloud_disk";
    public static final String PAGE_APP_STORE = "app_store";
    public static final String PAGE_APP_MANAGER = "app_manager";
    public static final String PAGE_CLOUD_DRIVE = "cloud_drive";
    public static final String PAGE_PC_CONNECT = "pc_connect";
    public static final String PAGE_DFM = "category_dfm";
    public static final String PAGE_PRIVATE_SAFE = "private_safe";
    public static final String PAGE_SHORTCUT_FOLDER = "shortcut_folder";
    public static final String PAGE_SETTING = "setting";
    public static final String PAGE_PRIVACY_PWD = "privacy_pwd";
    public static final String PAGE_SPLASH = "splash";

    /**
     * 卡片
     */
    public static final String CARD_RECENT = "1"; //最近卡片
    public static final String CARD_LABEL = "2"; //标签卡片
    public static final String CARD_FILE_SEEDLING = "3"; //最近文件泛在卡

    /**
     * 包名
     */
    public static final String PKG_REMOTE_CONTROL = "com.oplus.remotecontrol"; //远程控制电脑
    public static final String PKG_LINKER = "com.oplus.linker"; //跨屏互联
    public static final String PKG_LAUNCHER = "com.android.launcher"; //桌面
    public static final String PKG_SPEECH_ASSIST = "com.heytap.speechassist"; // 小布语音组手
    public static final String PKG_ACCESSORY = "com.heytap.accessory"; // 一碰分享

    public static String getPage(int type) {
        if (CategoryHelper.isSuperAppType(type)) {
            return Constants.PAGE_SUPER;
        } else if (CategoryHelper.isShortcutFolderType(type)) {
            return Constants.PAGE_SHORTCUT_FOLDER;
        } else if (CategoryHelper.isLabelType(type)) {
            return Constants.PAGE_LABEL;
        } else if (CategoryHelper.isRemoteMacDeviceType(type)) {
            return Constants.PAGE_REMOTE_FILE_LIST;
        }
        return switch (type) {
            case CategoryHelper.CATEGORY_RECYCLE_BIN -> Constants.PAGE_RECYCLE_BIN;
            case CategoryHelper.CATEGORY_DFM -> Constants.PAGE_DFM;
            case CategoryHelper.CATEGORY_FILE_BROWSER -> Constants.PAGE_FILE_BROWSER;
            case CategoryHelper.CATEGORY_SDCARD_BROWSER -> Constants.PAGE_SD_CARD;
            case CategoryHelper.CATEGORY_OTG_BROWSER -> Constants.PAGE_OTG;
            case CategoryHelper.CATEGORY_RECENT -> Constants.PAGE_RECENT;
            case CategoryHelper.CATEGORY_IMAGE -> Constants.PAGE_IMAGE;
            case CategoryHelper.CATEGORY_VIDEO -> Constants.PAGE_VIDEO;
            case CategoryHelper.CATEGORY_AUDIO -> Constants.PAGE_AUDIO;
            case CategoryHelper.CATEGORY_DOC -> Constants.PAGE_DOC;
            case CategoryHelper.CATEGORY_APK -> Constants.PAGE_APK;
            case CategoryHelper.CATEGORY_COMPRESS -> Constants.PAGE_COMPRESS;
            default -> "";
        };
    }
}
