/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFilePathHelper
 ** Description : 云端文件的路径帮助类
 ** Version     : 1.0
 ** Date        : 2023/12/14 15:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/12/14       1.0      create
 ***********************************************************************/
package com.filemanager.common.path

import java.io.File

class CloudFilePathHelper(val currentPath: String) : FilePathHelper(currentPath) {

    companion object {
        private const val TAG = "CloudFilePathHelper"
    }

    override fun isLocalFile(): Boolean = false

    override fun getExternalPath(): String? {
        return null
    }

    override fun getInternalPath(): String? {
        return null
    }

    override fun getOtgPath(): List<String>? {
        return null
    }

    override fun getDfmRootPath(): String? {
        return null
    }

    override fun getDfmDeviceName(): String? {
        return null
    }

    override fun getParentPath(path: String): String {
        if (isRootPath(path)) {
            return path
        }
        val index = path.lastIndexOf(File.separator)
        if (index != -1) {
            return currentPath.substring(index)
        }
        return path
    }

    override fun isRootExternalPath(path: String): Boolean {
        return false
    }

    override fun isRootInternalPath(path: String): Boolean {
        return false
    }

    override fun isRootOtgPath(path: String): Boolean {
        return false
    }

    override fun isRootDfmPath(path: String): Boolean {
        return false
    }
}