/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileBrowPathHelper
 ** Description : 本地文件的路径帮助类
 ** Version     : 1.0
 ** Date        : 2023/12/14 17:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/12/14       1.0      create
 ***********************************************************************/
package com.filemanager.common.path

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import java.io.File
import com.oplus.filemanager.dfm.DFMManager

class FileBrowPathHelper(currentPath: String, isFromShortcutFolder: Boolean = false, shortcutRootPath: String? = null) :
    FilePathHelper(currentPath, isFromShortcutFolder, shortcutRootPath) {
    private var mRootExternalPath: String? = null
    private var mRootInternalPath: String? = null
    private var mRootOtgPath: List<String>? = null
    private var rootDfmPath: String? = null
    private var dfmDeviceName: String? = null

    override fun getExternalPath(): String? {
        if (mRootExternalPath == null) {
            mRootExternalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        }
        return mRootExternalPath
    }

    override fun getInternalPath(): String? {
        if (mRootInternalPath == null) {
            mRootInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        }
        return mRootInternalPath
    }

    override fun getOtgPath(): List<String>? {
        if (mRootOtgPath == null) {
            mRootOtgPath = VolumeEnvironment.getOTGPath(MyApplication.sAppContext)
        }
        return mRootOtgPath
    }


    override fun getDfmRootPath(): String? {
        rootDfmPath = DFMManager.getDFSMountPath()
        Log.d("FileBrowPathHelper", "getDfmPath $rootDfmPath")
        return rootDfmPath
    }

    override fun getDfmDeviceName(): String? {
        dfmDeviceName = DFMManager.getDFSDeviceName()
        Log.d("FileBrowPathHelper", "getDfmDeviceName $dfmDeviceName")
        return dfmDeviceName
    }

    override fun isRootExternalPath(path: String): Boolean {
        return path.equals(mRootExternalPath)
    }

    override fun isRootInternalPath(path: String): Boolean {
        return path.equals(mRootInternalPath)
    }

    override fun isRootOtgPath(path: String): Boolean {
        for (otgPath in mRootOtgPath!!) {
            return otgPath.equals(path, ignoreCase = true)
        }
        return false
    }

    override fun isRootDfmPath(path: String): Boolean {
        Log.d("FileBrowPathHelper", "isRootDfmPath path $path mRootDfmPath $rootDfmPath")
        return path == rootDfmPath
    }

    override fun getParentPath(path: String): String {
        val parent = File(path).parentFile
        return parent?.absolutePath ?: ""
    }
}