/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IndexOperationResult.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/16
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.bean

data class IndexOperationResult(
    var errorCode: Int = ERROR_CODE_UNKNOWN,
    var indexBean: SearchIndexBean,
    var operationType: Int
) {

    companion object {
        const val TAG = "IndexOperationResult"


        const val OPERATION_TYPE_ADD = 1
        const val OPERATION_TYPE_UPDATE = 2
        const val OPERATION_TYPE_DELETE = 3


        /**
         * 错误码，值为int类型。 NOTE:xjn 重新整理error code
         * 公共：< 1000
         * 索引配置：[1000, 2000)
         * 修改索引: [2000, 3000)
         */
        const val ERROR_CODE_BUNDLE_OVERSIZE = -2

        //未知原因
        const val ERROR_CODE_UNKNOWN = -1

        //成功码
        const val ERROR_CODE_SUCCESS = 0

        //不支持的请求（如服务端不存在或不可用等）
        const val ERROR_CODE_UNSUPPORTED_REQUEST = 1

        //sdk和dmp的ipc通信错误
        const val ERROR_CODE_COMMUNICATION_FAILED = 2
        const val ERROR_CODE_RW_INDEX_ERROR = 3
        const val ERROR_CODE_JSON_ERROR = 4
        const val ERROR_CODE_FORBID_ACCESS = 5
        const val ERROR_CODE_CONFIG_NOT_ALLOWED = 1000
        const val ERROR_CODE_CONFIG_PARSE_FAILED = 1001
        const val ERROR_CODE_CONFIG_APPLY_FAILED = 1002
        const val ERROR_CODE_CONFIG_SAVE_FAILED = 1003
        const val ERROR_CODE_CONFIG_RECREATE_FAILED = 1004
        const val ERROR_CODE_CLEAR_DB_FAILED = 2000
        const val ERROR_CODE_CLEAR_CONFIG_FAILED = 2001
        //数据错误（文档和配置不一致等）
        const val ERROR_CODE_INDEX_DATA_WRONG = 3000

        //数据库异常
        const val ERROR_CODE_INDEX_DATABASE_OPERATION_FAILED = 3001

        //文档数超限（不应重试索引操作）
        const val ERROR_CODE_INDEX_DATA_EXCEEDS_LIMIT = 3002
        const val ERROR_CODE_INDEX_DATA_NULL_VALUE = 3003

        //跨组件API调用错误
        const val ERROR_CODE_OSTICH_INVOKE_ERROR = 10000

        //组件未继承错误
        const val ERROR_CODE_OSTICH_NO_COMPONENT = 10001

        //indexProxy为空（手机中安装的是老版本DMP时，会出现这种情况）
        const val ERROR_CODE_INDEX_PROXY_NULL = 10002


        /**
         * 失败原因，String类型。
         */
        const val ARG_ERROR_MSG = "errorMessage"
        const val ERROR_MSG_UNKNOWN = "Unknown error"
        const val ERROR_MSG_SUCCESS = "Success"
        const val ERROR_MSG_UNSUPPORTED_REQUEST = "Unsupported request"
        const val ERROR_MSG_CONFIG_PARSE_FAILED = "Config parse failed"
        const val ERROR_MSG_CONFIG_APPLY_FAILED = "Config apply failed"
        const val ERROR_MSG_CONFIG_SAVE_FAILED = "Config save failed"
        const val ERROR_MSG_INDEX_DATA_WRONG = "Indexing failed, data is wrong or empty"
        const val ERROR_MSG_INDEX_DATABASE_OPERATION_FAILED =
            "Indexing failed, database operation error"
        const val ERROR_MSG_INDEX_DATA_EXCEEDS_LIMIT =
            "Indexing failed, the number of documents exceeds the maximum limit"


        /**
         * 根据错误码获取错误信息
         */
        fun getErrorMessage(errCode: Int): String {
            return when (errCode) {
                ERROR_CODE_SUCCESS -> ERROR_MSG_SUCCESS
                ERROR_CODE_UNSUPPORTED_REQUEST -> ERROR_MSG_UNSUPPORTED_REQUEST
                ERROR_CODE_CONFIG_PARSE_FAILED -> ERROR_MSG_CONFIG_PARSE_FAILED
                ERROR_CODE_CONFIG_APPLY_FAILED -> ERROR_MSG_CONFIG_APPLY_FAILED
                ERROR_CODE_CONFIG_SAVE_FAILED -> ERROR_MSG_CONFIG_SAVE_FAILED
                ERROR_CODE_INDEX_DATA_WRONG -> ERROR_MSG_INDEX_DATA_WRONG
                ERROR_CODE_INDEX_DATABASE_OPERATION_FAILED -> ERROR_MSG_INDEX_DATABASE_OPERATION_FAILED
                ERROR_CODE_INDEX_DATA_EXCEEDS_LIMIT -> ERROR_MSG_INDEX_DATA_EXCEEDS_LIMIT
                else ->
                    ERROR_MSG_UNKNOWN
            }
        }
    }

    fun isSuccess(): Boolean {
        return errorCode == ERROR_CODE_SUCCESS
    }

    fun isOperationUpdate(): Boolean {
        return operationType == OPERATION_TYPE_UPDATE
    }

    fun isOperationAdd(): Boolean {
        return operationType == OPERATION_TYPE_ADD
    }

    fun isOperationDelete(): Boolean {
        return operationType == OPERATION_TYPE_DELETE
    }
}
