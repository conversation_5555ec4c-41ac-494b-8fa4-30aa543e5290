/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.interfaces
 * * Version     : 1.0
 * * Date        : 2020/7/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.interfaces

import com.filemanager.common.base.BaseFileBean

interface TransformNextFragmentListener {
    fun transformToNextFragment(path: String?)
    fun showSelectPathFragmentDialog(code: Int)
    fun showSelectPathFragmentDialog(code: Int, path: String? = null) {
    }
    fun onSelect(code: Int, paths: List<String>?)

    /**
     * 显示编辑标签面板
     *  @param fileList 已选择的标签
     */
    fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>)

    /**
     * 更新标签后通知Activity更新
     */
    fun onUpdatedLabel()

    /**
     * 是否显示面板（选择路径面板和标签面板）
     */
    fun hasShowPanel(): Boolean
}