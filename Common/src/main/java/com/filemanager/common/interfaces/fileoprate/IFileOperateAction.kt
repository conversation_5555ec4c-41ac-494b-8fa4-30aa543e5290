/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.interfaces.fileoprate.IFileOperateAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.interfaces.fileoprate

interface IFileOperateAction<T : IFileActionObserver> {
    fun execute(uiObserver: T): IFileOperateAction<T>

    /**
     * 隐藏弹窗
     */
    fun hideDialog()

    /**
     * 是否显示弹窗
     */
    fun isShowDialog(): Boolean
}