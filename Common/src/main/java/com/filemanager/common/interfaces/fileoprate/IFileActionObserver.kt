/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.interfaces.fileoprate.IFileActionObserver
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.interfaces.fileoprate

interface IFileActionObserver {
    fun onActionDone(result: <PERSON><PERSON><PERSON>, data: Any? = null)
    fun onActionCancelled()
    fun onActionReloadData()
    fun onActionReShowDialog()

    fun isShowDialog(): <PERSON><PERSON><PERSON>
}