/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.interfaces.LoadingLoaderListener
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/1/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.interfaces

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.base.FileLoader
import com.filemanager.common.controller.OnLoaderListener

/**
 * This class is used to monitor the loading progress and show the loading dialog
 */
abstract class LoadingLoaderListener<VM : BaseViewModel, LOADER : FileLoader<RESULT>, RESULT>(
    viewModel: VM,
    val mLoadingState: MutableLiveData<Int>
) : OnLoaderListener<RESULT> {
    private var mLoader: LOADER? = null
    var viewModel: VM? = viewModel

    final override fun onCreateLoader(): LOADER? {
        mLoader = onCreateLoader(viewModel)
        return mLoader
    }

    abstract fun onCreateLoader(viewModel: VM?): LOADER?

    final override fun onLoadStart() {
        mLoadingState.value = OnLoaderListener.STATE_START
    }

    final override fun onLoadCanceled() {
        mLoadingState.value = OnLoaderListener.STATE_CANCEL
    }

    override fun onLoadComplete(result: RESULT?) {
        mLoadingState.value = OnLoaderListener.STATE_DONE
        onLoadComplete(viewModel, result)
    }

    abstract fun onLoadComplete(viewModel: VM?, result: RESULT?)

    fun getLoader() = mLoader

    override fun onLoadDestroy() {
        mLoader = null
        viewModel = null
    }
}