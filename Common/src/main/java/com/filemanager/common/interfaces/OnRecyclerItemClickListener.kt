package com.filemanager.common.interfaces

import android.view.View
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.interfaces
 * * Version     : 1.0
 * * Date        : 2020/3/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

interface OnRecyclerItemClickListener {
    fun onItemClick(view: View, position: Int)

    fun onItemLongClick(view: View, position: Int)

    /**
     * 来源列表的Item数据项在Normal态会过滤掉 superAppSwitch=false的项，会导致position位置不对
     * 因此点击事件需要传递具体的MainCategoryItemsBean。
     */
    fun onSuperAppItemClick(data: MainCategoryItemsBean) {}

    /**
     * 来源列表的Item在Edit状态下可以点击开关进行显示和隐藏，
     * 此回调用于将点击事件传递到 MainCategoryFragment 中处理.
     */
    fun onSuperAppItemSwitchClick(dataList: MutableList<MainCategoryItemsBean>) {}
}