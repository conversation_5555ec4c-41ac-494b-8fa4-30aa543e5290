package com.filemanager.common.interfaces.fileoprate

import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.view.MenuItem
import android.view.MotionEvent
import androidx.activity.ComponentActivity
import com.filemanager.common.base.BaseFileBean

/**
 * This interface is used to define the file operation interface, the caller can implement it to
 * intercept the operation where in FileOperateController.
 */
interface IFileOperate {
    /**
     * The operation type, you can define the new operation type here
     */
    companion object {
        const val OP_ITEM_CLICK = 1
        const val OP_COMPRESS = 2
        const val OP_DECOMPRESS = 3
        const val OP_RENAME = 4
        const val OP_ENCRYPT = 5
        const val OP_DETAIL = 6
        const val OP_OPEN_BY_OTHER = 7
        const val OP_UPLOAD_CLOUD_DISK = 8
        const val OP_SHARE = 9
        const val OP_COPY = 10
        const val OP_CUT = 11
        const val OP_DELETE_TO_RECYCLE = 12
        const val OP_COMPRESS_PREVIEW = 13
        const val OP_CREATE_FOLDER = 14
        const val OP_RESTORE = 15
        const val OP_DELETE_FOREVER = 16 // Delete forever in recyclebin
        const val OP_FAVORITE = 17 // add favorite
        const val OP_REMOVE_FAVORITE = 18 // remove favorite
        const val OP_CREATE_SHORTCUT = 19
        const val OP_CREATE_SHORTCUT_FOLDER = 20
    }

    fun setResultListener(listener: OperateResultListener) {}
    fun pickResultListener(): OperateResultListener? = null
    fun setInterceptor(interceptor: IFileOperate) {}

    /** Select the compress dest dir */
    fun onSelectCompressDest(activity: ComponentActivity) = false
    fun onCompress(activity: ComponentActivity, destPath: String, fileName: String): Boolean = false

    /** Select the decompress dest dir */
    fun onSelectDecompressDest(activity: ComponentActivity) = false
    fun onDecompress(activity: ComponentActivity, destPath: String?, fileName: String?): Boolean = false
    fun onRename(activity: ComponentActivity) = false
    fun onEncrypt(activity: ComponentActivity) = false
    fun onDetail(activity: ComponentActivity) = false
    fun onOpenByOther(activity: ComponentActivity) = false
    fun onUploadCloudDisk(activity: ComponentActivity) = false
    fun onShare(activity: ComponentActivity, rect: Rect?): Boolean = false

    fun createShortCut(activity: ComponentActivity) {}

    /** Select the copy dest dir */
    fun onSelectCopyDir(activity: ComponentActivity) = false
    fun onCopy(activity: ComponentActivity, destPath: String) = false

    /** Select the cut dest dir */
    fun onSelectCutDir(activity: ComponentActivity) = false
    fun onAddLabel(activity: ComponentActivity): Boolean = false
    fun onCut(activity: ComponentActivity, destPath: String) = false
    fun onDelete(activity: ComponentActivity): Boolean = false
    fun onRestore(activity: ComponentActivity): Boolean = false
    fun onFileClick(
        activity: ComponentActivity,
        file: BaseFileBean,
        event: MotionEvent?,
        mediaIds: ArrayList<String>? = null
    ): Boolean = false
    fun onCreateFolder(activity: ComponentActivity, currentPath: String): Boolean = false
    fun onNavigationItemSelected(activity: ComponentActivity, item: MenuItem): Boolean = false
    fun onSelectPathReturn(activity: ComponentActivity, requestCode: Int, paths: List<String>?): Boolean = false

    fun onConfigurationChanged(newConfig: Configuration? = null) {
        // Default do nothing
    }

    /**
     * 是否显示弹窗
     */
    fun isShowDialog(): Boolean = false

    /**
     * The callback what used to delivery the operation result
     */
    interface OperateResultListener {
        /**
         * Action finished.
         *
         * @param opType the operation type where defined in IFileOperate
         * @param result the operation result state
         * @param data the operation result data, maybe null
         */
        fun onActionDone(opType: Int, result: Boolean, data: Any?) {}

        /**
         * Action cancelled.
         *
         * @param opType the operation type where defined in IFileOperate
         */
        fun onActionCancelled(opType: Int) {}

        /**
         * Action to ask the UI to reload data
         *
         * @param opType the operation type where defined in IFileOperate
         */
        fun onActionReloadData(opType: Int) {}
    }

    /**
     * 用户判断是否是最近页面的Listener
     */
    interface RecentOperatorListener : OperateResultListener {
        // Default do nothing
    }

    /**
     * 最近页面单独有一套文件操作响应流程，为了能嵌入预览界面的文件操作响应流程，
     * 需要按照最近页面的逻辑特征抽取接口并实现对应的数据提供方法，
     * 来完成预览界面文件操作与最近页面响应逻辑的对接
     */
    interface IRecentOperateBridge {

        fun getOperateContext(): Context

        fun getSelectFileItems(): List<BaseFileBean>

        fun getCutDestPath(): String

        fun getCopyDestPath(): String
    }
}