/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RecycleStore.java
 * Description: RecycleStore.java
 * Version: 1.0
 * Date : 2020/02/17
 * Author: <PERSON>afei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/17    1.0     create
 ****************************************************************/
package com.filemanager.common

import android.content.ContentUris
import android.net.Uri
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.constructFile
import org.jetbrains.annotations.VisibleForTesting
import java.io.File

object RecycleStore {
    private const val TAG = "RecycleStore"
    const val TABLE_NAME = "files"
    const val VOLUME_INTERNAL = "internal"

    /**
     * The MIME type of a directory of files.
     */
    const val FILES_CONTENT_TYPE = "vnd.android.cursor.dir/files"

    /**
     * The MIME type of a single file.
     */
    const val FILES_ENTRY_CONTENT_TYPE = "vnd.android.cursor.item/file"

    const val VOLUME_EXTERNAL_PRIMARY = "external_primary"

    /**
     * invalid id.
     */
    const val INVALID_ID = -2L

    /**
     * root id
     */
    const val ROOT_ID = -1L

    // folder name of recycler bin on android R
    const val RECYCLE_DIR_NAME = ".FileManagerRecycler"

    // folder name of recycler bin on android Q
    const val LEGACY_RECYCLE_DIR_NAME = ".recycler"

    //都是自己人，文档的回收目录也要屏蔽下
    const val DOC_RECYCLE_DIR_NAME = ".DocRecycleBin"

    //录音的回收站目录
    const val SOUND_RECORD_RECYCLE_DIR_NAME = ".SoundRecordRecycler"

    const val RECYCLE_NAME_SEPARATION = "_"

    // add surfix to recycle file name to cheat the mediastore
    const val RECYCLE_NAME_SUFFIX = "_temp"

    const val ANDROID_MAX_NAME_BYTE_LENGTH = 255

    const val RECYCLE_FILE = "file"

    const val RECYCLE_ROOT = "recycleroot"

    // help flag to request recycleprovider to move file to recyclebin
    const val PARAM_RECYCLE_DATA = "recycledata"

    const val DELETE_FLAG = "true"

    val AUTHORITY_URI: Uri by lazy { Uri.parse("content://${getRecycleAuthority()}") }

    /**
     * Do not direct access [_recycleDirectory]
     * Pls use [createdRecycleDirectory]
     */
    private var _recycleDirectory: String? = null

    @JvmStatic
    internal var createdRecycleDirectory: String?
        @VisibleForTesting get() = _recycleDirectory
        @VisibleForTesting set(value) = ::_recycleDirectory.set(value)

    fun getRecycleAuthority(flavorBand: String = MyApplication.flavorBrand, flavorRegion: String = MyApplication.flavorRegion): String {
        return KtAppUtils.getApplicationId(flavorBand, flavorRegion) + ".recycleprovider"
    }

    object Files {
        val INTERNAL_CONTENT_URI: Uri by lazy { getContentUri(VOLUME_INTERNAL) }

        val INTERNAL_CONTENT_ROOT_URI: Uri by lazy { getRecycleRootUri(VOLUME_INTERNAL) }

        @JvmStatic
        fun getContentUri(volumeName: String): Uri {
            return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath(RECYCLE_FILE).build()
        }

        @JvmStatic
        fun getContentUri(volumeName: String, rowId: Long): Uri {
            return ContentUris.withAppendedId(getContentUri(volumeName), rowId)
        }

        @JvmStatic
        private fun getRecycleRootUri(volumeName: String): Uri {
            return AUTHORITY_URI.buildUpon().appendPath(volumeName).appendPath(RECYCLE_ROOT).build()
        }

        interface FileColumns {
            companion object {
                /**
                 * The index of media item.
                 */
                const val RECYCLE_ID = "_id"

                /**
                 * The origin path of the media item.
                 */
                const val ORIGIN_PATH = "origin_path"

                /**
                 * The size of the media item.
                 */
                const val SIZE = "_size"

                /**
                 * The index of the parent directory of the file in recycle bin
                 */
                const val RECYCLE_PARENT = "recycle_parent"

                /**
                 * The display name of the media item.
                 */
                const val DISPLAY_NAME = "_display_name"

                /**
                 * The time the media item was last modified.
                 */
                const val DATE_MODIFIED = "date_modified"

                /**
                 * Non-zero if the media file is drm-protected
                 */
                const val IS_DRM = "is_drm"

                /**
                 * Volume name of the specific storage device where this media item is
                 * persisted. The value is typically one of the volume names returned
                 * This is a read-only column that is automatically computed.
                 */
                const val VOLUME_NAME = "volume_name"

                /**
                 * Relative path of this media item within the storage device where it
                 * is persisted. For example, an item stored at
                 * /storage/0000-0000/DCIM/Vacation/IMG1024.JPG` would have a
                 * path of `DCIM/Vacation/`.
                 */
                const val RELATIVE_PATH = "relative_path"

                /**
                 * The primary bucket ID of this media item. This can be useful to
                 * present the user a first-level clustering of related media items.
                 * This is a read-only column that is automatically computed.
                 */
                const val RECYCLE_BUCKET_ID = "recycle_bucket_id"

                /**
                 * The time the media item was deleted.
                 */
                const val RECYCLE_DATE = "recycle_date"

                /**
                 * The path for media item in recycle bin.
                 */
                const val RECYCLE_PATH = "recycle_path"

                /**
                 * The media type of the file, or 0 for not a media file
                 */
                const val MEDIA_TYPE = "media_type"

                /**
                 * The MIME type of the media item.
                 */
                const val MIME_TYPE = "mime_type"

                /**
                 * 0 for not a media file
                 */
                const val MEDIA_TYPE_NONE = 0

                /**
                 * file is an image file.
                 */
                const val MEDIA_TYPE_IMAGE = 1

                /**
                 * file is an video file.
                 */
                const val MEDIA_TYPE_VIDEO = 2

                /**
                 * file is an audio file.
                 */
                const val MEDIA_TYPE_AUDIO = 3

                /**
                 * file is a document file.
                 */
                const val MEDIA_TYPE_DOCUMENT = 4

                /**
                 * file is an apk file.
                 */
                const val MEDIA_TYPE_APK = 5

                /**
                 * file is an Archive file.
                 */
                const val MEDIA_TYPE_ARCHIVE = 6

                /**
                 * file is an folders and directories.
                 */
                const val MEDIA_TYPE_FOLDER = 100
            }
        }

        @JvmStatic
        @JvmOverloads
        fun extractMediaTypeByPath(path: String, isExterior: Boolean = false): Int {
            if (path.isEmpty()) {
                return FileColumns.MEDIA_TYPE_NONE
            }
            if (!isExterior) {
                runCatching {
                    if (constructFile(path).isDirectory) {
                        return FileColumns.MEDIA_TYPE_FOLDER
                    }
                }.onFailure {
                    Log.w(TAG, "extractMediaTypeByPath new File failed, err=$it")
                }
            }
            return when (MimeTypeHelper.getTypeFromPath(path)) {
                MimeTypeHelper.IMAGE_TYPE -> FileColumns.MEDIA_TYPE_IMAGE
                MimeTypeHelper.VIDEO_TYPE -> FileColumns.MEDIA_TYPE_VIDEO
                MimeTypeHelper.AUDIO_TYPE -> FileColumns.MEDIA_TYPE_AUDIO
                MimeTypeHelper.DOC_TYPE, MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.XLS_TYPE,
                MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.PPT_TYPE, MimeTypeHelper.PPTX_TYPE,
                MimeTypeHelper.PDF_TYPE, MimeTypeHelper.OFD_TYPE -> FileColumns.MEDIA_TYPE_DOCUMENT
                MimeTypeHelper.APPLICATION_TYPE -> FileColumns.MEDIA_TYPE_APK
                MimeTypeHelper.P7ZIP_TYPE, MimeTypeHelper.RAR_TYPE,
                MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.COMPRESSED_TYPE -> FileColumns.MEDIA_TYPE_ARCHIVE
                else -> FileColumns.MEDIA_TYPE_NONE
            }
        }

        @JvmStatic
        fun getFileSize(path: String): Long {
            if (path.isEmpty()) {
                return 0
            }
            runCatching {
                val file = constructFile(path)
                return if (file.isFile) file.length() else -1L
            }.onFailure { ex ->
                Log.w(TAG, "getFileSize get size failed: $ex")
            }
            return 0
        }

        @JvmStatic
        fun isDrmType(path: String): Boolean {
            return MimeTypeHelper.DRM_TYPE == MimeTypeHelper.getTypeFromPath(path)
        }

        @JvmStatic
        fun getVolumeName(uri: Uri): String? {
            val segments = uri.pathSegments
            return if (segments != null && segments.size > 0) {
                segments[0]
            } else {
                null
            }
        }

        @JvmStatic
        fun isInternalVolume(uri: Uri): Boolean {
            return VOLUME_INTERNAL == getVolumeName(uri)
        }
    }

    @JvmStatic
    fun ensureRestoreOriginPath(originDir: String, displayName: String, isDir: Boolean): String {
        val extension: String
        val name: String
        if (isDir) {
            name = displayName
            extension = ""
        } else {
            // extract extension for file
            val index = displayName.lastIndexOf('.')
            if (index > 0) {
                name = displayName.substring(0, index)
                extension = displayName.substring(index)
            } else {
                name = displayName
                extension = ""
            }
        }
        var newFile: File
        var temp: String
        for (index in 1..Constants.MAX_PROGRESS) {
            temp = checkNameIsOutOfLength(name, index.toString(), extension)
            newFile = File(originDir, temp)
            if (!newFile.exists()) {
                val builder: StringBuilder = StringBuilder()
                builder.append(originDir)
                builder.append(File.separator)
                builder.append(temp)
                return builder.toString()
            }
        }

        val currentTime: Long = System.currentTimeMillis()
        val builder: StringBuilder = StringBuilder()
        builder.append(originDir)
        builder.append(File.separator)
        builder.append(checkNameIsOutOfLength(name, currentTime.toString(), extension))
        return builder.toString()
    }

    /**
     * @param name File name, excluding file extension, E.g: fileName
     * @param suffix File name de-duplication, added index or timestamp suffix, E.g: 1,2...
     * @param extensionWhitPoint File extension, E.g: .docx, hidden file extension is empty
     */
    @JvmStatic
    private fun checkNameIsOutOfLength(name: String, suffix: String, extensionWhitPoint: String): String {
        var finalSuffix = "_$suffix$extensionWhitPoint"
        if (finalSuffix.toByteArray().size >= ANDROID_MAX_NAME_BYTE_LENGTH) {
            finalSuffix = finalSuffix.substring(0, finalSuffix.length / 2)
            Log.d(TAG, "checkNameIsOutOfLength suffix to long, cut out result $finalSuffix")
        }
        val maxByteLength = (ANDROID_MAX_NAME_BYTE_LENGTH - (finalSuffix.toByteArray().size))
        var ensureName = name + finalSuffix
        var isCrop = false
        while (ensureName.toByteArray().size >= maxByteLength) {
            ensureName = ensureName.substring(0, ensureName.length - 1)
            isCrop = true
        }
        if (isCrop) {
            ensureName += finalSuffix
            Log.d(TAG, "checkNameIsOutOfLength fileName to long, cut out result $ensureName")
        }
        return ensureName
    }

    @JvmStatic
    fun getBucketId(path: String): Int {
        val file = File(path.lowercase())
        return file.absolutePath.hashCode()
    }

    @JvmStatic
    @Synchronized
    private fun ensureRecycleRootPath(): String? {
        var recycleDir: String? = createdRecycleDirectory
        if (recycleDir != null) {
            return recycleDir
        }
        runCatching {
            val internalRootFile = OplusUsbEnvironmentCompat.getInternalSdDirectory(appContext)
            Log.d(TAG, "ensureRecycleRootPath rootPath: ${internalRootFile?.absolutePath}")
            internalRootFile?.let {
                val recycleRoot = constructFile(internalRootFile, RECYCLE_DIR_NAME)
                recycleDir = recycleRoot.absolutePath
                if (!recycleRoot.exists() && !recycleRoot.mkdirs()) {
                    Log.w(TAG, "ensureRecycleRootPath MAKE ROOT PATH FAILED")
                    return recycleDir
                }
                createdRecycleDirectory = recycleDir
            }
        }.onFailure { ex ->
            Log.w(TAG, "ensureRecycleRootPath error: $ex")
        }
        return recycleDir
    }

    /**
     * get root query selection
     * @return root query selection
     */
    @JvmStatic
    fun getRootQuerySelection(): String? {
        val rootPaths = getAllRecycleDirectorys()
        if (rootPaths.isEmpty()) {
            Log.w(TAG, "getRootQuerySelection ROOT PATHS EMPTY")
            return null
        }

        val bucketIdBuilder = StringBuilder()
        var bucketId: Int
        for (rootPath in rootPaths) {
            bucketId = getBucketId(rootPath)
            bucketIdBuilder.append(bucketId)
            bucketIdBuilder.append(",")
        }
        bucketIdBuilder.deleteCharAt(bucketIdBuilder.length - 1)

        val queryBuilder = StringBuilder()
        queryBuilder.append(Files.FileColumns.RECYCLE_BUCKET_ID)
        queryBuilder.append(" IN (")
        queryBuilder.append(bucketIdBuilder.toString())
        queryBuilder.append(" )")
        return queryBuilder.toString()
    }

    /**
     * Get recycle directory path
     * @param ensureCreated true is create directory if recycle path not exist, false is not, default true
     * @return current recycle directory
     */
    @JvmStatic
    fun getRecycleDirectory(ensureCreated: Boolean = true): String? {
        val notEnsureCreatedRecycleDir = ensureRecycleRootPath()
        return if (ensureCreated) createdRecycleDirectory else notEnsureCreatedRecycleDir
    }

    /**
     * get legacy recycle path
     * NOTE: it doesn't make the directory if recycle path not exist
     * @return legacy recycle directory
     */
    @JvmStatic
    private fun innerGetLegacyRecyclerDirectory(): String? {
        // Note: forbid call getExternalFilesDirs to prevent from creating the app private directory
        val rootPath = OplusUsbEnvironmentCompat.getInternalPath(appContext) ?: return null
        val builder = StringBuilder(50)
        builder.append(rootPath)
        builder.append(File.separator)
        builder.append("Android/data/")
        builder.append(appContext.packageName)
        builder.append(File.separator)
        builder.append("files/")
        builder.append(LEGACY_RECYCLE_DIR_NAME)
        return builder.toString()
    }

    /**
     * get current recycle path
     * NOTE: it doesn't make the directory if recycle path not exist
     * @return current recycle directory
     */
    @JvmStatic
    private fun innerGetRecyclerDirectory(): String? {
        val internalRootFile =
            OplusUsbEnvironmentCompat.getInternalSdDirectory(appContext) ?: return null
        return internalRootFile.absolutePath + File.separator + RECYCLE_DIR_NAME
    }

    /**
     * get all recycle directory,include current path and legacy path,
     * NOTE: it doesn't make the directory if recycle path not exist
     * @return all recycle directory
     */
    @JvmStatic
    fun getAllRecycleDirectorys(): ArrayList<String> {
        val recyclePaths = arrayListOf<String>()
        val currentPath = innerGetRecyclerDirectory()
        if (!currentPath.isNullOrEmpty()) {
            recyclePaths.add(currentPath)
        }

        val legacyPath = innerGetLegacyRecyclerDirectory()
        if (!legacyPath.isNullOrEmpty()) {
            recyclePaths.add(legacyPath)
        }

        return recyclePaths
    }
}
