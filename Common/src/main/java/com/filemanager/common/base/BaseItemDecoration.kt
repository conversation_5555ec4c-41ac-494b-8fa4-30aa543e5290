/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/9/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import androidx.recyclerview.widget.RecyclerView


abstract class BaseItemDecoration : RecyclerView.ItemDecoration() {
    var mSpanCount = 0
        set(value) {
            field = value
            updateSpanCount()
        }
    @Suppress("Empty")
    open fun updateSpanCount() {}
}