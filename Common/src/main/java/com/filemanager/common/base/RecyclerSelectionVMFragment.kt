/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - RecyclerSelectionVMFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/06/02
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/02    1.0     create
 ****************************************************************/

package com.filemanager.common.base

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.R
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DefaultDragListener
import com.filemanager.common.dragselection.DefaultSelectDelegate
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.dropdrag.dragdrop.DragDropScanner

abstract class RecyclerSelectionVMFragment<VM : SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>>
    : BaseVMFragment<VM>(), OnDragStartListener, OnItemClickListener<Int>, OnGetUIInfoListener {
    companion object {
        private const val TAG = "RecyclerSelectionVMFragment"
    }

    protected var fragmentRecyclerView: FileManagerRecyclerView? = null
    protected var fragmentFastScroller: RecyclerViewFastScroller? = null
    protected var fragmentViewModel: VM? = null
    protected var fragmentDragScanner: DragDropScanner<BaseFileBean>? = null

    /**
     * Add legacy offending naming alias for corrected naming member [fragmentViewModel].
     * Pls merge [fragmentViewModel] and [mViewModel] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fragmentViewModel"))
    protected var mViewModel: VM?
        get() = fragmentViewModel
        set(value) {
            fragmentViewModel = value
        }

    /**
     * Add legacy offending naming alias for corrected naming member [mRecyclerView].
     * Pls merge [fragmentRecyclerView] and [mRecyclerView] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fragmentRecyclerView"))
    protected var mRecyclerView: FileManagerRecyclerView?
        get() = fragmentRecyclerView
        set(value) {
            fragmentRecyclerView = value
        }

    /**
     * Add legacy offending naming alias for corrected naming member [mRecyclerViewFastScroller].
     * Pls merge [fragmentFastScroller] and [mRecyclerViewFastScroller]
     * after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fragmentFastScroller"))
    protected var mRecyclerViewFastScroller: RecyclerViewFastScroller?
        get() = fragmentFastScroller
        set(value) {
            fragmentFastScroller = value
        }

    /**
     * Add legacy offending naming alias for corrected naming member [mDragScanner].
     * Pls merge [fragmentDragScanner] and [mDragScanner] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fragmentDragScanner"))
    protected var mDragScanner: DragDropScanner<BaseFileBean>?
        get() = fragmentDragScanner
        set(value) {
            fragmentDragScanner = value
        }

    override fun getViewModel(): VM? = fragmentViewModel

    override fun getRecyclerView(): FileManagerRecyclerView? = fragmentRecyclerView

    protected inline fun withFragmentRecyclerView(action: (FileManagerRecyclerView) -> Unit) {
        fragmentRecyclerView?.let(action)
    }

    protected inline fun runOnFragmentRecyclerView(action: FileManagerRecyclerView.() -> Unit) {
        fragmentRecyclerView?.run(action)
    }

    protected inline fun withFragmentFastScroller(action: (RecyclerViewFastScroller) -> Unit) {
        fragmentFastScroller?.let(action)
    }

    protected inline fun runOnFragmentFastScroller(action: RecyclerViewFastScroller.() -> Unit) {
        fragmentFastScroller?.run(action)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        fragmentViewModel = createViewModel()
        super.onViewCreated(view, savedInstanceState)
        initSelectionTracker()
    }

    /**
     * Implement create detailed view model in subclass.
     */
    protected abstract fun createViewModel(): VM?

    open fun initSelectionTracker() {
        val recyclerView = fragmentRecyclerView ?: run {
            Log.w(TAG, "initSelectionTracker error: mRecyclerView is null")
            return
        }
        recyclerView.setMIsSupportDragSlide(true)
        runCatching {
            val keyProvider = DefaultKeyProvider(recyclerView)
            val detailsLookup = DefaultDetailsLookup(recyclerView)
            val trackerBuilder = com.oplus.dropdrag.RecycleSelectionBuilder(
                javaClass.name,
                recyclerView,
                DefaultSelectDelegate(fragmentViewModel),
                keyProvider,
                detailsLookup
            )
            trackerBuilder.withSlideSelection(true)
            trackerBuilder.withSlideSelectionStateListener(recyclerView)
            trackerBuilder.withOnDragStartListener(this)
            trackerBuilder.withOnItemClickListener(this)
            PCConnectAction.getItemTouchInterceptor()?.let {
                trackerBuilder.withOnItemTouchListener(it)
            }
            recyclerView.layoutManager?.let {
                if ((it is GridLayoutManager) && (it.spanCount > 1)) {
                    SelectionTracker.LAYOUT_TYPE.GRID
                } else SelectionTracker.LAYOUT_TYPE.LIST
            } ?: SelectionTracker.LAYOUT_TYPE.LIST
            trackerBuilder.build()
        }.onFailure { ex ->
            Log.w(TAG, "initSelectionTracker error: $ex")
        }
    }

    /**
     * onDragStart的返回值没有使用，默认返回 true
     */
    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart dragging:${DragUtils.isDragging}")
        if (DragUtils.isDragging) return false
        fragmentDragScanner?.cancel(true)
        val view = fragmentRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        var result = true
        fragmentRecyclerView?.post {
            val position = fragmentRecyclerView?.getChildAdapterPosition(view) ?: 0
            if (position == -1) {
                Log.e(TAG, "onDragStart position is -1")
                result = false
                return@post
            }
            val dragHoldDownFile = getDragHoldDownFile(position)
            val activityContext = this.activity ?: run { result = false; return@post }
            val selectList = fragmentViewModel?.getSelectItems() ?: run { result = false; return@post }
            val viewMode = fragmentViewModel?.getRecyclerViewScanMode()
            val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
                view.findViewById<ImageView>(R.id.file_list_item_icon).drawable
            } else {
                view.findViewById<ImageView>(R.id.file_grid_item_icon).drawable
            }
            val itemViewList = ArrayList<View>()
            // 全局记录被选中的文件，在页面切换之后进行聚合动画重建
            selectList.forEach { baseFileBean ->
                val files = (fragmentRecyclerView?.adapter as? BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, *>)?.mFiles
                val indexOf = files?.indexOf(baseFileBean)
                if (indexOf != null && indexOf >= 0 && indexOf < files.size) {
                    val itemView = fragmentRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                    itemView?.let {
                        it.tag = DropTag(getFragmentCategoryType(), DropTag.Type.ITEM_VIEW_NOTRESPONSE)
                        itemViewList.add(it)
                    }
                }
            }
            (baseVMActivity as? NavigationInterface)?.let { fragmentViewModel?.setNavigateItemAble(it) }
            DragUtils.createSelectedFileList(selectList)
            fragmentDragScanner = FileDragDropScanner(
                activityContext,
                DefaultDragListener(
                    activityContext,
                    view,
                    dragHoldDownFile,
                    dragHoldDrawable,
                    getFragmentCategoryType(),
                    e,
                    viewMode
                ).addSelectedView(itemViewList),
                viewMode,
                false
            )
            fragmentDragScanner?.takeIf {
                it.addData(selectList)
            }?.execute()
            Log.d(TAG, "onDragStart end")
        }
        return result
    }

    private fun getDragHoldDrawable(
        viewMode: SelectionTracker.LAYOUT_TYPE?,
        view: View
    ): Drawable? {
        val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
            view.findViewById<ImageView>(R.id.file_list_item_icon).drawable
        } else {
            view.findViewById<ImageView>(R.id.file_grid_item_icon).drawable
        }
        return dragHoldDrawable
    }

    open fun getDragHoldDownFile(position: Int): BaseFileBean? =
        fragmentViewModel?.uiState?.value?.fileList.takeUnless {
            it.isNullOrEmpty()
        }?.get(position)

    @SuppressLint("ObjectAnimatorBinding")
    fun changeActionModeAnim(
        toolbar: COUIToolbar,
        runnable: Runnable,
        isShowAnimation: Boolean? = true
    ) {
        if (isShowAnimation == false) {
            runnable.run()
            return
        }
        toolbar.clearAnimation()
        val toolbarOutAnimation = ObjectAnimator.ofFloat(toolbar, "alpha", 1f, 0f)
        val toolbarInAnimation = ObjectAnimator.ofFloat(toolbar, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet().apply {
            duration = KtConstants.ACTION_MODE_ALPHA_DURATION
            interpolator = LinearInterpolator()
            play(toolbarOutAnimation).before(toolbarInAnimation)
        }
        toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                runnable.run()
            }
        })
        toolbarAnimatorSet.start()
    }
}