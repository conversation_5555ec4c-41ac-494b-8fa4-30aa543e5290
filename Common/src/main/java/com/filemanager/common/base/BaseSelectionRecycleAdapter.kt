/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/3/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Looper
import android.view.View
import android.view.animation.PathInterpolator
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.noMoreAction
import com.filemanager.common.view.SelectItemLayout

abstract class BaseSelectionRecycleAdapter<V : RecyclerView.ViewHolder, T>(
    @get:VisibleForTesting(otherwise = VisibleForTesting.PROTECTED) val mContext: Context
) : AbstractSelectionAdapter<V, Int, T>(), BaseSelectionListener {

    companion object {
        private const val TAG = "BaseSelectionRecycleAdapter"
        private const val RIGHT_ANIMATION_START_POSITION = 0
        private const val RIGHT_ANIMATION_END_POSITION = 32
        private const val CHOICE_ANIMATION_START_POSITION = 0
        private const val CHOICE_ANIMATION_END_POSITION = 44
        private const val ANIM_DURATION = 300
        private const val ANIM_DURATION_RIGHT = 100
        private const val CHOICE_ANIMATION_START_ALPHA = 0f
        private const val CHOICE_ANIMATION_END_ALPHA = 1f
        private const val CONTROLX1_POSITION = 0
        private const val CONTROLY1_POSITION = 1
        private const val CONTROLX2_POSITION = 2
        private const val CONTROLY2_POSITION = 3
        private val sPathValue = floatArrayOf(0.3f, 0f, 0f, 1f)
        private val sPathValue2 = floatArrayOf(0.33f, 0.0f, 0.67f, 1.0f)
    }

    val mGridItemImgMarginSize by lazy { appContext.resources.getDimensionPixelOffset(R.dimen.grid_item_img_margin_size) }
    val mNormalTitleMaxSize by lazy { appContext.resources.getDimensionPixelOffset(R.dimen.file_list_item_info_selected_width_new) }
    val mFolderTitleMaxSize by lazy {
        if (mContext is Activity) {
            KtViewUtils.getWindowSize(mContext).x - mContext.getResources()
                .getDimensionPixelOffset(R.dimen.file_list_adapter_folder_max_size)
        } else {
            mNormalTitleMaxSize
        }
    }
    val mIsDarkModel by lazy { COUIDarkModeUtil.isNightMode(appContext) }
    var mFiles: MutableList<T> = mutableListOf()
        get() = field.noMoreAction()
    var mIsAnimRunning: Boolean = false
        protected set

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mChoiceMode: Boolean = false
    protected var mFinishChoiceAnimator = false
    protected var mSearchMode: Boolean = false
    protected var mStartChoiceAnimator = false
    protected var mRecyclerView: RecyclerView? = null

    private var mLastChoiceMode: Boolean = false
    private var mCurrentMode: Int = 0
    private var mChoiceAnimationStartPosition: Int = 0
    private var mChoiceAnimationEndPosition: Int = 0
    private var mRightAnimationStartPosition: Int = 0
    private var mRightAnimationEndPosition: Int = 0
    private var mChoiceInitPosition = java.lang.Float.MIN_VALUE
    private val mRightInitPosition = java.lang.Float.MIN_VALUE

    private var mStartAlpha = 0.0f
    private var mEndAlpha = 1.0f

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        mRecyclerView = recyclerView
    }

    fun notifyItemInsertedWrapper(index: Int) {
        checkComputingAndExecute {
            this.notifyItemInserted(index)
        }
    }

    fun getRecyclerView(): RecyclerView? = mRecyclerView

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)
        item?.let {
            if (it is BaseFileBean) {
                it.mFileWrapperViewType?.let { type ->
                    return type
                }
            }
        }
        return 0
    }

    val allCount: Int
        get() = itemCount

    open fun getHeadViewCount(): Int {
        return 0
    }

    open fun getLabelViewCount(): Int {
        return 0
    }

    open fun getFooterViewCount(): Int {
        return 0
    }

    fun getRealFileItemCount(): Int {
        return itemCount - getHeadViewCount() - getLabelViewCount() - getFooterViewCount()
    }


    override fun getItemCount(): Int {
        return mFiles.size
    }

    open fun getItem(position: Int): T? {
        return if (0 <= position && position < mFiles.size) {
            mFiles[position]
        } else null
    }

    override fun getItemKeyByPosition(position: Int): Int? {
        val item = getItem(position) ?: return null
        return getItemKey(item, position)
    }

    override fun setSelectEnabled(enabled: Boolean) {
        setSelectEnabled(enabled, true)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setSelectEnabled(enabled: Boolean, notifyData: Boolean = true) {
        mLastChoiceMode = mChoiceMode
        mChoiceMode = enabled
        Log.d(TAG, "mChoiceMode =$mChoiceMode")
        checkComputingAndExecute {
            if (!enabled) {
                cancelAll()
            }
            if (notifyData) {
                notifyDataSetChanged()
            }
            initListChoiceModeAnimFlag(enabled)
        }
    }

    override fun checkComputingAndExecute(method: () -> Unit) {
        if (mRecyclerView?.isComputingLayout == true || Looper.myLooper() != Looper.getMainLooper()) {
            mRecyclerView?.post {
                try {
                    method.invoke()
                } catch (e: IllegalStateException) {
                    Log.e(TAG, "checkComputingAndExecute exception: ${e.message}")
                }
            }
        } else {
            try {
                method.invoke()
            } catch (e: IllegalStateException) {
                Log.e(TAG, "checkComputingAndExecute UI-Thread exception: ${e.message}")
            }
        }
    }

    abstract fun initListChoiceModeAnimFlag(flag: Boolean)

    override fun cancelAll() {
        mSelectionArray.clear()
    }

    fun setCurrentMode(mode: Int) {
        mCurrentMode = mode
    }

    fun setChoiceModeAnimFlag(flag: Boolean) {
        if (flag) {
            mStartChoiceAnimator = true
        } else {
            mFinishChoiceAnimator = true
        }
    }

    private fun initAnimParams(checkBox: COUICheckBox, useDefault: Boolean) {
        if (mRightAnimationEndPosition == 0 || mChoiceAnimationEndPosition == 0) {
            val offset = if (appContext.resources.getBoolean(R.bool.animations_rtl)) -1 else 1
            val rightAnimParams = getRightAnimParams()
            val choiceAnimParams = getChoiceAnimParams()
            mRightAnimationStartPosition =
                offset * ViewHelper.dip2px(appContext, rightAnimParams.first)
            mRightAnimationEndPosition =
                offset * ViewHelper.dip2px(appContext, rightAnimParams.second)
            mChoiceAnimationStartPosition =
                offset * ViewHelper.dip2px(appContext, choiceAnimParams.first)
            mChoiceAnimationEndPosition =
                offset * ViewHelper.dip2px(appContext, choiceAnimParams.second)
        }
        mStartAlpha = if (useDefault) CHOICE_ANIMATION_START_ALPHA else 0.0f
        mEndAlpha = if (useDefault) CHOICE_ANIMATION_END_ALPHA else checkBox.alpha
        //Log.d(TAG, "initAnimParams useDefault $useDefault, mEndAlpha $mEndAlpha")
    }

    protected open fun getRightAnimParams(): Pair<Int, Int> {
        return Pair(RIGHT_ANIMATION_START_POSITION, RIGHT_ANIMATION_END_POSITION)
    }

    protected open fun getChoiceAnimParams(): Pair<Int, Int> {
        return Pair(CHOICE_ANIMATION_START_POSITION, CHOICE_ANIMATION_END_POSITION)
    }

    private fun resetPosition(checkBox: COUICheckBox, choiceMode: Boolean, useDefault: Boolean) {
        if (choiceMode && mChoiceInitPosition == java.lang.Float.MIN_VALUE) {
            mChoiceInitPosition = checkBox.x
        }
        checkBox.x =
            if (choiceMode) mChoiceInitPosition else mChoiceInitPosition + mChoiceAnimationEndPosition
        if (useDefault) {
            checkBox.alpha =
                if (choiceMode) CHOICE_ANIMATION_START_ALPHA else CHOICE_ANIMATION_END_ALPHA
        } else {
            checkBox.alpha = if (choiceMode) mStartAlpha else mEndAlpha
        }
    }

    fun setCheckBoxAnim(
        checkBoxAnimateInput: CheckBoxAnimateInput
    ) {
        val choiceMode = checkBoxAnimateInput.choiceMode
        val position = checkBoxAnimateInput.position
        val checkBox = checkBoxAnimateInput.checkBox
        val rightView = checkBoxAnimateInput.rightView
        val isNeedAnim = checkBoxAnimateInput.isNeedAnim
        val isDir = checkBoxAnimateInput.isDir
        val userDefault = checkBoxAnimateInput.userDefault
        Log.d(
            TAG, "startCheckBox Anim,choiceMode = " + choiceMode + "; mStartChoiceAnimator ="
                    + mStartChoiceAnimator + "; mFinishChoiceAnimator =" + mFinishChoiceAnimator
                    + "; position =" + position + "; checkbox x =" + checkBox.x + " userDefault: $userDefault"
        )
        if (checkBox.tag != null) {
            if (checkBox.tag as Boolean) {
                updateCheckBoxState(checkBox, position)
                return
            }
        }
        if (rightView != null && rightView.tag != null) {
            if (rightView.tag as Boolean) {
                updateCheckBoxState(checkBox, position)
                return
            }
        }
        if (isNeedAnim) {
            initAnimParams(checkBox, userDefault)
            resetPosition(checkBox, choiceMode, userDefault)
        }
        if (mStartChoiceAnimator) {
            checkBox.visibility = View.VISIBLE
            val rightViewOutConfig = getAnimationConfigForRightViewOut(rightView)
            startCustomTranslationAnimator(isDir, isChoiceView = false, rightViewOutConfig, position)
            val checkboxInConfig = getAnimationConfigForCheckboxIn(checkBox)
            startCustomTranslationAnimator(isDir, isChoiceView = true, checkboxInConfig, position)
        } else {
            processNoStart(choiceMode, rightView, isNeedAnim, checkBox)
        }
        if (mFinishChoiceAnimator && mLastChoiceMode) {
            val checkBoxOutConfig = gettAnimationConfigForCheckboxOut(checkBox)
            startCustomTranslationAnimator(isDir, true, checkBoxOutConfig, position)
            val rightViewIndConfig = getAnimationConfigForRightViewIn(rightView)
            startCustomTranslationAnimator(isDir, isChoiceView = false, rightViewIndConfig, position)
        } else {
            processNoFinish(choiceMode, rightView, isNeedAnim, checkBox)
        }
        updateCheckBoxState(checkBox, position)
    }

    private fun processNoFinish(
        choiceMode: Boolean,
        rightView: View?,
        isNeedAnim: Boolean,
        checkBox: COUICheckBox
    ) {
        if (!choiceMode) {
            if (rightView != null) {
                rightView.visibility = View.VISIBLE
                rightView.alpha = CHOICE_ANIMATION_END_ALPHA
            }
            if (isNeedAnim) {
                checkBox.translationX = mChoiceAnimationStartPosition.toFloat()
            }
            checkBox.alpha = mStartAlpha
            checkBox.visibility = View.GONE
        }
    }

    private fun processNoStart(
        choiceMode: Boolean,
        rightView: View?,
        isNeedAnim: Boolean,
        checkBox: COUICheckBox
    ) {
        if (choiceMode) {
            if (rightView != null) {
                rightView.visibility = View.INVISIBLE
            }
            if (isNeedAnim) {
                checkBox.translationX = mChoiceAnimationEndPosition.toFloat()
            }
            checkBox.alpha = mEndAlpha
            checkBox.visibility = View.VISIBLE
        }
    }

    open fun updateCheckBoxState(checkBox: COUICheckBox, position: Int) {
        if (mChoiceMode && mSelectionArray.contains(getItemKeyByPosition(position))) {
            checkBox.visibility = View.VISIBLE
            checkBox.state = COUICheckBox.SELECT_ALL
            getRootSelectItemLayout(checkBox)?.setChecked(true)
        } else {
            checkBox.visibility = View.VISIBLE
            checkBox.state = COUICheckBox.SELECT_NONE
            getRootSelectItemLayout(checkBox)?.setChecked(false)
        }
    }

    @VisibleForTesting
    fun getRootSelectItemLayout(view: View?): SelectItemLayout? {
        val parent = view?.parent ?: return null
        if (parent is SelectItemLayout) {
            return parent
        }
        return getRootSelectItemLayout(parent as? View)
    }

    private fun startCustomTranslationAnimator(
        isDir: Boolean,
        isChoiceView: Boolean,
        config: AnimationConfig,
        position: Int
    ) {
        //Log.d(TAG, "startCustomTranslationAnimator isDir $isDir, isChoiceView $isChoiceView, position $position, config: $config")
        val animSet = AnimatorSet()
        val transAnim: ObjectAnimator = ObjectAnimator.ofFloat(
            config.target,
            "translationX",
            config.startPosition.toFloat(),
            config.endPosition.toFloat()
        )
        val alphaAnim: ObjectAnimator =
            ObjectAnimator.ofFloat(config.target, "alpha", config.startAlpha, config.endAlpha)
        transAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationCancel(animation: Animator) {}

            override fun onAnimationEnd(animation: Animator) {
                if (config.inOrOut) {
                    mStartChoiceAnimator = false
                } else {
                    mFinishChoiceAnimator = false
                    config.target?.visibility = View.GONE
                }
                config.target?.tag = null
                config.target?.clearAnimation()
            }

            override fun onAnimationRepeat(animation: Animator) {}

            override fun onAnimationStart(animation: Animator) {
                config.target?.tag = true
            }
        })
        alphaAnim.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animator: Animator) {
                mIsAnimRunning = true
                if (isDir) {
                    config.target?.alpha = config.startAlpha
                    config.target?.visibility = View.VISIBLE
                    //Log.d(TAG, "alphaAnim start alpha ${config.startAlpha}, target ${config.target}")
                }
            }

            override fun onAnimationEnd(animator: Animator) {
                mIsAnimRunning = false
                val mkDir = runCatching {
                    config.target?.getTag(R.id.mark_dir) as Boolean
                }.onFailure {
                }.getOrDefault(isDir)
                if (mkDir) {
                    if (config.inOrOut) {
                        config.target?.visibility = View.VISIBLE
                    } else {
                        config.target?.visibility = View.INVISIBLE
                    }
                    //Log.d(TAG, "alphaAnim end alpha ${config.endAlpha}, target ${config.target}, isVisible ${config.target?.visibility}")
                }
            }

            override fun onAnimationCancel(animator: Animator) {
                mIsAnimRunning = false
                val mkDir = runCatching {
                    config.target?.getTag(R.id.mark_dir) as Boolean
                }.onFailure {
                }.getOrDefault(isDir)
                if (mkDir) {
                    if (config.inOrOut) {
                        config.target?.visibility = View.VISIBLE
                    } else {
                        config.target?.visibility = View.INVISIBLE
                    }
                }
            }

            override fun onAnimationRepeat(animator: Animator) {

            }
        })
        if (isChoiceView) {
            animSet.interpolator = PathInterpolator(
                sPathValue[CONTROLX1_POSITION],
                sPathValue[CONTROLY1_POSITION],
                sPathValue[CONTROLX2_POSITION],
                sPathValue[CONTROLY2_POSITION]
            )
            animSet.playTogether(transAnim, alphaAnim)
            animSet.duration = ANIM_DURATION.toLong()
            animSet.start()
        } else {
            alphaAnim.interpolator = PathInterpolator(
                sPathValue2[CONTROLX1_POSITION],
                sPathValue2[CONTROLY1_POSITION],
                sPathValue2[CONTROLX2_POSITION],
                sPathValue2[CONTROLY2_POSITION]
            )
            alphaAnim.duration = ANIM_DURATION_RIGHT.toLong()
            alphaAnim.start()
        }
    }

    private fun getAnimationConfigForRightViewIn(target: View?): AnimationConfig {
        val config = AnimationConfig(target, true)
        config.startPosition = mRightAnimationStartPosition
        config.endPosition = mRightAnimationEndPosition
        config.startAlpha = CHOICE_ANIMATION_START_ALPHA
        config.endAlpha = CHOICE_ANIMATION_END_ALPHA
        return config
    }

    private fun getAnimationConfigForRightViewOut(target: View?): AnimationConfig {
        val config = AnimationConfig(target, false)
        config.startPosition = mRightAnimationEndPosition
        config.endPosition = mRightAnimationStartPosition
        config.startAlpha = CHOICE_ANIMATION_END_ALPHA
        config.endAlpha = CHOICE_ANIMATION_START_ALPHA
        return config
    }

    private fun getAnimationConfigForCheckboxIn(target: View): AnimationConfig {
        val config = AnimationConfig(target, true)
        config.startPosition = mChoiceAnimationStartPosition
        config.endPosition = mChoiceAnimationEndPosition
        config.startAlpha = mStartAlpha
        config.endAlpha = mEndAlpha
        return config
    }

    private fun gettAnimationConfigForCheckboxOut(target: View): AnimationConfig {
        val config = AnimationConfig(target, false)
        config.startPosition = mChoiceAnimationEndPosition
        config.endPosition = mChoiceAnimationStartPosition
        config.startAlpha = mEndAlpha
        config.endAlpha = mStartAlpha
        return config
    }
}

data class CheckBoxAnimateInput(
    val isDir: Boolean,
    val choiceMode: Boolean,
    val rightView: View?,
    val checkBox: COUICheckBox,
    val position: Int,
    val isNeedAnim: Boolean = true,
    val userDefault: Boolean = true
)


internal data class AnimationConfig(var target: View?, var inOrOut: Boolean) {
    var startAlpha = 0.0f
    var endAlpha = 0.0f

    var startPosition = 0
    var endPosition = 0
    override fun toString(): String {
        return "AnimationConfig(target=$target, inOrOut=$inOrOut, startAlpha=$startAlpha, endAlpha=$endAlpha, " +
                "startPosition=$startPosition, endPosition=$endPosition)"
    }
}

interface BaseSelectionListener {
    fun setSelectEnabled(enabled: Boolean)
    fun cancelAll()
}