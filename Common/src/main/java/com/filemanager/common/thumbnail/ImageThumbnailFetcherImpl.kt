/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ImageThumbnailFetcherImpl
 * * Description : 图片的缩略图文件加载类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.media.ThumbnailUtils
import android.os.CancellationSignal
import android.util.Size
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.Log
import java.io.File
import java.io.IOException

class ImageThumbnailFetcherImpl : BaseThumbnailFetcher() {

    companion object {
        private const val TAG = "ImageThumbnailFetcher"
        private const val THUMBNAIL_SIZE = 200

        fun getThumbnailSize(): Size {
            return Size(THUMBNAIL_SIZE, THUMBNAIL_SIZE)
        }
    }

    override fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        try {
            val bitmap = ThumbnailUtils.createImageThumbnail(File(originPath), thumbnailSize, CancellationSignal())
            BitmapUtils.save(bitmap, thumbnailPath)
            return true
        } catch (e: IOException) {
            Log.e(TAG, "fetchSpecificThumbnail error ", e)
            return false
        }
    }
}