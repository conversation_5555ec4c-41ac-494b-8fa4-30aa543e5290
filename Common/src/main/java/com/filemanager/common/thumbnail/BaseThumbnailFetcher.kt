/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : BaseThumbnailFetcher
 * * Description : 获取缩略图文件的加载基类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.util.Size
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadUtils
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.KtThumbnailHelper.getClassifyDrawable
import com.filemanager.common.utils.Log
import java.io.File

abstract class BaseThumbnailFetcher : IThumbnailFetcher {

    companion object {
        private const val TAG = "BaseThumbnailFetcher"
        const val THUMBNAIL_DIR = "thumbnail"
    }

    protected lateinit var baseFile: BaseFileBean

    override fun fetchThumbnail(file: BaseFileBean, thumbnailSize: Size): String {
        this.baseFile = file
        val path = baseFile.mData ?: ""
        val modifyTime = baseFile.mDateModified
        val fileSize = baseFile.mSize
        // 获取缩略图文件的路径
        val startTime = System.currentTimeMillis()
        var thumbnailPath = generatorThumbnailPath(path, modifyTime, fileSize)
        if (isFileExits(thumbnailPath)) {
            Log.w(TAG, "fetchThumbnail exist thumbnail:$thumbnailPath")
            return thumbnailPath
        }
        // 加载特定文件的缩略图
        val isSuccess = ThreadUtils.callOnAsyncThread {
            isSpecific() && fetchSpecificThumbnail(path, thumbnailPath, thumbnailSize)
        }
        if (!isSuccess) {
            // 加载默认的缩略图
            thumbnailPath = fetchDefaultThumbnail(path, thumbnailSize)
        }
        Log.d(TAG, "fetchThumbnail specific result:$isSuccess, total cost:${System.currentTimeMillis() - startTime} ms")
        return thumbnailPath
    }

    protected open fun generatorThumbnailPath(path: String, modifyTime: Long, fileSize: Long): String {
        return KtThumbnailHelper.generatorThumbnailPath(THUMBNAIL_DIR, path, modifyTime, fileSize)
    }

    /**
     * 是否支持特定的缩略图
     */
    open fun isSpecific(): Boolean {
        return true
    }

    /**
     * 加载特定的缩略图
     */
    abstract fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean

    /**
     * 加载默认的缩略图
     */
    protected fun fetchDefaultThumbnail(originPath: String, thumbnailSize: Size): String {
        val startTime = System.currentTimeMillis()
        // 判断缩略图文件是否存在
        val type = MimeTypeHelper.getTypeFromPath(originPath)
        val thumbnailPath = generatorThumbnailPath("$type", 0, 0)
        if (isFileExits(thumbnailPath)) {
            Log.w(TAG, "fetchDefaultThumbnail exist thumbnail:$thumbnailPath")
            return thumbnailPath
        }
        // 根据类型获取对应的drawable
        val context = MyApplication.appContext
        val drawable = getClassifyDrawable(context, type)
        if (drawable == null) {
            Log.e(TAG, "fetchDefaultThumbnail drawable is null")
            return ""
        }
        // 将drawable保存为文件
        val bitmap = BitmapUtils.createBitmap(drawable)
        BitmapUtils.save(bitmap, thumbnailPath)
        Log.d(TAG, "fetchDefaultThumbnail cost:${System.currentTimeMillis() - startTime} ms")
        return thumbnailPath
    }

    private fun isFileExits(path: String): Boolean {
        return File(path).exists()
    }
}