/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ApkThumbnailFetcherImpl
 * * Description : apk的缩略图文件加载类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.graphics.Bitmap
import android.util.Size
import android.widget.ImageView
import com.filemanager.common.MyApplication
import com.filemanager.common.imageloader.application.ApplicationInfoImageLoadListener
import com.filemanager.common.imageloader.application.ApplicationInfoLoader
import com.filemanager.common.imageloader.application.ApplicationThumbnailLoaderListener
import com.filemanager.common.thread.LockUtils
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.Log

class ApkThumbnailFetcherImpl : BaseThumbnailFetcher() {

    companion object {
        private const val TAG = "ApkThumbnailFetcher"
        private const val TIME_OUT = 3000L
    }

    private val lock = Object()

    override fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        val context = MyApplication.appContext
        val imageView = ImageView(context)
        var isSuccess = false
        val appIconListener = object : ApplicationInfoImageLoadListener {
            override fun onLoadSuccess(tag: String?, bitmap: Bitmap?) {
                Log.d(TAG, "fetchSpecificThumbnail onLoadSuccess: ${bitmap != null}")
                if (bitmap != null) {
                    BitmapUtils.save(bitmap, thumbnailPath)
                    isSuccess = true
                }
                LockUtils.releaseLock(lock)
            }

            override fun onLoadFail(tag: String?, errorMsg: String?) {
                Log.d(TAG, "fetchSpecificThumbnail onLoadFail:$errorMsg")
                LockUtils.releaseLock(lock)
            }
        }
        ApplicationInfoLoader.getInstance().load(baseFile, imageView, ApplicationThumbnailLoaderListener(context, appIconListener, null))
        if (!isSuccess) {
            Log.d(TAG, "fetchSpecificThumbnail start...")
            LockUtils.waitLock(lock, TIME_OUT)
        }
        return isSuccess
    }
}