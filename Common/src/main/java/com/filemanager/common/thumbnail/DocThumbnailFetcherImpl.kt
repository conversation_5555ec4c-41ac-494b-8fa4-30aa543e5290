/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DocThumbnailFetcherImpl
 * * Description : 文档的缩略图文件加载类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.content.Context
import android.net.Uri
import android.util.Size
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.thread.LockUtils
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Log
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.DocThumbnailLoaderFactory
import com.filemanager.thumbnail.doc.IDocThumbnailCallback

class DocThumbnailFetcherImpl : BaseThumbnailFetcher() {

    companion object {
        private const val TAG = "DocThumbnailFetcher"
        private const val TIME_OUT = 3000L

        /**
         * 获取文档缩略图的高度
         */
        fun getThumbnailSize(context: Context): Size {
            val docSize = context.resources.getDimensionPixelSize(R.dimen.dimen_36dp)
            return Size(docSize, docSize)
        }
    }

    private val lock = Object()

    override fun isSpecific(): Boolean {
        return FileImageLoader.isSupportDocThumbnail(MyApplication.appContext)
    }

    override fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        val context = MyApplication.appContext
        val factory = DocThumbnailLoaderFactory.getInstance(context)
        if (factory == null) {
            Log.e(TAG, "fetchSpecificThumbnail doc factory is null")
            return false
        }
        var isSuccess = false
        val docThumbnail = DocThumbnail(originPath, baseFile.mDateModified, baseFile.mSize)
        factory.loadThumbnail(docThumbnail, thumbnailSize.width, thumbnailSize.height, object : IDocThumbnailCallback {
            override fun onDataReady(uri: Uri) {
                Log.d(TAG, "fetchSpecificThumbnail onDataReady $uri")
                kotlin.runCatching {
                    context.contentResolver.openInputStream(uri)?.use { input ->
                        BitmapUtils.save(input, thumbnailPath)
                        isSuccess = true
                    }
                }.onFailure {
                    Log.e(TAG, "fetchSpecificThumbnail onDataReady error", it)
                }
                LockUtils.releaseLock(lock)
            }

            override fun onLoadFailed(e: Throwable) {
                Log.e(TAG, "fetchSpecificThumbnail onLoadFailed", e)
                LockUtils.releaseLock(lock)
            }
        })
        if (!isSuccess) {
            Log.d(TAG, "fetchSpecificThumbnail start...")
            LockUtils.waitLock(lock, TIME_OUT)
        }
        return isSuccess
    }
}