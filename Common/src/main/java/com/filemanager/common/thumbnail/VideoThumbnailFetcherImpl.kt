/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : VideoThumbnailFetcherImpl
 * * Description : 视频的缩略图文件加载类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.media.ThumbnailUtils
import android.os.CancellationSignal
import android.util.Size
import androidx.annotation.WorkerThread
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.filemanager.common.MyApplication
import com.filemanager.common.thread.ThreadUtils
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.FileImageLoader.Companion.VIDEO_FRAME_VALUE
import com.filemanager.common.utils.Log
import java.io.File
import java.io.IOException
import java.util.concurrent.TimeUnit

class VideoThumbnailFetcherImpl : BaseThumbnailFetcher() {

    companion object {
        private const val TAG = "VideoThumbnailFetcher"
        private const val THUMBNAIL_SIZE = 200
        private const val TIME_OUT = 3000L

        fun getThumbnailSize(): Size {
            return Size(THUMBNAIL_SIZE, THUMBNAIL_SIZE)
        }
    }

    override fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        return fetchVideoFirstFrame(originPath, thumbnailPath, thumbnailSize)
    }

    /**
     * 通过 android 原生的ThumbnailUtils来获取的视频缩略图，不是第一帧的截图，而是中间的截图
     */
    private fun fetchVideoMiddleFrame(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        try {
            val bitmap = ThumbnailUtils.createVideoThumbnail(File(originPath), thumbnailSize, CancellationSignal())
            BitmapUtils.save(bitmap, thumbnailPath)
            return true
        } catch (e: IOException) {
            Log.e(TAG, "fetchVideoMiddleFrame error ", e)
            return false
        }
    }

    /**
     * 通过glide 视频缩略图的第一帧
     */
    @WorkerThread
    private fun fetchVideoFirstFrame(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        val context = MyApplication.appContext
        val feature = Glide.with(context).asBitmap().load(originPath)
            .override(thumbnailSize.width, thumbnailSize.height)
            .apply(RequestOptions().frame(VIDEO_FRAME_VALUE))
            .submit()
        Log.d(TAG, "fetchVideoFirstFrame start...")
        kotlin.runCatching {
            return ThreadUtils.callOnAsyncThread {
                val bitmap = feature.get(TIME_OUT, TimeUnit.MILLISECONDS)
                BitmapUtils.save(bitmap, thumbnailPath)
                Log.d(TAG, "fetchVideoFirstFrame success...")
                true
            }
        }.onFailure {
            Log.e(TAG, "fetchVideoFirstFrame error", it)
        }
        return false
    }
}