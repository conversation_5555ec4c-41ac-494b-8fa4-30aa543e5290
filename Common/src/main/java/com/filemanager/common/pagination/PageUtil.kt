/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PageUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/8/19      1.0        create
 ***********************************************************************/
package com.filemanager.common.pagination

object PageUtil {

    private const val DEFAULT_PAGE_SIZE = 300

    @JvmStatic
    fun <T : Any> page(source: List<T>, pageSize: Int = DEFAULT_PAGE_SIZE): List<PageInfo<T>> {
        val totalSize = source.size
        val requestCount = totalSize / pageSize
        val result = mutableListOf<PageInfo<T>>()
        for (i in 0..requestCount) {
            val startIndex = i * pageSize
            val endIndex = totalSize.coerceAtMost((i + 1) * pageSize)
            val pageList = source.subList(startIndex, endIndex)
            result.add(
                PageInfo(
                    pageIndex = i,
                    start = startIndex,
                    end = endIndex,
                    size = endIndex - startIndex,
                    pageData = pageList
                )
            )
            if (endIndex == totalSize) {
                break
            }
        }
        return result
    }

    data class PageInfo<T : Any>(
        var pageIndex: Int,
        // include
        var start: Int,
        // exclude
        var end: Int,
        var size: Int,
        var pageData: List<T>
    )
}