/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ThreadUtils
 * * Description : 线程工具类
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

import android.os.Looper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

object ThreadUtils {

    /**
     * 运行在主线程
     */
    @JvmStatic
    fun <T> callOnMainThread(run: () -> T): T {
        return if (isMainThread()) {
            run.invoke()
        } else {
            runBlocking(Dispatchers.Main) {
                run.invoke()
            }
        }
    }

    /**
     * 运行在子线程
     */
    @JvmStatic
    fun <T> callOnAsyncThread(run: () -> T): T {
        return if (isAsyncThread()) {
            run.invoke()
        } else {
            runBlocking(Dispatchers.IO) {
                run.invoke()
            }
        }
    }

    /**
     * 运行在主线程
     */
    @JvmStatic
    fun runOnMainThread(run: Runnable) {
        if (isMainThread()) {
            run.run()
        } else {
            MainScope().launch(Dispatchers.Main) {
                run.run()
            }
        }
    }

    /**
     * 运行在子线程
     */
    @JvmStatic
    fun runOnAsyncThread(run: Runnable) {
        if (isAsyncThread()) {
            run.run()
        } else {
            MainScope().launch(Dispatchers.IO) {
                run.run()
            }
        }
    }

    /**
     * 是否在主线程
     */
    @JvmStatic
    fun isMainThread(): Boolean {
        return Thread.currentThread() == Looper.getMainLooper().thread
    }

    /**
     * 是否在异步线程中
     */
    @JvmStatic
    fun isAsyncThread(): Boolean {
        return isMainThread().not()
    }
}