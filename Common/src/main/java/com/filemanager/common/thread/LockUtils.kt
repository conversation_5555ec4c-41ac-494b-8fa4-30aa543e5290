/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : LockUtils
 * * Description : Lock 相关工具类
 * * Version     : 1.0
 * * Date        : 2025/05/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

import com.filemanager.common.utils.Log

object LockUtils {

    private const val TAG = "LockUtils"

    @JvmStatic
    fun waitLock(lock: Object, timeout: Long = 0) {
        val thread = Thread.currentThread()
        if (thread.isInterrupted.not()) {
            kotlin.runCatching {
                synchronized(lock) {
                    lock.wait(timeout)
                }
            }.onFailure {
                Log.e(TAG, "waitLock exception", it)
            }
        }
    }

    @JvmStatic
    fun releaseLock(lock: Object) {
        kotlin.runCatching {
            synchronized(lock) {
                lock.notify()
            }
        }.onFailure {
            Log.e(TAG, "releaseLock exception", it)
        }
    }
}