/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: ViewHelper.java
 ** Description: for 6.0 GUI.
 ** Version: V 1.0
 ** Date : 2018-11-7
 ** Author: liangjuqing
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.helper;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.coui.appcompat.statusbar.COUIStatusbarTintUtil;
import com.coui.appcompat.toolbar.COUIToolbar;
import com.filemanager.common.compat.OplusChangeTextUtilCompat;
import com.filemanager.common.utils.Log;

import java.lang.reflect.Method;

public class ViewHelper {
    private static final String TAG = "ViewHelper";
    public static final int UNKNOWN = 0;
    public static final int COLOR_OS_3_0 = 6;

    private static final float DP_TRANS_OFFSET = 0.5f;

    public static void setStatusBarTransparentAndBlackFont(Activity mActivity) {
        Window window = mActivity.getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            window.setStatusBarColor(Color.TRANSPARENT);
        }

        int versionCode = getRomVersionCode();
        if ((versionCode >= COLOR_OS_3_0) || (versionCode == 0)) {
            View decorView = mActivity.getWindow().getDecorView();
            mActivity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                decorView.setSystemUiVisibility(decorView.getSystemUiVisibility()
                        | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            } else {
                decorView.setSystemUiVisibility(decorView.getSystemUiVisibility()
                        | COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT);
            }
        }
    }

    public static void setStatusBarWhiteFont(Activity activity, boolean flag) {
        View decorView = activity.getWindow().getDecorView();
        if (flag) {
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility()
                    | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        } else {
            decorView.setSystemUiVisibility(decorView.getSystemUiVisibility()
                    | ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }

    public static int getRomVersionCode() {
        int curRomVersion = UNKNOWN;
        try {
            Class<?> romClass = Class.forName("com.color.os.ColorBuild");
            if (romClass == null) {
                return curRomVersion;
            }
            Method getColorOSVERSION = romClass.getDeclaredMethod("getColorOSVERSION");
            Object o = getColorOSVERSION.invoke(romClass);
            curRomVersion = ((Integer) o).intValue();

        } catch (Exception e) {
            Log.e("RomVersionUtil", "getRomVersionCode failed. error = " + e.getMessage());
        }
        return curRomVersion;
    }

    public static void setViewPaddingTopBelowAnchor(final View anchor, final View[] setList) {
        if (anchor != null) {
            anchor.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    anchor.getViewTreeObserver().removeOnPreDrawListener(this);
                    int padding = anchor.getBottom();
                    View target = null;
                    for (int i = 0; i < setList.length; i++) {
                        target = setList[i];
                        target.setPadding(target.getPaddingLeft(), target.getPaddingTop() + padding,
                                target.getPaddingRight(), target.getPaddingBottom());
                    }
                    return false;
                }
            });
        }
    }

    public static void setViewPaddingAvoidCovered(final View coverView, final View mainView) {
        setViewPaddingAvoidCovered(coverView, mainView, 0);
    }

    public static void setViewPaddingAvoidCovered(final View coverView, final View mainView, final int defaultHeight) {
        coverView.post(new Runnable() {
            @Override
            public void run() {
                int topPadding = coverView.getMeasuredHeight();
                if (topPadding <= 0) {
                    topPadding = defaultHeight;
                }
                mainView.setPadding(mainView.getPaddingLeft(), mainView.getPaddingTop() + topPadding,
                        mainView.getPaddingRight(), mainView.getPaddingBottom());
            }
        });
    }

    public static void setViewMarginTopBelowAnchor(final View anchor, final View[] views) {
        if (anchor != null) {
            anchor.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    anchor.getViewTreeObserver().removeOnPreDrawListener(this);
                    int padding = anchor.getHeight();
                    View target = null;
                    ViewGroup.LayoutParams params = null;
                    for (int i = 0; i < views.length; i++) {
                        target = views[i];
                        params = target.getLayoutParams();
                        if ((params != null) && (params instanceof ViewGroup.MarginLayoutParams)) {
                            ViewGroup.MarginLayoutParams mlp = (ViewGroup.MarginLayoutParams) params;
                            mlp.topMargin = padding;
                        }
                    }
                    return false;
                }
            });
        }
    }


    public static int dip2px(Context context, int dpValue) {
        if (dpValue == 0) {
            return 0;
        }
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + DP_TRANS_OFFSET);
    }

    public static int px2dip(Context context, int pxValue) {
        if (pxValue == 0) {
            return 0;
        }
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + DP_TRANS_OFFSET);
    }

    public static void setClassificationTextSize(Context context, TextView textView) {
        setClassificationTextSize(context, textView, OplusChangeTextUtilCompat.G3);
    }

    public static void setClassificationTextSizeG2(Context context, TextView textView) {
        setClassificationTextSize(context, textView, OplusChangeTextUtilCompat.G2);
    }

    public static void setClassificationTextSize(Context context, TextView textView, int classificationType) {
        float suitTextSize = setClassificationTextSize(context, textView.getTextSize(), classificationType);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, suitTextSize);
    }

    public static float setClassificationTextSize(Context context, float curTextSize, int classificationType) {
        Resources resources = context.getResources();
        float fontScale = resources.getConfiguration().fontScale;
        return OplusChangeTextUtilCompat.getSuitableFontSize(curTextSize, fontScale, classificationType);
    }

    public static void setClassificationAndInvariantTextSize(Context context, TextView textView, int classificationType) {
        Resources resources = context.getResources();
        float suitTextSize = setClassificationTextSize(context, textView.getTextSize(), classificationType);
        float defaultDensity = (float) DisplayMetrics.DENSITY_DEVICE_STABLE / DisplayMetrics.DENSITY_DEFAULT;
        DisplayMetrics displayMetrics = resources.getDisplayMetrics();
        float densityScale = defaultDensity / displayMetrics.density;
        suitTextSize = suitTextSize * densityScale;
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, suitTextSize);
    }

    public static void setMenuItemVisible(MenuItem item, boolean isVisible) {
        if (item == null) {
            return;
        }

        if (isVisible && (!item.isVisible())) {
            item.setVisible(isVisible);
            return;
        }

        if ((!isVisible) && item.isVisible()) {
            item.setVisible(isVisible);
            return;
        }
    }

    public static void setMenuItemEnable(MenuItem item, boolean isEnable) {
        if (item == null) {
            return;
        }
        item.setEnabled(isEnable);
    }

    public static void setToolbarTitle(COUIToolbar toolbar, Context context, int titleResId) {
        if ((context == null) || (toolbar == null)) {
            return;
        }
        String title = context.getString(titleResId);
        setToolbarTitle(toolbar, title);
    }

    public static void setToolbarTitle(COUIToolbar toolbar, String title) {
        if ((toolbar == null) || (title == null)) {
            return;
        }

        if (!toolbar.getTitle().toString().equals(title)) {
            toolbar.setTitle(title);
        }
    }


    public static Rect getViewRect(View view) {
        if (null == view) {
            return null;
        }
        int[] location = new int[2];
        view.getLocationInWindow(location);
        int x = location[0];
        int y = location[1];
        Rect rect = new Rect(x, y, view.getWidth() + x, view.getHeight() + y);
        return rect;
    }
}
