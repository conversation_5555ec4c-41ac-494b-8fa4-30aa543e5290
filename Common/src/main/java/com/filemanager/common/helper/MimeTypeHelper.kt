/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:Main6.java
 * * Description:Parsing class for mimeType
 * * Version:1.0
 * * Date :20200219
 * * Author:yancongxian
 ****************************************************************/
package com.filemanager.common.helper

import android.content.Context
import android.drm.DrmManagerClient
import android.graphics.drawable.Drawable
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import android.webkit.MimeTypeMap
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import java.util.Locale

class MimeTypeHelper {

    object MimeType {
        const val MIMETYPE_UNKNOWN = "*/*"

        const val MIMETYPE_IMAGE = "image/*"
        const val MIMETYPE_VIDEO = "video/*"
        const val MIMETYPE_AUDIO = "audio/*"
        const val MIMETYPE_PDF = "application/pdf"
        const val MIMETYPE_OFD = "application/ofd"
        const val MIMETYPE_DOC = "application/vnd.ms-word"
        const val MIMETYPE_XLS = "application/vnd.ms-excel"
        const val MIMETYPE_PPT = "application/vnd.ms-powerpoint"
        const val MIMETYPE_TXT = "text/plain"
        const val MIMETYPE_LRC = "application/lrc"
        const val MIMETYPE_APPLICATION = "application/vnd.android.package-archive"
        const val MIMETYPE_HTML = "text/html"
        const val MIMETYPE_XML = "text/xml"
        const val MIMETYPE_VCF = "text/x-vcard"
        const val MIMETYPE_P12 = "application/x-pkcs12"
        const val MIMETYPE_CER = "application/pkix-cert"

        const val MIMETYPE_KEYNOTE = "application/vnd.apple.keynote"
        const val MIMETYPE_PAGES = "application/vnd.apple.pages"
        const val MIMETYPE_NUMBERS = "application/vnd.apple.numbers"
        const val MIMETYPE_MARKDOWN = "text/markdown"

        const val MIMETYPE_DWG = "application/acad"
        const val MIMETYPE_DWG_DEFAULT_APP = "application/x-dwg"
        const val MIMETYPE_AUTOCAD = "application/x-autocad"
        const val MIMETYPE_DWT = "application/x-dwt"
        const val MIMETYPE_VND_DWG = "image/vnd.dwg"
        const val MIMETYPE_DXF = "image/vnd.dxf"
        const val MIMETYPE_X_DWG = "image/x-dwg"
        const val MIMETYPE_X_DXF = "image/x-dxf"
        const val MIMETYPE_TIKA_DXF = "image/vnd.dxf; format\u003dascii"

        const val MIMETYPE_XMIND = "application/xmind"
        const val MIMETYPE_X_XMIND = "application/x-xmind"
        const val MIMETYPE_FREE_MIND = "application/x-freemind"

        const val MIMETYPE_TIKA_PSD = "image/vnd.adobe.photoshop"
        const val MIMETYPE_PSD = "application/photoshop"
        const val MIMETYPE_AI = "application/postscript"
        const val MIMETYPE_VISIO = "application/vnd.visio"
        const val MIMETYPE_VISIO_DEFAULT_APP = "application/vsdx"

        const val MIMETYPE_7Z = "application/x-7z-compressed"
        const val MIMETYPE_OGG = "audio/ogg"
        const val MIMETYPE_JAR = "application/java-archive"
        const val MIMETYPE_APE = "audio/ape"
        const val MIMETYPE_DM = "application/x-android-drm-fl"
        const val MIMETYPE_DCF = "application/x-android-drm-fl"

        const val MIMETYPE_STREAM = "application/octet-stream"
    }

    companion object {
        const val TAG = "MimeTypeHelper"

        const val UNKNOWN_TYPE = 0x00000001
        const val DIRECTORY_TYPE = 0x00000002
        const val UMD_TYPE = 0x00000003
        const val IMAGE_TYPE = 0x00000004
        const val VIDEO_TYPE = 0x00000010
        const val AUDIO_TYPE = 0x00000008
        const val HTML_TYPE = 0x00000100
        const val TXT_TYPE = 0x00000200
        const val APPLICATION_TYPE = 0x00000040
        const val EBK_TYPE = 0x00000005
        const val CHM_TYPE = 0x00000006
        const val EPUB_TYPE = 0x00080000
        const val DOC_TYPE = 0x00008000
        const val DOCX_TYPE = 0x00100000
        const val XLS_TYPE = 0x00200000
        const val XLSX_TYPE = 0x00400000
        const val PPT_TYPE = 0x00800000
        const val PPTX_TYPE = 0x01000000
        const val PDF_TYPE = 0x02000000
        const val OFD_TYPE = 0x02000001
        const val VMSG_TYPE = 0x00000007
        const val CSV_TYPE = 0x00000800
        const val VCF_TYPE = 0x00001000
        const val THEME_TYPE = 0x00000020
        const val ICS_TYPE = 0x00002000
        const val VCS_TYPE = 0x00004000
        const val LRC_TYPE = 0x00000400
        const val DAT_TYPE = 0x00040000
        const val COMPRESSED_TYPE = 0x00000080
        const val TORRENT_TYPE = -0x80000000
        const val DRM_TYPE = 0x60000000
        const val P12_TYPE = 0x00000060
        const val CER_TYPE = 0x00000070
        const val ZIP_TYPE = 0x20000000
        const val JAR_TYPE = 0x30000000
        const val RAR_TYPE = 0x40000000
        const val P7ZIP_TYPE = 0x50000000
        const val DB_TYPE = 0x00020000
        const val ALBUM_SET_TYPE_CARD_CASE = 0x00000600
        const val KEYNOTE_TYPE = 0x70000000
        const val PAGES_TYPE = 0x70000001
        const val NUMBERS_TYPE = 0x70000002
        const val MARKDOWN_TYPE = 0x70000003
        const val DWG_TYPE = 0x70000004
        const val DWT_TYPE = 0x70000005
        const val DXF_TYPE = 0x70000006
        const val XMIND_TYPE = 0x70000007
        const val PSD_TYPE = 0x70000008
        const val AI_TYPE = 0x70000009
        const val VSDX_TYPE = 0x7000000a
        const val VSDM_TYPE = 0x7000000b
        const val VSTX_TYPE = 0x7000000c
        const val VSTM_TYPE = 0x7000000d
        const val VSSX_TYPE = 0x7000000e
        const val VSSM_TYPE = 0x7000000f
        const val VSD_TYPE = 0x70000010
        const val VSS_TYPE = 0x70000011
        const val VST_TYPE = 0x70000012
        const val VDW_TYPE = 0x70000013
        const val JS_TYPE = 0x71000000
        const val EXE_TYPE = 0x71000001
        const val DMG_TYPE = 0x71000002
        const val KDOCS_OTL_TYPE = 0x71000003 //金山在线文档
        const val KDOCS_KSHEET_TYPE = 0x71000004 //金山在线表格
        const val KDOCS_DBT_TYPE = 0x71000005 //金山轻维表
        const val TENCENT_FORM_TYPE = 0x71000006 //腾讯在线收集表
        const val TENCENT_MIND_TYPE = 0x71000007 //腾讯在线思维导图
        const val TENCENT_FLOWCHART_TYPE = 0x71000008 //在线流程图
        const val TENCENT_SMART_SHEET_TYPE = 0x71000009 //腾讯智能表格
        const val TENCENT_SMART_CANVAS_TYPE = 0x7100000a //腾讯智能文档
        const val KDOCS_SHARE_FOLDER_TYPE = 0x7100000b //金山共享文件夹

        const val PSD_FORMAT = "psd"
        const val DOC_TYPE_PDF = ".pdf"
        const val DOC_TYPE_OFD = ".ofd"

        private const val KEYNOTE_FORMAT = "key"
        private const val PAGES_FORMAT = "pages"
        private const val NUMBERS_FORMAT = "numbers"
        private const val MARKDOWN_FORMAT = "md"
        const val DWG_FORMAT = "dwg"
        private const val DWT_FORMAT = "dwt"
        const val DXF_FORMAT = "dxf"
        private const val XMIND_FORMAT = "xmind"
        private const val AI_FORMAT = "ai"
        private const val VSDX_FORMAT = "vsdx"
        private const val VSDM_FORMAT = "vsdm"
        private const val VSTX_FORMAT = "vstx"
        private const val VSTM_FORMAT = "vstm"
        private const val VSSX_FORMAT = "vssx"
        private const val VSSM_FORMAT = "vssm"


        @JvmField
        val CATEGORY_DOC =
            arrayOf(".txt", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", DOC_TYPE_PDF, DOC_TYPE_OFD)
        private val compressedExtTypeMap = mutableMapOf<String, Int>()
        private var additionalMimeTypeMap = mutableMapOf<String, String>()
        private var extTypeMap = mutableMapOf<String, Int>()

        init {
            additionalMimeTypeMap["7z"] = MimeType.MIMETYPE_7Z
            additionalMimeTypeMap["ogg"] = MimeType.MIMETYPE_OGG
            additionalMimeTypeMap["jar"] = MimeType.MIMETYPE_JAR
            additionalMimeTypeMap["ape"] = MimeType.MIMETYPE_APE
            additionalMimeTypeMap["lrc"] = MimeType.MIMETYPE_LRC
            additionalMimeTypeMap["ebk2"] = MimeType.MIMETYPE_TXT
            additionalMimeTypeMap["ebk3"] = MimeType.MIMETYPE_TXT
            additionalMimeTypeMap["dm"] = MimeType.MIMETYPE_DM
            additionalMimeTypeMap["dcf"] = MimeType.MIMETYPE_DCF
            additionalMimeTypeMap["rm"] = MimeType.MIMETYPE_UNKNOWN

            additionalMimeTypeMap["key"] = MimeType.MIMETYPE_KEYNOTE
            additionalMimeTypeMap["pages"] = MimeType.MIMETYPE_PAGES
            additionalMimeTypeMap["numbers"] = MimeType.MIMETYPE_NUMBERS

            extTypeMap["doc"] = DOC_TYPE
            extTypeMap["html"] = HTML_TYPE
            extTypeMap["htm"] = HTML_TYPE
            extTypeMap["epub"] = EPUB_TYPE
            extTypeMap["lrc"] = LRC_TYPE
            extTypeMap["dat"] = DAT_TYPE
            extTypeMap["csv"] = CSV_TYPE
            extTypeMap["vcf"] = VCF_TYPE
            extTypeMap["ics"] = ICS_TYPE
            extTypeMap["vcs"] = VCS_TYPE
            extTypeMap["apk"] = APPLICATION_TYPE
            extTypeMap["theme"] = THEME_TYPE
            extTypeMap["db"] = DB_TYPE
            extTypeMap["docx"] = DOCX_TYPE
            extTypeMap["xls"] = XLS_TYPE
            extTypeMap["xlsx"] = XLSX_TYPE
            extTypeMap["ppt"] = PPT_TYPE
            extTypeMap["pptx"] = PPTX_TYPE
            extTypeMap["pdf"] = PDF_TYPE
            extTypeMap["ofd"] = OFD_TYPE
            extTypeMap["txt"] = TXT_TYPE
            extTypeMap["wav"] = AUDIO_TYPE
            extTypeMap["amr"] = AUDIO_TYPE
            extTypeMap["asf"] = VIDEO_TYPE
            extTypeMap["vmsg"] = VMSG_TYPE
            extTypeMap["torrent"] = TORRENT_TYPE
            extTypeMap["chm"] = CHM_TYPE
            extTypeMap["p12"] = P12_TYPE
            extTypeMap["cer"] = CER_TYPE
            extTypeMap["ebk2"] = EBK_TYPE
            extTypeMap["ebk3"] = EBK_TYPE
            extTypeMap["ico"] = IMAGE_TYPE
            if (FeatureCompat.sIsSupportDrm) {
                extTypeMap["fl"] = DRM_TYPE
                extTypeMap["dm"] = DRM_TYPE
                extTypeMap["dcf"] = DRM_TYPE
            }
            extTypeMap["rar"] = COMPRESSED_TYPE
            extTypeMap["zip"] = COMPRESSED_TYPE
            extTypeMap["ozip"] = COMPRESSED_TYPE
            extTypeMap["jar"] = COMPRESSED_TYPE
            extTypeMap["7z"] = COMPRESSED_TYPE

            extTypeMap["key"] = KEYNOTE_TYPE
            extTypeMap["pages"] = PAGES_TYPE
            extTypeMap["numbers"] = NUMBERS_TYPE
            extTypeMap["md"] = MARKDOWN_TYPE
            extTypeMap["dwg"] = DWG_TYPE
            extTypeMap["dwt"] = DWT_TYPE
            extTypeMap["dxf"] = DXF_TYPE
            extTypeMap["xmind"] = XMIND_TYPE
            extTypeMap["psd"] = PSD_TYPE
            extTypeMap["ai"] = AI_TYPE
            extTypeMap["vsdx"] = VSDX_TYPE
            extTypeMap["vsdm"] = VSDM_TYPE
            extTypeMap["vstx"] = VSTX_TYPE
            extTypeMap["vstm"] = VSTM_TYPE
            extTypeMap["vssx"] = VSSX_TYPE
            extTypeMap["vssm"] = VSSM_TYPE
            extTypeMap["vsd"] = VSD_TYPE
            extTypeMap["vss"] = VSS_TYPE
            extTypeMap["vst"] = VST_TYPE
            extTypeMap["vdw"] = VDW_TYPE
            extTypeMap["js"] = JS_TYPE
            extTypeMap["exe"] = EXE_TYPE
            extTypeMap["dmg"] = DMG_TYPE

            compressedExtTypeMap["rar"] = RAR_TYPE
            compressedExtTypeMap["zip"] = ZIP_TYPE
            compressedExtTypeMap["ozip"] = ZIP_TYPE
            compressedExtTypeMap["jar"] = JAR_TYPE
            compressedExtTypeMap["7z"] = P7ZIP_TYPE
        }

        @JvmStatic
        fun getMimeTypeFromPath(path: String?): String? {
            if (path == null) {
                return MimeType.MIMETYPE_UNKNOWN
            }
            val ext = FileTypeUtils.getExtension(path)?.lowercase(Locale.US)
            return getMimeTypeFromExtension(ext)
        }

        @JvmStatic
        private fun getMimeTypeFromExtension(extension: String?): String? {
            if (TextUtils.isEmpty(extension)) {
                return MimeType.MIMETYPE_UNKNOWN
            }
            if (additionalMimeTypeMap.containsKey(extension)) {
                return additionalMimeTypeMap[extension]
            }
            val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                extension?.lowercase(Locale.US)
            )
            return if (!TextUtils.isEmpty(mimeType)) {
                mimeType
            } else {
                MimeType.MIMETYPE_UNKNOWN
            }
        }

        @JvmStatic
        fun getTypeFromPath(path: String?): Int {
            if (null == path) {
                return UNKNOWN_TYPE
            }
            val ext = FileTypeUtils.getExtension(path)
            val type = getTypeFromExtension(ext)
            return if ((type == null) || (type == UNKNOWN_TYPE)) {
                getMediaType(path)
            } else {
                type
            }
        }

        @JvmStatic
        fun getTypeByMimeType(mimeType: String): Int {
            return when {
                mimeType.contains("photoshop") -> PSD_TYPE
                mimeType.startsWith("image/") -> IMAGE_TYPE
                mimeType.startsWith("video/") -> VIDEO_TYPE
                mimeType.startsWith("audio/") -> AUDIO_TYPE
                mimeType.startsWith("text/") -> TXT_TYPE
                else -> UNKNOWN_TYPE
            }
        }

        @JvmStatic
        fun getTypeFromExtension(extension: String?): Int? {
            if (TextUtils.isEmpty(extension)) {
                return UNKNOWN_TYPE
            }
            val ext = extension?.lowercase(Locale.US)
            if (extTypeMap.containsKey(ext)) {
                return extTypeMap[ext]
            } else {
                val mimeType = getMimeTypeFromExtension(ext)
                if (mimeType != null) {
                    return getTypeByMimeType(mimeType)
                }
            }
            return UNKNOWN_TYPE
        }

        @JvmStatic
        fun getMimeTypeFromExtensions(extensions: MutableList<String>): String? {
            if (extensions.isEmpty()) {
                return MimeType.MIMETYPE_UNKNOWN
            }
            var preMimeType: String? = null
            var curMimeType: String?
            for (extension in extensions) {
                curMimeType = getMimeTypeFromExtension(extension)
                if (preMimeType.isNullOrEmpty() || preMimeType == curMimeType) {
                    preMimeType = curMimeType
                } else {
                    if (curMimeType.isNullOrEmpty() || curMimeType == MimeType.MIMETYPE_UNKNOWN) {
                        return MimeType.MIMETYPE_UNKNOWN
                    } else {
                        preMimeType = preMimeType.substring(0, preMimeType.indexOf("/"))
                        curMimeType = curMimeType.substring(0, curMimeType.indexOf("/"))
                        if (preMimeType == curMimeType) {
                            preMimeType = preMimeType.plus("/*")
                        }
                    }
                }
                if (preMimeType.equals(MimeType.MIMETYPE_UNKNOWN)) {
                    return preMimeType
                }
            }
            return preMimeType
        }

        @JvmStatic
        fun getDrmMimeType(context: Context, uri: Uri): String? {
            var drmMimetype: String? = null
            val drmManagerClient = DrmManagerClient(context)
            try {
                if (drmManagerClient.canHandle(uri, null)) {
                    drmMimetype = drmManagerClient.getOriginalMimeType(uri)
                }
            } catch (e: Exception) {
                Log.e(TAG, e.message)
            } finally {
                drmManagerClient.close()
            }
            return drmMimetype
        }

        @JvmStatic
        fun getTypeFromDrm(context: Context, originalType: Int, path: String?): Int {
            val internalPath = OplusUsbEnvironmentCompat.getInternalPath(context)
            if (TextUtils.isEmpty(path) || TextUtils.isEmpty(internalPath)) {
                return originalType
            }
            if (originalType == DRM_TYPE && path!!.startsWith(internalPath)) {
                val mineType = getDrmMimeType(context, path)
                Log.d(TAG, "getTypeFromDrm mineType = $mineType")
                if (!TextUtils.isEmpty(mineType)) {
                    if (mineType?.startsWith("image/") == true) {
                        return IMAGE_TYPE
                    } else if (mineType?.startsWith("video/") == true) {
                        return VIDEO_TYPE
                    } else if (mineType?.startsWith("audio/") == true) {
                        return AUDIO_TYPE
                    }
                }
            }
            return originalType
        }

        @JvmStatic
        fun getDrmMimeType(context: Context, path: String): String? {
            var drmMimetype: String? = null
            val drmManagerClient = DrmManagerClient(context)
            try {
                if (drmManagerClient.canHandle(path, null)) {
                    drmMimetype = drmManagerClient.getOriginalMimeType(path)
                }
            } catch (e: Exception) {
                Log.e(TAG, e.message)
            } finally {
                drmManagerClient.close()
            }
            return drmMimetype
        }

        @JvmStatic
        fun isAudioType(type: Int): Boolean {
            return (type == AUDIO_TYPE)
        }

        @JvmStatic
        fun isDocType(type: Int): Boolean {
            return (type == TXT_TYPE) || (type == DOC_TYPE)
                    || (type == DOCX_TYPE) || (type == XLS_TYPE)
                    || (type == PPTX_TYPE) || (type == PDF_TYPE)
                    || (type == XLSX_TYPE) || (type == PPT_TYPE)
                    || (type == OFD_TYPE)
        }

        @JvmStatic
        fun isImageType(type: Int): Boolean {
            return (type == IMAGE_TYPE)
        }

        @JvmStatic
        fun isVideoType(type: Int): Boolean {
            return (type == VIDEO_TYPE)
        }

        @JvmStatic
        fun isOtherDocType(type: Int): Boolean {
            return (type == KEYNOTE_TYPE) || (type == PAGES_TYPE)
                    || (type == NUMBERS_TYPE) || (type == MARKDOWN_TYPE)
                    || (type == DWG_TYPE) || (type == DWT_TYPE)
                    || (type == DXF_TYPE) || (type == XMIND_TYPE)
                    || (type == PSD_TYPE) || (type == AI_TYPE)
                    || (type == VSDX_TYPE) || (type == VSDM_TYPE)
                    || (type == VSTX_TYPE) || (type == VSTM_TYPE)
                    || (type == VSSX_TYPE) || (type == VSSM_TYPE)
                    || (type == VSD_TYPE) || (type == VSS_TYPE)
                    || (type == VST_TYPE) || (type == VDW_TYPE)
        }

        @JvmStatic
        fun isCompressType(type: Int): Boolean {
            return (type == COMPRESSED_TYPE) || (type == ZIP_TYPE)
                    || (type == RAR_TYPE) || (type == JAR_TYPE) || (type == P7ZIP_TYPE)
        }

        @JvmStatic
        fun isIworkType(ext: String): Boolean {
            return ((ext == KEYNOTE_FORMAT) || (ext == PAGES_FORMAT) || (ext == NUMBERS_FORMAT))
        }

        @JvmStatic
        fun isCadType(ext: String): Boolean {
            return ((ext == DWG_FORMAT) || (ext == DWT_FORMAT) || (ext == DXF_FORMAT))
        }

        @JvmStatic
        fun isMarkdownType(ext: String): Boolean {
            return ext == MARKDOWN_FORMAT
        }

        @JvmStatic
        fun getMediaType(path: String?): Int {
            if (TextUtils.isEmpty(path)) {
                return UNKNOWN_TYPE
            }
            var type = UNKNOWN_TYPE
            val mediaFileType = MediaFileCompat.getFileType(path) ?: return UNKNOWN_TYPE
            if (MediaFileCompat.isAudioFileType(mediaFileType.fileType)) {
                type = AUDIO_TYPE
            } else if (MediaFileCompat.isImageFileType(mediaFileType.fileType)) {
                type = IMAGE_TYPE
            } else if (MediaFileCompat.isVideoFileType(mediaFileType.fileType)) {
                type = VIDEO_TYPE
            }
            return type
        }

        @JvmStatic
        fun getMediaTypeByType(originType: Int?, path: String? = ""): Int {
            var type = originType
            if (originType == DRM_TYPE) {
                type = getTypeFromDrm(appContext, originalType = originType, path = path)
            }
            var mediaType = MediaStore.Files.FileColumns.MEDIA_TYPE_NONE
            when (type) {
                IMAGE_TYPE -> {
                    mediaType = MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE
                }
                VIDEO_TYPE -> {
                    mediaType = MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO
                }
                AUDIO_TYPE -> {
                    mediaType = MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO
                }
                PDF_TYPE, OFD_TYPE, DOC_TYPE, DOCX_TYPE,
                XLS_TYPE, XLSX_TYPE, PPT_TYPE, PPTX_TYPE,
                TXT_TYPE, HTML_TYPE -> {
                    mediaType = MediaFileCompat.MEDIA_TYPE_DOC
                }
                APPLICATION_TYPE -> {
                    mediaType = MediaFileCompat.MEDIA_TYPE_APK
                }
                COMPRESSED_TYPE -> {
                    mediaType = MediaFileCompat.MEDIA_TYPE_COMPRESS
                }
                else -> {
                    //do nothing
                }
            }
            return mediaType
        }

        @JvmStatic
        fun getMimeTypeByFileType(type: Int): String {
            return when (type) {
                IMAGE_TYPE -> MimeType.MIMETYPE_IMAGE
                VIDEO_TYPE -> MimeType.MIMETYPE_VIDEO
                AUDIO_TYPE -> MimeType.MIMETYPE_AUDIO
                PDF_TYPE -> MimeType.MIMETYPE_PDF
                OFD_TYPE -> MimeType.MIMETYPE_OFD
                DOC_TYPE, DOCX_TYPE -> MimeType.MIMETYPE_DOC
                XLS_TYPE, XLSX_TYPE -> MimeType.MIMETYPE_XLS
                PPT_TYPE, PPTX_TYPE -> MimeType.MIMETYPE_PPT
                TXT_TYPE, CHM_TYPE, EBK_TYPE -> MimeType.MIMETYPE_TXT
                LRC_TYPE, EPUB_TYPE -> MimeType.MIMETYPE_LRC
                APPLICATION_TYPE -> MimeType.MIMETYPE_APPLICATION
                HTML_TYPE -> MimeType.MIMETYPE_HTML
                VCF_TYPE -> MimeType.MIMETYPE_VCF
                P12_TYPE -> MimeType.MIMETYPE_P12
                CER_TYPE -> MimeType.MIMETYPE_CER
                KEYNOTE_TYPE -> MimeType.MIMETYPE_KEYNOTE
                PAGES_TYPE -> MimeType.MIMETYPE_PAGES
                NUMBERS_TYPE -> MimeType.MIMETYPE_NUMBERS
                MARKDOWN_TYPE -> MimeType.MIMETYPE_MARKDOWN
                DWG_TYPE -> MimeType.MIMETYPE_DWG
                DXF_TYPE -> MimeType.MIMETYPE_DXF
                DWT_TYPE -> MimeType.MIMETYPE_DWT
                XMIND_TYPE -> MimeType.MIMETYPE_XMIND
                PSD_TYPE -> MimeType.MIMETYPE_PSD
                AI_TYPE -> MimeType.MIMETYPE_AI
                VSDX_TYPE, VSDM_TYPE, VSTX_TYPE, VSTM_TYPE, VSSX_TYPE, VSSM_TYPE, VSD_TYPE, VSS_TYPE, VST_TYPE, VDW_TYPE -> MimeType.MIMETYPE_VISIO
                else -> MimeType.MIMETYPE_UNKNOWN
            }
        }

        @JvmStatic
        fun getMimeTypeForDefaultApp(type: Int): String {
            return when (type) {
                IMAGE_TYPE -> MimeType.MIMETYPE_IMAGE
                VIDEO_TYPE -> MimeType.MIMETYPE_VIDEO
                AUDIO_TYPE -> MimeType.MIMETYPE_AUDIO
                PDF_TYPE -> MimeType.MIMETYPE_PDF
                OFD_TYPE -> MimeType.MIMETYPE_OFD
                DOC_TYPE, DOCX_TYPE -> MimeType.MIMETYPE_DOC
                XLS_TYPE, XLSX_TYPE -> MimeType.MIMETYPE_XLS
                PPT_TYPE, PPTX_TYPE -> MimeType.MIMETYPE_PPT
                TXT_TYPE, CHM_TYPE, EBK_TYPE -> MimeType.MIMETYPE_TXT
                LRC_TYPE, EPUB_TYPE -> MimeType.MIMETYPE_LRC
                APPLICATION_TYPE -> MimeType.MIMETYPE_APPLICATION
                HTML_TYPE -> MimeType.MIMETYPE_HTML
                VCF_TYPE -> MimeType.MIMETYPE_VCF
                P12_TYPE -> MimeType.MIMETYPE_P12
                CER_TYPE -> MimeType.MIMETYPE_CER
                KEYNOTE_TYPE -> MimeType.MIMETYPE_KEYNOTE
                PAGES_TYPE -> MimeType.MIMETYPE_PAGES
                NUMBERS_TYPE -> MimeType.MIMETYPE_NUMBERS
                MARKDOWN_TYPE -> MimeType.MIMETYPE_MARKDOWN
                DWG_TYPE, DXF_TYPE, DWT_TYPE -> MimeType.MIMETYPE_DWG_DEFAULT_APP
                XMIND_TYPE -> MimeType.MIMETYPE_XMIND
                PSD_TYPE -> MimeType.MIMETYPE_PSD
                AI_TYPE -> MimeType.MIMETYPE_AI
                VSDX_TYPE, VSDM_TYPE, VSTX_TYPE, VSTM_TYPE, VSSX_TYPE, VSSM_TYPE, VSD_TYPE, VSS_TYPE, VST_TYPE, VDW_TYPE
                -> MimeType.MIMETYPE_VISIO_DEFAULT_APP
                else -> MimeType.MIMETYPE_UNKNOWN
            }
        }

        @JvmStatic
        fun getCompressedTypeByPath(path: String?): Int {
            if (null == path) {
                return UNKNOWN_TYPE
            }
            val ext = FileTypeUtils.getExtension(path)
            var type = UNKNOWN_TYPE
            if (!ext.isNullOrEmpty()) {
                type = compressedExtTypeMap[ext.lowercase(Locale.US)] ?: UNKNOWN_TYPE
            }
            return type
        }

        @JvmStatic
        fun getIconByType(type: Int): Drawable? {
            val context = appContext
            return when (type) {
                KEYNOTE_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_keynote)
                PAGES_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_pages)
                NUMBERS_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_numbers)
                MARKDOWN_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_markdown)
                DWG_TYPE, DWT_TYPE, DXF_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_dwg)
                XMIND_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_xmind)
                PSD_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_photoshop)
                AI_TYPE -> ContextCompat.getDrawable(context, R.drawable.ic_file_ai)
                VSDX_TYPE, VSDM_TYPE, VSTX_TYPE, VSTM_TYPE, VSSX_TYPE, VSSM_TYPE, VSD_TYPE, VSS_TYPE, VST_TYPE, VDW_TYPE -> {
                    ContextCompat.getDrawable(context, R.drawable.ic_file_visio)
                }
                else -> {
                    KtThumbnailHelper.getClassifyDrawable(context, type)
                }
            }
        }
    }
}