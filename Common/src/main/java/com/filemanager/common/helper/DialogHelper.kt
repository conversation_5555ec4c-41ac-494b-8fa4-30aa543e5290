/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: BaseFileNameDialog.kt
 ** Description: use for COUIAlertDialogHelper, some dialog display in center when large and middle screen, other else in bottom
 ** Version: 1.0
 ** Date: 2022/5/11
 ** Author: Yan<PERSON>hen<PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.helper

import android.content.Context
import android.view.Gravity
import android.view.View


const val MIDDLE_AND_LARGE_SCREEN_SW_VALUE = 480

fun isMiddleAndLargeScreen(context: Context) =
    context.resources.configuration.smallestScreenWidthDp >= MIDDLE_AND_LARGE_SCREEN_SW_VALUE

/**
 * 底部确认弹框，输入弹框，当屏幕宽度大于480时，需要更改显示位置在中间
 */
fun getBottomAlertDialogWindowGravity(context: Context, anchorView: View? = null): Int {
    return if (isMiddleAndLargeScreen(context)) {
        if (anchorView != null) (Gravity.LEFT or Gravity.TOP) else Gravity.CENTER
    } else {
        Gravity.BOTTOM
    }
}

/**
 * 弹框根据是否是输入弹框，是否屏幕宽度大于480，显示不同的入场动画
 */
fun getBottomAlertDialogWindowAnimStyle(
    context: Context,
    hasEditText: Boolean = false,
    anchorView: View? = null
): Int {
    if (hasEditText && anchorView == null) {
        return com.support.dialog.R.style.Animation_COUI_Dialog_AutoShowKeyboard
    }
    return if (!isMiddleAndLargeScreen(context)) {
        com.support.dialog.R.style.Animation_COUI_Dialog
    } else if (anchorView != null) {
        com.support.poplist.R.style.Animation_COUI_PopupListWindow
    } else {
        com.support.dialog.R.style.Animation_COUI_Dialog_Alpha
    }
}