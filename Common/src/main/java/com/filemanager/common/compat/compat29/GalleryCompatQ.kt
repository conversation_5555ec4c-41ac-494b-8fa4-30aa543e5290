/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - GalleryCompatQ.java
 ** Description: Compat class for Gallery on android Q
 ** Version: 1.0
 ** Date : 2020/05/08
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/08    1.0     create
 ****************************************************************/

package com.filemanager.common.compat.compat29

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.ContentValues
import android.database.Cursor
import android.database.MergeCursor
import android.os.Build
import android.os.Bundle
import android.os.CancellationSignal
import android.provider.MediaStore.Files.FileColumns
import android.provider.MediaStore.Images
import androidx.annotation.RequiresApi
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.compat.MediaStoreCompat.AlbumComparable
import com.filemanager.common.compat.MediaStoreCompat.DEFAULT_CAPACITY
import com.filemanager.common.compat.MediaStoreCompat.MEDIA_STORE_CAMERA_RELATIVE_PATH
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_RELATIVE_PATH
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.compat.compat30.GalleryCompatR.getSortOrder
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.FolderNote
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WhiteList
import com.filemanager.common.wrapper.AlbumLoadResult
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.WebAlbumLoadResult
import java.io.File
import java.util.regex.Pattern

internal object GalleryCompatQ {
    private const val TAG = "GalleryCompatQ"
    private const val PARAM_COLLATE_NOCASE = " COLLATE NOCASE "
    private const val ALBUMSET_SQL_SELECTION_ANDROID_Q = "_id IN (SELECT _id FROM images WHERE " +
            "_id IN  (SELECT _id FROM images ORDER BY date_modified DESC) " +
            "AND (_size > 0) AND (_display_name not like '%.psd') " +
            "GROUP BY relative_path)"
    private const val SQL_QUERY_CAMERA_CSHOT = "((cshot_id=0) AND relative_path" + PARAM_COLLATE_NOCASE + "='DCIM/Camera/') " +
            "OR _id IN (" +
            "SELECT _id FROM images WHERE (cshot_id>0) AND (relative_path" + PARAM_COLLATE_NOCASE + "LIKE 'DCIM/Camera/%') GROUP BY bucket_id" +
            ")"
    /**
     * const, define in MediaStore.java of Android Q
     */
    private const val PARAM_LIMIT = "limit"

    private val ALBUMSET_PROJECTION = arrayOf(
            Images.Media.BUCKET_ID,
            Images.Media.DATA,
            Images.Media.RELATIVE_PATH,
            Images.Media.BUCKET_DISPLAY_NAME,
            Images.Media.ORIENTATION,
            Images.Media.DATE_MODIFIED,
            Images.Media.DATE_TAKEN
    )

    private const val ALBUMSET_COLUMN_INDEX_BUCKET_ID = 0
    private const val ALBUMSET_COLUMN_INDEX_DATA = 1
    private const val ALBUMSET_COLUMN_INDEX_RELATIVE_PATH = 2
    private const val ALBUMSET_COLUMN_INDEX_BUCKET_DISPLAY_NAME = 3
    private const val ALBUMSET_COLUMN_INDEX_ORIENTATION = 4
    private const val ALBUMSET_COLUMN_INDEX_DATE_MODIFIED = 5
    private const val ALBUMSET_COLUMN_INDEX_DATE_TAKEN = 6

    /**
     * count limit to avoid OOM of cursor
     */
    private const val ALBUMSET_COUTN_LIMIT = 60000

    private val IMAGE_PROJECTION = arrayOf(
            Images.Media._ID,
            Images.Media.DATA,
            Images.Media.DATE_TAKEN,
            "cshot_id",
            Images.Media.ORIENTATION,
            Images.Media.SIZE,
            Images.Media.DATE_MODIFIED,
            Images.Media.DISPLAY_NAME
    )

    private const val IMAGE_COLUMN_INDEX_ID = 0
    private const val IMAGE_COLUMN_INDEX_DATA = 1
    private const val IMAGE_COLUMN_INDEX_DATA_TAKEN = 2
    private const val IMAGE_COLUMN_INDEX_CSHOT_ID = 3
    private const val IMAGE_COLUMN_INDEX_ORIENTATION = 4
    private const val IMAGE_COLUMN_INDEX_SIZE = 5
    private const val IMAGE_COLUMN_INDEX_DATE_MODIFIED = 6
    private const val IMAGE_COLUMN_INDEX_DISPLAY_NAME = 7

    /**
     * count limit to avoid OOM of cursor
     */
    private const val ALBUM_COUNT_LIMIT = 50000

    private val PATTERN_MYALBUM_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        //Pattern.compile("(?i)(DCIM/MyAlbums/)([^/]+/)(Cshot/[1-9][0-9]*/)?")
        val builder = StringBuilder(DEFAULT_CAPACITY)
        builder.append("(?i)(")
        builder.append(MediaStoreCompat.MYALBUM_PATH)
        builder.append(")([^/]+/)(Cshot/[1-9][0-9]*/)?")
        Pattern.compile(builder.toString())
    }

    private val PATTERN_CAMERA_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Pattern.compile("(?i)(DCIM/Camera/)(Cshot/[1-9][0-9]*/)?")
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun getAlbumSet(): ArrayList<AlbumItem> {
        Log.d(TAG, "getAlbumSetCompatQ start")
        val albumSet: ArrayList<AlbumItem> = ArrayList<AlbumItem>()
        try {
            val queryArgs = Bundle()
            val order = "COALESCE(${FileColumns.DATE_TAKEN}, ${FileColumns.DATE_MODIFIED} * 1000) DESC, ${FileColumns.DATE_MODIFIED} DESC"
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, ALBUMSET_SQL_SELECTION_ANDROID_Q)
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, order)
            batchQuery(ALBUMSET_PROJECTION, queryArgs, null, ALBUMSET_COUTN_LIMIT).use { cursor ->
                if (cursor == null) {
                    return albumSet
                }
                var bucketId: String? = null
                var coverPath: String? = null
                var relativePath: String? = null
                var noteName: String? = null
                val note = FolderNote.getInstance()
                val whiteListMap = WhiteList.getInstance().whiteListMap
                val whiteAlbumSet = LinkedHashMap<Int, AlbumComparable>()
                val oppoAlbumSet = LinkedHashMap<Int, AlbumItem>()
                val leftAlbumSet = LinkedHashMap<Int, AlbumItem>()
                var albumItem: AlbumItem? = null
                var isMyAlbum = false
                var relativeBucketId = -1
                var orientation = 0
                var dateModified = 0L
                while (cursor.moveToNext()) {
                    relativePath = cursor.getString(ALBUMSET_COLUMN_INDEX_RELATIVE_PATH)
                    if (relativePath.isNullOrEmpty()) {
                        Log.d(TAG, "getAlbumSetCompatQ  empty relative path")
                        continue
                    }

                    bucketId = cursor.getString(ALBUMSET_COLUMN_INDEX_BUCKET_ID)
                    coverPath = cursor.getString(ALBUMSET_COLUMN_INDEX_DATA)
                    orientation = cursor.getInt(ALBUMSET_COLUMN_INDEX_ORIENTATION)
                    dateModified = cursor.getLong(ALBUMSET_COLUMN_INDEX_DATE_MODIFIED)
                    if (dateModified == 0L) {
                        Log.d(TAG, "dateModified is 0")
                        dateModified = (FileTimeUtil.getFileTime(coverPath)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                    }
                    val dateTaken = cursor.getString(ALBUMSET_COLUMN_INDEX_DATE_TAKEN)
                    Log.i(TAG, "dateModified：$dateModified dateTaken：$dateTaken relativePath=$relativePath")

                    isMyAlbum = false
                    // covert the cshot directory to album directory
                    val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
                    if (matcher.matches()) {
                        relativePath = matcher.group(1) + matcher.group(2)
                        isMyAlbum = true
                    } else {
                        val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
                        if (matcher1.matches()) {
                            relativePath = MEDIA_STORE_CAMERA_RELATIVE_PATH
                        }
                    }

                    // get notename for album
                    noteName = note.getNoteName(relativePath)
                    if (noteName.isNullOrEmpty()) {
                        if (File.separator.equals(relativePath)) {
                            noteName = MyApplication.sAppContext.resources.getString(R.string.root_album_name)
                        } else if (isMyAlbum) {
                            val parentName = matcher.group(2)
                            if (parentName.endsWith(File.separator)) {
                                noteName = parentName.substring(0, parentName.length - 1)
                            }
                        } else {
                            noteName = cursor.getString(ALBUMSET_COLUMN_INDEX_BUCKET_DISPLAY_NAME)
                            if (noteName.isNullOrEmpty()) {
                                val file = File(coverPath)
                                noteName = file.parentFile.name
                            }
                        }
                    }
                    relativeBucketId = relativePath.lowercase().hashCode()
                    // check white list first to make sure all album in control of white list
                    val entry = whiteListMap[relativeBucketId]
                    if (entry != null) {
                        if (!whiteAlbumSet.containsKey(relativeBucketId)) {
                            albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                            Log.d(TAG, "getAlbumSetCompatQ order: ${entry.order}")
                            whiteAlbumSet[relativeBucketId] = AlbumComparable(albumItem, entry.order)
                        }
                    } else if (isMyAlbum) {
                        if (!oppoAlbumSet.containsKey(relativeBucketId)) {
                            albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                            oppoAlbumSet[relativeBucketId] = albumItem
                        }
                    } else if (!leftAlbumSet.containsKey(relativeBucketId)) {
                        albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                        leftAlbumSet[relativeBucketId] = albumItem
                    }
                }

                // sort
                val whiteAlbumSort = ArrayList<AlbumComparable>(whiteAlbumSet.values)
                whiteAlbumSort.sort()
                for (whiteAlbum in whiteAlbumSort) {
                    albumSet.add(whiteAlbum.mAlbum)
                }

                albumSet.addAll(oppoAlbumSet.values)
                albumSet.addAll(leftAlbumSet.values)
                Log.d(TAG, "getAlbumSetCompatQ end")
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getAlbumSetCompatQ error: $ex")
        }
        return albumSet
    }

    fun getLocalAlbum(value: ContentValues): AlbumLoadResult<Int> {
        val imageList = ArrayList<ImageFileWrapper>()
        val imageHashMap = HashMap<Int, ImageFileWrapper>()
        val albumLoadResult = AlbumLoadResult(imageList, imageHashMap)

        val relativePath = value.getAsString(KEY_IMAGE_RELATIVE_PATH)
        val coverPath = value.getAsString(KtConstants.KEY_IMAGE_COVER_PATH)
        if (relativePath.isNullOrEmpty()) {
            return albumLoadResult
        }

        Log.d(TAG, "getLocalAlbum relativePath $relativePath coverPath $coverPath")

        val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (matcher.matches()) {
            // relative_path point to DCIM/MyAlbum for oppo gallery
            val myAlbumrelativePath = matcher.group(1) + matcher.group(2)
            builder.append(getMyAlbumSqlQuery(myAlbumrelativePath))
        } else {
            val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
            if (matcher1.matches()) {
                builder.append(SQL_QUERY_CAMERA_CSHOT)
            } else {
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = '")
                builder.append(relativePath)
                builder.append("'")
            }
        }
        val sortOrder = getSortOrder()
        try {
            val queryArgs = Bundle()
            Log.d(TAG, "getLocalAlbumCompatQ query: $builder")
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, sortOrder)
            batchQuery(IMAGE_PROJECTION, queryArgs, null, ALBUM_COUNT_LIMIT).use { cursor ->
                if (cursor == null) {
                    return albumLoadResult
                }
                var imageItem: ImageFileWrapper? = null
                var path: String? = null
                var datetaken: Long = -1L
                var id: Int = -1
                var cshotId = 0L
                var orientation = 0
                var size = 0L
                var dateModified = 0L
                var displayName: String
                while (cursor.moveToNext()) {
                    id = cursor.getInt(IMAGE_COLUMN_INDEX_ID)
                    path = cursor.getString(IMAGE_COLUMN_INDEX_DATA)
                    datetaken = cursor.getLong(IMAGE_COLUMN_INDEX_DATA_TAKEN)
                    cshotId = cursor.getLong(IMAGE_COLUMN_INDEX_CSHOT_ID)
                    orientation = cursor.getInt(IMAGE_COLUMN_INDEX_ORIENTATION)
                    size = cursor.getLong(IMAGE_COLUMN_INDEX_SIZE)
                    dateModified = cursor.getLong(IMAGE_COLUMN_INDEX_DATE_MODIFIED)
                    displayName = cursor.getString(IMAGE_COLUMN_INDEX_DISPLAY_NAME)
                    if (dateModified == 0L) {
                        Log.d(TAG, "dateModified is 0")
                        dateModified = (FileTimeUtil.getFileTime(path)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                    }
                    imageItem = ImageFileWrapper(datetaken, path, cshotId, orientation, id, size, dateModified, displayName)
                    imageList.add(imageItem)
                    imageHashMap[id] = imageItem
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getLocalAlbumCompatQ error: $ex")
        }
        return albumLoadResult
    }

    @SuppressLint("Range")
    fun getAllLocalAlbum(relationPath: String, pageNo: Int, pageSize: Int, limitPages: Int): WebAlbumLoadResult {
        val imageList = ArrayList<ImageFileWrapper>()
        var total = -1
        if ((pageSize < 1) || (pageNo < 1)) {
            return WebAlbumLoadResult(imageList, pageNo, total)
        }
        var temPageNo = pageNo
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            builder.append(FileColumns.RELATIVE_PATH)
            builder.append(PARAM_COLLATE_NOCASE)
            builder.append(" = '")
            builder.append(relationPath)
            builder.append("'")
        }
        try {
            val queryArgs = Bundle()
            Log.d(TAG, "getAllLocalAlbumCompatQ query: $builder")
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putString(
                ContentResolver.QUERY_ARG_SQL_SORT_ORDER,
                Images.ImageColumns.DATE_MODIFIED + " DESC"
            )
            batchQuery(IMAGE_PROJECTION, queryArgs, null, ALBUM_COUNT_LIMIT).use { cursor ->
                if (cursor == null) {
                    return WebAlbumLoadResult(imageList, temPageNo, total)
                }
                total = cursor.count
                val maxPageNo = if ((total % pageSize) > 0) {
                    total / pageSize + 1
                } else {
                    total / pageSize
                }
                if (temPageNo > maxPageNo) {
                    temPageNo = maxPageNo
                }
                cursor.moveToPosition((temPageNo - 1) * pageSize)
                addImageItems(cursor, imageList)
                while (cursor.moveToNext() && (imageList.size < pageSize * limitPages)) {
                    addImageItems(cursor, imageList)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getAllLocalAlbumCompatQ error: $ex")
        }
        Log.d(TAG, "getAllLocalAlbumCompatQ query total: $total")
        return WebAlbumLoadResult(imageList, temPageNo, total)
    }

    private fun addImageItems(cursor: Cursor, imageList: ArrayList<ImageFileWrapper>) {
        val imageItem = ImageFileWrapper(
            cursor.getLong(IMAGE_COLUMN_INDEX_DATA_TAKEN),
            cursor.getString(IMAGE_COLUMN_INDEX_DATA),
            cursor.getLong(IMAGE_COLUMN_INDEX_CSHOT_ID),
            cursor.getInt(IMAGE_COLUMN_INDEX_ORIENTATION),
            cursor.getInt(IMAGE_COLUMN_INDEX_ID),
            cursor.getLong(IMAGE_COLUMN_INDEX_SIZE),
            cursor.getLong(IMAGE_COLUMN_INDEX_DATE_MODIFIED),
            cursor.getString(IMAGE_COLUMN_INDEX_DISPLAY_NAME)
        )
        imageList.add(imageItem)
    }

    @SuppressLint("Range")
    fun getImageItems(pageNumber: Int, pageSize: Int, sortOrder: String?): WebAlbumLoadResult {
        val imageList = ArrayList<ImageFileWrapper>()
        var total = -1
        if ((pageSize < 1) || (pageNumber < 1)) {
            return WebAlbumLoadResult(imageList, pageNumber, total)
        }
        var temPageNo = pageNumber
        val builder = StringBuilder(DEFAULT_CAPACITY)
        try {
            val queryArgs = Bundle()
            if (sortOrder != null) {
                queryArgs.putInt(ContentResolver.QUERY_ARG_OFFSET, pageNumber * pageSize)
            }
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SORT_ORDER, sortOrder)
            batchQuery(IMAGE_PROJECTION, queryArgs, null, ALBUM_COUNT_LIMIT)?.use { cursor ->
                total = cursor.count
                val maxPageNo = if ((total % pageSize) > 0) {
                    total / pageSize + 1
                } else {
                    total / pageSize
                }
                if (temPageNo > maxPageNo) {
                    temPageNo = maxPageNo
                }
                cursor.moveToPosition((temPageNo - 1) * pageSize)
                addImageItems(cursor, imageList)
                while (cursor.moveToNext() && (imageList.size < pageSize)) {
                    addImageItems(cursor, imageList)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getImageFiles -> error: $e")
        }
        return WebAlbumLoadResult(imageList, temPageNo, total)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun getAlbumItemCount(relativePath: String?): Int {
        if (relativePath.isNullOrEmpty()) {
            Log.d(TAG, "getAlbumItemCount relativePath is empty")
            return 0
        }

        // covert the cshot directory to album directory
        val builder = StringBuilder(DEFAULT_CAPACITY)
        var matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
        if (matcher.matches()) {
            // relative_path point to DCIM/MyAlbum for oppo gallery
            val myAlbumrelativePath = matcher.group(1) + matcher.group(2)
            builder.append(getMyAlbumSqlQuery(myAlbumrelativePath))
            Log.d(TAG, "getAlbumItemCount relativePath1: $relativePath")
        } else {
            var matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
            if (matcher1.matches()) {
                // relative_path point to DCIM/Camera
                builder.append(SQL_QUERY_CAMERA_CSHOT)
            } else {
                Log.d(TAG, "getAlbumItemCount relativePath3: $relativePath")
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = '")
                builder.append(relativePath)
                builder.append("'")
            }
        }

        Log.d(TAG, "getAlbumItemCount selection: ${builder.toString()}")
        try {
            val queryArgs = Bundle()
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            val context = MyApplication.sAppContext
            context.contentResolver.query(Images.Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns._ID)
                    , queryArgs, null).use { cursor ->
                cursor?.let {
                    return it.count
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getAlbumItemCount query: ${builder.toString()}")
        }
        return 0
    }

    private fun getMyAlbumSqlQuery(relativePath: String): String {
        val builder = StringBuilder(DEFAULT_CAPACITY)
        builder.append("(")
        builder.append(FileColumns.RELATIVE_PATH)
        builder.append(PARAM_COLLATE_NOCASE)
        builder.append(" = '")
        builder.append(relativePath)
        builder.append("'")
        builder.append(" AND (cshot_id=0) ")
        builder.append(")")

        // apppend the my album cshot
        builder.append(" OR _id IN (")
        builder.append("SELECT _id FROM images WHERE (cshot_id>0) AND (relative_path")
        builder.append(PARAM_COLLATE_NOCASE)
        builder.append(" LIKE '")
        builder.append(relativePath)
        builder.append("%')")
        builder.append(" GROUP BY ")
        builder.append(FileColumns.BUCKET_ID)
        builder.append(")")
        return builder.toString()
    }

    /**
     * Batch query according to limitNum
     *
     * @param projection A list of which columns to return. Passing null will
     *         return all columns, which is inefficient.
     * @param queryArgs A Bundle containing any arguments to the query.
     * @param cancellationSignal A signal to cancel the operation in progress, or null if none.
     * If the operation is canceled, then {@link OperationCanceledException} will be thrown
     * when the query is executed.
     * @param limitNum A number of which maximum rows per query
     * @return A Cursor object or MergeCursor object, which is positioned before the first entry. May return
     *         <code>null</code> if the underlying content provider returns <code>null</code>,
     *         or if it crashes.
     */
    @RequiresApi(Build.VERSION_CODES.O)
    @SuppressLint("Recycle")
    fun batchQuery(projection: Array<String>?, queryArgs: Bundle, cancellationSignal: CancellationSignal?, limitNum: Int): Cursor? {
        var offset = 0
        var batchQueryHasError = false
        var needNextBatch = true
        val cursors = ArrayList<Cursor>()
        val context = MyApplication.sAppContext

        fun clearAllCursor() {
            if (cursors.isNotEmpty()) {
                cursors.forEach {
                    it.close()
                }
                cursors.clear()
            }
        }

        try {
            while (needNextBatch && batchQueryHasError.not()) {
                val uri = Images.Media.EXTERNAL_CONTENT_URI.buildUpon()
                        .appendQueryParameter(PARAM_LIMIT, "$limitNum OFFSET $offset").build()

                context.contentResolver.query(uri, projection, queryArgs, cancellationSignal)?.let { cursor ->
                    val count = cursor.count
                    cursors.add(cursor)
                    when {
                        //If count is equal to limit, then the next batch of queries is required
                        (count == limitNum) -> {
                            offset += limitNum
                        }
                        //If count is greater than limitNum, batchQuery has error
                        (count > limitNum) -> {
                            batchQueryHasError = true
                        }
                        //If count is less than limitNum, batchQuery finish
                        else -> {
                            needNextBatch = false
                        }
                    }
                } ?: kotlin.run {
                    batchQueryHasError = true
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "batchQuery has error:${e.message}")
            batchQueryHasError = true
        }

        if (batchQueryHasError) {
            clearAllCursor()
        }

        return when {
            cursors.size > 1 -> {
                MergeCursor(cursors.toTypedArray())
            }
            cursors.size == 1 -> {
                cursors[0]
            }
            batchQueryHasError -> {
                context.contentResolver.query(Images.Media.EXTERNAL_CONTENT_URI, projection, queryArgs, cancellationSignal)
            }
            else -> {
                clearAllCursor()
                null
            }
        }
    }
}