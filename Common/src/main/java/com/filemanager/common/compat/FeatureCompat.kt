/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/17,        v1.0,           Create
 ****************************************************************/
package com.filemanager.common.compat

import android.os.Build
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.compat29.FeatureCompatQ
import com.filemanager.common.compat.compat30.FeatureCompatR
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.PrivateSafeUtil
import com.filemanager.common.utils.SdkUtils

object FeatureCompat {
    private const val TAG = "FeatureCompat"
    private const val OS_13_2 = 29
    private const val OS_14 = 34
    private const val FEATURE_SE_CHIP = "com.oplus.hardware.secure_element.ese2"
    private const val FEATURE_ONE_PLUS_COMPANY_THEME_SUPPORT = "oplus.companyname.not.support"
    internal const val FEATURE_FLIP_DEVICE = "com.android.settings.flip_device"
    private const val FEATURE_NFC_CHIP = "com.oplus.hardware.secure_element.nfc_ese"

    private val sOPlusFeatureInterface by lazy {
        if (SdkUtils.isAtLeastR()) {
            FeatureCompatR
        } else {
            FeatureCompatQ
        }
    }

    @JvmStatic
    val sIsExpRom: Boolean by lazy {
        sOPlusFeatureInterface.isExpRom()
    }

    @JvmStatic
    val sIsLightVersion: Boolean by lazy {
        val isLight = ModelUtils.isLight(MyApplication.sAppContext)
        val lightOSByProperties = PropertyCompat.sIsLightOS
        Log.d(TAG, "isLightVersion = $isLight; lightOSByProperties = $lightOSByProperties")
        sOPlusFeatureInterface.isLightVersion() || isLight || lightOSByProperties
    }

    @JvmStatic
    val sIsNormalLightOS: Boolean by lazy {
        sIsLightVersion && sIsHighLevelLightOS.not()
    }

    //新轻量
    @JvmStatic
    val sIsHighLevelLightOS: Boolean by lazy {
        sOPlusFeatureInterface.isHighLevelLightOS()
    }

    @JvmStatic
    val sIsNotSupportSD: Boolean by lazy {
        sOPlusFeatureInterface.isNotSupportSD()
    }

    @JvmStatic
    val sIsSupportDrm: Boolean by lazy {
        sOPlusFeatureInterface.isSupportDrm()
    }

    @JvmStatic
    val sIsSupportEncryption: Boolean by lazy {
        sOPlusFeatureInterface.isSupportEncryption() && !PrivateSafeUtil.checkIsDisabledPrivateGarden()
    }

    /**
     * os 13.2以下的手机都认为是小屏手机
     * 判断是否是小屏手机（除了折叠屏，平板外的手机）
     */
    @JvmStatic
    val isSmallScreenPhone: Boolean by lazy {
        val context = MyApplication.sAppContext
        val isTablet = ModelUtils.isTablet()
        val isFold = UIConfigMonitor.instance.isFoldable(context)
        val osVersion = SdkUtils.getOSVersion()
        Log.e(TAG, "isSmallScreenPhone osVersion:$osVersion isTablet:$isTablet isFold:$isFold")
        isTablet.not() && isFold.not()
    }


    /**
     * 判断是否是小屏手机（包括小折叠屏展开）（除了大折叠屏，平板外的手机）
     */
    @JvmStatic
    fun isSmallScreenPhoneNew(): Boolean {
        val context = MyApplication.sAppContext
        val isTablet = ModelUtils.isTablet()
        if (isTablet) {
            return false
        }
        val foldState = UIConfigMonitor.instance.getFoldState(context)
        val smallScreen = UIConfigMonitor.isCurrentSmallScreen()
        Log.d(
            TAG,
            "isSmallScreenPhone isTablet:$isTablet foldState: $foldState smallScreen:$smallScreen"
        )
        if (foldState == UIConfigMonitor.FOLD_OPEN) {
            //折叠屏展开后 依然是小屏 按小屏直板机处理
            return smallScreen
        } else if (foldState == UIConfigMonitor.FOLD_CLOSE) {
            return false
        }
        return true
    }

    @JvmStatic
    val isOsVersionGreater29: Boolean by lazy {
        val osVersion = SdkUtils.getOSVersion()
        osVersion >= OS_13_2
    }

    @JvmStatic
    val isOsVersionLargeThan29: Boolean by lazy {
        val osVersion = SdkUtils.getOSVersion()
        osVersion > OS_13_2
    }

    /**
     * SDK_VERSION及SDK_SUB_VERSION都小于29的手机判断不适用跟手面板
     * 判断系统版本是否适用跟手面板
     */
    @JvmStatic
    val isApplicableForFlexibleWindow: Boolean by lazy {
        if (isLargerThanOS14()) {
            //跟手面板还没有上U，U上需要判断SDK版本，待U上支持之后，再移除拦截,另还有低于29的版本没有OplusBuild，文管此版本只发12及以上，不需要考虑
            false
        }
        val osVersion = SdkUtils.getOSVersion()
        if (osVersion < OplusBuildCompat.OS_13_0) {
            //版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
            false
        } else {
            val sdkVersion = SdkUtils.getOSSdkVersion()
            val subVersion = SdkUtils.getOSSdkSubVersion()
            Log.d(TAG, "isApplicableForFlexibleWindow SDK_VERSION:$sdkVersion SDK_SUB_VERSION:$subVersion")
            sdkVersion >= OS_13_2 && subVersion >= OS_13_2
        }
    }

    fun isLargerThanOS14(): Boolean = Build.VERSION.SDK_INT >= OS_14

    @JvmStatic
    val isHasOnePlusCompanyFeature: Boolean by lazy {
        MyApplication.sAppContext.packageManager.hasSystemFeature(FEATURE_ONE_PLUS_COMPANY_THEME_SUPPORT).also {
            Log.d(TAG, "isHasOnePlusCompanyFeature $it")
        }
    }

    @JvmStatic
    val isFlipDevice: Boolean by lazy {
        sOPlusFeatureInterface.isFlipDevice()
    }

    /**
     * 手机是否支持安全芯片
     */
    @JvmStatic
    val isSupportSecurityChip: Boolean by lazy {
        MyApplication.sAppContext.packageManager.hasSystemFeature(FEATURE_SE_CHIP)
    }

    /**
     * 手机是否支持NFC安全芯片
     */
    @JvmStatic
    val isSupportNFCSecurityChip: Boolean by lazy {
        MyApplication.sAppContext.packageManager.hasSystemFeature(FEATURE_NFC_CHIP)
    }

    /**
     * Get the pkg name and action of the PhoneManager.
     *
     * @return First: the package name of PhoneManager, Second: the action of PhoneManager.
     * If null, mean PhoneManager is not support
     */
    @JvmStatic
    val sPhoneManagerStartInfo: Pair<String, String>? by lazy {
        sOPlusFeatureInterface.getPhoneManagerStartInfo()
    }

    @JvmStatic
    val sIsStorageUnitNormal: Boolean by lazy {
        sOPlusFeatureInterface.isStorageUnitNormal()
    }

    @JvmStatic
    val sIsSupportRuntimePermissionAlert: Boolean by lazy {
        sOPlusFeatureInterface.isSupportRuntimePermissionAlert()
    }

    @JvmStatic
    val sIsOnlyUseBuildInMusicPlay: Boolean by lazy {
        sOPlusFeatureInterface.isOnlyUseBuildInMusicPlay()
    }

    @JvmStatic
    val sIsSupportOTG: Boolean by lazy {
        sOPlusFeatureInterface.isSupportOTG()
    }

    @JvmStatic
    val sIsNotSupportUnknownFile: Boolean by lazy {
        sOPlusFeatureInterface.isNotSupportUnknownFile()
    }

    @JvmStatic
    val sIsSupportEncryptOTAOnly: Boolean by lazy {
        sOPlusFeatureInterface.isSupportEncryptOTAOnly()
    }

    @JvmStatic
    val sIsSupportMultiApp: Boolean by lazy {
        sOPlusFeatureInterface.isSupportMultiApp()
    }

    @JvmStatic
    val sIsStorageHidden: Boolean by lazy {
        sOPlusFeatureInterface.isStorageHidden()
    }

    @JvmStatic
    val sIsSupportLinearmotorVibrator: Boolean by lazy {
        sOPlusFeatureInterface.isSupportLinearmotorVibrator()
    }

    @JvmStatic
    val sIsSupportLuxunVibrator: Boolean by lazy {
        sOPlusFeatureInterface.isSupportLuxunVibrator()
    }

    @JvmStatic
    val isSupportTaskbar: Boolean by lazy {
        sOPlusFeatureInterface.isSupportTaskbar()
    }

    @JvmStatic
    val isSupportPCConnect: Boolean by lazy {
        sOPlusFeatureInterface.isSupportPCConnect()
    }

    internal interface IOPlusFeatureInterface {
        fun isExpRom(): Boolean
        fun isLightVersion(): Boolean
        fun isNotSupportSD(): Boolean
        fun isSupportDrm(): Boolean
        fun isSupportEncryption(): Boolean
        fun getPhoneManagerStartInfo(): Pair<String, String>?
        fun isSupportRuntimePermissionAlert(): Boolean
        fun isOnlyUseBuildInMusicPlay(): Boolean
        fun isSupportOTG(): Boolean
        fun isNotSupportUnknownFile(): Boolean
        fun isSupportEncryptOTAOnly(): Boolean
        fun isSupportMultiApp(): Boolean
        /**
         * If it has this feature,show the size unit in 1024,else in 1000.
         * default false
         */
        fun isStorageUnitNormal(): Boolean
        /**
         * If it has this feature, show storage as 0B.
         */
        fun isStorageHidden(): Boolean
        fun isSupportLinearmotorVibrator(): Boolean
        fun isSupportLuxunVibrator(): Boolean

        fun isSupportTaskbar(): Boolean
        fun isFlipDevice(): Boolean

        /**
         * 是否支持跨屏互联
         */
        fun isSupportPCConnect(): Boolean
        /**
         是否是新轻量机型
         */
        fun isHighLevelLightOS(): Boolean
    }
}