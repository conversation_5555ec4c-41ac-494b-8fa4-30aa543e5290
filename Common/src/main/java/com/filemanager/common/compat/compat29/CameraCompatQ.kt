/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - CameraCompatQ.java
 ** Description: Camera Compat for android Q
 ** Version: 1.0
 ** Date : 2020/05/08
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/08    1.0     create
 ****************************************************************/
package com.filemanager.common.compat.compat29

import android.content.ContentResolver
import android.database.Cursor
import android.os.Bundle
import android.provider.MediaStore.Files.FileColumns
import android.provider.MediaStore.Images.Media
import com.filemanager.common.compat.MediaStoreCompat.DEFAULT_CAPACITY
import com.filemanager.common.compat.MediaStoreCompat.MYALBUM_PATH
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Log

internal object CameraCompatQ {
    private const val TAG = "CameraCompatQ"
    private const val COLUMN_INDEX_IMAGES_LIST_DATA = 0
    private const val COLUMN_INDEX_IMAGES_LIST_DATE_TAKEN = 1
    private const val COLUMN_INDEX_IMAGES_LIST_DATE_ORIENTATION = 2
    private const val COLUMN_INDEX_IMAGES_LIST_ID = 3
    private const val COLUMN_INDEX_IMAGES_LIST_CSHOT_ID = 4
    private const val COLUMN_INDEX_IMAGES_SIZE = 5
    private const val COLUMN_INDEX_IMAGES_DATE_MODIFIED = 6
    private const val COLUMN_INDEX_IMAGES_DISPLAY_NAME = 7
    private val SQL_QUERY_CSHOT by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        "id IN (SELECT _id FROM images WHERE (cshot_id>0) GROUP BY bucket_id)"
    }

    private val SQU_QUERY_CAMERA_CSHOT by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        "id IN (SELECT _id FROM images WHERE (cshot_id>0) AND (relative_path='DCIM/Camera/') GROUP BY bucket_id)"
    }
    private val CSHOT_IMAGE_PROJECTION by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        arrayOf(
                Media.DATA,
                Media.DATE_TAKEN,
                Media.ORIENTATION,
                Media._ID,
                "cshot_id",
                Media.SIZE,
                Media.DATE_MODIFIED,
                Media.DISPLAY_NAME)
    }

    /**
     * @return bucket id list of all cshot cover item in mediastore
     */
    fun getBucketIdsForCshotCompatQ(): ArrayList<Long> {
        val bucketIds = ArrayList<Long>()
        try {
            val queryArgs = Bundle()
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, SQL_QUERY_CSHOT)
            val context = MyApplication.sAppContext
            context.contentResolver.query(Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns.BUCKET_ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return bucketIds
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    bucketIds.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getBucketIdsForCshotCompatQ error: $ex")
        }
        return bucketIds
    }

    fun getIdsForCameraCshotCompatQ(): ArrayList<Long> {
        val bucketIds = ArrayList<Long>()
        try {
            val queryArgs = Bundle()
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, SQU_QUERY_CAMERA_CSHOT)
            val context = MyApplication.sAppContext
            context.contentResolver.query(Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns._ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return bucketIds
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    bucketIds.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getBucketIdsForCshotCompatQ error: $ex")
        }
        return bucketIds
    }

    fun getIdsForMyAlbumCshotCompatQ(key: String): ArrayList<Long> {
        val bucketIds = ArrayList<Long>()
        try {
            val queryArgs = Bundle()
            val builder = StringBuilder(DEFAULT_CAPACITY)
            builder.append("id IN (SELECT _id FROM images WHERE (cshot_id>0) AND (relative_path='")
            builder.append(MYALBUM_PATH)
            builder.append("') GROUP BY bucket_id")
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            val context = MyApplication.sAppContext
            context.contentResolver.query(Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns._ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return bucketIds
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    bucketIds.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getBucketIdsForCshotCompatQ error: $ex")
        }
        return bucketIds
    }

    /**
     * @return ImageFileBean list of all cShot items which under cShot_Cover list
     * attention: if the clause "?" more than 3000, this method may have exceptions
     */
    fun getCShotFiles(fileList: List<BaseFileBean>): ArrayList<ImageFileWrapper> {
        val resultList = ArrayList<ImageFileWrapper>()
        val whereArgs = java.util.ArrayList<String>()
        val whereClause = StringBuilder()
        for (file in fileList) {
            if (whereClause.isNotEmpty()) {
                whereClause.append(",")
            }
            whereClause.append("?")
            val csShot = if (file is ImageFileWrapper) {
                file.getCShot().toString()
            } else {
                ImageFileWrapper.EMPTY_CSHOT.toString()
            }
            whereArgs.add(csShot)
        }
        if (whereArgs.isEmpty()) {
            return resultList
        }
        val stringBuilder = StringBuilder()
        stringBuilder.append("cshot_id IN (")
                .append(whereClause.toString())
                .append(")")
        var cursor: Cursor? = null
        try {
            var selectArg: Array<String?>? = arrayOfNulls(whereArgs.size)
            selectArg = whereArgs.toArray(selectArg)
            val context = MyApplication.sAppContext
            cursor = context.contentResolver.query(Media.EXTERNAL_CONTENT_URI,
                    CSHOT_IMAGE_PROJECTION,
                    stringBuilder.toString(), selectArg, null)
            if (cursor != null) {
                Log.d(TAG, "getCShotFiles getCount: " + cursor.count)
                while (cursor.moveToNext()) {
                    try {
                        val path = cursor.getString(COLUMN_INDEX_IMAGES_LIST_DATA)
                        val dateTaken = cursor.getLong(COLUMN_INDEX_IMAGES_LIST_DATE_TAKEN)
                        val orientation = cursor.getInt(COLUMN_INDEX_IMAGES_LIST_DATE_ORIENTATION)
                        val id = cursor.getInt(COLUMN_INDEX_IMAGES_LIST_ID)
                        val cShot = cursor.getLong(COLUMN_INDEX_IMAGES_LIST_CSHOT_ID)
                        val size = cursor.getLong(COLUMN_INDEX_IMAGES_SIZE)
                        var dateModified = cursor.getLong(COLUMN_INDEX_IMAGES_DATE_MODIFIED)
                        val displayName = cursor.getString(COLUMN_INDEX_IMAGES_DISPLAY_NAME)
                        if (dateModified == 0L) {
                            Log.d(TAG, "dateModified is 0")
                            dateModified = (FileTimeUtil.getFileTime(path)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                        }
                        resultList.add(ImageFileWrapper(dateTaken, path, cShot, orientation, id, size, dateModified, displayName))
                    } catch (e: Exception) {
                        Log.e(TAG, "add cShot file error: " + e.message)
                    }
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "getCshotFiles error: " + ex.message)
        } finally {
            cursor?.close()
        }
        return resultList
    }
}