/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.filerefactor.ui.viewholder
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.noMoreAction
import com.filemanager.common.view.FileThumbView
import java.util.*

/**
 * category->document->grid mode
 **/
class FileDocGridVH(convertView: View) : BaseFileBrowserVH(convertView) {
    companion object {
        private const val TAG = "FileDocGridVH"

        //文档默认图像的高与宽的对比率，高:150,宽:100
        private const val DOC_WIDTH_HEIGHT_RATIO = 1.5f
        fun getLayoutId(): Int {
            return R.layout.file_doc_scan_grid_item
        }
    }

    @VisibleForTesting
    val mImg: FileThumbView = convertView.findViewById(R.id.file_grid_item_icon)
        get() = field.noMoreAction()
    private val mTitle: TextView = convertView.findViewById(R.id.title_tv)

    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_bg_radius)
    @VisibleForTesting
    val mImgDefaultWidth = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_size_width)
        get() = field.noMoreAction()

    init {
        mCheckBox = convertView.findViewById(R.id.gridview_scrollchoice_checkbox)
    }

    override fun updateViewHolder(context: Context, key: Int?, file: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>
                                  , sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>) {
        val path = file.mDisplayName
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        mTitle.text = path
        LabelVHUtils.displayLabelFlag(file, context, mTitle)
        mImg.setDrmState(file.mLocalType == MimeTypeHelper.DRM_TYPE)
        FileImageLoader.sInstance.clear(context, mImg)
        FileImageLoader.sInstance.displayDefault(
            file,
            mImg,
            radiusSize = mImgRadius,
            thumbnailType = FileImageLoader.THUMBNAIL_TYPE_RECT,
            loadDocThumbnail = true
        )
        mCheckBox?.let {
            if (choiceMode) {
                if (selectionArray.contains(key)) {
                    it.state = COUICheckBox.SELECT_ALL
                    it.visibility = View.VISIBLE
                } else {
                    it.isEnabled = false
                    it.state = COUICheckBox.SELECT_NONE
                    it.visibility = View.VISIBLE
                    it.isEnabled = true
                }
            } else {
                it.state = COUICheckBox.SELECT_NONE
                it.jumpDrawablesToCurrentState()
                it.visibility = View.GONE
            }
        }
        val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mImg.alpha = alpha
    }

    /**
     * 适配Item的大小。
     * Item有默认的宽高,100dp:150dp,当页面宽度不够显示时，宽度会变小。
     * 根据当前计算的的宽度[currentWidth]，更新高度，以适配显示
     * 若当前宽度比默认宽度大或相等，要重置为默认值
     */
    fun adaptItemSize(currentWidth: Int) {
        if (currentWidth == 0 || currentWidth > mImgDefaultWidth) return
        val currentHeight: Int = (DOC_WIDTH_HEIGHT_RATIO * currentWidth.toFloat()).toInt()
        mImg.apply {
            if (layoutParams == null) {
                layoutParams = constructLayoutParams(currentWidth, currentHeight)
            } else {
                layoutParams.width = currentWidth
                layoutParams.height = currentHeight
            }
        }
    }

    @VisibleForTesting
    fun constructLayoutParams(width: Int, height: Int) = ViewGroup.LayoutParams(width, height)

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        itemView.getGlobalVisibleRect(checkBoxRect)
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }
}