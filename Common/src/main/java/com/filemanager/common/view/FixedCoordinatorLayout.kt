package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.ViewConfiguration
import androidx.coordinatorlayout.widget.CoordinatorLayout
import kotlin.math.abs

/**
 * 修复 CoordinatorLayout 与 ViewPager 的触摸事件冲突问题
 * 解决向下滑动后左右滑动第一下无法触发的问题
 */
class FixedCoordinatorLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : CoordinatorLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "FixedCoordinatorLayout"
    }

    private var mInitialTouchX = 0f
    private var mInitialTouchY = 0f
    private var mTouchSlop = 0
    private var mIsBeingDragged = false
    private var mActivePointerId = -1
    private var mVelocityTracker: VelocityTracker? = null
    private var mLastMotionX = 0f
    private var mLastMotionY = 0f

    init {
        val configuration = ViewConfiguration.get(context)
        mTouchSlop = configuration.scaledTouchSlop
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        val action = ev.action and MotionEvent.ACTION_MASK

        // 如果已经在拖拽中，让父类处理
        if (action == MotionEvent.ACTION_CANCEL || action == MotionEvent.ACTION_UP) {
            mIsBeingDragged = false
            mActivePointerId = -1
            recycleVelocityTracker()
            return super.onInterceptTouchEvent(ev)
        }

        // 如果已经在拖拽中，继续拦截
        if (action != MotionEvent.ACTION_DOWN && mIsBeingDragged) {
            return super.onInterceptTouchEvent(ev)
        }

        when (action) {
            MotionEvent.ACTION_DOWN -> {
                mLastMotionX = ev.x
                mLastMotionY = ev.y
                mInitialTouchX = ev.x
                mInitialTouchY = ev.y
                mActivePointerId = ev.getPointerId(0)
                mIsBeingDragged = false

                initOrResetVelocityTracker()
                mVelocityTracker?.addMovement(ev)
            }

            MotionEvent.ACTION_MOVE -> {
                val activePointerIndex = ev.findPointerIndex(mActivePointerId)
                if (activePointerIndex == -1) {
                    return super.onInterceptTouchEvent(ev)
                }

                val x = ev.getX(activePointerIndex)
                val y = ev.getY(activePointerIndex)
                val xDiff = abs(x - mInitialTouchX)
                val yDiff = abs(y - mInitialTouchY)

                mVelocityTracker?.addMovement(ev)

                // 判断滑动方向
                if (xDiff > mTouchSlop || yDiff > mTouchSlop) {
                    if (xDiff > yDiff) {
                        // 水平滑动，不拦截，让 ViewPager 处理
                        mIsBeingDragged = false
                        return false
                    } else {
                        // 垂直滑动，让 CoordinatorLayout 处理
                        mIsBeingDragged = true
                        mLastMotionX = x
                        mLastMotionY = y
                    }
                }
            }

            MotionEvent.ACTION_POINTER_UP -> {
                onSecondaryPointerUp(ev)
            }
        }

        return super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        initOrResetVelocityTracker()
        mVelocityTracker?.addMovement(ev)

        val action = ev.action and MotionEvent.ACTION_MASK

        when (action) {
            MotionEvent.ACTION_DOWN -> {
                mLastMotionX = ev.x
                mLastMotionY = ev.y
                mActivePointerId = ev.getPointerId(0)
            }

            MotionEvent.ACTION_MOVE -> {
                val activePointerIndex = ev.findPointerIndex(mActivePointerId)
                if (activePointerIndex == -1) {
                    return super.onTouchEvent(ev)
                }

                mLastMotionX = ev.getX(activePointerIndex)
                mLastMotionY = ev.getY(activePointerIndex)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mIsBeingDragged = false
                mActivePointerId = -1
                recycleVelocityTracker()
            }

            MotionEvent.ACTION_POINTER_UP -> {
                onSecondaryPointerUp(ev)
            }
        }

        return super.onTouchEvent(ev)
    }

    private fun onSecondaryPointerUp(ev: MotionEvent) {
        val pointerIndex = ev.action and MotionEvent.ACTION_POINTER_INDEX_MASK shr MotionEvent.ACTION_POINTER_INDEX_SHIFT
        val pointerId = ev.getPointerId(pointerIndex)
        if (pointerId == mActivePointerId) {
            val newPointerIndex = if (pointerIndex == 0) 1 else 0
            mLastMotionX = ev.getX(newPointerIndex)
            mLastMotionY = ev.getY(newPointerIndex)
            mActivePointerId = ev.getPointerId(newPointerIndex)
            mVelocityTracker?.clear()
        }
    }

    private fun initOrResetVelocityTracker() {
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain()
        } else {
            mVelocityTracker?.clear()
        }
    }

    private fun recycleVelocityTracker() {
        mVelocityTracker?.recycle()
        mVelocityTracker = null
    }
}
