/**************************************************************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** OPLUS_EDIT
 ** File: - FullScreenFastScroller.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/04/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: --------------------------------------------------------
 **  <author>                   <data>         <version >     <desc>
 **  <EMAIL>    2021/04/30     1.0            Add file head
 **
 *************************************************************************************************/
package com.filemanager.common.view.fastscrolll

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.animation.Interpolator
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import com.filemanager.common.R
import com.filemanager.common.utils.VibratorUtil
import kotlin.math.abs

class FullScreenFastScroller @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private var touchPointY: Float = 0f
    private var lastVibratorTime: Long = -1
    private var maxScrollHeight: Int = 0
    private var minScrollHeight: Int = 0

    private val backgroundImage: ImageView by lazy {
        findViewById(R.id.full_screen_fast_scroller_background)
    }

    private val contentImage: ImageView by lazy {
        findViewById(R.id.full_screen_fast_scroller_content)
    }

    private val scrollerAnimatorSet: AnimatorSet by lazy {
        val animators: MutableList<Animator> = ArrayList<Animator>().apply {
            add(ObjectAnimator.ofFloat(backgroundImage, SCALE_X, 1F, SCALE_X_VALUE).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(backgroundImage, SCALE_Y, 1F, SCALE_Y_VALUE).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(backgroundImage, TRANSLATION_X, 0F, animationTranX).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(contentImage, TRANSLATION_X, 0F, animationTranX).setScaleTranslateConfig())
        }
        AnimatorSet().apply {
            playTogether(animators)
        }
    }

    private val reverseScrollerAnimatorSet: AnimatorSet by lazy {
        val animators: MutableList<Animator> = ArrayList<Animator>().apply {
            add(ObjectAnimator.ofFloat(backgroundImage, SCALE_X, SCALE_X_VALUE, 1F).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(backgroundImage, SCALE_Y, SCALE_Y_VALUE, 1F).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(backgroundImage, TRANSLATION_X, animationTranX, 0F).setScaleTranslateConfig())
            add(ObjectAnimator.ofFloat(contentImage, TRANSLATION_X, animationTranX, 0F).setScaleTranslateConfig())
        }
        AnimatorSet().apply {
            playTogether(animators)
        }
    }

    private val animationTranX = -context.resources.getDimensionPixelSize(R.dimen.dimen_3dp).toFloat()

    init {
        LayoutInflater.from(context).inflate(R.layout.common_full_screen_scroller_layout, this, true)
    }

    override fun setVisibility(visibility: Int) {
        if (visibility == View.VISIBLE) {
            doShowAnimation()
        } else {
            doHideAnimation(visibility)
        }
    }

    fun setVisibilityWithoutAnimation(visibility: Int) {
        super.setVisibility(visibility)
    }

    private fun doShowAnimation() {
        if (isVisible) {
            return
        }
        super.setVisibility(VISIBLE)
        alpha = 0F
        this.animate().apply {
            alpha(1F)
            duration = ALPHA_ANIMATION_DURING_TIME
            interpolator = ALPHA_INTERPOLATOR
        }.start()
    }

    private fun doHideAnimation(visibility: Int) {
        if (!isVisible) {
            return
        }
        this.animate().apply {
            alpha(0F)
            duration = ALPHA_ANIMATION_DURING_TIME
            interpolator = ALPHA_INTERPOLATOR
            withEndAction {
                super.setVisibility(visibility)
            }
        }.start()
    }

    override fun dispatchSetPressed(pressed: Boolean) {
        super.dispatchSetPressed(pressed)
        doTouchAnimation(!pressed)
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        super.dispatchTouchEvent(event)
        when (event?.action) {
            MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_UP       -> isPressed = false
            MotionEvent.ACTION_DOWN                                -> isPressed = true
        }
        //设备是否打开震动
        if (VibratorUtil.isHapticFeedbackOpen()) {
            velocityCalculationForTouchEvents(event)
        }
        return true
    }

    /**
     * 快滑条触摸事件，处理滑动震动
     * @param event 触摸滑动事件
     */
    private fun velocityCalculationForTouchEvents(event: MotionEvent?) {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> touchPointY = translationY
            MotionEvent.ACTION_MOVE -> vibrationWhenSliding()
        }
    }

    /**
     * 滑动快滑条触发震动
     */
    private fun vibrationWhenSliding() {
        //从按下的点滑动到当前的距离
        val disMove = translationY - touchPointY
        val separationCriteria: Int
        val intervalsTime: Long
        if (maxScrollHeight > (BASE_MULTIPLE * minScrollHeight)) {
            //直板机竖屏 、折叠屏竖屏、折叠屏展开横屏 这三种状态 快滑条的行程较大。 按照 1/3最小滑动距离  && 震动间隔60ms 触发
            separationCriteria = NUMBER_OF_EQUAL_PARTS_THREE
            intervalsTime = MIN_VIBRATOR_TIME_INTERVAL_VERTICAL
        } else {
            //直板机横屏、折叠机未展开的横屏  快滑条的行程较小。按照 1/6最小滑动距离  && 震动间隔30ms 触发
            separationCriteria = NUMBER_OF_EQUAL_PARTS_SIX
            intervalsTime = MIN_VIBRATOR_TIME_INTERVAL_HORIZONTAL
        }
        //(每次有效滑动偏移 大于 快滑条最小滑动范围的 separationCriteria ) && (震动间隔 > intervalsTime)
        if ((abs(disMove) > (minScrollHeight / separationCriteria))
            && ((System.currentTimeMillis() - lastVibratorTime) > intervalsTime)
        ) {
            //标记当前震动时间点
            lastVibratorTime = System.currentTimeMillis()
            VibratorUtil.vibrateLinearForScrollBar()
            touchPointY = translationY
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        cancelAllAnimators()
    }

    private fun cancelAllAnimators() {
        scrollerAnimatorSet.cancel()
        reverseScrollerAnimatorSet.cancel()
    }

    private fun doTouchAnimation(isReverse: Boolean) {
        if (isReverse) {
            scrollerAnimatorSet.cancel()
            reverseScrollerAnimatorSet.start()
        } else {
            reverseScrollerAnimatorSet.cancel()
            scrollerAnimatorSet.start()
        }
    }

    private fun ObjectAnimator.setScaleTranslateConfig() = apply {
        interpolator = SCALE_INTERPOLATOR
        duration = SCALE_ANIMATION_DURING_TIME
    }

    fun setScrollHeightRange(minScrollHeight: Int, maxScrollHeight: Int) {
        this.minScrollHeight = minScrollHeight
        this.maxScrollHeight = maxScrollHeight
    }

    companion object {
        private const val TAG = "FullScreenFastScroller"
        private const val SCALE_X                               = "scaleX"
        private const val SCALE_Y                               = "scaleY"
        private const val TRANSLATION_X                         = "translationX"
        private const val ALPHA_ANIMATION_DURING_TIME           = 350L
        private const val SCALE_ANIMATION_DURING_TIME           = 500L
        private const val SCALE_X_VALUE                         = 1.36F
        private const val SCALE_Y_VALUE                         = 1.27F
        private val ALPHA_INTERPOLATOR: Interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        private val SCALE_INTERPOLATOR: Interpolator = PathInterpolator(0.3f, 0f, 0.1f, 1f)
        private const val MIN_VIBRATOR_TIME_INTERVAL_VERTICAL: Long = 60
        private const val MIN_VIBRATOR_TIME_INTERVAL_HORIZONTAL: Long = 30
        private const val BASE_MULTIPLE: Int = 12
        private const val NUMBER_OF_EQUAL_PARTS_THREE: Int = 3
        private const val NUMBER_OF_EQUAL_PARTS_SIX: Int = 6
    }
}