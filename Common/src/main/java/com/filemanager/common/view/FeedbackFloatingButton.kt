/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FeedbackFloatingButton.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/7/19       1
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton

class FeedbackFloatingButton : COUIFloatingButton {
    var mOnInterceptTouchEventListener: OnInterceptTouchEventListener? = null

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr)

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (isPointInMainButtonBounds(ev.x.toInt(), ev.y.toInt()) && mOnInterceptTouchEventListener?.onInterceptTouchEvent(ev) == true) {
            return true
        }
        return super.onInterceptTouchEvent(ev)
    }

    interface OnInterceptTouchEventListener {
        fun onInterceptTouchEvent(ev: MotionEvent): Boolean
    }

    private fun isPointInMainButtonBounds(x: Int, y: Int): Boolean {
        return mainFloatingButton?.let {
            it.left < it.right && it.top < it.bottom && x >= it.left && x < it.right && y >= it.top && y < it.bottom
        } ?: false
    }
}