/***********************************************************
 ** Copyright (C), 2020-2025 Oplus. All rights reserved.
 ** File:  - SelectDragItemViewParent.kt
 ** Description: Drag animation build
 ** Version: 1.0
 ** Date : 2025/04/10
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  zhangyitong  2025/04/10    1.0        create
 ****************************************************************/
package com.filemanager.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.core.view.forEach
import androidx.core.view.isVisible
import androidx.dynamicanimation.animation.FloatValueHolder
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.R
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_LIST
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.resources
import com.oplus.dropdrag.SelectionTracker
import kotlin.math.pow
import kotlin.math.sqrt

class SelectDragItemViewParent(
    private val context: Context,
    private val eventX: Float,
    private val eventY: Float,
    private var startDragAndDrop: IStartDragAndDrop? = null,
    private var viewMode: SelectionTracker.LAYOUT_TYPE?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle
) : View(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "SelectDragItemViewParent"
        private const val DRAG_ITEM_OPACITY = 0.3f
        private const val ALPHA = 255
        private const val PAINT_ALPHA_PERCENTAGE = 0.85f
        private const val ROTATION_VALUE = 2f
        private const val ADSORPTION_MAX_RESPONSE_VALUE = 50f
        private const val ANIMATION_DELAY_TIME = 350L
        private const val ANIMATION_RESPONSE_ONE = 0.45f
        private const val ANIMATION_RESPONSE_TWO = 0.4f
        private const val ANIMATION_RESPONSE_THREE = 0.3f
        private const val CALCULATED_DATA = 2
        private const val SHADOW_DX = 0f
        private const val SHADOW_DY = 10f
        private const val ANIMATION_FOLLOW_INTERVAL = 50
        private const val GRID_SHADOW_RADIUS: Float = 20f
        private const val SINGLE_ANIMATION_COUNT: Int = 6
        private const val DRAG_ANIMATION_COUNT: Int = 9
        private const val DROP_ANIMATION_COUNT: Int = 7
        private const val GROUN_BOUND = 0.2f
        private const val MOVE_BOUNCE = 0F
        private const val MOVE_RESPONSE = 0.2F
    }

    private var radiusValue = resources().getDimension(R.dimen.dimen_4dp)
    private var targetViewList: ArrayList<View>? = null
    private var animationItemList: ArrayList<ItemViewSpringAnimation>? = null
    private var viewType = 0
    private var fileCount = 0
    private val mainHandler = android.os.Handler(Looper.getMainLooper())
    private var animationCount = 0
    private var adsorptionMaxValue = context.resources.getDimensionPixelOffset(R.dimen.drag_item_adsorption_animation_max_value)
    private var lastUpdatePositionTime: Long = 0L
    private var offsetThreeDp: Float = context.resources.getDimensionPixelOffset(R.dimen.dimen_3dp).toFloat()
    private var offsetTwoDp: Float = context.resources.getDimensionPixelOffset(R.dimen.dimen_2dp).toFloat()
    var drag = false

    private val roundRectShadowPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        color = context.getColor(R.color.drag_shadow_item_single_background)
        val shadowSingleBlur = context.resources.getDimensionPixelOffset(R.dimen.dimen_10dp).toFloat()
        val singleShadowColor = context.resources.getColor(R.color.drag_shadow_item_whole_shadow_color)
        setShadowLayer(shadowSingleBlur, SHADOW_DX, SHADOW_DY, singleShadowColor)
        setLayerType(LAYER_TYPE_HARDWARE, this)
    }

    private val roundRectBackground = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        color = context.getColor(R.color.drag_shadow_item_single_background)
    }

    init {
        isForceDarkAllowed = false
        viewType = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
            STATUS_LIST
        } else {
            STATUS_GRID
        }
    }

    fun cancelUpAnimation() {
        animationItemList?.forEach { animation ->
            animation.updateAnimationTargetValue(
                1f,
                1f,
                1f,
                0f,
                0f
            )
            animation.cancelUpAnimation(
                animation.initialPositionX,
                animation.initialPositionY,
            )
        }
    }

    fun updateTargetPosition(eventX: Float, eventY: Float) {
        if (eventX == this.eventX && eventY == this.eventY) return
        animationItemList?.forEach { animationItem ->
            animationItem.apply {
                val targetPositionX = eventX - view.width / CALCULATED_DATA + animationItem.displacementOffsetX
                val targetPositionY = eventY - view.height / CALCULATED_DATA + animationItem.displacementOffsetY
                updateAnimationTargetValue(
                    targetPositionX,
                    targetPositionY
                )
                updateAnimationTargetValue(
                    scaleWidthMultiple,
                    scaleHeightMultiple,
                    PAINT_ALPHA_PERCENTAGE,
                    <EMAIL>,
                    targetRotationValue
                )
            }
        }
    }

    fun addTargetView(viewList: ArrayList<View>, state: Int = 0) {
        drag = state == 0
        fileCount = viewList.size
        if (targetViewList == null) {
            targetViewList = ArrayList()
        }
        if (animationItemList == null) {
            animationItemList = ArrayList()
        }
        targetViewList?.clear()
        targetViewList?.addAll(viewList)
        targetViewList?.let {
            createAnimation()
        }
    }

    @SuppressLint("ResourceAsColor")
    private fun createAnimation() {
        animationItemList?.clear()
        val topView = DragUtils.getShadowView()?.get()
        if (drag && topView != null) {
            targetViewList?.remove(topView)
            targetViewList?.add(topView)
        }
        targetViewList?.let {
            val listSize = it.size - 1
            for ((index, view) in it.withIndex()) {
                val intArray = IntArray(CALCULATED_DATA)
                view.getLocationOnScreen(intArray)
                val itemViewSpringAnimation: ItemViewSpringAnimation = if (drag) {
                    ItemViewSpringAnimation(
                        view
                    ).apply {
                        initDragAnimation(index, listSize, intArray, view)
                    }
                } else {
                    ItemViewSpringAnimation(
                        view
                    ).apply {
                        initDropAnimation(view, intArray)
                    }
                }
                view.alpha = DRAG_ITEM_OPACITY
                animationItemList?.add(itemViewSpringAnimation)
                startAnimation(itemViewSpringAnimation)
            }
        }
    }

    private fun ItemViewSpringAnimation.initDropAnimation(
        view: View,
        intArray: IntArray
    ) {
        val (scaleWidthMultiple, scaleHeightMultiple) = setScaleMultiple()
        val itemViewRadiusValue = if (viewType == STATUS_GRID) {
            GRID_SHADOW_RADIUS
        } else {
            <EMAIL>
        }
        setAnimationStartValue(
            scaleWidthMultiple,
            scaleHeightMultiple,
            PAINT_ALPHA_PERCENTAGE,
            ROTATION_VALUE,
            itemViewRadiusValue
        )
        setAnimationStartValue(
            eventX - (view.width) / CALCULATED_DATA + displacementOffsetX,
            eventY - (view.height) / CALCULATED_DATA + displacementOffsetY,
        )
        setInitSpringAnimationFinalValue(
            intArray[0].toFloat(),
            intArray[1].toFloat(),
        )
        setInitSpringAnimationFinalValue(
            1f,
            1f,
            1f,
            0f,
            0f
        )
    }

    private fun ItemViewSpringAnimation.initDragAnimation(
        index: Int,
        listSize: Int,
        intArray: IntArray,
        view: View
    ) {
        var (rotationValue, displacementOffsetX, displacementOffsetY) = calculateRotationAndDisplacement(
            index, listSize
        )
        // 缩放比例
        var (scaleWidthMultiple, scaleHeightMultiple) = setScaleMultiple()
        setAnimationStartValue(
            1f,
            1f,
            1f,
            0f,
            0f
        )
        setAnimationStartValue(
            intArray[0].toFloat(),
            intArray[1].toFloat(),
        )
        setDisplacementOffset(displacementOffsetX, displacementOffsetY)
        val targetPositionX = eventX - view.width / CALCULATED_DATA + displacementOffsetX
        val targetPositionY = eventY - view.height / CALCULATED_DATA + displacementOffsetY
        setInitSpringAnimationFinalValue(
            targetPositionX,
            targetPositionY
        )
        <EMAIL> = if (viewType == STATUS_GRID) {
            GRID_SHADOW_RADIUS
        } else {
            <EMAIL>
        }
        setInitSpringAnimationFinalValue(
            scaleWidthMultiple,
            scaleHeightMultiple,
            PAINT_ALPHA_PERCENTAGE,
            <EMAIL>,
            rotationValue
        )
    }

    private fun startAnimation(itemViewSpringAnimation: ItemViewSpringAnimation) {
        // 单个动画
        if (targetViewList?.size == 1) {
            itemViewSpringAnimation.setSpringForce(
                ANIMATION_RESPONSE_TWO,
                ANIMATION_RESPONSE_THREE,
                0f,
                ANIMATION_RESPONSE_THREE
            )
            itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_TWO)
            itemViewSpringAnimation.startSingleAnimation()
            return
        }
        if (drag) {
            itemViewSpringAnimation.setSpringForce(
                ANIMATION_RESPONSE_TWO,
                ANIMATION_RESPONSE_THREE,
                ANIMATION_RESPONSE_THREE,
                ANIMATION_RESPONSE_THREE
            )
            itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_ONE)
            itemViewSpringAnimation.startDragAnimation()
        } else {
            itemViewSpringAnimation.setSpringForce(
                ANIMATION_RESPONSE_ONE,
                ANIMATION_RESPONSE_THREE,
                ANIMATION_RESPONSE_ONE,
                ANIMATION_RESPONSE_ONE
            )
            itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_ONE)
            itemViewSpringAnimation.startDropAnimation()
        }
    }

    private fun calculateRotationAndDisplacement(index: Int, listSize: Int): Triple<Float, Float, Float> {
        var rotationValue: Float
        var displacementOffsetX: Float
        var displacementOffsetY: Float
        if (index == 0) {
            rotationValue = 0f
            displacementOffsetX = 0f
            displacementOffsetY = 0f
        } else {
            when (index % CALCULATED_DATA) {
                0 -> {
                    rotationValue = -ROTATION_VALUE
                    displacementOffsetX = offsetThreeDp
                    displacementOffsetY = -offsetThreeDp
                }
                1 -> {
                    rotationValue = ROTATION_VALUE
                    displacementOffsetX = -offsetTwoDp
                    displacementOffsetY = -offsetThreeDp
                }

                else -> {
                    rotationValue = 0f
                    displacementOffsetX = 0f
                    displacementOffsetY = 0f
                }
            }
            if (listSize >= CALCULATED_DATA && index == listSize) {
                rotationValue = -ROTATION_VALUE
                displacementOffsetX = offsetThreeDp
                displacementOffsetY = -offsetThreeDp
                return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
            } else if (listSize >= 1 && index == listSize) {
                rotationValue = ROTATION_VALUE
                displacementOffsetX = -offsetTwoDp
                displacementOffsetY = -offsetThreeDp
                return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
            }
        }
        return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        animationItemList?.forEach { animationItem ->
            val view = animationItem.view
            canvasAnimation(view, canvas, animationItem)
        }
    }

    private fun canvasAnimation(view: View, canvas: Canvas, animationItem: ItemViewSpringAnimation) {
        val viewPosition = IntArray(CALCULATED_DATA)
        view.getLocationOnScreen(viewPosition)
        val saveCount = canvas.saveLayerAlpha(
            animationItem.positionList[0],
            animationItem.positionList[1],
            animationItem.positionList[0] + view.width,
            animationItem.positionList[1] + view.height,
            (animationItem.alphaValue * ALPHA).toInt()
        )
        // 应用缩放
        canvas.scale(
            animationItem.scaleValueList[0],
            animationItem.scaleValueList[1],
            animationItem.positionList[0] + view.width / CALCULATED_DATA,
            animationItem.positionList[1] + view.height / CALCULATED_DATA
        )
        // 应用旋转
        canvas.rotate(
            animationItem.currentRotationValue,
            animationItem.positionList[0] + view.width / CALCULATED_DATA,
            animationItem.positionList[1] + view.height / CALCULATED_DATA
        )
        // 绘制矩形作为阴影
        canvas.drawRoundRect(
            animationItem.positionList[0],
            animationItem.positionList[1],
            animationItem.positionList[0] + view.width,
            animationItem.positionList[1] + view.height,
            animationItem.radiusValue,
            animationItem.radiusValue,
            roundRectShadowPaint
        )
        // 绘制矩形作为背景
        canvas.drawRoundRect(
            animationItem.positionList[0],
            animationItem.positionList[1],
            animationItem.positionList[0] + view.width,
            animationItem.positionList[1] + view.height,
            animationItem.radiusValue,
            animationItem.radiusValue,
            roundRectBackground
        )
        animationItem.viewBitmap?.let {
            canvas.save()
            canvas.drawBitmap(it, animationItem.positionList[0], animationItem.positionList[1], null)
            canvas.restore()
        }
         canvas.restoreToCount(saveCount)
      }

    inner class ItemViewSpringAnimation(val view: View) {
        private val childView = View(context)
        var width = 0
        var height = 0
        var targetPositionX: Float = 0f
        var targetPositionY: Float = 0f
        var initialPositionX: Float = 0f
        var initialPositionY: Float = 0f
        var targetRotationValue: Float = 0f
        var scaleWidthMultiple = 0f
        var scaleHeightMultiple = 0f
        var displacementOffsetX = 0f
        var displacementOffsetY = 0f
        var animationTotalCount = 0

        // 位移动画 聚合/分散
        private var groupValueHolderX = FloatValueHolder(initialPositionX)
        private var groupValueHolderY = FloatValueHolder(initialPositionY)
        private var groupAnimationX = COUISpringAnimation(groupValueHolderX)
        private var groupAnimationY = COUISpringAnimation(groupValueHolderY)
        private val groupForceX = COUISpringForce()
        private val groupForceY = COUISpringForce()
        // 吸附动画
        private var adsorptionValueHolderY = FloatValueHolder(initialPositionY)
        private var adsorptionValueHolderX = FloatValueHolder(initialPositionX)
        private val adsorptionForceX = COUISpringForce()
        private val adsorptionForceY = COUISpringForce()
        private var adsorptionAnimationX = COUISpringAnimation(adsorptionValueHolderX)
        private var adsorptionAnimationY = COUISpringAnimation(adsorptionValueHolderY)
        // 缩放动画
        var scaleValueList = FloatArray(CALCULATED_DATA)
        private var scaleXAnimation = COUISpringAnimation(childView, COUISpringAnimation.SCALE_X, scaleWidthMultiple)
        private var scaleYAnimation = COUISpringAnimation(childView, COUISpringAnimation.SCALE_Y, scaleHeightMultiple)
        // 不透明度动画
        var alphaValue = 0f
        private var alphaAnimation = COUISpringAnimation(childView, COUISpringAnimation.ALPHA, PAINT_ALPHA_PERCENTAGE)
        // 旋转动画
        var currentRotationValue = 0f
        private var rotationAnimation = COUISpringAnimation(childView, COUISpringAnimation.ROTATION, ROTATION_VALUE)
        // 圆角动画
        var radiusValue = 0f
        private var radiusAnimation = COUISpringAnimation(childView, COUISpringAnimation.ROTATION,
            <EMAIL>
        )
        private var differenceX = 0f
        private var differenceY = 0f
        var positionList = ArrayList<Float>(CALCULATED_DATA)
        val delayTask = {
            adsorptionAnimationY.cancel()
            adsorptionAnimationX.cancel()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            scaleXAnimation.start()
            scaleYAnimation.start()
            groupAnimationX.start()
            groupAnimationY.start()
            rotationAnimation.start()
        }
        var viewBitmap: Bitmap? = null
        init {
            view.measure(
                MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY),
                MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY)
            )
            width = view.width
            height = view.height
            setAniamtionUpdateListener()
            if (view is ViewGroup) {
                viewBitmap = getChildView(view, view)
            }
        }

        @SuppressLint("Range")
        private fun getChildView(view: ViewGroup, rootView: View): Bitmap? {
            val arrayList = ArrayList<View>()
            view.forEach { childView ->
                Log.d(TAG, "view is child $childView")
                if (childView is ViewGroup) {
                    getChildView(childView, rootView)
                } else {
                    val divider = childView.findViewById<View>(R.id.divider_line)
                    if (childView is COUICheckBox || divider != null) {
                        if (childView.visibility == VISIBLE) arrayList.add(childView)
                        childView.visibility = GONE
                    }
                }
            }
            if (view == rootView) {
                val bitmap =
                    Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)
                view.draw(canvas)
                arrayList.forEach { view ->
                    view.visibility = VISIBLE
                }
                return bitmap
            }
            return null
        }

        fun setSpringForce(
            scaleResponse: Float,
            alphaResponse: Float,
            rotationResponse: Float,
            radiusResponse: Float
        ) {
            scaleXAnimation.spring.apply {
                setBounce(0f)
                setResponse(scaleResponse)
            }
            scaleYAnimation.spring.apply {
                setBounce(0f)
                setResponse(scaleResponse)
            }
            alphaAnimation.spring.apply {
                setBounce(0f)
                setResponse(alphaResponse)
            }
            rotationAnimation.spring.apply {
                setBounce(0f)
                setResponse(rotationResponse)
            }
            radiusAnimation.spring.apply {
                setBounce(0f)
                setResponse(radiusResponse)
            }
        }

        fun setGroupForceBounce(groupResponse: Float) {
            groupForceX.setBounce(GROUN_BOUND)
            groupForceX.setResponse(groupResponse)
            groupForceY.setBounce(GROUN_BOUND)
            groupForceY.setResponse(groupResponse)
        }

        private fun setAniamtionUpdateListener() {
            //监听位移变化
            setGroupAnimationListener()
            //监听吸附位移变化
            setAdsorptionAnimationListener()
            //监听宽度缩放变化
            setScaleAnimationListener()
            //监听旋转变化
            rotationAnimationListener()
            //监听透明度变化
            alphaAnimationListener()
            //监听圆角变化
            radiusAnimationListener()
        }

        private fun animationFinish() {
            mainHandler.post {
                val isRunning = when(animationTotalCount) {
                    SINGLE_ANIMATION_COUNT -> singleAnimationIsRunning()
                    DRAG_ANIMATION_COUNT -> dragAnimationIsRunning()
                    DROP_ANIMATION_COUNT -> dropAnimationIsRunning()
                    else -> {false}
                }
                if (!isRunning) {
                    animationCount++
                }
                if (animationCount == targetViewList?.size) {
                    animationCount = 0
                    startDragAndDrop?.startDragAndDrop()
                }
            }
        }

        private fun radiusAnimationListener() {
            radiusAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "radiusAnimation value is $value")
                radiusValue = value
                invalidate()
            }
            radiusAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun alphaAnimationListener() {
            alphaAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "alphaAnimation value is $value")
                alphaValue = value
                invalidate()
            }
            alphaAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun rotationAnimationListener() {
            rotationAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "rotationAnimation value is $value")
                currentRotationValue = value
                invalidate()
            }
            rotationAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun setScaleAnimationListener() {
            scaleXAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "scaleXAnimation value is $value")
                scaleValueList[0] = value
                invalidate()
            }
            //监听高度缩放变化
            scaleYAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "scaleYAnimation value is $value")
                scaleValueList[1] = value
                invalidate()
            }
            scaleYAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
            scaleXAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun setAdsorptionAnimationListener() {
            adsorptionAnimationY.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "adsorptionAnimationY value is $value")
                positionList[1] = value
                invalidate()
            }
            adsorptionAnimationX.addUpdateListener { _, value, _ ->
                Log.d(TAG, "adsorptionAnimationX value is $value")
                positionList[0] = value
                invalidate()
            }
            adsorptionAnimationX.addEndListener { couiDynamicAnimation, b, value, fl2 ->
                animationFinish()
            }
            adsorptionAnimationY.addEndListener { couiDynamicAnimation, b, value, fl2 ->
                animationFinish()
            }
        }

        private fun setGroupAnimationListener() {
            groupAnimationX.addUpdateListener { animation, value, velocity ->
                Log.d(TAG, "groupAnimationX value is $value")
                positionList[0] = value
                invalidate()
            }
            groupAnimationY.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "groupAnimationY value is $value")
                positionList[1] = value
                invalidate()
            }
            groupAnimationX.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
            groupAnimationY.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        fun setAnimationStartValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            alphaValue: Float,
            rotation: Float,
            radius: Float
        ) {
            scaleValueList[0] = scaleWidthMultiple
            scaleValueList[1] = scaleHeightMultiple
            scaleXAnimation.setStartValue(scaleWidthMultiple)
            scaleYAnimation.setStartValue(scaleHeightMultiple)
            alphaAnimation.setStartValue(alphaValue)
            rotationAnimation.setStartValue(rotation)
            radiusAnimation.setStartValue(radius)
        }

        fun setAnimationStartValue(
            initialPositionX: Float,
            initialPositionY: Float,
        ) {
            this.initialPositionX = initialPositionX
            this.initialPositionY = initialPositionY
            positionList.add(initialPositionX)
            positionList.add(initialPositionY)
            groupAnimationX.setStartValue(initialPositionX)
            groupAnimationY.setStartValue(initialPositionY)
            adsorptionAnimationY.setStartValue(initialPositionY)
            adsorptionAnimationX.setStartValue(initialPositionX)
        }

        fun setDisplacementOffset(offsetX: Float, offsetY: Float) {
            displacementOffsetX = offsetX
            displacementOffsetY = offsetY
        }

        fun setInitSpringAnimationFinalValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            currentAlphaValue: Float,
            currentRadiusValue: Float,
            rotationValue: Float
        ) {
            this.targetRotationValue = rotationValue

            //缩放比例
            scaleXAnimation.spring.finalPosition = scaleWidthMultiple
            scaleYAnimation.spring.finalPosition = scaleHeightMultiple

            // 透明度变化
            alphaAnimation.spring.finalPosition = currentAlphaValue

            // 圆角变化
            radiusAnimation.spring.finalPosition = currentRadiusValue

            // 旋转角度
            rotationAnimation.spring.finalPosition = rotationValue

            val hypotenuseValue = sqrt((differenceX).toDouble().pow(CALCULATED_DATA)
                    + (differenceY).toDouble().pow(CALCULATED_DATA))
            val adsorptionResponse = if (hypotenuseValue > adsorptionMaxValue) {
                ADSORPTION_MAX_RESPONSE_VALUE
            } else {
                (hypotenuseValue.toFloat().div(adsorptionMaxValue) * ADSORPTION_MAX_RESPONSE_VALUE)
            }
            adsorptionForceY.setResponse(adsorptionResponse)
            adsorptionForceX.setResponse(adsorptionResponse)
            groupAnimationX.setSpring(groupForceX)
            groupAnimationY.setSpring(groupForceY)
            adsorptionAnimationY.setSpring(adsorptionForceY)
            adsorptionAnimationX.setSpring(adsorptionForceX)
            Log.d("hypotenuseValue", "adsorptionForceY hypotenuseValue is $hypotenuseValue response is $adsorptionResponse")
        }

        fun setInitSpringAnimationFinalValue(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            differenceX = this.targetPositionX - this.initialPositionX
            differenceY = this.targetPositionY - this.initialPositionY
            adsorptionForceY.finalPosition = targetPositionY
            adsorptionForceX.finalPosition = targetPositionX
            groupForceX.finalPosition = targetPositionX
            groupForceY.finalPosition = targetPositionY
        }

        fun updateAnimationTargetValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            alphaValue: Float,
            radiusValue: Float,
            rotationValue: Float
        ) {
            scaleXAnimation.animateToFinalPosition(scaleWidthMultiple)
            scaleYAnimation.animateToFinalPosition(scaleHeightMultiple)
            alphaAnimation.animateToFinalPosition(alphaValue)
            radiusAnimation.animateToFinalPosition(radiusValue)
            if (rotationValue != this.targetRotationValue) {
                rotationAnimation.animateToFinalPosition(rotationValue)
            }
        }

        fun updateAnimationTargetValue(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            adsorptionAnimationX.cancel()
            adsorptionAnimationY.cancel()
            if (!groupAnimationX.isRunning && !groupAnimationY.isRunning) {
                groupAnimationX.setStartValue(positionList[0])
                groupAnimationY.setStartValue(positionList[1])
            }
            groupAnimationX.animateToFinalPosition(targetPositionX)
            groupAnimationY.animateToFinalPosition(targetPositionY)
            groupAnimationX.spring.setBounce(MOVE_BOUNCE)
            groupAnimationX.spring.setResponse(MOVE_RESPONSE)
            groupAnimationY.spring.setBounce(MOVE_BOUNCE)
            groupAnimationY.spring.setResponse(MOVE_RESPONSE)
        }

        fun cancelUpAnimation(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            mainHandler.removeCallbacks(delayTask)
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            adsorptionAnimationX.cancel()
            adsorptionAnimationY.cancel()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            groupAnimationX.animateToFinalPosition(targetPositionX)
            groupAnimationY.animateToFinalPosition(targetPositionY)
        }

        fun startSingleAnimation() {
            animationTotalCount = SINGLE_ANIMATION_COUNT
            scaleXAnimation.start()
            scaleYAnimation.start()
            alphaAnimation.start()
            radiusAnimation.start()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            groupAnimationX.start()
            groupAnimationY.start()
        }

        private fun singleAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning || alphaAnimation.isRunning
                    || radiusAnimation.isRunning || groupAnimationX.isRunning || groupAnimationY.isRunning)
        }

        fun startDragAnimation() {
            animationTotalCount = DRAG_ANIMATION_COUNT
            alphaAnimation.start()
            radiusAnimation.start()
            adsorptionAnimationY.start()
            adsorptionAnimationX.start()
            mainHandler.postDelayed({
                delayTask.invoke()
            }, ANIMATION_DELAY_TIME)
        }

        private fun dragAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning
                    || alphaAnimation.isRunning
                    || adsorptionAnimationY.isRunning || adsorptionAnimationX.isRunning
                    || radiusAnimation.isRunning
                    || groupAnimationX.isRunning || groupAnimationY.isRunning
                    || rotationAnimation.isRunning)
        }

        fun startDropAnimation() {
            animationTotalCount = DROP_ANIMATION_COUNT
            scaleXAnimation.start()
            scaleYAnimation.start()
            alphaAnimation.start()
            radiusAnimation.start()
            rotationAnimation.start()
            groupAnimationX.start()
            groupAnimationY.start()
        }

        private fun dropAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning
                    || alphaAnimation.isRunning
                    || radiusAnimation.isRunning
                    || groupAnimationX.isRunning || groupAnimationY.isRunning
                    || rotationAnimation.isRunning)
        }

        fun setScaleMultiple(): Pair<Float, Float> {
            MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY)
            MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY)
            val (width, height) = DragUtils.getItemSize(
                viewType,
                fileCount,
                view.measuredWidth,
                view.measuredHeight,
                context
            )
            scaleWidthMultiple = (width.toDouble()).div(view.measuredWidth.toDouble()).toFloat()
            scaleHeightMultiple = (height.toDouble()).div(view.measuredHeight.toDouble()).toFloat()
            scaleXAnimation.spring.finalPosition = scaleWidthMultiple
            scaleYAnimation.spring.finalPosition = scaleHeightMultiple
            return Pair(scaleWidthMultiple, scaleHeightMultiple)
        }
    }
    interface IStartDragAndDrop {
        fun startDragAndDrop()
    }
}