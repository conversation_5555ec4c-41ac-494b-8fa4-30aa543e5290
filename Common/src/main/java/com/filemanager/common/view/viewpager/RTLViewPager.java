/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RTLViewPager
 ** Description : ViewPager support rtl and intercept event
 ** Version     : 1.0
 ** Date        : 2023/3/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2023/3/7       1.0        create
 ***********************************************************************/

package com.filemanager.common.view.viewpager;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.filemanager.common.utils.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 支持 RTL ViewPager实现
 */
public class RTLViewPager extends ViewPager {

    private static final String TAG = "RTLViewPager";
    private static final int DEFAULT_ITEM = 0;
    private final Map<OnPageChangeListener, ReverseOnPageChangeListener> mListenerMap = new HashMap<>();
    private int mDirection = View.LAYOUT_DIRECTION_LTR;

    private boolean mIsUserInputEnabled = true;

    public RTLViewPager(Context context) {
        this(context, null);
    }

    public RTLViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public boolean isUserInputEnabled() {
        return mIsUserInputEnabled;
    }

    public void setUserInputEnabled(boolean userInputEnabled) {
        mIsUserInputEnabled = userInputEnabled;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (!mIsUserInputEnabled) {
            return false;
        }
        boolean result;
        try {
             result = super.onTouchEvent(ev);
        } catch (Exception e) {
            Log.e(TAG, "onTouchEvent exception:" + e.getMessage());
            result = false;
        }
        return result;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!mIsUserInputEnabled) {
            return false;
        }
        boolean result;
        try {
            result = super.onInterceptTouchEvent(ev);
        } catch (Exception e) {
            Log.e(TAG, "onInterceptTouchEvent exception:" + e.getMessage());
            result = false;
        }
        return result;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int heightSpec = heightMeasureSpec;
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        if (MeasureSpec.UNSPECIFIED == heightMode) {
            int height = 0;
            int childCount = getChildCount();
            int childSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
            for (int i = 0; i < childCount; i++) {
                View child = getChildAt(i);
                child.measure(widthMeasureSpec, childSpec);
                int childH = child.getMeasuredHeight();
                height = Math.max(height, childH);
            }
            heightSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY);
        }
        super.onMeasure(widthMeasureSpec, heightSpec);
    }

    @Override
    public void onRtlPropertiesChanged(int layoutDirection) {
        super.onRtlPropertiesChanged(layoutDirection);
        if (layoutDirection != mDirection) {
            PagerAdapter adapter = superGetAdapter();
            int position = 0;
            if (adapter != null) {
                position = getCurrentItem();
            }
            mDirection = layoutDirection;
            if (adapter != null) {
                adapter.notifyDataSetChanged();
                setCurrentItem(position);
            }
        }
    }

    public PagerAdapter superGetAdapter() {
        return super.getAdapter();
    }

    private PagerAdapter reverseAdapter(PagerAdapter adapter) {
        if (adapter == null) {
            return null;
        }
        return new ReverseAdapter(adapter);
    }

    @Override
    public void setAdapter(PagerAdapter adapter) {
        PagerAdapter newAdapter = reverseAdapter(adapter);
        super.setAdapter(newAdapter);
        setCurrentItem(DEFAULT_ITEM);
    }

    @Override
    public PagerAdapter getAdapter() {
        PagerAdapter adapter = superGetAdapter();
        if (adapter instanceof ReverseAdapter) {
            return ((ReverseAdapter) adapter).getDelegate();
        }
        return adapter;
    }

    private int reversePosition(int position) {
        PagerAdapter adapter = superGetAdapter();
        if ((adapter != null) && isRTL()) {
            return adapter.getCount() - 1 - position;
        }
        return position;
    }

    private boolean isRTL() {
        return View.LAYOUT_DIRECTION_RTL == mDirection;
    }

    @Override
    public int getCurrentItem() {
        int item = super.getCurrentItem();
        return reversePosition(item);
    }

    @Override
    public void setCurrentItem(int position, boolean smoothScroll) {
        int newPosition = reversePosition(position);
        super.setCurrentItem(newPosition, smoothScroll);
    }

    @Override
    public void setCurrentItem(int position) {
        int newPosition = reversePosition(position);
        super.setCurrentItem(newPosition);
    }

    @Deprecated
    @Override
    public void setOnPageChangeListener(@NonNull OnPageChangeListener listener) {
        super.setOnPageChangeListener(new ReverseOnPageChangeListener(this, listener));
    }

    @Override
    public Parcelable onSaveInstanceState() {
        Parcelable state = super.onSaveInstanceState();
        return new SavedState(mDirection, state);
    }

    @Override
    public void onRestoreInstanceState(Parcelable state) {
        if (!(state instanceof SavedState)) {
            super.onRestoreInstanceState(state);
            return;
        }

        SavedState ss = (SavedState) state;
        mDirection = ss.mDirection;
        super.onRestoreInstanceState(ss.mOriginSavedState);
    }

    public static class SavedState implements Parcelable {

        /**
         * The `CREATOR` field is used to create the parcelable from a parcel, even though it is never referenced directly.
         */
        public static final ClassLoaderCreator<SavedState> CREATOR
                = new ClassLoaderCreator<SavedState>() {

            @Override
            public SavedState createFromParcel(Parcel source) {
                return createFromParcel(source, null);
            }

            @Override
            public SavedState createFromParcel(Parcel source, ClassLoader loader) {
                return new SavedState(source, loader);
            }

            @Override
            public SavedState[] newArray(int size) {
                return new SavedState[size];
            }
        };

        private final int mDirection;
        private final Parcelable mOriginSavedState;

        private SavedState(int direction, Parcelable originSavedState) {
            mDirection = direction;
            mOriginSavedState = originSavedState;
        }

        private SavedState(Parcel in, ClassLoader loader) {
            ClassLoader newLoader = loader;
            if (loader == null) {
                newLoader = getClass().getClassLoader();
            }
            mDirection = in.readInt();
            mOriginSavedState = in.readParcelable(newLoader);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel out, int flags) {
            out.writeInt(mDirection);
            out.writeParcelable(mOriginSavedState, flags);
        }
    }

    @Override
    public void addOnPageChangeListener(@NonNull OnPageChangeListener listener) {
        ReverseOnPageChangeListener reverseListener = new ReverseOnPageChangeListener(this, listener);
        mListenerMap.put(listener, reverseListener);
        super.addOnPageChangeListener(reverseListener);
    }

    @Override
    public void removeOnPageChangeListener(@NonNull OnPageChangeListener listener) {
        ReverseOnPageChangeListener reverseListener = mListenerMap.remove(listener);
        if (reverseListener == null) {
            return;
        }
        super.removeOnPageChangeListener(reverseListener);
    }

    @Override
    public void clearOnPageChangeListeners() {
        mListenerMap.clear();
        super.clearOnPageChangeListeners();
    }

    private class ReverseOnPageChangeListener implements OnPageChangeListener {

        private final RTLViewPager mViewPager;
        private final OnPageChangeListener mListener;

        ReverseOnPageChangeListener(RTLViewPager viewPager, OnPageChangeListener listener) {
            this.mViewPager = viewPager;
            this.mListener = listener;
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            final int width = getWidth();
            PagerAdapter adapter = mViewPager.superGetAdapter();
            int pos = position;
            float posOffset = positionOffset;
            int posPx = positionOffsetPixels;
            if (mViewPager.isRTL() && (adapter != null)) {
                int count = adapter.getCount();
                int remainingWidth = posPx + (int) (width * (1 - adapter.getPageWidth(pos)));
                while (pos < count && remainingWidth > 0) {
                    pos += 1;
                    remainingWidth -= (int) (width * adapter.getPageWidth(pos));
                }
                pos = count - pos - 1;
                posPx = -remainingWidth;
                posOffset = posPx / (width * adapter.getPageWidth(pos));
            }
            mListener.onPageScrolled(pos, posOffset, posPx);
        }

        @Override
        public void onPageSelected(int position) {
            int newPosition = mViewPager.reversePosition(position);
            mListener.onPageSelected(newPosition);
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            mListener.onPageScrollStateChanged(state);
        }
    }

    private class ReverseAdapter extends DelegatePagerAdapter {

        ReverseAdapter(@NonNull PagerAdapter adapter) {
            super(adapter);
        }

        public int reversePosition(int position) {
            if (isRTL()) {
                return getCount() - 1 - position;
            }
            return position;
        }

        @Override
        public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            int newPosition = reversePosition(position);
            super.destroyItem(container, newPosition, object);
        }

        @Deprecated
        @Override
        public void destroyItem(@NonNull View container, int position, @NonNull Object object) {
            int newPosition = reversePosition(position);
            super.destroyItem(container, newPosition, object);
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            int position = super.getItemPosition(object);
            if (isRTL()) {
                if ((position == POSITION_UNCHANGED) || (position == POSITION_NONE)) {
                    position = POSITION_NONE;
                } else {
                    position = reversePosition(position);
                }
            }
            return position;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            int newPosition = reversePosition(position);
            return super.getPageTitle(newPosition);
        }

        @Override
        public float getPageWidth(int position) {
            int newPosition = reversePosition(position);
            return super.getPageWidth(newPosition);
        }

        @Override
        public @NonNull
        Object instantiateItem(@NonNull ViewGroup container, int position) {
            int newPosition = reversePosition(position);
            return super.instantiateItem(container, newPosition);
        }

        @Deprecated
        @Override
        public @NonNull
        Object instantiateItem(@NonNull View container, int position) {
            int newPosition = reversePosition(position);
            return super.instantiateItem(container, newPosition);
        }

        @Override
        public void setPrimaryItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
            int newPosition = reversePosition(position);
            super.setPrimaryItem(container, newPosition, object);
        }

        @Deprecated
        @Override
        public void setPrimaryItem(@NonNull View container, int position, @NonNull Object object) {
            int newPosition = reversePosition(position);
            super.setPrimaryItem(container, newPosition, object);
        }
    }
}

