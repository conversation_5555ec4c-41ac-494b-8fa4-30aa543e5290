/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.view.ViewPagerWrapperForPC
 * * Description : 安装包，全局搜索，文档，压缩包，文件来源等有ViewPager2的页面，PC互联的时候，在PC上长按会触发ViewPager2左右滚动，
 *                  在左右滑动的过程中，若是触发了DragFileOnRemote会导致ViewPager2滑动卡在中间不动
 *                 在ViewPager2的封装一层父布局,当是PC端点击的时候，禁用左右滑动
 *                 当非PC点击的时候，恢复可左右滑动状态，恢复状态要判断当前是否编辑模式，编辑模式下不恢复左右滑动
 * * Version     : 1.0
 * * Date        : 2022/4/20
 * * Author      : Yan<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.viewpager.COUIViewPager2
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.view.viewpager.RTLViewPager

class ViewPagerWrapperForPC : FrameLayout {

    companion object{
        private const val TAG = "ViewPagerWrapperForPC"
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle)

    @VisibleForTesting
    var mViewPager2: COUIViewPager2? = null

    @VisibleForTesting
    var viewPager: RTLViewPager? = null

    private var mHasChangedEnable = false
    private var isEditMode = false
    var notifyMainViewPager: ((Boolean) -> Unit)? = null

    @Suppress("TooGenericExceptionCaught")
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            when (it.action) {
                MotionEvent.ACTION_DOWN -> notifyMainViewPager?.invoke(false)
                MotionEvent.ACTION_MOVE -> {}
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> notifyMainViewPager?.invoke(true)
                else -> notifyMainViewPager?.invoke(true)
            }
        }
        return try {
            super.dispatchTouchEvent(ev)
        } catch (e: Exception) {
            Log.e(TAG, "dispatchTouchEvent e:${e.message}")
            false
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        if (mViewPager2 == null && viewPager == null) {
            val child = getChildAt(0)
            if (child is COUIViewPager2) {
                mViewPager2 = child
            } else if (child is RTLViewPager) {
                viewPager = child
            }
        }
        ev?.let { eventMotion ->
            when (eventMotion.action) {
                MotionEvent.ACTION_DOWN -> {
                    mViewPager2?.isUserInputEnabled = judgeUserInputEnabled(PCConnectAction.isTouchFromPC(ev),
                        mViewPager2?.isUserInputEnabled ?: false, isEditMode)

                    viewPager?.isUserInputEnabled = judgeUserInputEnabled(PCConnectAction.isTouchFromPC(ev),
                        viewPager?.isUserInputEnabled ?: false, isEditMode)
                }
                MotionEvent.ACTION_MOVE -> {
                    // 确保 ViewPager 在移动时能够正确响应
                    parent?.requestDisallowInterceptTouchEvent(true)
                }
                else -> {}
            }
        }
        return try {
            super.onInterceptTouchEvent(ev)
        } catch (e: Exception) {
            Log.e(TAG, "onInterceptTouchEvent e:${e.message}")
            false
        }
    }

    @VisibleForTesting
    fun judgeUserInputEnabled(isTouchFromPC: Boolean, userInputEnabled: Boolean, editMode: Boolean): Boolean {
        if (isTouchFromPC) {
            if (userInputEnabled) {
                mHasChangedEnable = true
                return false
            }
        } else if (mHasChangedEnable) {
            mHasChangedEnable = false
            return !editMode
        }
        return userInputEnabled
    }

    fun setEditMode(editMode: Boolean) {
        isEditMode = editMode
    }
}