/***********************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** File        :   SharedPreferenceManager.kt
 ** Description :   SharedPreference 管理器
 ** Version: 1.0
 ** Date : 2024/9/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <data>       <version>       <desc>
 ****************************************************************/
package com.filemanager.common.managers

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

/**
 * SharedPreference 管理器
 */
object SharedPreferenceManager {

    /**
     * 默认 SharedPreferences 实例名称.
     */
    private const val CONFIG_NAME = "filemanager_config"

    /**
     * 默认 SharedPreferences 实例对象.
     */
    lateinit var sharedPreferences: SharedPreferences

    /**
     * 初始化默认 SharedPreferences 实例对象.
     *
     * @param context 上下文.
     */
    fun initSharedPreferencesIfUninitialized(context: Context) {
        if (!SharedPreferenceManager::sharedPreferences.isInitialized) {
            sharedPreferences = context.getSharedPreferences(CONFIG_NAME, Context.MODE_PRIVATE)
        }
    }

    /**
     * 储存多个参数
     */
    @JvmStatic
    fun putValues(
        context: Context,
        commit: Boolean = false,
        action: SharedPreferences.Editor.() -> Unit
    ) {
        initSharedPreferencesIfUninitialized(context)
        sharedPreferences.edit(commit) {
            action()
        }
    }

    /**
     * 存储参数.
     *
     * @param context 引用上下文
     * @param key 保存参数的 key 值
     * @param value 保存参数的 value 值
     */
    @JvmStatic
    fun putValue(context: Context, key: String, value: Any) {
        putValue(context, key, value, false)
    }

    /**
     * 存储参数.
     *
     * @param context 引用上下文
     * @param key 保存参数的 key 值
     * @param value 保存参数的 value 值
     * @param commit
     */
    @JvmStatic
    fun putValue(context: Context, key: String, value: Any, commit: Boolean = false) {
        initSharedPreferencesIfUninitialized(context)
        sharedPreferences.edit(commit) {
            when (value) {
                is Long -> putLong(key, value)
                is Int -> putInt(key, value)
                is String -> putString(key, value)
                is Float -> putFloat(key, value)
                is Boolean -> putBoolean(key, value)
                else -> throw UnsupportedOperationException("SharedPreferences doesn't support this type")
            }
        }
    }



    fun putStringSet(context: Context, key: String, value: Set<String>) {
        initSharedPreferencesIfUninitialized(context)
        with(sharedPreferences.edit()) {
            putStringSet(key, value).apply()
        }
    }

    fun removeValue(context: Context, key: String) {
        initSharedPreferencesIfUninitialized(context)
        sharedPreferences.edit().remove(key).apply()
    }

    /**
     * 获取参数.
     *
     * @param context 引用上下文
     * @param key 待获取参数的 key 值
     * @param defValue (可选) 如果没有获取到该参数的值, 则默认使用的值, 如果不传或传空, 则按类型返回默认值.
     */
    inline fun <reified T> getValue(context: Context, key: String, defValue: T? = null): T {
        initSharedPreferencesIfUninitialized(context)
        with(sharedPreferences) {
            return when (T::class) {
                Int::class -> getInt(key, if (defValue is Int) defValue else 0) as T
                String::class -> getString(key, if (defValue is String) defValue else "") as T
                Long::class -> getLong(key, if (defValue is Long) defValue else 0L) as T
                Float::class -> getFloat(key, if (defValue is Float) defValue else 0.0f) as T
                Boolean::class -> getBoolean(key, if (defValue is Boolean) defValue else false) as T
                else -> throw IllegalArgumentException("SharedPreferences 类型错误")
            }
        }
    }

    fun getStringSet(context: Context, key: String): Set<String> {
        initSharedPreferencesIfUninitialized(context)
        return sharedPreferences.getStringSet(key, null) ?: emptySet()
    }

    fun contains(context: Context, key: String): Boolean {
        initSharedPreferencesIfUninitialized(context)
        return sharedPreferences.contains(key)
    }

    /**
     * 获取默认 SharedPreferences 实例对象.
     *
     * @param context 引用上下文
     * @return 默认 SharedPreferences 实例对象.
     */
    fun getSharedPreferences(context: Context): SharedPreferences {
        initSharedPreferencesIfUninitialized(context)
        return sharedPreferences
    }

    fun getStringPreferenceImpl(context: Context, key: String, defaultValue: String?): String? {
        return getSharedPreferences(context).getString(key, defaultValue)
    }

    @JvmStatic
    fun getLong(context: Context, key: String, defValue: Long): Long {
        initSharedPreferencesIfUninitialized(context)
        return sharedPreferences.getLong(key, defValue)
    }

    @JvmStatic
    fun getBoolean(context: Context, key: String, defValue: Boolean): Boolean {
        initSharedPreferencesIfUninitialized(context)
        return sharedPreferences.getBoolean(key, defValue)
    }
}