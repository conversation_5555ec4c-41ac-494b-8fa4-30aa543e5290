/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.module.imageloader.glide
 * * Version     : 1.0
 * * Date        : 2020/5/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.imageloader.glide;

import com.bumptech.glide.load.Key;

import java.io.File;
import java.security.MessageDigest;

public class DrmCoverSignature implements Key {
    private final File mFile;
    private StringBuilder mStringBuilder;

    public DrmCoverSignature(String path) {
        this.mFile = new File(path);
        mStringBuilder = new StringBuilder();
    }

    @Override
    public void updateDiskCacheKey(MessageDigest messageDigest) {
        mStringBuilder.append(mFile.lastModified()).append(mFile.getAbsolutePath());
        byte[] bs = mStringBuilder.toString().getBytes();
        messageDigest.update(bs, 0, bs.length);
    }
}
