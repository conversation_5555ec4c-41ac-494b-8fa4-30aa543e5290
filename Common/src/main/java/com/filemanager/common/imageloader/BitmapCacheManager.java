/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : BitmapCacheManager.java
 * * Description : FileManager, Caching Application Information
 * * and add anniversary to remind.
 * * Version     : 1.0
 * * Date        : 2017/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/8/1  1.0    Caching Application Information
 ***********************************************************************/
package com.filemanager.common.imageloader;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.graphics.Bitmap;
import android.graphics.Bitmap.CompressFormat;
import android.graphics.BitmapFactory;
import android.os.Environment;

import androidx.annotation.Nullable;

import com.filemanager.common.DiskLruCache;
import com.filemanager.common.helper.VolumeEnvironment;
import com.filemanager.common.imageloader.application.ApplicationInfoDetail;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.Md5Utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;

public class BitmapCacheManager {
    private static final String TAG = "BitmapCacheManager";
    private static final boolean SD_CACHE_ENABLED = false;

    public static Bitmap getFromDiskCache(String path, DiskLruCache diskLruCache) {
        if (diskLruCache == null) {
            return null;
        }
        InputStream in = null;
        try {
            DiskLruCache.Snapshot snapshot = diskLruCache.get(Md5Utils.toKey(path));
            if (snapshot != null) {
                in = snapshot.getInputStream(0);
                if (in != null) {
                    Bitmap bitmap = BitmapFactory.decodeStream(in);
                    snapshot.close();
                    return bitmap;
                } else {
                    snapshot.close();
                }
            }
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    // do nothing
                }
            }
        }
        return null;
    }

    public static void addToDisk(String path, Bitmap bmp, DiskLruCache diskLruCache) {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            final int quality = 100;
            baos = new ByteArrayOutputStream();
            bmp.compress(CompressFormat.PNG, quality, baos);
            byte[] buf = baos.toByteArray();
            DiskLruCache.Editor editor = diskLruCache.edit(Md5Utils.toKey(path));
            if (editor != null) {
                out = editor.newOutputStream(0);
                out.write(buf);
                out.flush();
                editor.commit();
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                // do nothing
            }
        }
    }

    public static void addApplicationToDisk(String path, ApplicationInfoDetail applicationInfoDetail, DiskLruCache diskLruCache) {
        ByteArrayOutputStream baos = null;
        ObjectOutputStream oos = null;
        OutputStream out = null;
        try {
            baos = new ByteArrayOutputStream();
            oos = new ObjectOutputStream(baos);
            oos.writeObject(applicationInfoDetail);
            byte[] buf = baos.toByteArray();
            DiskLruCache.Editor editor = diskLruCache.edit(Md5Utils.toKey(path));
            if (editor != null) {
                out = editor.newOutputStream(0);
                out.write(buf);
                out.flush();
                editor.commit();
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
                if (oos != null) {
                    oos.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                // do nothing
            }
        }
    }

    public static ApplicationInfoDetail getApplicationDiskCache(String path, DiskLruCache diskLruCache) {
        if (diskLruCache == null) {
            return null;
        }
        InputStream in = null;
        DiskLruCache.Snapshot snapshot = null;
        ObjectInputStream ois = null;
        try {
            snapshot = diskLruCache.get(Md5Utils.toKey(path));
            if (snapshot != null) {
                in = snapshot.getInputStream(0);
                if (in != null) {
                    ois = new ObjectInputStream(in);
                    ApplicationInfoDetail applicationInfoDetail = (ApplicationInfoDetail) ois.readObject();
                    return applicationInfoDetail;
                }
            }
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        } catch (ClassNotFoundException e) {
            Log.e(TAG, e.getMessage());
        } finally {
            try {
                if (snapshot != null) {
                    snapshot.close();
                }
                if (ois != null) {
                    ois.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                // do nothing
            }
        }
        return null;
    }

    public static boolean clearApplicationDiskCache(@Nullable String path, @Nullable DiskLruCache diskLruCache) {
        if ((diskLruCache == null) || (path == null)) {
            return false;
        }
        try {
            return diskLruCache.remove(Md5Utils.toKey(path));
        } catch (IOException e) {
            Log.w(TAG, "clearApplicationDiskCache " + e.getMessage());
        }
        return false;
    }

    public static DiskLruCache getDiskLruCache(Context context, String dirName) {
        File directory = getCacheDirectory(context, dirName);
        if ((directory == null) || (!directory.exists()) || (directory.isFile())) {
            return null;
        }
        int version = getAppVersion(context);
        long maxBytesSize = 10 * 1024 * 1024; // 10M

        DiskLruCache diskLruCache = null;
        try {
            diskLruCache = DiskLruCache.open(directory, version, 1, maxBytesSize);
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
            diskLruCache = null;
        }

        return diskLruCache;
    }

    private static int getAppVersion(Context context) {
        PackageManager manager = context.getPackageManager();
        try {
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            return info.versionCode;
        } catch (NameNotFoundException e) {
            Log.e(TAG, e.getMessage());
        }

        return 1;
    }

    @SuppressLint("SdCardPath")
    private static File getCacheDirectory(Context context, String dirname) {
        String path = "";
        if (SD_CACHE_ENABLED) {
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                    || !Environment.isExternalStorageRemovable()) {
                path = context.getExternalCacheDir().getPath();

            } else {
                path = context.getCacheDir().getPath();
            }
        } else {
            path = VolumeEnvironment.getDataDirPath(context, "iconCache");
        }

        File file = new File(path + File.separator + dirname);
        if (!file.exists()) {
            if (!file.mkdirs()) {
                Log.e(TAG, "mkdirs error");
            }
        }
        return file;
    }
}
