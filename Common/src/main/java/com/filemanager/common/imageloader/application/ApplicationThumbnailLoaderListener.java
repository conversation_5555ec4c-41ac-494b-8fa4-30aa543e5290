/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  ApplicationThumbnailLoaderListener.java
 *  * * Description : ApplicationThumbnailLoaderListener.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:58
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.filemanager.common.imageloader.application;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.widget.ImageView;

import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.utils.AppInfo;
import com.filemanager.common.utils.AppUtils;
import com.filemanager.common.utils.Log;

import java.io.File;

public class ApplicationThumbnailLoaderListener {

    private static final String TAG = "ApplicationThumbnailLoaderListener";

    private static final int BITMAP_WIDTH = 100;
    private Context mContext;
    private PackageManager mPackageManager;
    private ApplicationInfoImageLoadListener mApplicationInfoLoadListener;
    private ApplicationInfoDetailLoadListener mApplicationInfoDetailLoadListener;

    public ApplicationThumbnailLoaderListener(Context context, ApplicationInfoImageLoadListener applicationInfoLoadListener, ApplicationInfoDetailLoadListener applicationInfoDetailLoadListener) {
        mContext = context;
        mApplicationInfoLoadListener = applicationInfoLoadListener;
        mApplicationInfoDetailLoadListener = applicationInfoDetailLoadListener;
        mPackageManager = mContext.getPackageManager();
    }

    public void onLoad(ImageView view, Bitmap bitmap, String tag) {
        if (mApplicationInfoLoadListener != null) {
            if ((bitmap == null) || bitmap.isRecycled()) {
                mApplicationInfoLoadListener.onLoadFail(tag, "bitmap is null or isRecycled");
            } else {
                mApplicationInfoLoadListener.onLoadSuccess(tag, bitmap);
            }
        }
    }

    public ApplicationInfoDetail fetchImage(String path, int type) {
        if (MimeTypeHelper.APPLICATION_TYPE == type) {
            AppInfo appInfo = null;
            String title = "";
            String version = "";
            ApplicationInfoDetail applicationInfoDetail = new ApplicationInfoDetail();
            if (path.contains(File.separator)) {
                try {
                    appInfo = AppUtils.getAppInfoByPath(mContext, path);
                } catch (Exception e) {
                    Log.e(TAG, "fetchImage  error " + e.getMessage());
                }
                if (appInfo == null) {
                    try {
                        PackageInfo packageInfo = mPackageManager.getPackageInfo(
                                AppUtils.getPackageNameByPath(mContext, path), 0);
                        title = (String) packageInfo.applicationInfo.loadLabel(mPackageManager);
                    } catch (Exception e) {
                        Log.e(TAG, e.getMessage());
                    }
                } else {
                    title = (!TextUtils.isEmpty(appInfo.getAppName())) ? (appInfo.getAppName().toString()) : "";
                    version = (!TextUtils.isEmpty(appInfo.getVersionName())) ? (appInfo.getVersionName().toString()) : "";
                }
            } else {
                try {
                    Drawable icon = mPackageManager.getApplicationIcon(path);
                    Bitmap bitmap = Bitmap.createBitmap(BITMAP_WIDTH, BITMAP_WIDTH, Bitmap.Config.ARGB_8888);
                    Canvas canvas = new Canvas(bitmap);
                    icon.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                    icon.draw(canvas);
                    applicationInfoDetail.mBitmap = bitmap;
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                }
            }
            if (appInfo != null) {
                applicationInfoDetail.mBitmap = appInfo.getIcon();
            }
            applicationInfoDetail.mApkName = title;
            applicationInfoDetail.mApkVersion = version;
            return applicationInfoDetail;
        }
        return null;
    }


    public void onLoadTextView(String title, String detail, String path) {
        if (mApplicationInfoDetailLoadListener != null) {
            ApplicationInfoDetail applicationInfoDetail = new ApplicationInfoDetail();
            applicationInfoDetail.mPath = path;
            applicationInfoDetail.mApkName = title;
            applicationInfoDetail.mApkVersion = detail;
            mApplicationInfoDetailLoadListener.onLoadDetail(applicationInfoDetail);
        }
    }

    public void clear() {
        mContext = null;
        mPackageManager = null;
        mApplicationInfoLoadListener = null;
        mApplicationInfoDetailLoadListener = null;
    }
}
