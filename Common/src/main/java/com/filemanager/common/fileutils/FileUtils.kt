/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileUtils.kt
 ** Description: methods about File.java
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: Liu<PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.fileutils

import android.content.Context
import android.net.Uri
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants.COPY_BUF_SIZE
import com.filemanager.common.constants.CommonConstants.FILE_HEADER_MAP
import com.filemanager.common.constants.CommonConstants.NAME_BYTES_LEN
import com.filemanager.common.constants.CommonConstants.P7ZIP
import com.filemanager.common.constants.CommonConstants.UNKNOWN_TYPE
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.dfm.DFMManager
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.nio.file.Files
import java.nio.file.Path

val sUriToFileDirectoryPath by lazy { MyApplication.sAppContext.cacheDir.absolutePath.plus(".uriFileTemp").plus(File.separator) }
val sDFMToFileDirectoryPath by lazy { MyApplication.sAppContext.cacheDir.absolutePath.plus(File.separator).plus(".dfmFileTemp").plus(File.separator) }
private const val TAG = "FileUtils"
const val MIN_RECYCLER_SIZE = 100 * 1024 * 1024

/**
 * @param parentDir Parent file.
 * @param fileNameWithoutExt File name with out file extension, Can be an empty string.
 * @param ext File extension with symbol '.', E.g:".apk";
 * No extension can pass a empty value,E.g:""
 */
fun fetchFileName(parentDir: BaseFileBean, fileNameWithoutExt: String, ext: String): BaseFileBean? {
    val parentDirPath = parentDir.mData
    if (parentDirPath.isNullOrEmpty().not()) {
        fetchFileName(parentDirPath!!, fileNameWithoutExt, ext)?.let {
            return PathFileWrapper(it)
        }
    }
    return null
}

fun fetchFileName(parentDir: String, fileNameWithoutExt: String, ext: String): String? {
    if (parentDir.isNotEmpty()) {
        val stringBuilder = StringBuilder()
        val notEmptyName = fileNameWithoutExt.isNotEmpty()
        for (i in 0 until Constants.MAX_PROGRESS) {
            val suffix = "_$i"
            stringBuilder.append(fileNameWithoutExt)
            if (notEmptyName) {
                if (i > 0) {
                    stringBuilder.append(suffix)
                }
                stringBuilder.append(ext)
            } else {
                //if fileNameWithoutExt is empty, the file name is like as ".xxxxx",
                //This file is a file without a suffix,The number should be appended to the end
                stringBuilder.append(ext)
                if (i > 0) {
                    stringBuilder.append(suffix)
                }
            }
            var newFileName = stringBuilder.toString()
            stringBuilder.clear()
            val suffixMaxLength = suffix.length + 1
            if ((i > 0) && (newFileName.toByteArray().size >= NAME_BYTES_LEN)) {
                var newFileNameWithoutExt = fileNameWithoutExt
                var newExt = ext
                if (fileNameWithoutExt.length > suffixMaxLength) {
                    newFileNameWithoutExt =
                        fileNameWithoutExt.substring(0, (fileNameWithoutExt.length - suffixMaxLength))
                } else if (ext.length > suffixMaxLength) {
                    newExt = ext.substring(0, (ext.length - suffixMaxLength))
                }
                newFileName = if (notEmptyName) {
                    stringBuilder.append(newFileNameWithoutExt).append(suffix).append(newExt).toString()
                } else {
                    stringBuilder.append(newFileNameWithoutExt).append(newExt).append(suffix).toString()
                }
                stringBuilder.clear()
                Log.d(TAG, "fetchFileName newName is too long, cut short -> $newFileName")
            }
            val newFile = File(parentDir, newFileName)
            if (JavaFileHelper.safeCheck({ newFile.exists() }, false).not()) {
                return newFile.absolutePath
            }
        }
    }
    return null
}


fun getNewFileNameWhenFileExist(existFileNameList: List<String>, fileNameWithoutExt: String, ext: String): String? {
    val stringBuilder = StringBuilder()
    val notEmptyName = fileNameWithoutExt.isNotEmpty()
    for (i in 0 until Constants.MAX_PROGRESS) {
        val suffix = "_$i"
        stringBuilder.append(fileNameWithoutExt)
        if (notEmptyName) {
            if (i > 0) {
                stringBuilder.append(suffix)
            }
            stringBuilder.append(ext)
        } else {
            stringBuilder.append(ext)
            if (i > 0) {
                stringBuilder.append(suffix)
            }
        }
        var newFileName = stringBuilder.toString()
        stringBuilder.clear()
        val suffixMaxLength = suffix.length + 1
        if ((i > 0) && (newFileName.toByteArray().size >= NAME_BYTES_LEN)) {
            var newFileNameWithoutExt = fileNameWithoutExt
            var newExt = ext
            if (fileNameWithoutExt.length > suffixMaxLength) {
                newFileNameWithoutExt =
                    fileNameWithoutExt.substring(0, (fileNameWithoutExt.length - suffixMaxLength))
            } else if (ext.length > suffixMaxLength) {
                newExt = ext.substring(0, (ext.length - suffixMaxLength))
            }
            newFileName = if (notEmptyName) {
                stringBuilder.append(newFileNameWithoutExt).append(suffix).append(newExt).toString()
            } else {
                stringBuilder.append(newFileNameWithoutExt).append(newExt).append(suffix).toString()
            }
            stringBuilder.clear()
            Log.d(TAG, "fetchFileName newName is too long, cut short -> $newFileName")
        }
        if (!existFileNameList.contains(newFileName)) {
            return newFileName
        } else {
            continue
        }
    }
    return null
}

fun getCompressFileType(baseFileBean: BaseFileBean): Int {
    if (baseFileBean.mData?.endsWith(".7z", true) == true || baseFileBean.mLocalFileUri.toString().endsWith(".7z", true)) {
        return P7ZIP
    }
    if (baseFileBean.mData.isNullOrEmpty() && (baseFileBean.mLocalFileUri == null)) {
        return UNKNOWN_TYPE
    }
    val head = ByteArray(3)
    var fio: InputStream? = null
    if (baseFileBean.mData.isNullOrEmpty()) {
        fio = MyApplication.sAppContext.contentResolver.openInputStream(baseFileBean.mLocalFileUri!!)
    } else {
        fio = FileInputStream(File(baseFileBean.mData))
    }
    try {
        fio?.read(head)
    } catch (e2: Exception) {
        Log.e(TAG, "getCompressFileType failed: ${e2.message}")
        return UNKNOWN_TYPE
    } finally {
        try {
            fio?.close()
        } catch (e: Exception) {
        }
    }
    val headString = ("" + Integer.toHexString(head[0].toInt()) + Integer.toHexString(head[1].toInt())
            + Integer.toHexString(head[2].toInt()))
    Log.d(TAG, "getCompressFileType headString=$headString")
    return FILE_HEADER_MAP[headString] ?: UNKNOWN_TYPE
}

fun hasDrmFile(files: List<out BaseFileBean>?): Boolean {
    if (null == files || files.isEmpty()) {
        return false
    }
    for (file in files) {
        if (file.mLocalType == MimeTypeHelper.DRM_TYPE) {
            return true
        }
    }
    return false
}


fun checkDestStorageSpace(dest: BaseFileBean, srcSize: Long)
        : Pair<Boolean, String> {
    val destPath = dest.mData
    val rootPath: String
    var storage: String = ""
    val context = MyApplication.sAppContext
    when (KtUtils.getStorageByPath(context, destPath)) {
        KtUtils.STORAGE_INTERNAL, KtUtils.STORAGE_INTERNAL_MULTI_APP -> {
            rootPath = VolumeEnvironment.getInternalSdPath(context)
            storage = context.getString(R.string.not_enough_storage_internal)
        }
        KtUtils.STORAGE_EXTERNAL -> {
            rootPath = VolumeEnvironment.getExternalSdPath(context)
            storage = context.getString(R.string.storage_external)
        }
        KtUtils.STORAGE_DMF -> {
            rootPath = DFMManager.getDFSMountPath() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
            storage = DFMManager.getDFSDeviceName() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
        }
        else -> {
            val str: Array<String> = destPath!!.split("/").toTypedArray()
            rootPath = File.separator + str[1] + File.separator + str[2]
        }
    }
    val availableSize = Utils.getStorageAvailableSize(rootPath) - MIN_RECYCLER_SIZE
    Log.d(TAG, "srcSize =$srcSize,availableSize =$availableSize,rootPath =$rootPath")
    return Pair(availableSize < srcSize, storage)
}

@WorkerThread
fun deleteUriTempDirectory() {
    sUriToFileDirectoryPath?.apply {
        Log.d(TAG, "deleteUriTempDirectory mUriToFileDirectoryPath=$this")
        FileDeleteHelper().delete(PathFileWrapper(this))
    }
}

fun getInputStreamByUri(context: Context, uri: Uri): InputStream? {
    return try {
        context.contentResolver.openInputStream(uri)
    } catch (e: Exception) {
        Log.e(TAG, "getInputStreamByUri copy e=$e")
        null
    }
}

@WorkerThread
fun copyToFileByUri(uriInputStream: InputStream?, destPath: Path, useBuffer: Boolean = false): Boolean {
    if (!Files.isDirectory(destPath.parent)) {
        Files.createDirectories(destPath.parent)
    }
    val fileOutputStream = FileOutputStream(destPath.toFile())
    val buffer = ByteArray(COPY_BUF_SIZE)
    var bufferedInputStream: BufferedInputStream? = null
    var bufferedOutputStream: BufferedOutputStream? = null
    try {
        if (useBuffer) {
            bufferedInputStream = BufferedInputStream(uriInputStream, COPY_BUF_SIZE)
            bufferedOutputStream = BufferedOutputStream(fileOutputStream, COPY_BUF_SIZE)
        } else {
            bufferedInputStream = BufferedInputStream(uriInputStream)
            bufferedOutputStream = BufferedOutputStream(fileOutputStream)
        }
        var n = 0
        while (0 < bufferedInputStream.read(buffer).also { n = it }) {
            bufferedOutputStream.write(buffer, 0, n)
        }
        return true
    } catch (e: Exception) {
        Log.e(TAG, "copyToFileByUri copy e=$e")
    } finally {
        try {
            bufferedInputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            bufferedOutputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            fileOutputStream.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            uriInputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
    }
    return false
}


@WorkerThread
fun copyToFileByUri(uri: Uri, destPath: Path, useBuffer: Boolean = false): Boolean {
    if (!Files.isDirectory(destPath.parent)) {
        Files.createDirectories(destPath.parent)
    }
    val fileOutputStream = FileOutputStream(destPath.toFile())
    var uriInputStream: InputStream? = null
    val buffer = ByteArray(COPY_BUF_SIZE)
    var bufferedInputStream: BufferedInputStream? = null
    var bufferedOutputStream: BufferedOutputStream? = null
    try {
        uriInputStream = MyApplication.sAppContext.contentResolver.openInputStream(uri)
        if (useBuffer) {
            bufferedInputStream = BufferedInputStream(uriInputStream, COPY_BUF_SIZE)
            bufferedOutputStream = BufferedOutputStream(fileOutputStream, COPY_BUF_SIZE)
        } else {
            bufferedInputStream = BufferedInputStream(uriInputStream)
            bufferedOutputStream = BufferedOutputStream(fileOutputStream)
        }
        var n = 0
        while (0 < bufferedInputStream.read(buffer).also { n = it }) {
            bufferedOutputStream.write(buffer, 0, n)
        }
        return true
    } catch (e: Exception) {
        Log.e(TAG, "copyToFileByUri copy e=$e")
    } finally {
        try {
            bufferedInputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            bufferedOutputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            fileOutputStream.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
        try {
            uriInputStream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "copyToFileByUri close e=$e")
        }
    }
    return false
}

fun getDisplayNameByString(data: String): String {
    val cut: Int = data.lastIndexOf('/')
    if (cut != -1) {
        return data.substring(cut + 1)
    }
    return ""
}

/**
 * @return true : filePath contains dirPath
 */
fun contains(dirPath: String?, filePath: String?): Boolean {
    var dirPath = dirPath ?: return false
    if (dirPath == filePath) {
        return true
    }
    if (!dirPath.endsWith("/")) {
        dirPath += "/"
    }
    return filePath?.startsWith(dirPath) ?: false
}

fun getAllFilePathFromDirectory(file: File): List<String> {
    val childs: ArrayList<String> = ArrayList()
    childs.addAll(getChildFromFile(file))
    return childs
}

fun getChildFromFile(file: File): List<String> {
    val childPath = ArrayList<String>()
    if (file.isDirectory) {
        JavaFileHelper.listFiles(file)?.forEach {
            if (it.isDirectory) {
                childPath.addAll(getChildFromFile(it))
            } else {
                if (it.name.startsWith(".").not()) {
                    childPath.add(it.absolutePath)
                }
            }
        }
    } else {
        if (file.name.startsWith(".").not()) {
            childPath.add(file.absolutePath)
        }
    }
    return childPath
}

fun createOrExistsFile(file: File?): Boolean {
    if (file == null) return false
    if (file.exists()) return file.isFile
    return if (!createOrExistsDir(file.parentFile)) {
        false
    } else {
        try {
            file.createNewFile()
        } catch (e: IOException) {
            Log.e(TAG, "createOrExistsFile exception", e)
            false
        }
    }
}

private fun createOrExistsDir(file: File?): Boolean {
    return if (file != null) {
        if (file.exists()) {
            file.isDirectory
        } else {
            file.mkdirs()
        }
    } else {
        false
    }
}

fun createFile(filePath: String): File? {
    val file = File(filePath)
    var isSuccess = false
    if (file.exists()) {
        return file
    }
    try {
        isSuccess = file.createNewFile()
    } catch (e: IOException) {
        Log.e(TAG, e.message)
    }
    return if (isSuccess) file else null
}