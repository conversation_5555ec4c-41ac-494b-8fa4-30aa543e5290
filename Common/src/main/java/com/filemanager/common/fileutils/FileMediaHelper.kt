/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.filemanager.common.fileutils

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.RemoteException
import android.os.storage.StorageManager
import android.provider.MediaStore
import android.provider.MediaStore.Files.FileColumns
import android.provider.OpenableColumns
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.bean.OpenAnyAppInfo
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.closeQuietly
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.MediaFileWrapper
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.*
import kotlin.collections.ArrayList

object FileMediaHelper {
    val VIDEO_MEDIA_URI = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
    val AUDIO_MEDIA_URI = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
    val IMAGES_MEDIA_URI = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    val FILE_URI = MediaStore.Files.getContentUri("external")
    const val MEDIA_TYPE_IMAGE = 1
    const val MEDIA_TYPE_AUDIO = 2
    const val MEDIA_TYPE_VIDEO = 3
    const val MEDIA_TYPE_DOC = 10003
    const val MEDIA_TYPE_APK = 10002
    private const val MAX_FILE_LIST_SIZE = 2000
    private const val MAX_FILE_LIST_SIZE_500 = MAX_FILE_LIST_SIZE / 4
    private const val TAG = "FileMediaHelper"
    private const val PARAM_DELETE_DATA = "deletedata"
    private const val SLOW_MOTION_CHECK = "oppo_0slow_motion_hsr"
    private const val NEW_SLOW_MOTION_CHECK = "oppo_0slow_motion_hfr"
    private const val CAMERA_CSHOT_RELATIVE_PATH = "/DCIM/Camera/"
    private const val MYALBUM_CSHOT_RELATIVE_PATH = "/DCIM/MyAlbums/"

    //format for folder and directory
    private const val FORMAT_ASSOCIATION = 0X3001
    private const val COLUMNS_0 = 0
    private const val COLUMNS_1 = 1
    private const val COLUMNS_2 = 2
    private const val COLUMNS_3 = 3
    private const val COLUMNS_4 = 4
    private const val COLUMNS_5 = 5
    private const val COLUMNS_6 = 6
    private const val COLUMNS_7 = 7
    private const val COLUMN_INDEX_IMAGES_LIST_DATA = 0
    private const val COLUMN_INDEX_IMAGES_LIST_DATE_TAKEN = 1
    private const val COLUMN_INDEX_IMAGES_LIST_DATE_ORIENTATION = 2
    private const val COLUMN_INDEX_IMAGES_LIST_ID = 3
    private const val COLUMN_INDEX_IMAGES_LIST_CSHOT_ID = 4
    private const val COLUMN_INDEX_IMAGES_SIZE = 5
    private const val COLUMN_INDEX_IMAGES_DATE_MODIFIED = 6
    private const val COLUMN_INDEX_IMAGES_DISPLAY_NAME = 7
    private val IMAGE_PROJECTION = arrayOf(
            MediaStore.Images.Media.DATA,
            MediaStore.Images.Media.DATE_TAKEN,
            MediaStore.Images.Media.ORIENTATION,
            MediaStore.Images.Media._ID,
            "cshot_id",
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.DISPLAY_NAME
    )

    private const val INDEX_ID = 0
    private const val INDEX_DATA = 1
    private const val INDEX_DISPLAY_NAME = 2
    private const val INDEX_SIZE = 3
    private const val INDEX_DATE_MODIFIED = 4
    private const val INDEX_DATE_MIME_TYPE = 5
    private const val TIME = 1000L

    private val MEDIA_PROJECTION = arrayOf(
        FileColumns._ID,
        FileColumns.DATA,
        FileColumns.DISPLAY_NAME,
        FileColumns.SIZE,
        FileColumns.DATE_MODIFIED,
        FileColumns.MIME_TYPE
    )

    fun getFileUri(context: Context, file: BaseFileBean): Uri? {
        return when {
            file.mLocalFileUri != null -> file.mLocalFileUri
            !TextUtils.isEmpty(file.mData) -> {
                var uri: Uri? = null
                val cr = context.contentResolver
                val projectStrings = arrayOf("_id")
                val where = "_data = ?"
                val dbUri = FILE_URI
                var cursor: Cursor? = null
                try {
                    cursor = cr.query(dbUri, projectStrings, where, arrayOf(file.mData), null)
                    if (cursor != null && cursor.count > 0) {
                        cursor.moveToFirst()
                        val rowId = cursor.getLong(0)
                        uri = ContentUris.withAppendedId(dbUri, rowId)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, e.message)
                } finally {
                    if (null != cursor) {
                        cursor.close()
                        cursor = null
                    }
                }
                uri
            }
            else -> null
        }
    }

    fun queryPathFromUri(context: Context, uri: Uri): String? {
        var path: String? = null
        val column = arrayOf(MediaStore.Audio.Media.DATA)
        var c: Cursor? = null
        try {
            c = context.contentResolver.query(uri, column, null, null, null)
            if (null != c && c.moveToFirst()) {
                path = c.getString(0)
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message);
        } finally {
            if (null != c) {
                c.close()
                c = null
            }
        }
        return path
    }

    fun getDataColumn(context: Context, uri: Uri, selection: String?, selectionArgs: Array<String>?): String? {
        val column = MediaStore.Audio.Media.DATA
        val projection = arrayOf(column)
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(uri, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val columnIndex: Int = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(columnIndex)
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor?.close()
        }
        return null
    }

    fun deleteMediaDBFile(file: BaseFileBean): Boolean {
        val context = MyApplication.sAppContext
        return when {
            file.mData.isNullOrEmpty().not() -> {
                if (!Utils.isOperateDatabase(context, file.mData)) {
                    return true
                }
                var uri = MediaStore.Files.getContentUri("external") ?: return false
                val builder = uri.buildUpon()
                builder.appendQueryParameter(PARAM_DELETE_DATA, "false")
                uri = builder.build()
                var where = ""
                var argsStrings: Array<String>? = null
                if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                    where = "_data like ?"
                    argsStrings = arrayOf(file.mData + File.separator + "%")
                } else {
                    where = "_data = ?"
                    argsStrings = arrayOf(file.mData!!)
                }
                var cursor: Cursor? = null
                try {
                    Log.v(TAG, "uri=" + uri + "|where =" + where + argsStrings[0])
                    if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                        context.contentResolver.delete(uri, "_data = ?", arrayOf(file.mData))
                    }
                    val count = context.contentResolver.delete(uri, where, argsStrings)
                    Log.v(TAG, "count =$count")
                    if (count <= 0) {
                        cursor = context.contentResolver.query(uri, arrayOf("_id"), where, argsStrings,
                                null)
                        return null == cursor || cursor.count == 0
                    }
                } catch (e: Exception) {
                    Log.v(TAG, e.toString())
                    return false
                } finally {
                    cursor?.close()
                }
                true
            }
            else -> false
        }
    }

    private fun getFileTitleInMediaDB(context: Context, file: BaseFileBean): String? {
        var result: String? = null
        var cursor: Cursor? = null
        try {
            cursor = if (file.mLocalFileUri != null) context.contentResolver
                    .query(file.mLocalFileUri!!, arrayOf(MediaStore.MediaColumns.TITLE), null, null, null)
            else {
                val where = "_data = ?"
                context.contentResolver
                        .query(FILE_URI, arrayOf(MediaStore.MediaColumns.TITLE), where, arrayOf(file.mData), null)
            }
            if (cursor != null && cursor.count > 0) {
                cursor.moveToFirst()
                result = cursor.getString(0)
                Log.d(TAG, "title =$result")
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor?.close()
        }
        return result
    }

    fun updateFileNameInMediaDB(context: Context, oldFile: BaseFileBean, newFile: BaseFileBean): Boolean {
        if (oldFile.mData.isNullOrEmpty() || newFile.mData.isNullOrEmpty()) {
            return false
        }
        if (!Utils.isOperateDatabase(context, newFile.mData)) {
            return true
        }

        val uri = getVolumeUri(oldFile.mLocalType, oldFile.mData) ?: return false
        val where = "_data = ?"
        val argsStrings = arrayOf(oldFile.mData)
        val cv = ContentValues()
        val newName = newFile.mDisplayName
        cv.put(MediaStore.MediaColumns.DATA, newFile.mData)
        cv.put(MediaStore.MediaColumns.DISPLAY_NAME, newName)
        // Update new file's mime type
        val oldMimeType = MediaFileCompat.getMimeTypeForFile(oldFile.mData)
        val newMimeType = MediaFileCompat.getMimeTypeForFile(newFile.mData)
        if (!TextUtils.equals(oldMimeType, newMimeType)) {
            Log.d(TAG, "updateFileNameInMediaDB: oldMimeType=$oldMimeType, newMimeType=$newMimeType")
            cv.put(MediaStore.MediaColumns.MIME_TYPE, newMimeType)
        }
        val oldMediaType = if (HiddenFileHelper.isHiddenFile(oldFile.mDisplayName)) {
            FileColumns.MEDIA_TYPE_NONE
        } else {
            MimeTypeHelper.getMediaTypeByType(oldFile.mLocalType, oldFile.mData)
        }
        //If the new file is hidden, Update new file's media type to none
        val newMediaType =
                if (HiddenFileHelper.isHiddenFile(newName)) {
                    FileColumns.MEDIA_TYPE_NONE
                } else {
                    if (oldMediaType != FileColumns.MEDIA_TYPE_NONE) {
                        oldMediaType
                    } else {
                        getMediaType(newFile.mData)
                    }
                }
        if (oldMediaType != newMediaType) {
            cv.put(FileColumns.MEDIA_TYPE, newMediaType)
            Log.d(TAG, "updateFileNameInMediaDB: oldMediaType=${oldMediaType}, newMediaType=$newMediaType")
        }
        var position = -1
        if (newName != null) {
            position = newName.lastIndexOf(".")
        }
        var withoutExtName: String? = null
        if (position > 0) {
            withoutExtName = newName?.substring(0, position)
        } else if (position < 0) {
            withoutExtName = newName
        }
        if (withoutExtName != null) {
            var title = getFileTitleInMediaDB(context, oldFile)
            title = title?.toLowerCase()
            if (title == null || !title.contains(SLOW_MOTION_CHECK) && !title.contains(NEW_SLOW_MOTION_CHECK)) {
                cv.put(MediaStore.MediaColumns.TITLE, withoutExtName)
            }
        }
        var parentName: String? = null
        var bucketId: String? = null
        val parentFile = File(oldFile.mData).parentFile
        if (null != parentFile && parentFile.exists()) {
            parentName = parentFile.name
            bucketId = getBucketId(parentFile.absolutePath)
        }
        if (null != parentName && null != bucketId
                && (uri == VIDEO_MEDIA_URI || uri == IMAGES_MEDIA_URI)) {
            cv.put("bucket_id", newFile.mData)
            cv.put("bucket_display_name", newFile.mDisplayName)
        }
        try {
            val count = context.contentResolver.update(uri, cv, where, argsStrings)
            // maybe uri is wrong for audio,video or image,do again with file uri
            if (count <= 0) {
                Log.d(TAG, "updateFileNameInMediaDB, Uri is wrong for audio/video/image, do again with file uri!")
                return doAgainWithFileUri(context, cv, where, argsStrings)
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
            return doAgainWithFileUri(context, cv, where, argsStrings)
        }
        return true
    }

    private fun getMediaType(path: String?): Int {
        var type = FileColumns.MEDIA_TYPE_NONE
        if (TextUtils.isEmpty(path)) {
            return type
        }
        val mediaFileType = MediaFileCompat.getFileType(path) ?: return type
        when {
            MediaFileCompat.isImageFileType(mediaFileType.fileType) -> type = FileColumns.MEDIA_TYPE_IMAGE

            MediaFileCompat.isAudioFileType(mediaFileType.fileType) -> type = FileColumns.MEDIA_TYPE_AUDIO

            MediaFileCompat.isVideoFileType(mediaFileType.fileType) -> type = FileColumns.MEDIA_TYPE_VIDEO

            MediaFileCompat.isDocFileType(mediaFileType.fileType) -> type = MediaFileCompat.MEDIA_TYPE_DOC
        }
        if (SdkUtils.isAtLeastR().not()) {
            val localType = MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(path))
                    ?: MimeTypeHelper.UNKNOWN_TYPE
            if (MediaFileCompat.isApkMimeType(mediaFileType.mimeType)) {
                type = MediaFileCompat.MEDIA_TYPE_APK
            } else if (MimeTypeHelper.isCompressType(localType)) {
                type = MediaFileCompat.MEDIA_TYPE_COMPRESS
            }
        }
        return type
    }

    private fun doAgainWithFileUri(context: Context, cv: ContentValues, where: String, argsStrings: Array<String?>): Boolean {
        var count = try {
            context.contentResolver.update(FILE_URI, cv, where, argsStrings)
        } catch (e: Exception) {
            Log.d(TAG, "doAgainWithFileUri, but still error! ${e.message}")
            return false
        }
        return count >= 0
    }

    private fun getVolumeUri(type: Int, path: String?): Uri? {
        if (TextUtils.isEmpty(path)) {
            return null
        }
        val file = File(path)
        val volumeName = getVolumeName(file)
        return if (MimeTypeHelper.AUDIO_TYPE == type) {
            MediaStore.Audio.Media.getContentUri(volumeName)
        } else if (MimeTypeHelper.VIDEO_TYPE == type) {
            MediaStore.Video.Media.getContentUri(volumeName)
        } else if (MimeTypeHelper.IMAGE_TYPE == type) {
            MediaStore.Images.Media.getContentUri(volumeName)
        } else {
            MediaStore.Files.getContentUri(volumeName)
        }
    }

    private fun getVolumeName(path: File): String? {
        if (contains(VolumeEnvironment.ANDROID_STORAGE_HEADER, path.absolutePath)) {
            val sm = MyApplication.sAppContext.getSystemService(Context.STORAGE_SERVICE) as? StorageManager
            if (sm != null) {
                val sv = sm.getStorageVolume(path)
                if (sv != null) {
                    return if (sv.isPrimary) {
                        MediaStore.VOLUME_EXTERNAL_PRIMARY
                    } else {
                        val fsUuid = sv.uuid
                        val normalizeUuid = fsUuid?.toLowerCase(Locale.US)
                        checkArgumentVolumeName(normalizeUuid)
                    }
                }
                Log.d(TAG, "getVolume Unknown volume at $path")
            }
        }
        return MediaStore.VOLUME_INTERNAL
    }

    /**
     * Copy MediaStore.checkArgumentVolumeName(String volumeName)
     *
     * @param volumeName
     * @return
     */
    private fun checkArgumentVolumeName(volumeName: String?): String? {
        if (TextUtils.isEmpty(volumeName)) {
            Log.d(TAG, "checkArgumentVolume volume is $volumeName")
            return MediaStore.VOLUME_INTERNAL
        }
        if (MediaStore.VOLUME_INTERNAL == volumeName) {
            return volumeName
        } else if (MediaStore.VOLUME_EXTERNAL == volumeName) {
            return volumeName
        } else if (MediaStore.VOLUME_EXTERNAL_PRIMARY == volumeName) {
            return volumeName
        }

        // When not one of the well-known values above, it must be a hex UUID
        for (element in volumeName!!) {
            return if (element in 'a'..'f' || element in '0'..'9' || element == '-') {
                continue
            } else {
                Log.d(TAG, "checkArgumentVolume Invalid volume: $volumeName")
                MediaStore.VOLUME_INTERNAL
            }
        }
        return volumeName
    }

    private fun getBucketId(path: String?): String? {
        return path?.toLowerCase(Locale.getDefault())?.hashCode()?.toString()
    }

    fun insertFileMediaDB(context: Context, absolutePath: String, scanScene: String) {
        if (absolutePath.isNullOrEmpty() || !Utils.isOperateDatabase(context, absolutePath)) {
            return
        }
        Log.v(TAG, "insertFileMediaDB")
        MediaScannerCompat.sendMediaScanner(absolutePath, scanScene)
    }

    /**
     * Add For Android R.
     * On Android R, the file interface method is time-consuming,
     * so it will not judge and return true directly on R.
     *
     * @param [path]  file path
     * @return return true on R, else return file.exists()
     */
    private fun checkFileExist(path: String): Boolean {
        return if (!SdkUtils.isAtLeastR()) {
            val file = File(path)
            file.exists()
        } else {
            true
        }
    }

    fun getImageFileList(context: Context?, fileList: ArrayList<BaseFileBean>?) {
        Log.d(TAG, "getImageFileList")
        if (!fileList.isNullOrEmpty()) {
            if (isCShotCameraPath(context, fileList[0])) {
                try {
                    val tempFileList = ArrayList<BaseFileBean>()
                    val cshotFileList = ArrayList<BaseFileBean>()
                    for (file in fileList) {
                        if ((file is ImageFileWrapper) && (file.getCShot() > ImageFileWrapper.EMPTY_CSHOT)) {
                            cshotFileList.add(file)
                        } else {
                            tempFileList.add(file)
                        }
                    }
                    getCshotFiles(context, cshotFileList)
                    fileList.clear()
                    fileList.addAll(tempFileList)
                    fileList.addAll(cshotFileList)
                } catch (e: Exception) {
                    Log.e(TAG, "getImageFileList error: " + e.message)
                }
            }
        }
    }

    private fun getCshotFiles(context: Context?, fileList: ArrayList<BaseFileBean>?) {
        if (null == context || null == fileList) {
            return
        }
        val whereArgs = ArrayList<String>()
        val whereClause = StringBuilder()
        var cshotId: Long = -1
        var file: BaseFileBean? = null
        //found all cshot image.
        val size = fileList.size
        for (i in 0 until size) {
            file = fileList[i]
            cshotId = if (file is ImageFileWrapper) {
                file.getCShot()
            } else {
                ImageFileWrapper.EMPTY_CSHOT
            }
            if (whereClause.isNotEmpty()) {
                whereClause.append(",")
            }
            whereClause.append("?")
            whereArgs.add(cshotId.toString())
        }
        //no cshot image found.so return
        if (whereArgs.isEmpty()) {
            return
        }
        val sb = StringBuilder()
        sb.append("cshot_id IN (")
        sb.append(whereClause.toString())
        sb.append(")")
        fileList.clear()
        var cursor: Cursor? = null
        try {
            var selectArg: Array<String?>? = arrayOfNulls(whereArgs.size)
            selectArg = whereArgs.toArray(selectArg)
            cursor = context.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    IMAGE_PROJECTION,
                    sb.toString(), selectArg, null)
            if (cursor != null) {
                Log.d(TAG, "getCshotFiles getCount: " + cursor.count)
                while (cursor.moveToNext()) {
                    try {
                        val path = cursor.getString(COLUMN_INDEX_IMAGES_LIST_DATA)
                        val dateTaken = cursor.getLong(COLUMN_INDEX_IMAGES_LIST_DATE_TAKEN)
                        val orientation = cursor.getInt(COLUMN_INDEX_IMAGES_LIST_DATE_ORIENTATION)
                        val id = cursor.getInt(COLUMN_INDEX_IMAGES_LIST_ID)
                        val cshot = cursor.getLong(COLUMN_INDEX_IMAGES_LIST_CSHOT_ID)
                        val size = cursor.getLong(COLUMN_INDEX_IMAGES_SIZE)
                        var dateModified = cursor.getLong(COLUMN_INDEX_IMAGES_DATE_MODIFIED)
                        val displayName = cursor.getString(COLUMN_INDEX_IMAGES_DISPLAY_NAME)
                        var tempFile: BaseFileBean? = null
                        if (dateModified == 0L) {
                            Log.d(TAG, "dateModified is 0")
                            dateModified = (FileTimeUtil.getFileTime(path)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                        }
                        tempFile = ImageFileWrapper(dateTaken, path, cshot, orientation, id, size, dateModified, displayName)
                        fileList.add(tempFile)
                    } catch (ex: Exception) {
                        Log.e(TAG, "add cshot file error: " + ex.message)
                    }
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "getCshotFiles error: " + ex.message)
        } finally {
            if (null != cursor) {
                cursor.close()
                cursor = null
            }
        }
    }

    fun isCShotCameraPath(context: Context?, file: BaseFileBean?): Boolean {
        if (context == null || file == null) {
            Log.d(TAG, "isCShotCameraPath invalid parameter")
            return false
        }
        val path = file.mData
        if (TextUtils.isEmpty(path)) {
            Log.d(TAG, "isCShotCameraPath path is empty")
            return false
        }
        Log.d(TAG, "isCShotCameraPath path: $path")
        /* note path must like DCIM/Camera/Cshot/ not like DCIM/Camera/Cshot111/ */
        val internalPath = VolumeEnvironment.getInternalSdPath(context)
        if (!TextUtils.isEmpty(internalPath)) {
            if (path!!.startsWith(internalPath + CAMERA_CSHOT_RELATIVE_PATH)
                    || path!!.startsWith(internalPath + MYALBUM_CSHOT_RELATIVE_PATH)) {
                return true
            }
        }
        val externalPath = VolumeEnvironment.getExternalSdPath(context)
        if (!TextUtils.isEmpty(externalPath)) {
            if (path!!.startsWith(externalPath + CAMERA_CSHOT_RELATIVE_PATH)
                    || path!!.startsWith(externalPath + MYALBUM_CSHOT_RELATIVE_PATH)) {
                return true
            }
        }
        return false
    }

    /**
     * 获取可打开uri属性：文件名和大小
     * @param context 上下文
     * @param uri uri
     * @return 返回BaseFileBean
     */
    fun getOpenableProperty(context: Context, uri: Uri?): BaseFileBean {
        val fileBean = BaseFileBean()

        if (uri == null) {
            fileBean.mDisplayName = ""
            fileBean.mSize = 0L
            fileBean.mData = ""
            return fileBean
        }
        val column = arrayOf(OpenableColumns.DISPLAY_NAME, OpenableColumns.SIZE, MediaStore.MediaColumns.DATA)
        var c: Cursor? = null
        try {
            c = context.contentResolver.query(uri, column, null, null, null)
            if (null != c && c.moveToFirst()) {
                fileBean.mDisplayName = c.getString(0)
                fileBean.mSize = c.getLong(1)
                fileBean.mData = c.getString(2)
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            c?.close()
            c = null
        }
        return fileBean
    }

    @JvmStatic
    fun queryInfo(context: Context, filePath: String): OpenAnyAppInfo? {
        val selection = StringBuilder()
        val name = FilenameUtils.getName(filePath)
        selection.append("(")
            .append(FileColumns.DISPLAY_NAME)
            .append(" LIKE '%")
            .append(name)
            .append("')")
        var cursor: Cursor? = null
        var openAnyAppInfo: OpenAnyAppInfo? = null
        try {
            cursor = context.contentResolver.query(FILE_URI, MEDIA_PROJECTION, selection.toString(), null, null)
            while (cursor?.moveToNext() == true) {
                val data = cursor.getString(INDEX_DATA)
                val size = cursor.getLong(INDEX_SIZE)
                var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * TIME
                if (dateModified == 0L) {
                    Log.d(TAG, "dateModified is 0")
                    dateModified = FileTimeUtil.getFileTime(data) ?: 0
                }
                openAnyAppInfo = OpenAnyAppInfo(data, size, dateModified)
            }
        } catch (e: RemoteException) {
            Log.e(TAG, "loadData exception", e)
        } finally {
            cursor?.closeQuietly()
        }
        Log.d(TAG, "loadData openAnyAppInfo:$openAnyAppInfo")
        return openAnyAppInfo
    }

    /**
     * 获取图片的id list通过bundle传给相册，由于bundle传输有大小限制，相册对参数个数做了2000个限制
     * 不包含隐藏文件
     * @param fileList 当前页面的文件列表
     * @return 返回图片id的集合
     */
    @JvmStatic
    fun getMediaImgIds(curFile: BaseFileBean, fileList: List<BaseFileBean>?): ArrayList<String> {
        Log.d(TAG, "getMediaImgIds: fileList size=${fileList?.size}")
        val queryBeanList = ArrayList<BaseFileBean>()
        fileList?.forEach { baseFileBean ->
            if (baseFileBean.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
                // 过滤图片
                queryBeanList.add(baseFileBean)
            }
        }
        if (queryBeanList.isEmpty()) {
            Log.d(TAG, "queryBeanList is empty")
            return ArrayList<String>().apply {
                queryFileMediaIdFromData(curFile)?.let {
                    add(it.toString())
                }
            }
        }
        // 查询
        val mediaIds = getMediaIdFromDataList(curFile, queryBeanList)
        Log.d(TAG, "getMediaImgIds: media size=${mediaIds.size}")

        // 限制列表大小
        limitNumberOfFileList(curFile, mediaIds)
        return mediaIds
    }

    /**
     * 传给相册的图片媒体库id列表，不能超过2000个。以当前点击的文件为中心，截取2000个文件
     * @param curFile 当前点击的文件
     * @param mediaIds 需要限制的列表
     */
    @JvmStatic
    fun limitNumberOfFileList(curFile: BaseFileBean, mediaIds: ArrayList<String>) {
        if (mediaIds.size > MAX_FILE_LIST_SIZE) {
            val curFileId = (queryFileMediaIdFromData(curFile) ?: (mediaIds.size / 2)).toString()
            Log.d(
                TAG,
                "限制之前，文件位置 index=${mediaIds.indexOf(curFileId)} mediaIds size=${mediaIds.size}"
            )
            while (mediaIds.size > MAX_FILE_LIST_SIZE) {
                val index = mediaIds.indexOf(curFileId)
                // 列表前面删除的区间
                var preDeleteIndex = 0
                // 列表后面删除的区间
                var afterDeleteIndex = mediaIds.size
                if (index < MAX_FILE_LIST_SIZE / 2) {
                    afterDeleteIndex = MAX_FILE_LIST_SIZE
                } else if (index > mediaIds.size - MAX_FILE_LIST_SIZE / 2) {
                    preDeleteIndex = mediaIds.size - MAX_FILE_LIST_SIZE
                } else {
                    preDeleteIndex = index - (MAX_FILE_LIST_SIZE / 2)
                    afterDeleteIndex = index + (MAX_FILE_LIST_SIZE / 2)
                }
                if (afterDeleteIndex > 0 && afterDeleteIndex < mediaIds.size) {
                    mediaIds.subList(afterDeleteIndex, mediaIds.size).clear()
                }
                if (preDeleteIndex in 1 until index) {
                    mediaIds.subList(0, preDeleteIndex).clear()
                }
            }
            Log.d(TAG, "限制之后，文件位置 index=${mediaIds.indexOf(curFileId)} mediaIds size=${mediaIds.size}")
        }
    }

    @JvmStatic
    fun isImgAndInMedia(baseFileBean: BaseFileBean): Boolean {
        return if (baseFileBean.mLocalType == MimeTypeHelper.IMAGE_TYPE && baseFileBean.mSize > 0) {
            queryFileMediaIdFromData(baseFileBean)?.let { true } ?: false
        } else false
    }

    private fun queryFileMediaIdFromData(file: BaseFileBean): Int? {
        Log.d(TAG, "getFileMediaId()")
        when (file) {
            is ImageFileWrapper -> return file.mId
            is MediaFileWrapper -> return file.id
        }
        var rowId: Int? = null
        val cr = appContext.contentResolver
        val projectStrings = arrayOf("_id")
        val where = "_data = ?"
        val dbUri = when (MimeTypeHelper.getTypeFromPath(file.mData)) {
            MimeTypeHelper.AUDIO_TYPE -> AUDIO_MEDIA_URI
            MimeTypeHelper.VIDEO_TYPE -> VIDEO_MEDIA_URI
            MimeTypeHelper.IMAGE_TYPE -> IMAGES_MEDIA_URI
            else -> FILE_URI
        }
        var cursor: Cursor? = null
        try {
            cursor = cr.query(dbUri, projectStrings, where, arrayOf(file.mData), null)
            if (cursor != null && cursor.count > 0) {
                cursor.moveToFirst()
                rowId = cursor.getLong(0).toInt()
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor?.close()
        }
        return rowId
    }

    /**
     * @param handleArray 需要查询的图片 mData数组
     * @return 返回查询的结果，排序按照 handleArray顺序 排序
     */
    private fun getResult(handleArray: Array<String>): ArrayList<String> {
        val resultList = ArrayList<String>()
        val cr = appContext.contentResolver
        val projectStrings = arrayOf("_id")
        val where = "_data IN (${handleArray.joinToString(", ") { "?" }}) AND _size > 0"
        val dbUri = FILE_URI
        // 按照提供的顺序对结果排序
        val order = "CASE _data ${
            handleArray.mapIndexed { index, data -> "WHEN '$data' THEN $index" }
                .joinToString(" ")
        } END"
        var cursor: Cursor? = null
        try {
            cursor = cr.query(dbUri, projectStrings, where, handleArray, order)
            while (cursor?.moveToNext() == true) {
                resultList.add(cursor.getString(INDEX_ID))
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor?.close()
        }
        return resultList
    }

    private fun getMediaIdFromDataList(curFile: BaseFileBean, fileList: List<BaseFileBean>): ArrayList<String> {
        val mediaIdAndDataList = ArrayList<String>()
        val fileDataList = mutableListOf<String>().apply {
            fileList.forEach { fileBean -> fileBean.mData?.let { data -> add(data) } }
        }
        val startTime = System.currentTimeMillis()
        /**
         * 1.如果文件列表大于2000条，以点击文件为中心选取2000个文件查询，查询结果添加到 mediaIdAndDataList
         * 2.如果结果不足2000，则先向右取500条数据，将查询后结果添加到 mediaIdAndDataList
         * 3.如果结果还不足2000，则向左取500条数据，将查询后结果添加到 mediaIdAndDataList
         * 4.如果结果依旧不足2000，则重复2、3步骤，直到数据查询完或者满足2000条数据
         */
        val leftArray: ArrayList<String> /*以点击文件为中心选取2000个后剩下的左边数据*/
        val rightArray: ArrayList<String>/*以点击文件为中心选取2000个后剩下的右边数据*/
        val index = fileDataList.indexOf(curFile.mData)
        if (index < 0) {
            /*点击的文件不在被查询的列表里*/
            Log.d(TAG, "点击的文件不在被查询的列表里")
            return ArrayList<String>().apply { queryFileMediaIdFromData(curFile)?.let { add(it.toString()) } }
        }
        if (fileDataList.size > MAX_FILE_LIST_SIZE) {
            rightArray = if (index + MAX_FILE_LIST_SIZE / 2 < fileDataList.size) {
                ArrayList(fileDataList.subList(index + MAX_FILE_LIST_SIZE / 2, fileDataList.size)).apply {
                    fileDataList.subList(index + MAX_FILE_LIST_SIZE / 2, fileDataList.size).clear()
                }
            } else ArrayList()
            leftArray = if (index - MAX_FILE_LIST_SIZE / 2 > 0) {
                ArrayList(fileDataList.subList(0, index - MAX_FILE_LIST_SIZE / 2)).apply {
                    fileDataList.subList(0, index - MAX_FILE_LIST_SIZE / 2).clear()
                }
            } else ArrayList()
        } else {
            val result = getResult(fileDataList.toTypedArray())
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "getMediaIdFromDataList: result size=${mediaIdAndDataList.size} time=${endTime - startTime}")
            return result
        }
        Log.d(TAG, "leftArray size=${leftArray.size} fileDataList size=${fileDataList.size} rightArray size=${rightArray.size}")
        /*1.如果文件列表大于2000条，以点击文件为中心选取2000个文件查询，查询结果添加到 mediaIdAndDataList*/
        mediaIdAndDataList.addAll(getResult(fileDataList.toTypedArray()))
        while (mediaIdAndDataList.size < MAX_FILE_LIST_SIZE && (leftArray.isNotEmpty() || rightArray.isNotEmpty())) {
            if (rightArray.isNotEmpty()) {
                /*2.如果结果不足2000，则先向右取500条数据，将查询后结果添加到 mediaIdAndDataList*/
                val rightIndex = if (rightArray.size > MAX_FILE_LIST_SIZE_500) MAX_FILE_LIST_SIZE_500 else rightArray.size
                mediaIdAndDataList.addAll(getResult(rightArray.subList(0, rightIndex).toTypedArray()))
                rightArray.subList(0, rightIndex).clear()   /*将查询过的数据移除*/
                if (mediaIdAndDataList.size > MAX_FILE_LIST_SIZE) break
            }
            if (leftArray.isNotEmpty()) {
                /*3.如果结果还不足2000，则向左取500条数据，将查询后结果添加到 mediaIdAndDataList*/
                val leftIndex = if (leftArray.size > MAX_FILE_LIST_SIZE_500) leftArray.size - MAX_FILE_LIST_SIZE_500 else leftArray.size
                mediaIdAndDataList.addAll(0, getResult(leftArray.subList(leftIndex, leftArray.size).toTypedArray()))
                leftArray.subList(leftIndex, leftArray.size).clear()   /*将查询过的数据移除*/
            }
        }
        val endTime = System.currentTimeMillis()
        Log.d(TAG, "getMediaIdFromDataList: result size=${mediaIdAndDataList.size} time=${endTime - startTime}")
        return mediaIdAndDataList
    }
}