/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File:UriHelper.kt
 * * Description:the Top Function to help getting uri of files
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * <EMAIL>    2020/01/09   1.0       the Top Function to help getting uri of files
 ****************************************************************/
package com.filemanager.common.fileutils

import android.annotation.SuppressLint
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import androidx.core.content.FileProvider
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants.AUTHORITIES
import com.filemanager.common.constants.KtConstants.AUTHORITIES_FILE
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.UNKNOWN_TYPE
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.provider.IProvider
import org.apache.commons.io.FilenameUtils
import java.io.File

object UriHelper {
    private const val TAG = "UriHelper"
    private val VIDEO_MEDIA_URI = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
    private val AUDIO_MEDIA_URI = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
    private val IMAGES_MEDIA_URI = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    private val FILE_URI = MediaStore.Files.getContentUri("external")

    @JvmStatic
    fun geCategoryUri(category: Int): Uri {
        return when (category) {
            CategoryHelper.CATEGORY_IMAGE -> IMAGES_MEDIA_URI
            CategoryHelper.CATEGORY_AUDIO -> AUDIO_MEDIA_URI
            CategoryHelper.CATEGORY_VIDEO -> VIDEO_MEDIA_URI
            else -> FILE_URI
        }
    }

    @JvmStatic
    fun getFileUri(
        file: BaseFileBean,
        intent: Intent? = null,
        fileType: Int? = null,
        mediaFirstParam: Boolean = true,
        isShortCut: Boolean = false
    ): Uri? {
        if (file.mData.isNullOrEmpty()) {
            Log.d(TAG, "getFileUri: file path or context is null")
            return null
        }

        if (KtUtils.checkIsDfmPath(file.mData)) {
            return file.mLocalFileUri
        }

        intent?.let {
            it.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            it.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        }
        val path = file.mData!!
        var type = fileType ?: MimeTypeHelper.getTypeFromPath(path)
        if (type == UNKNOWN_TYPE) {
            type = MimeTypeHelper.getMediaType(path)
        }
        type = MimeTypeHelper.getTypeFromDrm(appContext, type, path)

        val mediaFirst = (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE)
                || FeatureCompat.sIsExpRom.not() || isShortCut
        val uri = innerGetFileUri(file, mediaFirst && mediaFirstParam)
        Log.d(TAG, "getFileUri: type=$type, path=${file.mData}, uri=$uri")
        return uri
    }

    @JvmStatic
    fun getFileUriForDragDrop(file: BaseFileBean, intent: Intent? = null, fileType: Int? = null): Uri? {
        if (file.mData.isNullOrEmpty()) {
            Log.d(TAG, "getFileUriForDragDrop: file path or context is null")
            return null
        }

        intent?.let {
            it.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            it.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        }
        val path = file.mData!!
        var type = fileType ?: MimeTypeHelper.getTypeFromPath(path)
        if (type == UNKNOWN_TYPE) {
            type = MimeTypeHelper.getMediaType(path)
        }
        type = MimeTypeHelper.getTypeFromDrm(appContext, type, path)
        val uri = innerGetFileUri(file, true)
        Log.d(TAG, "getFileUriForDragDrop: type=$type, path=${file.mData}, uri=$uri")
        return uri
    }

    private fun innerGetFileUri(file: BaseFileBean, mediaFirst: Boolean): Uri? {
        fun getFileMediaUri(file: BaseFileBean): Uri? {
            Log.d(TAG, "getFileMediaUri()")
            when (file) {
                is ImageFileWrapper, is MediaFileWrapper -> {
                    if (file.mLocalFileUri != null) {
                        return file.mLocalFileUri
                    }
                }
            }
            var uri: Uri? = null
            val cr = appContext.contentResolver
            val projectStrings = arrayOf("_id")
            val where = "_data = ?"
            val dbUri = when (MimeTypeHelper.getTypeFromPath(file.mData)) {
                MimeTypeHelper.AUDIO_TYPE -> AUDIO_MEDIA_URI
                MimeTypeHelper.VIDEO_TYPE -> VIDEO_MEDIA_URI
                MimeTypeHelper.IMAGE_TYPE -> IMAGES_MEDIA_URI
                else -> FILE_URI
            }
            var cursor: Cursor? = null
            try {
                cursor = cr.query(dbUri, projectStrings, where, arrayOf(file.mData), null)
                if (cursor != null && cursor.count > 0) {
                    cursor.moveToFirst()
                    val rowId = cursor.getLong(0)
                    uri = ContentUris.withAppendedId(dbUri, rowId)
                }
            } catch (e: Exception) {
                Log.e(TAG, e.message)
            } finally {
                cursor?.close()
            }
            return uri
        }

        return if (mediaFirst) {
            getFileMediaUri(file) ?: getFileProviderUri(file.mData)
        } else {
            getFileProviderUri(file.mData) ?: getFileMediaUri(file)
        }
    }

    fun getFileProviderUri(filePath: String?): Uri? {
        if (filePath.isNullOrEmpty()) {
            return null
        }
        Log.d(TAG, "getFileProviderUri()")
        return when {
            SdkUtils.isAtLeastR() -> {
                FileProvider.getUriForFile(appContext, AUTHORITIES, File(filePath))
            }

            else -> {
                val provider = Injector.injectFactory<IProvider>()
                provider?.getUriForFile(appContext, AUTHORITIES_FILE, File(filePath)) ?: FileProvider.getUriForFile(
                    appContext, AUTHORITIES, File(filePath)
                )
            }
        }
    }

    /**
     * 通过图片uri构建BaseFileBean对象
     */
    @SuppressLint("Range")
    fun getBaseFileBeanFromUri(context: Context?, uri: Uri?): BaseFileBean? {
        if (context == null || uri == null) {
            return null
        }
        var baseFileBeanFileBean: BaseFileBean? = null
        val projection = arrayOf(
            MediaStore.Images.ImageColumns.DATA,
            MediaStore.Images.ImageColumns.DISPLAY_NAME,
            MediaStore.Images.ImageColumns.SIZE,
            MediaStore.Images.ImageColumns.DATE_MODIFIED,
        )
        try {
            val cursor: Cursor? = context.contentResolver.query(uri, projection, null, null, null)
            cursor?.use {
                if (cursor.moveToFirst()) {
                    baseFileBeanFileBean = BaseFileBean()
                    baseFileBeanFileBean?.mData =
                        cursor.getString(cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA))
                    val name =
                        cursor.getString(cursor.getColumnIndex(MediaStore.Images.ImageColumns.DISPLAY_NAME))
                    baseFileBeanFileBean?.mDisplayName = name
                    baseFileBeanFileBean?.mLocalType =
                        MimeTypeHelper.getTypeFromExtension(FilenameUtils.getExtension(name))
                            ?: UNKNOWN_TYPE
                    baseFileBeanFileBean?.mDateModified =
                        cursor.getLong(cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATE_MODIFIED))
                    baseFileBeanFileBean?.mSize =
                        cursor.getLong(cursor.getColumnIndex(MediaStore.Images.ImageColumns.SIZE))
                    baseFileBeanFileBean?.mLocalFileUri = uri
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, e.message)
        }
        Log.d(TAG, baseFileBeanFileBean?.toString())
        return baseFileBeanFileBean
    }
}