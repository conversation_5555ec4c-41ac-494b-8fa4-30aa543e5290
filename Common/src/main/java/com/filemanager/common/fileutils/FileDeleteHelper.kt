/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: FileDeleteHelper
 * * Description: the helper for counting size when delete
 * * Version: 1.0
 * * Date : 2020/8/27
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/8/27       1.0         the helper for counting size when delete
 ****************************************************************/
package com.filemanager.common.fileutils

import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import java.io.File

class FileDeleteHelper {

    companion object {
        private const val TAG = "FileDeleteHelper"
    }

    private var mSize = 0L

    /**
     * @param [size] the size of counting start for file deleting
     * attention : initial valve is 0, don't change if not necessary
     */
    fun setInitSize(size: Long) {
        mSize = size
    }

    /**
     * @return  the size of counting value for file deleted with counting start
     */
    fun getFileDeletedSize(): Long {
        return mSize
    }

    /**
     * @param [fileBean] the BaseFileBean need to delete
     * @return  the size of counting value for file deleted with counting start
     */
    fun delete(fileBean: BaseFileBean): Boolean {
        fun innerDelete(file: File): Boolean {
            JavaFileHelper.listFiles(file)?.forEach {
                if (!innerDelete(it)) {
                    return false
                }
            }
            val tempSize = file.length()
            return file.delete().let {
                if (it) {
                    mSize += tempSize
                    true
                } else {
                    if (KtUtils.checkDfmFileAndDfmDisconnected(file.absolutePath)) {
                        false
                    } else {
                        val exist = file.exists()
                        Log.w(TAG, "delete file failed: exist=$exist, file=${file.name}")
                        !exist
                    }
                }
            }
        }

        if (fileBean.mData.isNullOrEmpty()) {
            Log.w(TAG, "delete failed: file path is null/empty")
            return false
        } else {
            try {
                return innerDelete(File(fileBean.mData))
            } catch (e: StackOverflowError) {
                CustomToast.showShort(R.string.toast_file_name_deep_path)
            }
        }
        return false
    }

}