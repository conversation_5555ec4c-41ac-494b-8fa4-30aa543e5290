/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/2/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.wrapper

import android.os.Build
import androidx.annotation.RequiresApi
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.Log
import java.io.File
import java.nio.file.Files
import java.nio.file.attribute.BasicFileAttributes

/**
 * @NOTE: Because of the file properties interface has performance problem in android R, avoid to
 * init the file properties directly in UI thread.
 */
open class PathFileWrapper : BaseFileBean {
    override var mDisplayName: String? = null
        get() = if (!field.isNullOrEmpty()) {
            field
        } else {
            if (!mData.isNullOrEmpty()) {
                File(mData).name
            } else {
                null
            }
        }
    override var mSize: Long = 0
        get() = kotlin.run {
            if (mNeedInit != null) {
                parseFileProperties()
            }
            field
        }
    override var mDateModified: Long = 0
        get() = kotlin.run {
            if (mNeedInit != null) {
                parseFileProperties()
            }
            field
        }
    override var mLocalType: Int = MimeTypeHelper.UNKNOWN_TYPE
        get() = kotlin.run {
            if (mNeedInit != null) {
                parseFileProperties()
            }
            field
        }

    /**
     * 耗时方法 需要在子线程中调用
     */
    fun getMediaDuration(): Long {
        kotlin.runCatching {
            mMediaDuration = GetMediaDurationUtil.getDuration(this)
        }.onFailure {
            Log.e("PathFileWrapper", "Failed to getDuration", it)
        }
        return mMediaDuration
    }

    @Volatile
    private var mNeedInit: Byte? = 0

    @RequiresApi(Build.VERSION_CODES.O)
    private fun parseFileProperties() {
        mNeedInit = null
        if (!mData.isNullOrEmpty()) {
            var properties: BasicFileAttributes? = null
            try {
                properties = Files.readAttributes(File(mData).toPath(), BasicFileAttributes::class.java)
                properties?.apply {
                    mSize = size()
                    mDateModified = lastModifiedTime().toMillis()
                    mLocalType = if (isDirectory) {
                        MimeTypeHelper.DIRECTORY_TYPE
                    } else {
                        MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName))
                                ?: MimeTypeHelper.UNKNOWN_TYPE
                    }
                }
            } catch (e: Exception) {
                Log.w("PathFileWrapper", "Failed to read attributes, ${e.message}")
            }
        }
    }

    constructor(absolutePath: String) {
        mData = absolutePath
    }

    constructor(absolutePath: String, localType: Int) {
        mData = absolutePath
        mLocalType = localType
    }
}