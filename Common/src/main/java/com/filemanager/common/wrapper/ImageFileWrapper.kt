package com.filemanager.common.wrapper

import android.content.ContentUris
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import org.apache.commons.io.FilenameUtils
import java.io.File

data class AlbumLoadResult<K>(val mResultList: MutableList<ImageFileWrapper>, val mResultMap: HashMap<K, ImageFileWrapper>)
data class WebAlbumLoadResult(
    val mResultList: MutableList<ImageFileWrapper>,
    val mPageNo: Int,
    val mTotal: Int
)
class ImageFileWrapper : BaseFileBean {
    companion object {
        const val EMPTY_CSHOT = 0L
    }

    private var mCShotId: Long
    private var mDateTaken: Long
    var mId: Int
    private var mOrientation: Int

    constructor() {
        mId = 0
        mDateTaken = 0
        mCShotId = 0
        mOrientation = 0
    }

    constructor(dateTaken: Long, pathname: String?, cShot: Long, orientation: Int, id: Int, size: Long, dateModified: Long, displayName: String?) {
        mId = id
        mDateTaken = dateTaken
        mCShotId = cShot
        mOrientation = orientation
        mData = pathname
        mSize = size
        mDateModified = dateModified * 1000
        mDisplayName = displayName
        mLocalFileUri = ContentUris.withAppendedId(FileMediaHelper.IMAGES_MEDIA_URI, mId.toLong())
        mLocalType = MimeTypeHelper.getTypeFromExtension(FilenameUtils.getExtension(pathname))
                ?: MimeTypeHelper.UNKNOWN_TYPE
        mData?.let {
            //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
            if (mSize == 0L) {
                val file = File(it)
                mSize = file.length()
                mDateModified = file.lastModified()
            }
        }
    }

    @Deprecated("cShotId may be not able to get in MediaStore with Android R")
    fun getCShot(): Long {
        return mCShotId
    }

    fun getId(): Int {
        return mId
    }

    fun getDateTaken(): Long {
        return mDateTaken
    }

    override fun getOrientation(): Int {
        return mOrientation
    }
}