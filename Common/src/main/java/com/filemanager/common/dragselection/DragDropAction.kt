/***********************************************************
 ** Copyright (C), 2008-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DragDropAction.kt
 ** Description: drag drop action
 ** Version: 1.0
 ** Date: 2024/6/5
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.dragselection

import android.app.Activity
import android.net.Uri
import com.filemanager.common.dragselection.action.DragParseData
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain
import org.koin.core.component.KoinComponent

object DragDropAction : KoinComponent {

    @JvmStatic
    fun getMainTab(activity: Activity): Int {
        return Injector.injectFactory<IMain>()?.getMainTab(activity) ?: 0
    }

    @JvmStatic
    fun getMainCategoryType(activity: Activity): Int {
        return Injector.injectFactory<IMain>()?.getMainCategoryType(activity) ?: 0
    }

    @JvmStatic
    fun saveFile(
        activity: Activity,
        listUris: ArrayList<Uri>,
        listTexts: ArrayList<String>,
        listHtmls: ArrayList<String>,
        destPath: String
    ) {
        val parseData = DragParseData()
        parseData.uriList.addAll(listUris)
        parseData.textList.addAll(listTexts)
        parseData.htmlList.addAll(listHtmls)
        Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
    }
}