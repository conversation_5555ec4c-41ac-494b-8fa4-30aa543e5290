/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DragDropScanner.kt
 ** Description: scanner for dragdrop share
 ** Version: 1.0
 ** Date : 2020/05/27
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/27    1.0     create
 ****************************************************************/

package com.filemanager.common.dragselection

import android.content.ClipData
import android.content.ClipDescription
import android.content.Context
import android.net.Uri
import android.os.PersistableBundle
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants.IS_FOLDER
import com.filemanager.common.constants.CommonConstants.KEY_FOLDER_PATH_LIST
import com.filemanager.common.constants.CommonConstants.KEY_FOLDER_PATH_SIZE
import com.filemanager.common.constants.CommonConstants.KEY_NONE_MEDIA_PATH_LIST
import com.filemanager.common.constants.CommonConstants.MAX_SEND_COUNT
import com.filemanager.common.constants.KtConstants.FILE_MANAGER_URI_PREFIX
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_SUCCESS
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.dragdrop.DragDropScanner
import com.oplus.dropdrag.dragdrop.DragScanResult
import com.oplus.dropdrag.dragdrop.DragScannerListener

/**
 * intermediate data class for drag scan
 * @param items item list has been scanned
 * @param clipTypes mimetype list for items
 * @param drmCount drm file count
 */
data class ScanData(val items: ArrayList<ClipData.Item>,
                    val clipTypes: ArrayList<String>,
                    var drmCount: Int, var statusCode: Int = STATUS_SUCCESS)

open class FileDragDropScanner(
    context: Context,
    scannerListener: DragScannerListener?,
    layoutType: SelectionTracker.LAYOUT_TYPE?,
    isGridImg: Boolean
) : DragDropScanner<BaseFileBean>(context, scannerListener) {
    companion object {
        private const val TAG = "FileDragDropScanner"
        /**
         * status code imply scan success can send the file
         */
        const val STATUS_SUCCESS = 0

        /**
         * status code imply all file is drm file
         */
        const val ERROR_DRM_FILE = 1

        /**
         * status code imply no file can send to target APP
         */
        const val ERROR_EMPTY_FOLDER = 2

        /**
         * status code imply Activity is finished or destroyed
         */
        const val ERROR_CONTEXT_INVALID = 3

        /**
         * status code imply items count reach to limit
         */
        const val ERROR_REACH_MAX_COUNT = 4
        /**
         * status code imply scan success, is img in grid mode or recent
         */
        const val STATUS_GRID_IMG = 5
        /**
         * status code imply scan success, is single img in recent
         */
        const val STATUS_RECENT_SINGLE_IMG = 6
        /**
         * status code imply scan success, is grid mode
         */
        const val STATUS_GRID = 7
        /**
         * status code imply scan success, is list mode
         */
        const val STATUS_LIST = 8
        /**
         * status code imply scan success, is img in recent
         */
        const val STATUS_RECENT_IMG = 9
    }

    private var mPaths: ArrayList<String>? = null
    private val mLayoutType = layoutType
    private val isGridImg = isGridImg

    fun addPaths(paths: List<String>?): Boolean {
        if (mPaths != null) {
            Log.w(TAG, "addPaths already add a task")
            return false
        }

        if (!paths.isNullOrEmpty()) {
            mPaths = ArrayList<String>()
            mPaths?.addAll(paths)
            return true
        }

        Log.d(TAG, "addPaths INVALID PARAMETER")
        return false
    }

    override fun scanData(): DragScanResult? {
        try {
            when {
                mPaths != null -> {
                    return scanPaths()
                }
                mDataList != null -> {
                    return scanFileBeans()
                }
                else -> {
                    Log.w(TAG, "doInBackground nothing to do")
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground error: $ex")
        }
        return null
    }

    private fun scanPaths(): DragScanResult? {
        val files = ArrayList<BaseFileBean>()
        var file: BaseFileBean? = null
        for (path in mPaths!!) {
            if (path.isNullOrEmpty()) {
                continue
            }

            file = PathFileWrapper(path)
            files.add(file)
        }

        return innerScanFiles(files)
    }

    private fun scanFileBeans(): DragScanResult {
        return innerScanFiles(mDataList!!)
    }

    @Suppress("LongMethod")
    @VisibleForTesting
    open fun innerScanFiles(files: ArrayList<BaseFileBean>): DragScanResult {
        val clipItems = ArrayList<ClipData.Item>()
        val clipTypes = ArrayList<String>()
        val scanData = ScanData(clipItems, clipTypes, 0)
        val grantUriPermissionList = arrayListOf<Uri?>()
        val folderPathList = ArrayList<String>()
        val noneMediaPathList = ArrayList<String>()
        var isHasFolder = false
        for (file in files) {
            if (!JavaFileHelper.exists(file)) {
                Log.w(TAG, "innerscanFiles FILE not exist ")
                continue
            }
            Log.d(TAG, "innerscanFiles file.type : ${file.mLocalType} ")
            if (file.mLocalType == MimeTypeHelper.DRM_TYPE) {
                // skip the drm file
                scanData.drmCount++
                continue
            } else if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                // skip the folder
                file.mData?.let { folderPathList.add(it) }
                traversalFile(file, scanData, grantUriPermissionList, folderPathList, noneMediaPathList)
                isHasFolder = true
                when (scanData.statusCode) {
                    ERROR_REACH_MAX_COUNT -> {
                        return DragScanResult(ERROR_REACH_MAX_COUNT, null,
                                null, null, null, null)
                    }
                    ERROR_CONTEXT_INVALID -> {
                        return DragScanResult(ERROR_CONTEXT_INVALID, null,
                                null, null,
                                null, null)
                    }
                }
                continue
            }

            when (file.mLocalType) {
                MimeTypeHelper.UNKNOWN_TYPE -> {
                    DragUtils.getMimeTypeByPath(file, scanData.clipTypes)
                }
                else -> {
                    scanData.clipTypes.add(DragUtils.getMimeTypeByType(file.mLocalType))
                }
            }

            val uri = generateUriByFile(file, scanData, grantUriPermissionList)
            addNoneMediaPath(uri, file, noneMediaPathList)
            // if items count reach MAX_COUNT,just reutrn right now
            if (checkMaxCount(scanData.items.size)) {
                return DragScanResult(ERROR_REACH_MAX_COUNT, null,
                        null, null, null, null)
            }
        }

        if (scanData.items.isEmpty() && folderPathList.isEmpty()) {
            Log.w(TAG, "doInBackground clipItem is NULL ")
            grantUriPermissionList.clear()
            return DragScanResult(if (scanData.drmCount == 0) {
                ERROR_EMPTY_FOLDER
            } else {
                ERROR_DRM_FILE
            }, null, null,
                    null, null, null)
        }

        val isList: Boolean
        if (mLayoutType == null) {
            return DragScanResult(ERROR_CONTEXT_INVALID, null,
                null, null,
                null, null)
        } else {
            isList = (mLayoutType == SelectionTracker.LAYOUT_TYPE.LIST)
        }
        // build the detail and cover icon
        val description = ClipDescription(
                "", clipTypes.toTypedArray())
        PCConnectAction.grantUriPermissionsForPad(grantUriPermissionList)
        val bundle = PersistableBundle()
        bundle.putBoolean(IS_FOLDER, isHasFolder)
        val folderPathArray = folderPathList.toTypedArray()
        bundle.putStringArray(KEY_FOLDER_PATH_LIST, folderPathArray)
        bundle.putInt(KEY_FOLDER_PATH_SIZE, folderPathList.size)
        val noneMediaPathArray = noneMediaPathList.toTypedArray()
        bundle.putStringArray(KEY_NONE_MEDIA_PATH_LIST, noneMediaPathArray)
        description.extras = bundle

        return if (isGridImg) {
            DragScanResult(STATUS_GRID_IMG,
                DragUtils.createClipData(description, clipItems), null, null, clipItems.size, null
            )
        } else if (isList) {
            DragScanResult(STATUS_LIST,
                DragUtils.createClipData(description, clipItems), null, null, clipItems.size, null)
        } else {
            DragScanResult(STATUS_GRID,
                DragUtils.createClipData(description, clipItems), null, null, clipItems.size, null)
        }
    }

    fun generateUriByFile(file: BaseFileBean, scanData: ScanData, grantUriPermissionList: ArrayList<Uri?>): Uri? {
        if (file.mIsDirectory) {
            return null
        }
        val fileUri = UriHelper.getFileUriForDragDrop(file)
        Log.d(TAG, "doInBackground fileUri : $fileUri ")
        fileUri?.let {
            scanData.items.add(ClipData.Item(fileUri))
        }
        grantUriPermissionList.add(fileUri)
        return fileUri
    }

    private fun addNoneMediaPath(uri: Uri?, file: BaseFileBean, noneMediaPathList: ArrayList<String>) {
        if (uri != null && DropUtil.isFileManagerUri(uri.toString())) {
            file.mData?.let { noneMediaPathList.add(it) }
        }
    }

    /**
     * @param fileBean filewrap parameters
     * @param clipDataItems ClipData Items list
     */
    @VisibleForTesting
    fun traversalFile(
        fileBean: BaseFileBean,
        scanData: ScanData,
        grantUriPermissionList: ArrayList<Uri?>,
        folderPathList: ArrayList<String>,
        noneMediaPathList: ArrayList<String>
    ) {
        val files = JavaFileHelper.listFileBeans(fileBean, excludeHideFile = HiddenFileHelper.isNeedShowHiddenFile().not()) ?: return
        var file: BaseFileBean? = null
        for (i in files.indices) {
            file = files[i]
            if ((file == null) || !JavaFileHelper.exists(file)) {
                continue
            }

            val displayName = file.mDisplayName
            if (displayName.isNullOrEmpty()) {
                continue
            }

            if (file.mLocalType == MimeTypeHelper.DRM_TYPE) {
                // skip the drm file
                scanData.drmCount++
                continue
            } else if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                // skip the folder file
                file.mData?.let { folderPathList.add(it) }
                traversalFile(file, scanData, grantUriPermissionList, folderPathList, noneMediaPathList)
                continue
            }

            when (file.mLocalType) {
                MimeTypeHelper.UNKNOWN_TYPE -> {
                    DragUtils.getMimeTypeByPath(file, scanData.clipTypes)
                }
                else -> {
                    scanData.clipTypes.add(DragUtils.getMimeTypeByType(file.mLocalType))
                }
            }

            val uri = generateUriByFile(file, scanData, grantUriPermissionList)
            addNoneMediaPath(uri, file, noneMediaPathList)
            // if items count reach MAX_COUNT,just reutrn right now
            if (checkMaxCount(scanData.items.size)) {
                scanData.statusCode = ERROR_REACH_MAX_COUNT
                return
            }
        }
    }

    @VisibleForTesting
    fun checkMaxCount(size: Int) = size > MAX_SEND_COUNT
}