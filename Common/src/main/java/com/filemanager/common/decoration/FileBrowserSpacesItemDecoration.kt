/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/9/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseItemDecoration
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Utils

class FileBrowserSpacesItemDecoration(spanCount: Int, spacing: Int, includeEdge: Boolean, edgeSpace: Int = 0, vertical: Int = spacing, verticalFirst: Int = 0)
    : BaseItemDecoration() {

    private var mSpace = spacing
    private var mIncludeEdge = includeEdge
    private var mLeftEdgeSpace: Int = edgeSpace
    private var mRightEdgeSpace: Int = edgeSpace
    private var mVertical: Int = vertical
    private var mVerticalFirst: Int = verticalFirst
    private val mIsRtl by lazy { Utils.isRtl() }

    init {
        mSpanCount = spanCount
    }

    override fun updateSpanCount() {
        mLeftEdgeSpace = FileImageVHUtils.getListLeftMargin()
        mRightEdgeSpace = FileImageVHUtils.getListRightMargin()
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        if (mSpanCount > 1) {
            var position = parent.getChildViewHolder(view).adapterPosition // item position
            if (position == RecyclerView.NO_POSITION) {
                val oldPosition = parent.getChildViewHolder(view).oldPosition
                if (oldPosition == RecyclerView.NO_POSITION) return
                position = oldPosition
            }
            val layoutManager = parent.layoutManager as GridLayoutManager
            val column = layoutManager.spanSizeLookup.getSpanIndex(position, layoutManager.spanCount)
            val row = layoutManager.spanSizeLookup.getSpanGroupIndex(position, layoutManager.spanCount)
            if (mIncludeEdge) {
                outRect.left = mSpace - column * mSpace / mSpanCount // spacing - column * ((1f / spanCount) * spacing)
                outRect.right = (column + 1) * mSpace / mSpanCount // (column + 1) * ((1f / spanCount) * spacing)
                if (position < mSpanCount) { // top edge
                    outRect.top = mVertical
                }
                outRect.bottom = mVertical // item bottom
            } else {
                val viewType = parent.adapter?.getItemViewType(position)
                if ((BaseFileBean.TYPE_LABEL_FILE == viewType) || (BaseFileBean.TYPE_FILE_LIST_HEADER == viewType)
                    || (BaseFileBean.TYPE_FILE_AD == viewType) || (BaseFileBean.TYPE_DOC_GROUP_TITLE == viewType)) {
                    return
                } else {
                    val mEachSpace = (mLeftEdgeSpace + mRightEdgeSpace + (mSpanCount - 1) * mSpace) / mSpanCount
                    val diff = ((mEachSpace - mLeftEdgeSpace) - mRightEdgeSpace) / (mSpanCount - 1)
                    val left = (column + 1 - 1) * diff + mLeftEdgeSpace
                    val right = mEachSpace - left
                    if (mIsRtl) {
                        when (row) {
                            0 -> outRect.set(right, mVerticalFirst, left, mVertical)
                            else -> outRect.set(right, 0, left, mVertical)
                        }
                    } else {
                        when (row) {
                            0 -> outRect.set(left, mVerticalFirst, right, mVertical)
                            else -> outRect.set(left, 0, right, mVertical)
                        }
                    }
                }
            }
        }
    }
}