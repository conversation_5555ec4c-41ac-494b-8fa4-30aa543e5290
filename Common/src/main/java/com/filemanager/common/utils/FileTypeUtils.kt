/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileTypeUtils
 * * Description : file Type Util
 * * Version     : 1.0
 * * Date        : 2023/02/02
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   chao.xue                 2023/02/02       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import org.apache.commons.io.FilenameUtils
import java.util.regex.Pattern

object FileTypeUtils {

    /**
     * .：表示匹配任意字符,\\. 表示“.”
     * +：表示匹配一次或者更多
     * *：表示匹配至少一次或者没有
     * ?：表示匹配一次或者没有
     * $：表示从结尾的位置开始匹配
     * 匹配以 *.apk 或者 *.apk.x 格式结尾的路径
     */
    private const val ALL_APK_REGEX = ".+(\\.apk)(\\.[0-9]+[0-9]*)?$"
    private val allApkPattern = Pattern.compile(ALL_APK_REGEX, Pattern.CASE_INSENSITIVE)

    /**
     * 匹配以 *.apk.x 格式结尾的路径
     */
    private const val APK_X_REGEX = ".+(\\.apk)(\\.[0-9]+[0-9]*)$"
    private val apkXPattern = Pattern.compile(APK_X_REGEX, Pattern.CASE_INSENSITIVE)


    /**
     * 匹配以 .apk 格式结尾的路径
     */
    private const val REAL_APK_REGEX = ".+(\\.apk)$"
    private val realApkPattern = Pattern.compile(REAL_APK_REGEX, Pattern.CASE_INSENSITIVE)

    /**
     * 获取文件名后缀
     */
    @JvmStatic
    fun getExtension(path: String?): String? {
        if (path == null) {
            return null
        }
        // 支持文件随心开，则 .apk.x 格式也识别为.apk格式
        if (isApkXType(path)) {
            return "apk"
        }
        return FilenameUtils.getExtension(path)
    }

    /**
     * 判断路径是apkX类型
     * 则 .apk.x 格式也识别为.apk格式
     */
    @JvmStatic
    fun isApkType(path: String?): Boolean {
        if (path == null) {
            return false
        }
        // 忽略大小写
        val matcher = allApkPattern.matcher(path)
        return matcher.matches()
    }

    /**
     * 判断路径是apkX类型
     * 则 .apk.x 格式也识别为.apk格式, 只识别.apk.x 格式
     */
    @JvmStatic
    fun isApkXType(path: String): Boolean {
        // 忽略大小写
        val matcher = apkXPattern.matcher(path)
        return matcher.matches()
    }
}