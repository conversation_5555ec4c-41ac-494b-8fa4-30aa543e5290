/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : HighlightUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/10/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/10/17       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import androidx.annotation.ColorInt
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.R

object HighlightUtil {

    private const val TAG = "HighlightUtil"
    private const val HIGHLIGHT_DURATION_MILLIS = 250L
    private const val HIGHLIGHT_FADE_OUT_DURATION = 500L
    private const val HIGHLIGHT_FADE_IN_DURATION = 100L

    @ColorInt
    private var highlightColor: Int = MyApplication.sAppContext.getColor(R.color.highlight_color)

    @ColorInt
    private var normalBackgroundColor: Int = MyApplication.sAppContext.getColor(R.color.normal_color)

    private var fadeInLoop: ValueAnimator? = null
    private var fadeOutLoop: ValueAnimator? = null
    var highlightPosition: Int = RecyclerView.NO_POSITION

    @JvmStatic
    private fun addHighlightBackground(holder: RecyclerView.ViewHolder) {
        val v = holder.itemView
        v.setTag(R.id.item_highlighted, true)
        val colorFrom: Int = normalBackgroundColor
        val colorTo: Int = highlightColor
        fadeInLoop = ValueAnimator.ofObject(
            ArgbEvaluator(), colorFrom, colorTo
        ).apply {
            duration = HIGHLIGHT_FADE_IN_DURATION
            addUpdateListener { animator: ValueAnimator -> v.setBackgroundColor(animator.animatedValue as Int) }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    removeHighlightBackground(holder)
                    Log.d(TAG, "addHighlightBackground onAnimationEnd")
                }
            })
            start()
        }
        Log.d(TAG, "AddHighlight: starting fade in animation")
        holder.setIsRecyclable(false)
    }

    @JvmStatic
    private fun removeHighlightBackground(holder: RecyclerView.ViewHolder) {
        val v = holder.itemView
        if (java.lang.Boolean.TRUE != v.getTag(R.id.item_highlighted)) {
            // Not highlighted, no-op
            Log.d(TAG, "RemoveHighlight: Not highlighted - skipping")
            return
        }
        val colorFrom: Int = highlightColor
        val colorTo: Int = normalBackgroundColor
        v.setTag(R.id.item_highlighted, false)
        fadeOutLoop = ValueAnimator.ofObject(
            ArgbEvaluator(), colorFrom, colorTo
        ).apply {
            duration = HIGHLIGHT_FADE_OUT_DURATION
            startDelay = HIGHLIGHT_DURATION_MILLIS
            addUpdateListener { animator: ValueAnimator -> v.setBackgroundColor(animator.animatedValue as Int) }
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // Animation complete - the background is now white. Change to mNormalBackgroundRes so it is white and has ripple on touch.
                    v.setBackgroundResource(R.drawable.select_list_item_background_selector)
                    holder.setIsRecyclable(true)
                    Log.d(TAG, "removeHighlightBackground onAnimationEnd")
                }
            })
            start()
        }
        Log.d(TAG, "Starting fade out animation")
    }

    @JvmStatic
    private fun setAnimColor(context: Context) {
        highlightColor = context.getColor(R.color.highlight_color)
        normalBackgroundColor = context.getColor(R.color.normal_color)
    }

    @JvmStatic
    fun addItemHighlight(context: Context, holder: RecyclerView.ViewHolder, position: Int) {
        if (highlightPosition != position) {
            return
        }
        Log.d(TAG, "addItemHighlight position:$position")
        highlightPosition = RecyclerView.NO_POSITION
        setAnimColor(context)
        addHighlightBackground(holder)
    }

    @JvmStatic
    fun endAnimation() {
        Log.d(TAG, "endAnimation fadeInLoop?.isStarted = ${fadeInLoop?.isStarted}  fadeOutLoop?.isStarted = ${fadeOutLoop?.isStarted}")
        if (fadeInLoop?.isStarted == true || fadeOutLoop?.isStarted == true) {
            fadeInLoop?.end()
            fadeOutLoop?.end()
            Log.d(TAG, "endAnimation")
        }
    }

    @JvmStatic
    fun cancelAnimation() {
        fadeInLoop?.cancel()
        fadeOutLoop?.cancel()
        fadeInLoop = null
        fadeOutLoop = null
        Log.d(TAG, "cancelAnimation")
    }
}