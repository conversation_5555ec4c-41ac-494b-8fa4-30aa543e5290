/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  - 
 * * Description : romUpdate file for app config
 * * Version     : 1.0
 * * Date        : 2017/8/30
 * * Author      : <PERSON>@Apps.FileManager
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/8/30  1.0        build this module
 ***********************************************************************/
package com.filemanager.common.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.content.res.AssetManager;
import android.database.Cursor;
import android.net.Uri;
import android.util.JsonReader;

import com.filemanager.common.helper.VolumeEnvironment;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class CategoryAppConfig {
    private static final String TAG = "CategoryAppConfig";
    public static final String APP_CONFIG_VERSION = "app_config_version";
    public static final String APP_CONFIG = "filemanager_category_view_app_config";
    public static final String FOLDER_NOTES_NEARME_URI = "content://com.nearme.romupdate.provider.db/update_list";

    public static final String CATEGORY_VIEW_APP_CONFIG_DIR = "shared_prefs";
    public static final String CATEGORY_VIEW_APP_CONFIG_FILE_NAME = "filemanager_category_view_app_config.xml";

    /*
    local version for filemanager_category_view_app_config, if filemanager_category_view_app_config update,must it
     */
    private static final int LOCAL_APP_CONFIG_VERSION = **********;

    public static void initialize(Context context) {
        final int localVersion = PreferencesUtils.getInt(APP_CONFIG, APP_CONFIG_VERSION, 0);
        int xmlVersion = localVersion;

        String[] projection = new String[] { "version", "binary" };
        byte[] data = null;
        ContentResolver resolver = context.getContentResolver();
        Cursor cursor = null;
        try {
            cursor = resolver.query(Uri.parse(FOLDER_NOTES_NEARME_URI), projection,
                    "filtername=" + "\"" + APP_CONFIG + "\"", null, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                data = cursor.getBlob(1);
                xmlVersion = cursor.getInt(0);
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
                cursor = null;
            }
        }

        Log.d(TAG, "initialize xmlVer = " + xmlVersion + ", localVer = " + localVersion);
        if ((data != null) && ((localVersion < xmlVersion) || ((localVersion == xmlVersion) && (xmlVersion == 0)))) {
            try {
                File file = new File(VolumeEnvironment.getDataDirPath(context, CATEGORY_VIEW_APP_CONFIG_DIR, CATEGORY_VIEW_APP_CONFIG_FILE_NAME));
                if (file.exists()) {
                    if (!file.delete()) {
                        Log.e(TAG, "delete error");
                        return;
                    }
                }
                final ByteArrayInputStream in = new ByteArrayInputStream(data);
                parseJson(xmlVersion, in);
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
                loadLocalDefaultAppConfig(context, localVersion);
            }
        }  else if (localVersion < LOCAL_APP_CONFIG_VERSION) {
            Log.d(TAG, "initialize config file ver = " + LOCAL_APP_CONFIG_VERSION);
            loadLocalDefaultAppConfig(context, LOCAL_APP_CONFIG_VERSION);
        }
    }

    private static void loadLocalDefaultAppConfig(Context context, final int localVersion) {
        AssetManager assetManager = context.getAssets();
        InputStream in = null;
        try {
            Log.d(TAG, "open filemanager_category_view_app_config");
            in = assetManager.open("filemanager_category_view_app_config");
            parseJson(localVersion, in);
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    // ignore
                }
            }
        }
    }

    private static void parseJson(final int localVersion, InputStream in) {
        BufferedReader bufferReader = null;
        JsonReader jsonReader = null;
        try {
            bufferReader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8));
            jsonReader = new JsonReader(bufferReader);
            jsonReader.beginArray();
            Map<String, String> map = new HashMap<>();
            while (jsonReader.hasNext()) {
                jsonReader.beginObject();
                boolean firstApp = true;
                String region = "";
                String packagenames = "";
                String appPaths = "";
                while (jsonReader.hasNext()) {
                    String name = jsonReader.nextName();
                    String value = jsonReader.nextString();
                    if ("region".equals(name)) {
                        region = value;
                    } else {
                        if (firstApp) {
                            packagenames += name;
                        } else {
                            packagenames += "#" + name;
                        }
                        appPaths = value;
                        map.put(name, appPaths);
                        firstApp = false;
                    }
                }
                map.put(region, packagenames);
                jsonReader.endObject();
            }
            PreferencesUtils.putAll(APP_CONFIG, map);
            jsonReader.endArray();
            saveXmlVersion(localVersion);
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
            rollbackVersion();
        } finally {
            if (bufferReader != null) {
                try {
                    jsonReader.close(); // this method also close bufferReader
                } catch (IOException e) {
                    Log.e(TAG, e.getMessage());
                }
            }
        }
        Log.d(TAG, "parseJson return result");
    }

    private static void rollbackVersion() {
        final int localVersion = PreferencesUtils.getInt(APP_CONFIG, APP_CONFIG_VERSION, 0);
        saveXmlVersion(localVersion - 1);
    }

    private static void saveXmlVersion(int xmlVersion) {
        PreferencesUtils.put(APP_CONFIG, APP_CONFIG_VERSION, xmlVersion);
    }
}
