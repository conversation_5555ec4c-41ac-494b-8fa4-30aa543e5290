/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: EncryptViewUtils
 ** Description: main page encrypt view utils
 ** Version: 1.0
 ** Date : 2022/7/22
 ** Author: <EMAIL>
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * chao.xue  2022/7/25      1.0     create
 ****************************************************************/
package com.filemanager.common.utils

import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants

object EncryptViewUtils {

    /**
     * @attention set [mIsNeedShowEncryptBox] value when change the preference NEED_SHOW_HIDDEN_FILE
     */
    @JvmStatic
    var mIsNeedShowEncryptBox: Boolean? = null
        get() {
            if (field == null) {
                field = PreferencesUtils.getBoolean(
                    key = CommonConstants.NEED_SHOW_ENCRYPT_BOX,
                    default = true
                )
                PreferencesUtils.put(key = CommonConstants.NEED_SHOW_ENCRYPT_BOX, value = field)
            }
            return field
        }

    @JvmStatic
    fun isNeedShowEncryptBox(): Boolean {
        return mIsNeedShowEncryptBox!!
    }

    /**
     * 判断系统是否支持
     * os13去掉一加的判断，在os12及以下仍然保留一加的判断
     *
     * @param isOnePlus 是否是一加项目
     * @return
     */
    @JvmStatic
    fun isSupportEncryption(isOnePlus: Boolean): Boolean {
        return (SdkUtils.isAtLeastOS13() || isOnePlus) && FeatureCompat.sIsSupportEncryption
    }

    /**
     * 是否在首页显示私密保险箱
     * 判断系统是否支持，如果系统是13及以上还需判断设置项为打开
     *
     * @return
     */
    @JvmStatic
    fun isShowEncryptView(): Boolean {
        return isSupportEncryption(KtAppUtils.isOnePlus) && (if (SdkUtils.isAtLeastOS13()) isNeedShowEncryptBox() else true)
    }
}