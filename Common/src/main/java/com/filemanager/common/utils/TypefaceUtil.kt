/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TypefaceUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/10/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/10/26       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.graphics.Typeface

object TypefaceUtil {

    private const val TAG = "TypefaceUtil"

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    val mediumTypeface: Typeface by lazy {
        try {
            Typeface.create("sans-serif-medium", Typeface.NORMAL)
        } catch (e: Exception) {
            Log.e(TAG, "get mediumTypeface error, the e is $e")
            Typeface.DEFAULT
        }
    }
}