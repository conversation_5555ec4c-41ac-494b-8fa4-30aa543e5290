/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils.FileEmptyUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.view.View
import com.filemanager.common.R
import com.filemanager.common.helper.ViewHelper

object FileEmptyUtils {

    const val SEARCH_EMPTY_ANIMATION_FILE = "empty_search.json"
    const val FILE_EMPTY_ANIMATION_JSON = "empty_file.json"
    const val LABEL_EMPTY_ANIMATION_JSON = "empty_label.json"
    const val NO_CONNECTION_ANIMATION_JSON = "no_connection.json"

    const val SEARCH_EMPTY_ANIMATION_FILE_NIGHT = "empty_search_night.json"
    const val FILE_EMPTY_ANIMATION_JSON_NIGHT = "empty_file_night.json"
    const val LABEL_EMPTY_ANIMATION_JSON_NIGHT = "empty_label_night.json"
    const val NO_CONNECTION_ANIMATION_JSON_NIGHT = "no_connection_night.json"
    const val LOAD_FAILED_ANIMATION_JSON = "load_failed_light.json"
    const val LOAD_FAILED_ANIMATION_JSON_NIGHT = "load_failed_night.json"

    private const val ICON_VISIBLE_HEIGHT = 200
    private const val ICON_SCALE_RATIO_MAX = 1.0f
    private const val ICON_SCALE_RATIO_MIN = 0.6f
    private const val ICON_SCALE_HEIGHT = 420
    private const val ICON_SCALE_WIDTH = 360

    @JvmStatic
    fun getEmptyIconVisible(context: Context, height: Int, contentHeight: Int = 0): Int {
        if (contentHeight >= height) return View.GONE
        val heightDp = ViewHelper.px2dip(context, height)
        return if (heightDp <= ICON_VISIBLE_HEIGHT) View.GONE else View.VISIBLE
    }

    @JvmStatic
    fun updateIconScale(view: View?, width: Int, height: Int) {
        view?.let {
            val widthDp = ViewHelper.px2dip(it.context, width)
            val heightDp = ViewHelper.px2dip(it.context, height)
            val ratio = if (widthDp < ICON_SCALE_WIDTH || heightDp < ICON_SCALE_HEIGHT) {
                ICON_SCALE_RATIO_MIN
            } else {
                ICON_SCALE_RATIO_MAX
            }
            val lp = it.layoutParams
            lp.width = (it.resources.getDimensionPixelSize(R.dimen.empty_content_img_width) * ratio).toInt()
            lp.height = (it.resources.getDimensionPixelSize(R.dimen.empty_content_img_height) * ratio).toInt()
            it.layoutParams = lp
        }
    }
}