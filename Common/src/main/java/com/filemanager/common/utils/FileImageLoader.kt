/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/3/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import androidx.core.view.setPadding
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.MultiTransformation
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.FitCenter
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomViewTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.ThirdAppFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.ImageLoaderFactory
import com.filemanager.common.imageloader.application.ApplicationInfoDetailLoadListener
import com.filemanager.common.imageloader.application.ApplicationInfoImageLoadListener
import com.filemanager.common.imageloader.application.ApplicationInfoLoader
import com.filemanager.common.imageloader.application.ApplicationThumbnailLoaderListener
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_ALL
import com.filemanager.common.imageloader.glide.RoundedCornersBitmapTransformation
import com.filemanager.common.view.FileThumbView
import com.filemanager.thumbnail.FileThumbnailSignature
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.audio.AudioThumbnailNew
import com.filemanager.thumbnail.audio.AudioThumbnailResult
import com.filemanager.thumbnail.audio.AudioThumbnailTransformation
import com.filemanager.thumbnail.doc.DocThumbnail

class FileImageLoader {
    companion object {
        private const val TAG = "FileImageLoader"
        private const val RELOAD_AUDIO_IMAGE_PREFIX = "audio:"
        const val THUMBNAIL_TYPE_LIST = 0
        const val THUMBNAIL_TYPE_SQUARE = 1
        const val THUMBNAIL_TYPE_RECT = 2
        const val ERROR_IMAGE_DEFAULT = 0
        const val ERROR_IMAGE_NORMAL_GRID = 1
        const val VIDEO_FRAME_VALUE = 0L

        @JvmStatic
        val sInstance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            FileImageLoader()
        }

        private val sDocThumbnailSize: IntArray by lazy {
            val width =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_size_width)
            val height =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_size_height)
            val width_small =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_size_width_s)
            val height_small =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_size_height_s)
            intArrayOf(width, height, width_small, height_small)
        }

        val pptWidth: Int = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_ppt_thumb_width)
        val pptHeight = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_ppt_thumb_height)
        val otherWidth: Int = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_other_thumb_width)
        val otherHeight = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_other_thumb_height)
        val remoteWidth = appContext.resources.getDimensionPixelSize(R.dimen.file_remote_thumb_width)
        val remoteHeight = appContext.resources.getDimensionPixelSize(R.dimen.file_remote_thumb_height)

        /**
         * 判断是否支持doc缩略图
         */
        fun isSupportDocThumbnail(context: Context): Boolean {
            return ThumbnailManager.isDocThumbnailSupported(context)
        }
    }

    fun clear(mContext: Context, view: View) {
        if (mContext.isActivityAndInvalid()) {
            return
        }
        if (view is ImageView && view.drawable == null) {
            return
        }
        Glide.with(mContext).clear(view)
    }

    fun pauseRequests(fragment: Fragment) {
        try {
            Glide.with(fragment).pauseRequests()
        } catch (e: Exception) {
            Log.e(TAG, "pauseRequests", e)
        }
    }

    fun resumeRequests(fragment: Fragment) {
        try {
            Glide.with(fragment).resumeRequests()
        } catch (e: Exception) {
            Log.e(TAG, "resumeRequests", e)
        }
    }

    fun displayDefault(
        baseFileBean: BaseFileBean,
        imageView: ImageView,
        orientation: Int = 0,
        radiusSize: Int = 0,
        thumbnailType: Int = THUMBNAIL_TYPE_LIST,
        roundConnerType: Int = ROUND_CONNER_ALL,
        errorImageType: Int = ERROR_IMAGE_DEFAULT
    ) {
        displayDefault(baseFileBean, imageView, orientation, radiusSize, thumbnailType, false, roundConnerType = roundConnerType, errorImageType = errorImageType)
    }

    @Suppress("LongParameterList", "LongMethod")
    fun displayDefault(
        baseFileBean: BaseFileBean,
        imageView: ImageView,
        orientation: Int = 0,
        radiusSize: Int = 0,
        thumbnailType: Int = THUMBNAIL_TYPE_LIST,
        loadDocThumbnail: Boolean = false,
        isCoverError: Boolean = false,
        roundConnerType: Int = ROUND_CONNER_ALL,
        isSmallDoc: Boolean = false,
        errorImageType: Int = FileImageLoader.ERROR_IMAGE_DEFAULT
    ) {
        val context = imageView.context
        val isGridMode = (thumbnailType == THUMBNAIL_TYPE_SQUARE)
        // Clear the tag used by displayApplication()
        imageView.tag = null
        imageView.setTag(R.id.glide_tag_id, "")
        if (baseFileBean.mLocalType == MimeTypeHelper.IMAGE_TYPE || baseFileBean.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
            if (baseFileBean is RemoteFileBean) {
                var remotePlaceholder =
                    KtThumbnailHelper.getClassifyResId(MimeTypeHelper.IMAGE_TYPE)
                if (baseFileBean.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                    remotePlaceholder =
                        KtThumbnailHelper.getClassifyResId(MimeTypeHelper.VIDEO_TYPE)
                }
                val transformation: Transformation<Bitmap> = if (radiusSize > 0) {
                    MultiTransformation(CenterCrop(), RoundedCorners(radiusSize))
                } else {
                    CenterCrop()
                }
                Glide.with(context).asBitmap().load(baseFileBean)
                    .override(remoteWidth, remoteHeight)
                    .placeholder(remotePlaceholder)
                    .transform(transformation)
                    .apply(RequestOptions().frame(VIDEO_FRAME_VALUE))
                    .into(imageView)
            } else {
                ImageLoaderFactory.buildImageLoader().display(
                    context,
                    baseFileBean,
                    radiusSize,
                    orientation,
                    imageView,
                    isCoverError = false,
                    roundConnerType = roundConnerType,
                    errorImageType = errorImageType
                )
            }
        } else if (baseFileBean.mLocalType == MimeTypeHelper.APPLICATION_TYPE) {
            displayApplicationWithDetail(baseFileBean, imageView, null, isGridMode)
        } else if (baseFileBean.mLocalType == MimeTypeHelper.DRM_TYPE) {
            baseFileBean.mData?.apply {
                val typeString = MimeTypeHelper.getDrmMimeType(context, this)
                if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith(
                        "image/"
                    ))
                ) {
                    ImageLoaderFactory.buildImageLoader()
                        .display(context, baseFileBean, radiusSize, orientation, imageView)
                } else {
                    if (!TextUtils.isEmpty(typeString) && typeString!!.startsWith("audio/")) {
                        imageView.setImageDrawable(
                            KtThumbnailHelper.getClassifyDrawable(
                                context,
                                MimeTypeHelper.AUDIO_TYPE,
                                isGridMode
                            )
                        )
                    } else {
                        imageView.setImageDrawable(
                            KtThumbnailHelper.getClassifyDrawable(
                                context,
                                MimeTypeHelper.DRM_TYPE,
                                isGridMode
                            )
                        )
                    }
                    imageView.setBackgroundResource(R.color.color_transparent)
                }
            }
        } else if (baseFileBean.mLocalType == MimeTypeHelper.COMPRESSED_TYPE) {
            val compressedType = MimeTypeHelper.getCompressedTypeByPath(
                if (baseFileBean.mDisplayName.isNullOrEmpty()) {
                    baseFileBean.mData
                } else {
                    baseFileBean.mDisplayName
                }
            )
            imageView.setImageDrawable(
                KtThumbnailHelper.getClassifyDrawable(
                    context,
                    compressedType,
                    isGridMode
                )
            )
            imageView.setBackgroundResource(R.color.color_transparent)
        } else if (MimeTypeHelper.isAudioType(baseFileBean.mLocalType)) {
            //load cover image from audio file
            val audioThumbnail = AudioThumbnailNew(
                baseFileBean.mData ?: "",
                baseFileBean.mDateModified, baseFileBean.mSize
            )
            val errorRes = when (thumbnailType) {
                THUMBNAIL_TYPE_LIST -> R.drawable.ic_file_audio
                 else -> R.drawable.ic_file_audio
            }
            val holderResId = R.drawable.ic_file_audio
            displayThumbnail(baseFileBean, audioThumbnail, imageView, radiusSize, errorRes, holderResId, isGridMode = isGridMode)
        } else if (MimeTypeHelper.isDocType(baseFileBean.mLocalType)) {
            if (loadDocThumbnail && ThumbnailManager.isDocThumbnailSupported(context)) {
                val docThumbnail = DocThumbnail(
                    baseFileBean.mData ?: "",
                    baseFileBean.mDateModified, baseFileBean.mSize
                )
                val holderResId = KtThumbnailHelper.getClassifyResId(baseFileBean.mLocalType)
                if (baseFileBean.mLocalType == MimeTypeHelper.PPT_TYPE || baseFileBean.mLocalType == MimeTypeHelper.PPTX_TYPE) {
                    displayThumbnail(
                        baseFileBean, docThumbnail, imageView, radiusSize, holderResId, holderResId,
                        width = pptWidth, height = pptHeight, isGridMode = isGridMode
                    )
                } else {
                    displayThumbnail(
                        baseFileBean, docThumbnail, imageView, radiusSize, holderResId, holderResId,
                        width = otherWidth, height = otherHeight, isGridMode = isGridMode
                    )
                }
            } else {
                val drawable = KtThumbnailHelper.getClassifyDrawable(context, baseFileBean.mLocalType, isGridMode)
                imageView.setImageDrawable(drawable)
                imageView.setBackgroundResource(R.color.color_transparent)
            }
        } else if (baseFileBean.mLocalType == MimeTypeHelper.ALBUM_SET_TYPE_CARD_CASE) {
            if (isGridMode) {
                ImageLoaderFactory.buildImageLoader().display(context, R.drawable.grid_album_set_card_case, radiusSize, imageView)
            } else {
                ImageLoaderFactory.buildImageLoader().display(context, R.drawable.file_album_set_card_case, radiusSize, imageView)
            }
        } else if (MimeTypeHelper.isOtherDocType(baseFileBean.mLocalType)) {
            val drawable = MimeTypeHelper.getIconByType(baseFileBean.mLocalType)
            imageView.setImageDrawable(drawable)
            imageView.setBackgroundResource(R.color.color_transparent)
        } else {
            val drawable =
                KtThumbnailHelper.getClassifyDrawable(context, baseFileBean.mLocalType, isGridMode)
            Glide.with(context).load(drawable).into(imageView)
            imageView.setBackgroundResource(R.color.color_transparent)
        }
    }

    private fun displayOtherDocThumbnail(
        drawable: Drawable?,
        imageView: ImageView,
        radiusSize: Int,
        placeHolderRes: Int = -1,
        width: Int = -1,
        height: Int = -1
    ) {
        val transformation: Transformation<Bitmap> = if (radiusSize > 0) {
            MultiTransformation(FitCenter(), RoundedCorners(radiusSize))
        } else {
            FitCenter()
        }

        val modelTagId = R.id.glide_fail_tag_id
        val newPlaceHolderRes = if (placeHolderRes != -1) {
            placeHolderRes
        } else {
            (imageView.getTag(modelTagId) as? Int) ?: -1
        }
        Glide.with(imageView.context)
            .load(drawable)
            .transform(transformation)
            .override(width, height)
            .placeholder(newPlaceHolderRes)
            .addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                    if (placeHolderRes == -1) {
                        imageView.setTag(modelTagId, drawable)
                    }
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    if (placeHolderRes == -1) {
                        imageView.setTag(modelTagId, -1)
                    }
                    return false
                }
            })
            .into(imageView)
        imageView.setBackgroundResource(R.color.color_transparent)
    }

    private fun displayThumbnail(
        baseFileBean: BaseFileBean,
        thumbnail: Any,
        imageView: ImageView,
        radiusSize: Int,
        errorRes: Int,
        placeHolderRes: Int = -1,
        width: Int = -1,
        height: Int = -1,
        isGridMode: Boolean = false
    ) {
        //load thumbnail image from doc file
        var requestOptions = RequestOptions.signatureOf(
            //AudioThumbnailResultByteArray一开始没有指定serialVersionUID导致会反序列化失败，为了让老版本(覆盖安装)可以正常显示出已缓存的音频缩图需要重新指定signature加载音频缩图。
            if (thumbnail is AudioThumbnailNew) {
                FileThumbnailSignature(
                    RELOAD_AUDIO_IMAGE_PREFIX + baseFileBean.mData,
                    baseFileBean.mDateModified, baseFileBean.mSize
                )
            } else {
                FileThumbnailSignature(
                    baseFileBean.mData ?: "",
                    baseFileBean.mDateModified, baseFileBean.mSize
                )
            }
        )
        val transformation: Transformation<Bitmap> = if (radiusSize > 0) {
            MultiTransformation(FitCenter(), RoundedCorners(radiusSize))
        } else {
            FitCenter()
        }
        requestOptions = requestOptions.transform(transformation)

        val modelTagId = R.id.glide_fail_tag_id
        val newPlaceHolderRes = if (placeHolderRes != -1) {
            placeHolderRes
        } else {
            (imageView.getTag(modelTagId) as? Int) ?: -1
        }

        when (thumbnail) {
            is AudioThumbnailNew -> {
                requestOptions = requestOptions.transform(
                    AudioThumbnailResult::class.java,
                    AudioThumbnailTransformation(transformation)
                )
                imageView.setImageResource(newPlaceHolderRes)
                Glide.with(imageView.context)
                    .`as`(AudioThumbnailResult::class.java)
                    .load(thumbnail)
                    .apply(requestOptions)
                    .override(width, height)
                    .placeholder(newPlaceHolderRes)
                    .error(errorRes)
                    .into(object : CustomViewTarget<ImageView, AudioThumbnailResult>(imageView) {
                        override fun onLoadFailed(errorDrawable: Drawable?) {
                            imageView.setImageResource(errorRes)

                            if (placeHolderRes == -1) {
                                imageView.setTag(modelTagId, errorRes)
                            }
                        }

                        override fun onResourceReady(
                            resource: AudioThumbnailResult,
                            transition: Transition<in AudioThumbnailResult>?
                        ) {
                            Log.d(
                                TAG,
                                "load audio for ${baseFileBean.mData}: bitmap is ${resource.mBitmap}"
                            )
                            if (resource.mBitmap != null) {
                                imageView.setPadding(MyApplication.sAppContext.resources.getDimension(R.dimen.file_list_image_padding).toInt())
                                imageView.setImageBitmap(resource.mBitmap)
                            } else {
                                imageView.setImageResource(errorRes)
                            }

                            if (placeHolderRes == -1) {
                                imageView.setTag(modelTagId, -1)
                            }
                        }

                        override fun onResourceCleared(placeholder: Drawable?) {
                            //do nothing
                        }
                    })
            }
            is DocThumbnail -> {
                Log.d(TAG, "displayThumbnail width = $width height = $height")
                val strokeWidth = MyApplication.appContext.resources.getDimension(R.dimen.divider_stroke_width)
                val borderColor = COUIContextUtil.getAttrColor(
                    imageView.context,
                    com.support.appcompat.R.attr.couiColorDivider
                )
                val roundedCorner = RoundedCornersBitmapTransformation(
                    radiusSize.toFloat(),
                    Utils.isRtl(),
                    true,
                    true,
                    true,
                    true,
                    strokeWidth,
                    borderColor
                )
                Glide.with(imageView.context)
                    .asBitmap()
                    .load(thumbnail)
                    .apply(requestOptions)
                    .override(width, height)
                    .transform(roundedCorner)
                    .placeholder(newPlaceHolderRes)
                    .error(errorRes)
                    .addListener(object : RequestListener<Bitmap> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Bitmap>,
                            isFirstResource: Boolean
                        ): Boolean {
                            if (placeHolderRes == -1) {
                                imageView.setTag(modelTagId, errorRes)
                            }
                            Log.d(TAG, "DocThumbnail onLoadFailed mUri:${thumbnail.uri}")
                            (imageView as? FileThumbView)?.let {
                                val fileBean = BaseFileBean().apply {
                                    mData = baseFileBean.mData
                                    mLocalType = baseFileBean.mLocalType
                                    when (baseFileBean) {
                                        is ThirdAppFileWrapper, is DriveFileWrapper -> {
                                            mFileWrapperViewType = BaseFileBean.TYPE_SEARCH_THIRD_APP_FILE
                                        }
                                        else -> {}
                                    }
                                }
                                if (isGridMode) {
                                    FileImageVHUtils.updateFileGridImgSize(it.context, it, fileBean)
                                } else {
                                    FileImageVHUtils.updateFileListImgSize(it.context, it, fileBean)
                                }
                            }
                            return false
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            model: Any,
                            target: Target<Bitmap>?,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            if (placeHolderRes == -1) {
                                imageView.setTag(modelTagId, -1)
                            }
                            Log.d(TAG, "DocThumbnail onResourceReady mUri:${thumbnail.uri}")
                            return false
                        }
                    })
                    .into(imageView)
            }
        }
        imageView.setBackgroundResource(R.color.color_transparent)
    }

    fun displayApplicationWithDetail(
        baseFileBean: BaseFileBean, imageView: ImageView,
        applicationInfoDetailLoadListener: ApplicationInfoDetailLoadListener?,
        isGridMode: Boolean = false
    ) {
        displayApplication(baseFileBean, imageView, object : ApplicationInfoImageLoadListener {
            override fun onLoadSuccess(tag: String, bitmap: Bitmap) {
                Log.d(TAG, "displayApplicationWithDetail onLoadFail tag $tag, bitmap $bitmap")
                val viewTag: String? = imageView.tag as? String
                if (tag == viewTag) {
                    imageView.setImageBitmap(bitmap)
                    imageView.setBackgroundResource(R.color.color_transparent)
                } else {
                    Log.d(TAG, "onLoadSuccess has tag not equals viewTag")
                }
            }

            override fun onLoadFail(tag: String, errorMsg: String) {
                Log.d(TAG, "displayApplicationWithDetail onLoadFail tag $tag, errorMsg $errorMsg")
                val viewTag: String? = imageView.tag as? String
                if (tag == viewTag) {
                    (imageView as? FileThumbView)?.let {
                        val fileBean = BaseFileBean().apply {
                            mData = baseFileBean.mData
                            mLocalType = MimeTypeHelper.UNKNOWN_TYPE
                        }
                        if (isGridMode) {
                            FileImageVHUtils.updateFileGridImgSize(it.context, it, fileBean)
                            val layoutParams = it.layoutParams as? MarginLayoutParams
                            layoutParams?.bottomMargin =
                                MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_icon_margin_bottom)
                        } else {
                            FileImageVHUtils.updateFileListImgSize(it.context, it, fileBean)
                        }
                    }
                    imageView.setImageDrawable(
                        KtThumbnailHelper.getClassifyDrawable(
                            imageView.context,
                            MimeTypeHelper.APPLICATION_TYPE,
                            isGridMode
                        )
                    )
                    imageView.setBackgroundResource(R.color.color_transparent)
                } else {
                    Log.d(TAG, "onLoadFail has tag not equals viewTag")
                }
            }
        }, applicationInfoDetailLoadListener)
    }

    private fun displayApplication(
        baseFileBean: BaseFileBean,
        imageView: ImageView,
        applicationInfoLoadListener: ApplicationInfoImageLoadListener,
        applicationInfoDetailLoadListener: ApplicationInfoDetailLoadListener?
    ) {
        ApplicationInfoLoader.getInstance().load(
            baseFileBean,
            imageView,
            ApplicationThumbnailLoaderListener(
                imageView.context,
                applicationInfoLoadListener,
                applicationInfoDetailLoadListener
            )
        )
    }

    fun exit() {
        ApplicationInfoLoader.stopApplicationInfoLoader()
    }
}
