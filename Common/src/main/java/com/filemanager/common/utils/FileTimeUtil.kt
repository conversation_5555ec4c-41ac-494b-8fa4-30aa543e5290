/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: FileTimeUtil
 ** Description: File image utils for grid or list
 ** Version: 1.0
 ** Date : 2025/2/11
 ** Author: W9057214
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.utils

import java.io.IOException
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.attribute.BasicFileAttributes

object FileTimeUtil {

    const val TAG = "FileTimeUtil"

    @JvmStatic
    fun getFileTimeData(path: String?): FileTimeData? {
        if (path.isNullOrEmpty()) {
            Log.w(TAG, "getFileTimeData path is null or empty")
            return null
        }
        Log.d(TAG, "getFileTimeData start")
        return try {
            val properties = Files.readAttributes(Paths.get(path), BasicFileAttributes::class.java)
            val dateModified = properties.lastModifiedTime().toMillis()
            val dateAccess = properties.lastAccessTime().toMillis()
            Log.d(TAG, "getFileTimeData END  dateModified $dateModified, dateAccess: $dateAccess")
            FileTimeData(dateModified, dateAccess)
        } catch (e: NoSuchFileException) {
            Log.w(TAG, "getFileTimeData File not found: $path", e)
            null
        } catch (e: IOException) {
            Log.w(TAG, "getFileTimeData Failed to read attributes for path: $path", e)
            null
        }
    }


    @JvmStatic
    fun getFileTime(path: String?): Long? {
        val fileTimeData = getFileTimeData(path)
        if (fileTimeData != null) {
            val lastModifyTime = fileTimeData.lastModifyTime
            if (lastModifyTime == 0L) {
                val lastAccessTime = fileTimeData.lastAccessTime
                if (lastAccessTime != 0L) {
                    return lastAccessTime
                }
            } else {
                return lastModifyTime
            }
        }
        return null
    }
}


data class FileTimeData(val lastModifyTime: Long, val lastAccessTime: Long)
