/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils.LabelVHUtils
 * * Description : 标签ViewHolder工具类，显示标签标记方法
 * * Version     : 1.0
 * * Date        : 2022/8/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean

object LabelVHUtils {

    @JvmStatic
    fun displayLabelFlag(file: BaseFileBean, context: Context, titleTv: TextView, isLabelFileList: Boolean = false) {
        if (file.mHasLabel) {
            val drawableFileLabelFlag = ContextCompat.getDrawable(context, R.drawable.ic_file_label_flag_on_text_new)
            if (Utils.isRtl()) {
                titleTv.setCompoundDrawablesWithIntrinsicBounds(null, null, drawableFileLabelFlag, null)
            } else {
                titleTv.setCompoundDrawablesWithIntrinsicBounds(drawableFileLabelFlag, null, null, null)
            }
            titleTv.compoundDrawablePadding = context.resources.getDimensionPixelSize(R.dimen.dimen_2dp)
            if (isLabelFileList) {
                adjustLayoutForLabelFileListItem(titleTv)
            }
        } else {
            titleTv.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
        }
    }

    @JvmStatic
    private fun adjustLayoutForLabelFileListItem(titleTv: TextView) {
        val lp = titleTv.layoutParams
        if (lp.width != ViewGroup.LayoutParams.MATCH_PARENT) {
            lp.width = ViewGroup.LayoutParams.MATCH_PARENT
            titleTv.layoutParams = lp
            titleTv.setPadding(
                titleTv.paddingLeft,
                titleTv.paddingTop,
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.dimen_4dp),
                titleTv.paddingBottom
            )
        }
    }
}