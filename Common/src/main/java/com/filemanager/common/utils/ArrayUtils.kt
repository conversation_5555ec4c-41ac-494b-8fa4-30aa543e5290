/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ArrayUtils
 * * Description : ArrayUtils
 * * Version     : 1.0
 * * Date        : 2025/01/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import java.util.Arrays

object ArrayUtils {

    /**
     * 添加数据
     * @param array 原数组
     * @param item 添加的item
     * @param canDuplicate 是否能重复
     */
    @JvmStatic
    fun <T> add(array: Array<T>, item: T, canDuplicate: Boolean = false): Array<T> {
        if (!canDuplicate && array.contains(item)) {
            return array
        }
        return concat(array, item)
    }

    @JvmStatic
    fun <T> concat(array: Array<T>, item: T): Array<T> {
        val newArray = Arrays.copyOf(array, array.size + 1)
        newArray[array.size] = item
        return newArray
    }
}