/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:ResponseCall.java
 * * Description:
 * * Version:1.0
 * * Date :2019/6/3
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.filemanager.common.MyApplication;


public class ResponseCall<T> {
    //exchange data in child-thread to main-thread
    Handler mHandler;

    public ResponseCall(final HttpUtils.HttpCallbackBytesListener listener) {
        Looper looper = MyApplication.getSAppContext().getMainLooper();
        mHandler = new Handler(looper) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                if (msg.what == 0) {
                    //success
                    listener.onFinish((byte[]) msg.obj);
                } else if (msg.what == 1) {
                    //fail
                    listener.onError((Exception) msg.obj);
                }
            }
        };
    }

    public void doSuccess(T response) {
        Message message = Message.obtain();
        message.obj = response;
        message.what = 0;
        mHandler.sendMessage(message);
    }

    public void doFail(Exception e) {
        Message message = Message.obtain();
        message.obj = e;
        message.what = 1;
        mHandler.sendMessage(message);
    }
}