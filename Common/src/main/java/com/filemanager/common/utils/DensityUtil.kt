/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DensityUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/7/22       1
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context

fun dp2px(context: Context, dp: Int): Int {
    val density = context.resources.displayMetrics.density
    return (dp * density).toInt()
}

fun dp2px(context: Context, dp: Float): Int {
    val density = context.resources.displayMetrics.density
    return (dp * density).toInt()
}


fun px2sp(context: Context, px: Int): Float {
    val fontScale = context.resources.displayMetrics.scaledDensity
    return (px / fontScale + 0.5f)
}