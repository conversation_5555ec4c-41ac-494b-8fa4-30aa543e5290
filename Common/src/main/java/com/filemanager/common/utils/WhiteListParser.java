/************************************************************
 * Copyright 2010-2020 Oplus. All rights reserved.
 * Description : My files
 * History :( ID, Date, Author, Description)
 * v1.0, 2017-06-26, den<PERSON><PERSON><PERSON>, create
 ***********************************************************/
package com.filemanager.common.utils;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Context;
import android.content.res.AssetManager;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;

import com.filemanager.common.MyApplication;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.helper.VolumeEnvironment;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressLint("SdCardPath")
public class WhiteListParser {
    private static final String TAG = "WhiteListParser";
    public static final String FILEMANAGER_WHITE_LIST_VERSION = "filemanager_whitelist";
    public static final String FILEMANAGER_WHITE_LIST = "filemanager_whitelist";
    public static final String FILEMANAGER_WHITE_LIST_DIR = "shared_prefs";
    public static final String FILEMANAGER_WHITE_LIST_FILE_NAME = "filemanager_whitelist.xml";
    public static final String FILEMANAGER_FOLDER_NOTES_NEARME_URI = "content://com.nearme.romupdate.provider.db/update_list";
    private static final String ROM_UPDATE_FILEMANAGER_WHITE_LIST = "filemanager_file_customization_whitelist";
    private static final String DOCUMENT_FORMAT_STRING = "document_format_string";
    private static final String DEFAULT_DOCUMENT_FORMAT = "default_document_format";
    private static final String DEFAULT_FILES_FILTER_CONFIG = "default_files_filter_config";
    // local version for filemanager_whitelist, if filemanager_whitelist update,must it
    private static final int LOCAL_TARGET_REFER_VERSION = **********;
    private static final int FLAG_RECEIVER_INCLUDE_BACKGROUND = 0x01000000;

    private static List<String> sRecentAppDirs = new ArrayList<>();
    private static ArrayList<String> sDocumentFormat;
    private static int sVersion = 0;


    public static void initialize(Context context) {
        final int localVersion = PreferencesUtils.getInt(FILEMANAGER_WHITE_LIST, FILEMANAGER_WHITE_LIST_VERSION, 0);
        int xmlVersion = localVersion;

        String[] projection = new String[]{"version", "xml"};
        String xml = null;
        ContentResolver resolver = context.getContentResolver();
        Cursor cursor = null;

        try {
            cursor = resolver.query(Uri.parse(FILEMANAGER_FOLDER_NOTES_NEARME_URI), projection,
                    "filtername=" + "\"" + ROM_UPDATE_FILEMANAGER_WHITE_LIST + "\"", null, null);
            if ((cursor != null) && (cursor.moveToFirst())) {
                xml = cursor.getString(1);
                xmlVersion = cursor.getInt(0);
            }
        } catch (Exception e) {
            if (xml != null) {
                xml = null;
            }
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
                cursor = null;
            }
        }

        Log.d(TAG, "initialize xmlVer = " + xmlVersion + ", localVer = " + localVersion);
        if ((xml != null) && (((localVersion < xmlVersion) && (xmlVersion >= LOCAL_TARGET_REFER_VERSION)) || ((localVersion == xmlVersion) && (xmlVersion == 0)))) {
            try {
                File file = new File(VolumeEnvironment.getDataDirPath(context, FILEMANAGER_WHITE_LIST_DIR, FILEMANAGER_WHITE_LIST_FILE_NAME));
                if (file.exists()) {
                    if (!file.delete()) {
                        Log.e(TAG, "delete error");
                        return;
                    }
                }
                parserXml(xml, null);
            } catch (XmlPullParserException e) {
                Log.e(TAG, e.getMessage());
            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
            } catch (Exception e) {
                Log.e(TAG, "parserXml " + e.getMessage());
            }
        } else if (localVersion < LOCAL_TARGET_REFER_VERSION) {
            AssetManager assetManager = context.getAssets();
            InputStream in = null;
            try {
                File file = new File(VolumeEnvironment.getDataDirPath(context, FILEMANAGER_WHITE_LIST_DIR, FILEMANAGER_WHITE_LIST_FILE_NAME));
                if (file.exists()) {
                    if (!file.delete()) {
                        Log.e(TAG, "delete error");
                    }
                }
                in = assetManager.open("filemanager_whitelist.xml");
                if (in != null) {
                    parserXml(null, in);
                    saveXmlVersion(sVersion);
                }
            } catch (XmlPullParserException e) {
                Log.e(TAG, e.getMessage());
            } catch (IOException e) {
                Log.e(TAG, e.getMessage());
            } catch (Exception e) {
                Log.e(TAG, "parserXml " + e.getMessage());
            }
        }
    }

    public static String getDocumentList(final Context context) {
        if (context == null) {
            return null;
        }
        return PreferencesUtils.getString(FILEMANAGER_WHITE_LIST, DOCUMENT_FORMAT_STRING, null);
    }

    private static void saveXmlVersion(int xmlVersion) {
        PreferencesUtils.put(FILEMANAGER_WHITE_LIST, FILEMANAGER_WHITE_LIST_VERSION, xmlVersion);
    }

    private static void parserXml(String xml, InputStream in)
            throws XmlPullParserException, IOException, NumberFormatException {
        String typeString = null;
        XmlPullParser parser = XmlPullParserFactory.newInstance().newPullParser();
        ArrayList<String> videoFilterList = new ArrayList<String>();
        String documentWhiteList = null;
        sRecentAppDirs.clear();
        if (!TextUtils.isEmpty(xml)) {
            parser.setInput(new StringReader(xml));
        } else if (null != in) {
            parser.setInput(in, "UTF-8");
        }
        parser.nextTag();
        int evenType = parser.getEventType();
        while (evenType != XmlPullParser.END_DOCUMENT) {
            if (XmlPullParser.START_TAG == evenType) {
                final String tagName = parser.getName();
                if ("string-array".equals(tagName)) {
                    typeString = parser.getAttributeValue(0);
                } else if ("version".equals(tagName)) {
                    String values = parser.nextText();
                    sVersion = Integer.parseInt(values);
                } else if ("isWpsClose".equals(tagName)) {
                    String values = parser.nextText();
                    if (Integer.parseInt(values) == 1) {
                        WpsManager.setCloseWpsPreview(true);
                    }
                }
                if (null != typeString) {
                    if (DEFAULT_FILES_FILTER_CONFIG.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            videoFilterList.add(values);
                        }
                    } else if (DEFAULT_DOCUMENT_FORMAT.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            documentWhiteList = values;
                        }
                    }
                }
            }
            evenType = parser.next();
        }
        Map<String, Object> map = new HashMap<>();
        if (videoFilterList.size() > 0) {
            for (String value : videoFilterList) {
                CategoryHelper.videoFilterConfigParser(map, value);
            }
        }
        map.put(DOCUMENT_FORMAT_STRING, documentWhiteList);
        PreferencesUtils.putAll(FILEMANAGER_WHITE_LIST, map);
    }

    public static ArrayList<String> getDocumentFormat() {
        if ((sDocumentFormat == null) || (sDocumentFormat.size() == 0)) {
            String tempDoc = WhiteListParser.getDocumentList(MyApplication.getSAppContext());
            if (tempDoc != null) {
                try {
                    String[] string = tempDoc.split(":");
                    sDocumentFormat = new ArrayList(Arrays.asList(string));
                } catch (Exception e) {
                    Log.d(TAG, "initDocumentFormatData error");
                    sDocumentFormat = new ArrayList(Arrays.asList(CategoryHelper.DEFAULT_DOCUMENT_TYPE));
                }

            } else {
                sDocumentFormat = new ArrayList(Arrays.asList(CategoryHelper.DEFAULT_DOCUMENT_TYPE));
            }
        }
        return sDocumentFormat;
    }

}
