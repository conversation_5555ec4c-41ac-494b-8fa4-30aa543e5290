/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:AppUtils.java
 * * Description:
 * * Version:1.0
 * * Date :2019/5/23
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.text.TextUtils;

import com.filemanager.common.MyApplication;
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils;

import java.lang.reflect.Field;


public class AppUtils {
    private static final String TAG = AppUtils.class.getSimpleName();

    public static String getVersionName() {
        try {
            PackageManager packageManager = MyApplication.getSAppContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    MyApplication.getSAppContext().getPackageName(), 0);
            return packageInfo.versionName;
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return getBuildConfigValue("VERSION_NAME");
    }


    public static int getVersionCode() {
        try {
            PackageManager packageManager = MyApplication.getSAppContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    MyApplication.getSAppContext().getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return 0;
    }

    public static String getPackageName() {
        try {
            PackageManager packageManager = MyApplication.getSAppContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    MyApplication.getSAppContext().getPackageName(), 0);
            return packageInfo.packageName;
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return getBuildConfigValue("APPLICATION_ID");
    }

    public static String getBuildConfigValue(String fieldName) {
        try {
            Class<?> clazz = Class.forName(MyApplication.getSAppContext().getPackageName() + ".BuildConfig");
            Field field = clazz.getField(fieldName);
            return (String) field.get(null);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return "";
    }

    /**
     * Get app version name by package name
     *
     * @return attention: maybe null
     */
    public static String getAppVersionNameByPkgName(String pkgName) {
        try {
            PackageManager packageManager = MyApplication.getSAppContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    pkgName, 0);
            return packageInfo.versionName;
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return "";
    }


    /**
     * Get app version code by package name
     *
     * @param pkgName
     * @return
     */
    public static int getAppVersionCode(String pkgName) {
        try {
            PackageManager packageManager = MyApplication.getSAppContext().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    pkgName, 0);
            return packageInfo.versionCode;
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return 0;
    }

    public static AppInfo getAppInfoByPath(Context context, String path) {
        PackageManager pm = context.getPackageManager();
        PackageInfo pkgInfo = pm.getPackageArchiveInfo(path, PackageManager.GET_ACTIVITIES);
        AppInfo ai = new AppInfo();
        if (null != pkgInfo) {
            ApplicationInfo appInfo = pkgInfo.applicationInfo;
            appInfo.sourceDir = path;
            appInfo.publicSourceDir = path;
            ai.setAppName(pm.getApplicationLabel(appInfo).toString());
            Drawable icon = pm.getApplicationIcon(appInfo);
            Bitmap bitmap = null;
            if (null == icon) {
                icon = appInfo.loadIcon(pm);
            }
            if (null != icon) {
                bitmap = getBitmapFromDrawable(icon);
            }

            ai.setIcon(bitmap);
            ai.setVersionName(pkgInfo.versionName);
            ai.setPackageName(appInfo.packageName);
            ai.setApplicationInfo(appInfo);
        }
        return ai;
    }

    public static Bitmap getBitmapFromDrawable(Drawable drawable) {
        if ((drawable == null) || (drawable.getIntrinsicWidth() <= 0) || (drawable.getIntrinsicHeight() <= 0)) {
            return null;
        } else {
            int width = drawable.getIntrinsicWidth();
            int height = drawable.getIntrinsicHeight();
            if ((width <= 0) || (height <= 0)) {
                return null;
            }
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
            return bitmap;
        }
    }

    public static String getPackageNameByPath(Context context, String filePath) {
        String packageName = "";
        PackageManager pm = context.getPackageManager();
        PackageInfo info = pm.getPackageArchiveInfo(filePath, PackageManager.GET_ACTIVITIES);
        if (info != null) {
            packageName = info.packageName;
        }
        return packageName;
    }

    /**
     * get phone model
     *
     * @return
     */
    public static String getModel() {
        return Build.MODEL;
    }

    /**
     * get phone brand
     *
     * @return
     */
    public static String getBrand() {
        CollectPrivacyUtils.collectDeviceBrand(Build.BRAND);
        return Build.BRAND;
    }

    /**
     * get phone device
     *
     * @return
     */
    public static String getDevice() {
        return Build.DEVICE;
    }

    /**
     * get phone maker
     *
     * @return
     */
    public static String getMaker() {
        return android.os.Build.MANUFACTURER;
    }

    /**
     * get current android version
     *
     * @return
     */
    public static String getVersionRelease() {
        return android.os.Build.VERSION.RELEASE;
    }

    /**
     * network is can used
     *
     * @return network is conneted, return true, else return false
     */
    public static boolean getNetworkState(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity != null) {
                NetworkInfo networkinfo = connectivity.getActiveNetworkInfo();
                if (networkinfo != null) {
                    if ((networkinfo.isAvailable()) && (networkinfo.isConnected())) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * Check app status by pkgName
     *
     * @param context
     * @param pkgName
     * @return
     */
    public static boolean isAppInstalledByPkgName(Context context, String pkgName) {
        PackageInfo packageInfo = null;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(pkgName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "isPkgInstalled " + pkgName + "Not Found");
            packageInfo = null;
        }
        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * Check app status by path
     *
     * @param context
     * @return
     */
    public static boolean checkApkInstalledByPath(Context context, String path) {
        if ((context == null) || TextUtils.isEmpty(path)) {
            Log.e(TAG, "checkApkInstalledByPath context or path = null");
            return true;
        }
        PackageManager pm = context.getPackageManager();
        try {
            PackageInfo packageInfo = pm.getPackageArchiveInfo(path, PackageManager.GET_ACTIVITIES);
            String packageName = null;
            if (null != packageInfo) {
                packageName = packageInfo.packageName;
                Log.v("checkApkInstall", "packageName =" + packageName);
            } else {
                return true;
            }
            if (TextUtils.isEmpty(packageName)) {
                return false;
            }
            pm.getApplicationInfo(packageName, PackageManager.GET_UNINSTALLED_PACKAGES);
            return false;
        } catch (PackageManager.NameNotFoundException e) {
            return true;
        }
    }

    public static boolean checkApkInstalledByPackageName(Context context, String packageNameString) {
        if ((context == null) || TextUtils.isEmpty(packageNameString)) {
            return false;
        }

        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(packageNameString, PackageManager.GET_ACTIVITIES);
            if ((null != packageInfo) && (!TextUtils.isEmpty(packageInfo.packageName))) {
                Log.v(TAG, "checkApkInstalledByPackageName=" + packageInfo.packageName);
                CollectPrivacyUtils.collectInstalledAppList(packageInfo.packageName);
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取对应包名的appIcon
     *
     * @param context context
     * @param pkgName 包名
     * @return app icon
     */
    public static Drawable getAppIcon(Context context, String pkgName) {
        Drawable icon = null;
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(pkgName, PackageManager.GET_META_DATA);
            if (packageInfo != null) {
                ApplicationInfo appInfo = packageInfo.applicationInfo;
                if (appInfo != null) {
                    icon = appInfo.loadIcon(pm);
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "getAppIcon error, pkg:" + pkgName, e);
        }
        return icon;
    }
}