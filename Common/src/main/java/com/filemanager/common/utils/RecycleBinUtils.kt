/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/5/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.provider.MediaStore
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.RecycleStore
import com.filemanager.common.helper.VolumeEnvironment
import java.io.File

object RecycleBinUtils {

    @JvmStatic
    private val INTERNAL_PATH: String by lazy { VolumeEnvironment.getInternalSdPath(appContext) }

    @JvmStatic
    // folder path of recycler bin on android R
    private val RECYCLER_DIR_PATH by lazy { "$INTERNAL_PATH${File.separator}${RecycleStore.RECYCLE_DIR_NAME}" }

    @JvmStatic
    private val DOC_RECYCLER_DIR_PATH by lazy { "$INTERNAL_PATH${File.separator}${RecycleStore.DOC_RECYCLE_DIR_NAME}" }

    @JvmStatic
    private val SOUND_RECORD_RECYCLER_DIR_PATH by lazy { "$INTERNAL_PATH${File.separator}${RecycleStore.SOUND_RECORD_RECYCLE_DIR_NAME}" }

    @JvmStatic
    // folder path of recycler bin on android Q
    private val LEGACY_RECYCLER_DIR_PATH by lazy {
        "$INTERNAL_PATH/Android/data/" + KtAppUtils.getApplicationId() +
                "${File.separator}${RecycleStore.LEGACY_RECYCLE_DIR_NAME}"
    }

    @JvmStatic
    val SQL_FILTER_RECYCLER_BIN by lazy {
        " AND ${MediaStore.Files.FileColumns.DATA} <> '${RECYCLER_DIR_PATH}'" +
                " AND ${MediaStore.Files.FileColumns.DATA} <> '${LEGACY_RECYCLER_DIR_PATH}'" +
                " AND ${MediaStore.Files.FileColumns.DATA} <> '$DOC_RECYCLER_DIR_PATH'" +
                " AND ${MediaStore.Files.FileColumns.DATA} <> '$SOUND_RECORD_RECYCLER_DIR_PATH'" +
                " AND ${MediaStore.Files.FileColumns.DATA} NOT LIKE '%${RECYCLER_DIR_PATH}${File.separator}%'" +
                " AND ${MediaStore.Files.FileColumns.DATA} NOT LIKE '%${LEGACY_RECYCLER_DIR_PATH}${File.separator}%'" +
                " AND ${MediaStore.Files.FileColumns.DATA} NOT LIKE '%${DOC_RECYCLER_DIR_PATH}${File.separator}%'" +
                " AND ${MediaStore.Files.FileColumns.DATA} NOT LIKE '%${SOUND_RECORD_RECYCLER_DIR_PATH}${File.separator}%'"
    }

    @JvmStatic
    fun isRecycleDirPath(path: String?): Boolean {
        return ((path?.startsWith(RECYCLER_DIR_PATH + File.separator) ?: false) or
                (path?.startsWith(LEGACY_RECYCLER_DIR_PATH + File.separator) ?: false) or
                (path?.startsWith(DOC_RECYCLER_DIR_PATH + File.separator) ?: false) or
                (path?.startsWith(SOUND_RECORD_RECYCLER_DIR_PATH + File.separator) ?: false) or
                (path == RECYCLER_DIR_PATH) or
                (path == LEGACY_RECYCLER_DIR_PATH)) or
                (path == DOC_RECYCLER_DIR_PATH) or
                (path == SOUND_RECORD_RECYCLER_DIR_PATH)
    }
}