/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  ExtFunction.kt
 ** Description: define extension methods
 ** Version: 1.0
 ** Date : 2021/02/25
 ** Author: W9001165
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  W9001165    2021/02/25      1.0        create
 ****************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.filemanager.common.MyApplication
import com.oplus.anim.EffectiveAnimationView


/**
 * If the context is Activity.class and isFinishing() or isDestroyed() return true.
 * Otherwise, return false
 */
fun Context.isActivityAndInvalid(): Boolean {
    return (this is Activity) && (isFinishing || isDestroyed)
}

/**
 * If Activity isFinishing() or isDestroyed() return true.
 * Otherwise, return false
 */
fun Activity.isInvalid(): Boolean {
    return (isFinishing || isDestroyed)
}

/**
 * Closes the given Closeable and swallows any IOException that may occur.
 */
fun AutoCloseable?.closeQuietly() {
    if (this != null) {
        try {
            this.close()
        } catch (e: Exception) {
        }
    }
}

fun String.getString(stringId: Int): String {
    if (stringId <= 0) {
        Log.w("ExtFunc", "String get string param invalid")
    }
    return MyApplication.sAppContext.getString(stringId)
}

fun EffectiveAnimationView.destroy() {
    try {
        cancelAnimation()
        setImageDrawable(null)
    } catch (e: Exception) {
    }
}

/**
 * 扩展方法直接返回当前属性变量的值，可以生成getter/setter方法在kotlin中
 * 可用于单元测试mock这些属性变量
 * 用例：
 * var member: T
 *      get() = field.noMoreAction()
 *      set(value) { field = value.noMoreAction() }
 */
inline fun <reified T> T.noMoreAction(): T = this


/**
 * 注册广播的扩展方法
 */
fun Context.registerExportedReceiver(receiver: BroadcastReceiver?, filter: IntentFilter, exported: Boolean = true): Intent? {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        val receiverFlag = if (exported) {
            Context.RECEIVER_EXPORTED
        } else {
            Context.RECEIVER_NOT_EXPORTED
        }
        return this.registerReceiver(receiver, filter, receiverFlag)
    } else {
        return this.registerReceiver(receiver, filter)
    }
}