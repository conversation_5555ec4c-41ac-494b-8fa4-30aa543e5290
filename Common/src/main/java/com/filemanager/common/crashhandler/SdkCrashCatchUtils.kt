/***********************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** File: - SdkCrashCatchUtils
 ** Description:
 ** Version: 1.0
 ** Date : 2024/9/23
 ** Author: <EMAIL>
 **
 ** --------------------- Revision History: -------------------------------
 ** <********>	<NA> 	  <version >	   <desc>
 ****************************************************************/
package com.filemanager.common.crashhandler

import android.content.Context
import android.content.SharedPreferences
import com.filemanager.common.crashhandler.CrashHandler.SdkCrashInfo
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import org.json.JSONObject
import java.util.Calendar
import java.util.Date

object SdkCrashCatchUtils {
    private const val TAG = "SdkCrashCatchUtils"
    private const val SP = "crash_info"
    const val CRASH_COUNT = "crash_count"
    const val CRASH_TIME = "crash_time"
    var sp: SharedPreferences? = null
    const val LIMIT_CRASH_COUNT = 3
    private var canCallADApi = true
    private var canCallCloudConfigApi = true

    //广告包名
    private const val AD_SDK_PACKAGE1_NAME = "com.opos"
    private const val AD_SDK_PACKAGE2_NAME = "com.vungle"

    //云控sdk包名
    private const val CLOUD_CONFIG_SDK_PACKAGE_NAME = "com.heytap.nearx.cloudconfig"

    private val sdkPackageName =
        arrayOf(
            AD_SDK_PACKAGE1_NAME, AD_SDK_PACKAGE2_NAME, CLOUD_CONFIG_SDK_PACKAGE_NAME
        )

    init {
        sp = MyApplication.appContext.getSharedPreferences(SP, Context.MODE_PRIVATE)
    }

    @JvmStatic
    private fun isCanCallSdkByPKG(sdkPackageName: String): Boolean {
        kotlin.runCatching {
            sp?.let {
                val crashInfoStr = it.getString(sdkPackageName, "")
                if (crashInfoStr?.isNotEmpty() == true) {
                    val json = JSONObject(crashInfoStr)
                    val crashCount = json.getInt(CRASH_COUNT)
                    Log.d(TAG, "isCanCallSdkByPKG  $sdkPackageName crashCount:$crashCount")
                    if (crashCount >= LIMIT_CRASH_COUNT) {
                        return false
                    }
                }
            }
        }.onFailure {
            Log.e(TAG, "isCanCallSdkByPKG has error:${it.message}")
        }
        return true
    }

    @JvmStatic
    fun isCanCallADApi(): Boolean {
        return canCallADApi
    }

    @JvmStatic
    fun isCanCallCloudConfigApi(): Boolean {
        return canCallCloudConfigApi
    }

    @JvmStatic
    fun loadAllCanCallApi() {
        canCallADApi = isCanCallSdkByPKG(AD_SDK_PACKAGE1_NAME) && isCanCallSdkByPKG(AD_SDK_PACKAGE2_NAME)
        canCallCloudConfigApi = isCanCallSdkByPKG(CLOUD_CONFIG_SDK_PACKAGE_NAME)
    }

    @JvmStatic
    fun loadLocalConfig(): ArrayList<SdkCrashInfo> {
        Log.d(TAG, "loadLocalConfigFile")
        val list = ArrayList<SdkCrashInfo>()
        sdkPackageName.forEach {
            val sdkCrashInfo = SdkCrashInfo(LIMIT_CRASH_COUNT, it)
            list.add(sdkCrashInfo)
        }
        return list
    }

    @JvmStatic
    fun isSameDay(firstCrashTime: Long): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastCrashDate = Date(firstCrashTime)
        val toDayDate = Date(currentTime)

        val cal1: Calendar = Calendar.getInstance()
        val cal2: Calendar = Calendar.getInstance()
        cal1.time = lastCrashDate
        cal2.time = toDayDate

        val isSameDay = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR)
                && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH)
                && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH)
        return isSameDay
    }
}