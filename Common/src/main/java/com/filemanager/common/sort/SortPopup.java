package com.filemanager.common.sort;

import static com.filemanager.common.sort.SortHelper.FILE_DATE_TAKEN_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_LAST_OPEN_TIME_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_NAME_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_TIME_DELETE_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_TIME_REVERSE_ORDER;
import static com.filemanager.common.sort.SortHelper.FILE_TYPE_ORDER;
import static com.filemanager.common.utils.StatisticsUtils.EVENT_CLICK_SORT;
import static com.filemanager.common.utils.StatisticsUtils.SORT_BY_NAME;
import static com.filemanager.common.utils.StatisticsUtils.SORT_BY_SIZE;
import static com.filemanager.common.utils.StatisticsUtils.SORT_BY_TIME;
import static com.filemanager.common.utils.StatisticsUtils.SORT_BY_TYPE;
import static com.filemanager.common.utils.Utils.isRtl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.PopupWindow;

import androidx.annotation.DrawableRes;

import com.coui.appcompat.poplist.COUIPopupListWindow;
import com.coui.appcompat.poplist.PopupListItem;
import com.filemanager.common.MyApplication;
import com.filemanager.common.R;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.PreferencesUtils;
import com.filemanager.common.utils.StatisticsUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class SortPopup {
    private static final String TAG = "SortPopup";
    private int mDefaultItem;
    private static int mDefaultOffset = 720;
    private int mRecordSortMode;
    private int mOffset;
    private boolean mIsDesc = true;
    private String mCategoryMode;
    private Context mContext;
    private COUIPopupListWindow mPopupWindow;
    private SelectItemListener mSelectItemListener;

    private SortMode mSortMode;

    public SortPopup(Context context, Bundle bundle) {

        this.mContext = context;
        initDate(bundle);
        initPopup();
    }

    public SortPopup(Context context) {
        this.mContext = context;
    }

    public int getDefaultItem() {
        return mDefaultItem;
    }

    public boolean getIsDesc() {
        return mIsDesc;
    }

    public void initDate(Bundle bundle) {
        if (null != bundle) {
            this.mOffset = bundle.getInt(SortModeUtils.RECORD_DEFAULT_OFFSET, mDefaultOffset);
            this.mCategoryMode = bundle.getString(SortModeUtils.RECORD_CATEGORY_MODE, SortRecordModeFactory.INSTANCE.getCategoryKey(CategoryHelper.CATEGORY_DOC));
        }
        mRecordSortMode = SortModeUtils.getSharedSortMode(mContext, mCategoryMode);
        mIsDesc = SortModeUtils.getSharedSortOrder(mCategoryMode);
        if (null != bundle) {
            int tempSort = -1;
            int tempDesc = -1;
            tempSort = bundle.getInt(Constants.TEMP_SORT_TYPE, tempSort);
            tempDesc = bundle.getInt(Constants.TEMP_SORT_DESC, tempDesc);
            if (tempDesc != -1) {
                mIsDesc = (tempDesc == 0);
            }
            if (tempSort != -1) {
                mRecordSortMode = tempSort;
            }
        }
        mSortMode = initSortMode(mCategoryMode);
        mDefaultItem = initDefaultItem(mSortMode, bundle, mRecordSortMode);
        notifyPopup(mDefaultItem);
        Log.d(TAG, "initData categoryMode:" + this.mCategoryMode
                + " recordSortMode:" + mRecordSortMode + " desc:" + mIsDesc + " mDefaultItem:" + mDefaultItem
                + " sortMode:" + mSortMode);
    }

    private void notifyPopup(int selectPosition) {
        if (mPopupWindow != null) {
            List<PopupListItem> itemList = mPopupWindow.getItemList();
            for (int i = 0; i < itemList.size(); i++) {
                PopupListItem item = itemList.get(i);
                int imgRes = getSelectImageResource(i);
                item.setChecked(i == selectPosition);
                item.setStateIconId(imgRes);
            }
            mPopupWindow.setItemList(itemList);
        }
    }

    private SortMode initSortMode(String categoryMode) {
        SortMode result = SortMode.DEFAULT;
        if (SortModeUtils.RECYCLE_SORT_RECORD.equals(categoryMode)) {
            //RecycleBin Sort
            result = SortMode.RECYCLE_BIN;
        } else if (SortRecordModeFactory.INSTANCE.isLocalDocs(categoryMode)) {
            //Doc Sort
            result = SortMode.LOCAL_DOCS;
        } else if (SortRecordModeFactory.INSTANCE.isTencentDocs(categoryMode)) {
            // tencent docs
            result = SortMode.TENCENT_DOCS;
        } else if (SortRecordModeFactory.INSTANCE.isKingSoftDocs(categoryMode)) {
            // kingsoft docs
            result = SortMode.KINGSOFT_DOCS;
        } else if (SortModeUtils.ALBUM_FILE_SORT_RECORD.equals(categoryMode)) {
            //Album Sort
            result = SortMode.ALBUM;
        } else if (SortRecordModeFactory.INSTANCE.isRemoteMacKey(categoryMode)) {
            //Remote Sort
            result = SortMode.REMOTE_MAC;
        } else {
            //Other Sort
            result = SortMode.DEFAULT;
        }
        return result;
    }

    private int initDefaultItem(SortMode mode, Bundle bundle, int recordSortMode) {
        if (mode == SortMode.LOCAL_DOCS) {
            int tempSort = -1;
            if (null != bundle) {
                tempSort = bundle.getInt(Constants.TEMP_SORT_TYPE, tempSort);
            }
            if (tempSort == FILE_LAST_OPEN_TIME_ORDER) {
                //从侧边栏显示打开的页面，打开popup显示最近打开时间，降序
                mIsDesc = true;
                return mode.indexOf(FILE_LAST_OPEN_TIME_ORDER);
            }
        }
        return mode.indexOf(recordSortMode);
    }

    @SuppressLint("ResourceType")
    private String[] getSortArray() {
        return mContext.getResources().getStringArray(mSortMode.getDisplayId());
    }

    private void initPopup() {
        List<PopupListItem> list = createListItems();
        mPopupWindow = new COUIPopupListWindow(mContext);
        mPopupWindow.setItemList(list);
        mPopupWindow.setDismissTouchOutside(true);
        mPopupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (null != mSelectItemListener) {
                    mSelectItemListener.onDismiss();
                }
            }
        });
        mPopupWindow.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int position, long l) {
                onItemClickHandle(position);
                notifyPopup(position);
            }
        });
    }

    public void onItemClickHandle(int position) {
        selectItem(position);
        mRecordSortMode = mSortMode.getSortMode(position);
        PreferencesUtils.put(SortModeUtils.SHARED_PREFS_NAME, mCategoryMode, mRecordSortMode);
        SortModeUtils.putSharedSortOrder(mCategoryMode, mIsDesc);
        Log.d(TAG, "mCategoryMode:" + mCategoryMode + "  mRecordSortMode:" + mRecordSortMode);
        if (null != mSelectItemListener) {
            mSelectItemListener.onPopUpItemClick(true, mRecordSortMode, mIsDesc);
        }
        HashMap<String, String> map = new HashMap<>();
        switch (mRecordSortMode) {
            case FILE_TIME_REVERSE_ORDER:
            case FILE_TIME_DELETE_ORDER:
            case FILE_DATE_TAKEN_ORDER:
                map.put(EVENT_CLICK_SORT, SORT_BY_TIME);
                break;
            case FILE_SIZE_SUMDIR_REVERSE_ORDER:
                map.put(EVENT_CLICK_SORT, SORT_BY_SIZE);
                break;
            case FILE_TYPE_ORDER:
                map.put(EVENT_CLICK_SORT, SORT_BY_TYPE);
                break;
            case FILE_NAME_ORDER:
                map.put(EVENT_CLICK_SORT, SORT_BY_NAME);
                break;
            default:
                break;
        }
        Log.d(TAG, "onItemClick: mRecordSortMode = " + mRecordSortMode + " desc：" + mIsDesc);
        StatisticsUtils.onCommon(MyApplication.getSAppContext(), EVENT_CLICK_SORT, map);
    }

    private boolean selectItem(int position) {
        Log.d(TAG, "selectItem position:" + position + "select:" + mDefaultItem + " desc:" + mIsDesc);
        if (position == mDefaultItem) {
            mIsDesc = !mIsDesc;
        } else {
            mDefaultItem = position;
            mIsDesc = true;
        }
        return mIsDesc;
    }

    private List<PopupListItem> createListItems() {
        PopupListItem.Builder builder = new PopupListItem.Builder();
        ArrayList<PopupListItem> list = new ArrayList<>();
        String[] sortArray = getSortArray();
        for (int i = 0; i < sortArray.length; i++) {
            int imgRes = getSelectImageResource(i);
            PopupListItem item = builder.reset()
                    .setTitle(sortArray[i])
                    .setIsChecked(i == mDefaultItem)
                    .setStateIconId(imgRes)
                    .build();
            list.add(item);
        }
        return list;
    }

    @DrawableRes
    private int getSelectImageResource(int position) {
        int res = R.drawable.ic_order_default;
        if (position == mDefaultItem) {
            if (mIsDesc) {
                res = R.drawable.ic_order_desc;
            } else {
                res = R.drawable.ic_order_asc;
            }
        }
        return res;
    }

    public void showPopUp(View anchor) {
        if (mPopupWindow == null) {
            Log.d(TAG, "showPopUp is null");
            return;
        }
        if (!mPopupWindow.isShowing()) {
            if (isRtl()) {
                mPopupWindow.setOffset(mOffset, 0, 0, 0);
            } else {
                mPopupWindow.setOffset(0, 0, mOffset, 0);
            }
            mPopupWindow.show(anchor);
        } else {
            mPopupWindow.dismiss();
        }
    }

    public void dismissPopUp() {
        if ((mPopupWindow != null) && mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }

    public void setOnPathClickListener(SelectItemListener listener) {
        mSelectItemListener = listener;
    }
}
