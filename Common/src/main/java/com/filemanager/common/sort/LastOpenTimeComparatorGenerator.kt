/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2023/9/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean

open class LastOpenTimeComparatorGenerator : AbstractFileComparator(), Comparator<BaseFileBean> {

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        val result = o1.lastOpenTime - o2.lastOpenTime
        return when {
            result < 0 -> -1
            result > 0 -> 1
            else -> 0
        }
    }

    override fun compare(file: BaseFileBean, t1: BaseFileBean): Int {
        return compare1(file, t1, true)
    }

    companion object {

        val comparator = LastOpenTimeComparatorGenerator()

        fun getComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genReverseComparator()
            } else {
                comparator.genComparator()
            }
        }
    }
}