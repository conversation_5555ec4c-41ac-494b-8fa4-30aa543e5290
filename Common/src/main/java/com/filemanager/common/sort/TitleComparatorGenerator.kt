package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import java.text.Collator
import java.util.*

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
class TitleComparatorGenerator(titleMap: HashMap<String, String>) : AbstractFileComparator() {
    private val mCollator: Collator
    private var mTitleMap = HashMap<String, String>()

    init {
        mCollator = Collator.getInstance(Locale.CHINA)
        mTitleMap.clear()
        mTitleMap = titleMap
    }

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        val baseNameWithExt1 = o1.mDisplayName
        val baseNameWithExt2 = o2.mDisplayName

        if (baseNameWithExt1 == null && baseNameWithExt2 == null) {
            return 0
        } else if (baseNameWithExt1 == null) {
            return -1
        } else if (baseNameWithExt2 == null) {
            return 1
        }
        return mCollator.compare(baseNameWithExt1, baseNameWithExt2)
    }

}