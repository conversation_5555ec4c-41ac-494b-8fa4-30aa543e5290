package com.filemanager.common.sort;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Build.VERSION;
import android.transition.Transition;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.MeasureSpec;
import android.view.View.OnLayoutChangeListener;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.AnimationUtils;
import android.view.animation.Interpolator;
import android.view.animation.LinearInterpolator;
import android.view.animation.ScaleAnimation;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.PopupWindow;

import com.coui.appcompat.list.COUIForegroundListView;
import com.coui.appcompat.poplist.PopupListItem;
import com.coui.appcompat.uiutil.UIUtil;
import com.coui.appcompat.uiutil.ShadowUtils;
import com.filemanager.common.R;
import com.filemanager.common.utils.Log;

import java.util.ArrayList;
import java.util.List;
public class SinglePopUpWindow extends PopupWindow implements OnLayoutChangeListener {
    private static final String TAG = "SinglePopUpWindow";

    private Context mContext;
    private BaseAdapter mDefaultAdapter;
    private BaseAdapter mCustomAdapter;
    private BaseAdapter mAdapter;
    private View mAnchor;
    private Rect mDecorViewRect;
    private Rect mAnchorRect;
    private Rect mBackgroundPaddingRect;
    private List<PopupListItem> mItemList;
    private ViewGroup mContentView;
    private ListView mListView;
    private ListView mListViewUsedToMeasure;
    private OnItemClickListener mOnItemClickListener;
    private Point mCoordinate = new Point();
    private int[] mTempLocation = new int[2];
    private int[] mWindowLocationOnScreen = new int[2];
    private int[] mPopupWindowOffset = new int[4];
    private float mPivotX;
    private float mPivotY;
    private int mScaleAnimationDuration;
    private int mAlphaAnimationDuration;
    private Interpolator mScaleAnimationInterpolator;
    private Interpolator mAlphaAnimationInterpolator;
    private int mPopupListWindowMaxWidth;
    private int mPopupListWindowMinWidth;
    private boolean mHasVerticalSpace = true;
    private boolean mHasHorizontalSpace = true;
    private boolean mShowAboveFirst;
    private Animation.AnimationListener mAnimationListener = new Animation.AnimationListener() {
        public void onAnimationStart(Animation animation) {
        }

        public void onAnimationEnd(Animation animation) {
            SinglePopUpWindow.this.dismissPopupWindow();
        }

        public void onAnimationRepeat(Animation animation) {
        }
    };

    public SinglePopUpWindow(Context context) {
        super(context);
        this.mContext = context;
        this.mItemList = new ArrayList();
        this.mScaleAnimationDuration = context.getResources().getInteger(com.support.poplist.R.integer.coui_animation_time_move_veryfast);
        this.mAlphaAnimationDuration = context.getResources().getInteger(com.support.poplist.R.integer.coui_animation_time_move_veryfast);
        if (Build.VERSION.SDK_INT >= 21) {
            this.mScaleAnimationInterpolator = AnimationUtils.loadInterpolator(context, com.support.poplist.R.anim.coui_curve_opacity_inout);
        } else {
            this.mScaleAnimationInterpolator = new LinearInterpolator();
        }

        this.mAlphaAnimationInterpolator = this.mScaleAnimationInterpolator;
        this.mPopupListWindowMaxWidth = context.getResources().getDimensionPixelSize(com.support.poplist.R.dimen.coui_popup_list_window_max_width);
        this.mPopupListWindowMinWidth = context.getResources().getDimensionPixelSize(com.support.poplist.R.dimen.coui_popup_list_window_min_width);
        this.mListViewUsedToMeasure = new ListView(context);
        this.mListViewUsedToMeasure.setDivider((Drawable) null);
        this.mListViewUsedToMeasure.setLayoutParams(new LayoutParams(-1, -1));
        this.mContentView = this.createContentView(context);
        this.setBackgroundDrawable(new ColorDrawable(0));
        this.setClippingEnabled(false);
        if (VERSION.SDK_INT > Build.VERSION_CODES.M) {
            this.setExitTransition((Transition) null);
            this.setEnterTransition((Transition) null);
        }
    }

    private ViewGroup createContentView(Context context) {
        FrameLayout contentView = (FrameLayout) LayoutInflater.from(context)
                .inflate(com.support.poplist.R.layout.coui_popup_list_window_layout_compat, (ViewGroup) null);
        this.mListView = (ListView) contentView.findViewById(com.support.poplist.R.id.coui_popup_list_view);
        TypedArray typedArray = context.getTheme()
                .obtainStyledAttributes(new int[]{com.support.poplist.R.attr.couiPopupWindowBackground});
        Drawable background = typedArray.getDrawable(0);
        if (background == null) {
            background = context.getResources().getDrawable(com.support.poplist.R.drawable.coui_popup_window_bg);
        }
        int shadowSize = context.getResources().getDimensionPixelOffset(com.support.appcompat.R.dimen.support_shadow_size_level_three);
        int shadowBottom = context.getResources().getDimensionPixelOffset(com.support.poplist.R.dimen.coui_popup_list_window_margin_bottom);
        int shadowTop = context.getResources().getDimensionPixelOffset(com.support.poplist.R.dimen.coui_popup_list_window_margin_top);
        mBackgroundPaddingRect = new Rect(shadowSize, shadowTop, shadowSize, shadowBottom);
        mListView.setBackground(background);

        ((COUIForegroundListView) mListView)
                .setRadius(mContext.getResources().getDimensionPixelOffset(com.support.poplist.R.dimen.coui_popup_list_window_content_radius));
        ShadowUtils.setElevationToView(
                mListView, ShadowUtils.SHADOW_LV3,
                context.getResources().getDimensionPixelOffset(com.support.appcompat.R.dimen.support_shadow_size_level_three),
                context.getResources().getColor(com.support.poplist.R.color.coui_popup_outline_spot_shadow_color)
        );
        typedArray.recycle();
        return contentView;
    }

    public void show(View anchor, int item) {
        // If the activity no longer exist, not allow show this window, otherwise memory leak occurred
        if (contextIsActivityAndInvalid()) {
            Log.d(TAG, "SinglePopUpWindow show: context is activity and invalid");
            mContext = null;
            return;
        }
        if (null != anchor && (null != this.mDefaultAdapter || null != this.mCustomAdapter) && !this.isShowing()) {
            this.mAnchor = anchor;
            this.mAnchor.removeOnLayoutChangeListener(this);
            this.mAnchor.addOnLayoutChangeListener(this);
            if (null == this.mCustomAdapter) {
                this.mAdapter = this.mDefaultAdapter;
            } else {
                this.mAdapter = this.mCustomAdapter;
            }
            this.mListView.setAdapter(this.mAdapter);
            this.mListView.setSelection(item);
            if (null != this.mOnItemClickListener) {
                this.mListView.setOnItemClickListener(this.mOnItemClickListener);
            }

            this.mDecorViewRect = new Rect();
            this.mAnchorRect = new Rect();
            this.mAnchor.getWindowVisibleDisplayFrame(this.mDecorViewRect);
            this.mAnchor.getGlobalVisibleRect(this.mAnchorRect);
            Rect var10000 = this.mAnchorRect;
            var10000.left -= this.mPopupWindowOffset[0];
            var10000 = this.mAnchorRect;
            var10000.top -= this.mPopupWindowOffset[1];
            var10000 = this.mAnchorRect;
            var10000.right += this.mPopupWindowOffset[2];
            var10000 = this.mAnchorRect;
            var10000.bottom += this.mPopupWindowOffset[3];
            this.mAnchor.getRootView().getLocationOnScreen(this.mTempLocation);
            this.mAnchorRect.offset(this.mTempLocation[0], this.mTempLocation[1]);
            this.calculateWindowLocation();
            this.measurePopupWindow();
            this.calculateCoordinate();
            if (this.mHasVerticalSpace && this.mHasHorizontalSpace) {
                this.setContentView(this.mContentView);
                this.calculatePivot();
                this.animateEnter();
                this.showAtLocation(this.mAnchor, 0, this.mCoordinate.x, this.mCoordinate.y);
            }
        }
    }

    private void calculateWindowLocation() {
        this.mAnchor.getRootView().getLocationOnScreen(this.mTempLocation);
        int rootViewLeftOnScreen = this.mTempLocation[0];
        int rootViewTopOnScreen = this.mTempLocation[1];
        this.mAnchor.getRootView().getLocationInWindow(this.mTempLocation);
        int rootViewLeftOnWindow = this.mTempLocation[0];
        int rootViewTopOnWindow = this.mTempLocation[1];
        this.mWindowLocationOnScreen[0] = rootViewLeftOnScreen - rootViewLeftOnWindow;
        this.mWindowLocationOnScreen[1] = rootViewTopOnScreen - rootViewTopOnWindow;
    }

    @SuppressLint("WrongConstant")
    private void measurePopupWindow() {
        BaseAdapter adapter = this.mAdapter;
        int maxItemWidth = 0;
        int totalHeight = 0;
        View itemView = null;
        int itemType = 0;
        int widthMeasureSpec = MeasureSpec.makeMeasureSpec(this.mPopupListWindowMaxWidth, -2147483648);
        int heightMeasureSpec = MeasureSpec.makeMeasureSpec(0, 0);
        int count = adapter.getCount();

        for (int i = 0; i < count; ++i) {
            int currentType = adapter.getItemViewType(i);
            if (currentType != itemType) {
                itemType = currentType;
                itemView = null;
            }

            itemView = adapter.getView(i, itemView, this.mListViewUsedToMeasure);
            android.widget.AbsListView.LayoutParams lp = (android.widget.AbsListView.LayoutParams) itemView.getLayoutParams();
            if (lp.height != -2) {
                heightMeasureSpec = MeasureSpec.makeMeasureSpec(lp.height, 1073741824);
            }

            itemView.measure(widthMeasureSpec, heightMeasureSpec);
            int itemWidth = itemView.getMeasuredWidth();
            int itemHeight = itemView.getMeasuredHeight();
            if (itemWidth > maxItemWidth) {
                maxItemWidth = itemWidth;
            }

            totalHeight += itemHeight;
        }

        this.setWidth(Math.max(maxItemWidth, this.mPopupListWindowMinWidth) + this.mBackgroundPaddingRect.left + this.mBackgroundPaddingRect.right);
        this.setHeight(totalHeight + this.mBackgroundPaddingRect.top + this.mBackgroundPaddingRect.bottom);
    }

    private void calculateCoordinate() {
        if (this.mDecorViewRect.right - this.mDecorViewRect.left < this.getWidth()) {
            this.mHasHorizontalSpace = false;
        } else {
            int x = Math.min(this.mAnchorRect.centerX() - this.getWidth() / 2, this.mDecorViewRect.right - this.getWidth());
            x = Math.max(this.mDecorViewRect.left, x) - this.mWindowLocationOnScreen[0];
            int availableHeightAboveAnchor = this.mAnchorRect.top - this.mDecorViewRect.top;
            int availableHeightBelowAnchor = this.mDecorViewRect.bottom - this.mAnchorRect.bottom;
            int popupWindowHeight = this.getHeight();
            boolean aboveHasSpace = availableHeightAboveAnchor >= popupWindowHeight;
            boolean belowHasSpace = availableHeightBelowAnchor >= popupWindowHeight;
            int aboveY = this.mAnchorRect.top - popupWindowHeight;
            int belowY = this.mAnchorRect.bottom;
            if (availableHeightBelowAnchor <= 0 && availableHeightAboveAnchor <= 0) {
                this.mHasVerticalSpace = false;
            } else {
                int y;
                label65:
                {
                    label66:
                    {
                        if (this.mShowAboveFirst) {
                            if (!aboveHasSpace) {
                                break label66;
                            }
                        } else if (!belowHasSpace) {
                            break label66;
                        }

                        y = this.mShowAboveFirst ? aboveY : belowY;
                        break label65;
                    }

                    label67:
                    {
                        if (this.mShowAboveFirst) {
                            if (!belowHasSpace) {
                                break label67;
                            }
                        } else if (!aboveHasSpace) {
                            break label67;
                        }

                        y = this.mShowAboveFirst ? belowY : aboveY;
                        break label65;
                    }

                    if (availableHeightAboveAnchor > availableHeightBelowAnchor) {
                        y = this.mDecorViewRect.top;
                        this.setHeight(availableHeightAboveAnchor);
                    } else {
                        y = this.mAnchorRect.bottom;
                        this.setHeight(availableHeightBelowAnchor);
                    }
                }

                this.mCoordinate.set(x, y - this.mWindowLocationOnScreen[1]);
            }
        }
    }

    private void calculatePivot() {
        if (this.mAnchorRect.centerX() - this.mWindowLocationOnScreen[0] - this.mCoordinate.x >= this.getWidth()) {
            this.mPivotX = 1.0F;
        } else {
            this.mPivotX = (float) (this.mAnchorRect.centerX() - this.mWindowLocationOnScreen[0] - this.mCoordinate.x) / (float) this.getWidth();
        }

        if (this.mCoordinate.y >= this.mAnchorRect.top - this.mWindowLocationOnScreen[1]) {
            this.mPivotY = 0.0F;
        } else {
            this.mPivotY = 1.0F;
        }

    }

    private void animateEnter() {
        Animation scaleAnimation = new ScaleAnimation(0.5F, 1.0F, 0.5F, 1.0F, 1, this.mPivotX, 1, this.mPivotY);
        Animation alphaAnimation = new AlphaAnimation(0.0F, 1.0F);
        scaleAnimation.setDuration((long) this.mScaleAnimationDuration);
        scaleAnimation.setInterpolator(this.mScaleAnimationInterpolator);
        alphaAnimation.setDuration((long) this.mAlphaAnimationDuration);
        alphaAnimation.setInterpolator(this.mAlphaAnimationInterpolator);
        AnimationSet animationSet = new AnimationSet(false);
        animationSet.addAnimation(scaleAnimation);
        animationSet.addAnimation(alphaAnimation);
        this.mContentView.startAnimation(animationSet);
    }

    private void animateExit() {
        Animation animation = mContentView.getAnimation();
        if ((animation != null) && animation.hasStarted()) {
            Log.d(TAG, "animateExit , Animation running");
            return;
        }
        Animation scaleAnimation = new ScaleAnimation(1.0F, 0.5F, 1.0F, 0.5F, 1, this.mPivotX, 1, this.mPivotY);
        Animation alphaAnimation = new AlphaAnimation(1.0F, 0.0F);
        scaleAnimation.setDuration((long) this.mScaleAnimationDuration);
        scaleAnimation.setInterpolator(this.mScaleAnimationInterpolator);
        alphaAnimation.setDuration((long) this.mAlphaAnimationDuration);
        alphaAnimation.setInterpolator(this.mAlphaAnimationInterpolator);
        AnimationSet animationSet = new AnimationSet(false);
        animationSet.addAnimation(scaleAnimation);
        animationSet.addAnimation(alphaAnimation);
        animationSet.setAnimationListener(this.mAnimationListener);
        this.mContentView.startAnimation(animationSet);
    }

    public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
        Rect newRect = new Rect(left, top, right, bottom);
        Rect oldRect = new Rect(oldLeft, oldTop, oldRight, oldBottom);
        if (this.isShowing() && !newRect.equals(oldRect)) {
            this.dismiss();
        }

    }

    private void dismissPopupWindow() {
        super.dismiss();
        this.setContentView((View) null);
    }

    public void dismiss() {
        if (contextIsActivityAndInvalid()) {
            Log.d(TAG, "SinglePopUpWindow dismiss: context is activity and invalid");
            this.dismissPopupWindow();
            mContext = null;
        } else {
            this.animateExit();
        }
    }

    private boolean contextIsActivityAndInvalid() {
        if (mContext == null) {
            return true;
        } else if (mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            return activity.isFinishing() || activity.isDestroyed();
        }
        return false;
    }

    public List<PopupListItem> getItemList() {
        return this.mItemList;
    }

    public void setItemList(List<PopupListItem> itemList) {
        if (null != itemList) {
            this.mItemList = itemList;
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    public void setAdapter(BaseAdapter adapter) {
        this.mCustomAdapter = adapter;
    }

    public ListView getListView() {
        return this.mListView;
    }

    public void setDismissTouchOutside(boolean isDismiss) {
        if (isDismiss) {
            this.setTouchable(true);
            this.setFocusable(true);
            this.setOutsideTouchable(true);
        } else {
            this.setFocusable(false);
            this.setOutsideTouchable(false);
        }

        this.update();
    }

    public void setOffset(int left, int top, int right, int bottom) {
        this.mPopupWindowOffset[0] = left;
        this.mPopupWindowOffset[1] = top;
        this.mPopupWindowOffset[2] = right;
        this.mPopupWindowOffset[3] = bottom;
    }

    public void showAboveFirst(boolean showAboveFirst) {
        this.mShowAboveFirst = showAboveFirst;
    }

}
