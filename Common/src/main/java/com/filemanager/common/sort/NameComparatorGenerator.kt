package com.filemanager.common.sort

import androidx.annotation.VisibleForTesting
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.common.base.BaseFileBean
import java.text.Collator
import java.util.*
import kotlin.Comparator

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
open class NameComparatorGenerator : AbstractFileComparator() {

    protected val mCollator: Collator = Collator.getInstance(Locale.CHINA)

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        if (o1 === o2) {
            return 0
        }
        if ((o1.mDisplayName == null) || (o2.mDisplayName == null)) {
            return 0
        }
        o1.mDisplayName?.let { s1 ->
            o2.mDisplayName?.let { s2 ->
                return compareString(s1, s2)
            }
        }
        return mCollator.compare(o1.mDisplayName, o2.mDisplayName)
    }

    @VisibleForTesting
    fun filterPreviousZero(s: String): String {
        var indexTemp = 0
        while (true) {
            if (indexTemp >= s.length) {
                return ""
            }
            if (s[indexTemp] == '0') {
                indexTemp++
            } else {
                break
            }
        }
        return s.substring(indexTemp, s.length)
    }

    @VisibleForTesting
    fun compareNumberString(s1: String, s2: String): Int {
        if (s1 == s2) {
            return 0
        }
        val tempString1 = filterPreviousZero(s1)
        val tempString2 = filterPreviousZero(s2)
        if (tempString1.length != tempString2.length) {
            return tempString1.length - tempString2.length
        } else {
            var index = 0
            while (true) {
                if (index == tempString1.length) {
                    return s2.length - s1.length
                }
                if (tempString1[index] != tempString2[index]) {
                    return tempString1[index] - tempString2[index]
                } else {
                    index++
                }
            }
        }
    }

    @VisibleForTesting
    fun getNumberString(s: String, start: Int): String {
        var end = start + 1
        while (true) {
            if (end >= s.length) {
                break
            }
            if (checkIsDigit(s[end])) {
                end++
            } else {
                break
            }
        }
        return s.substring(start, end)
    }

    fun compareString(s1: String, s2: String): Int {
        val maxLength = Math.min(s1.length, s2.length)
        var i = 0
        while (true) {
            if (i >= maxLength) {
                return s1.length - s2.length
            }
            val isDigit1 = checkIsDigit(s1[i])
            val isDigit2 = checkIsDigit(s2[i])
            if (isDigit1 && isDigit2) {
                val numberString1 = getNumberString(s1, i)
                val numberString2 = getNumberString(s2, i)
                val result = compareNumberString(numberString1, numberString2)
                if (result == 0) {
                    i += numberString1.length
                } else {
                    return result
                }
            } else {
                if (s1[i] == s2[i]) {
                    i++
                } else {
                    return mCollator.compare(s1.substring(i, i + 1), s2.substring(i, i + 1))
                }
            }
        }
    }

    @VisibleForTesting
    fun checkIsDigit(c: Char): Boolean {
        return c in DIGIT_START..DIGIT_END
    }


    override fun compareRecycleFileWrapper(o1: RecycleFileWrapper, o2: RecycleFileWrapper, isDesc: Boolean): Int {
        if (o1 === o2) {
            return 0
        }
        val baseNameWithExt1 = o1?.mDisplayName
        val baseNameWithExt2 = o2?.mDisplayName
        return mCollator.compare(baseNameWithExt1, baseNameWithExt2)
    }


    companion object {
        val comparator = NameComparatorGenerator()
        val COMPARATOR = comparator.genComparator()
        val COMPARATOR_CATEGORY = comparator.genCategoryComparator()
        val SEARCH_ALL_COMPARATOR = comparator.genSearchAllComparator()
        val COMPARATOR_RECYCLEBIN = comparator.genRecycleFileComparator()
        const val DIGIT_START = '0'
        const val DIGIT_END = '9'
        fun getNameComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genComparator()
            } else {
                comparator.genReverseComparator()
            }
        }

        fun getCategoryComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return comparator.genCategoryComparator(isDesc)
        }

        fun getRecycleBinComparator(isDesc: Boolean): Comparator<RecycleFileWrapper> {
            return comparator.genRecycleFileComparator(isDesc)
        }
    }}
