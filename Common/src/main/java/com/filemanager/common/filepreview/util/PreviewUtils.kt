/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: PreviewUtils
 ** Description: Preview utils
 ** Version: 1.0
 ** Date : 2024/11/06
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview.util

import com.filemanager.common.MyApplication
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.WindowUtils

object PreviewUtils {

    private const val SHARED_PREFERENCE_NAME = "preview_state"
    private const val PREVIEW_STATE_KEY = "preview_state_key"

    @JvmStatic
    fun setPreviewState(isOpen: Boolean) {
        PreferencesUtils.put(SHARED_PREFERENCE_NAME, PREVIEW_STATE_KEY, isOpen)
    }

    @JvmStatic
    fun isPreviewOpen(): Boolean {
        return PreferencesUtils.getBoolean(SHARED_PREFERENCE_NAME, PREVIEW_STATE_KEY, false)
    }

    @JvmStatic
    fun isShowPreview(): Boolean {
        return isPreviewOpen() && !WindowUtils.isSmallScreen(MyApplication.appContext)
    }
}