<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M91.43,74.63L96.46,74.8H96.34L101.04,74.2C101.33,74.16 101.63,74.25 101.87,74.43C102.11,74.61 102.26,74.88 102.29,75.18C102.33,75.48 102.25,75.77 102.07,76.01C101.88,76.25 101.61,76.4 101.32,76.43H101.2L96.46,76.52H96.33L91.33,75.85C91.17,75.84 91.02,75.76 90.92,75.64C90.81,75.52 90.76,75.36 90.77,75.19C90.78,75.03 90.86,74.88 90.98,74.78C91.11,74.67 91.27,74.62 91.43,74.63Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M101.68,75.01L97.03,75.58H96.77L91.14,74.56C91.03,74.49 90.94,74.39 90.89,74.27C90.83,74.15 90.82,74.02 90.84,73.89C90.86,73.77 90.92,73.65 91.01,73.55C91.09,73.46 91.21,73.39 91.34,73.36L97.01,73.89H96.75L101.32,72.87C101.6,72.8 101.9,72.86 102.15,73.01C102.4,73.17 102.58,73.42 102.64,73.71C102.7,74 102.65,74.3 102.49,74.54C102.34,74.79 102.09,74.97 101.8,75.03L101.68,75.01Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M100.2,77.83C103.1,77.28 106.04,77.03 108.99,77.1C109.28,77.17 109.59,77.17 109.88,77.12C110.18,77.06 110.46,76.94 110.7,76.76C110.94,76.59 111.15,76.36 111.3,76.1C111.45,75.84 111.54,75.56 111.57,75.26C112.21,72.91 111.18,71.29 106.35,70.91C105.83,70.91 101.4,70.91 101.22,71.35L100.2,77.83Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M108.63,71.24C108.63,71.24 107.26,68.5 100.96,69.63C99.2,69.95 97.3,69.9 97.01,70.1C96.73,70.31 96.82,71.13 97.54,71.45C98.5,71.6 99.48,71.64 100.45,71.56L103.99,72.67L108.63,71.24Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M102.54,74.06L97.26,74.28H97.1L92.09,73.55C92.01,73.54 91.93,73.51 91.86,73.48C91.79,73.44 91.73,73.39 91.68,73.33C91.57,73.2 91.52,73.04 91.54,72.88C91.55,72.72 91.63,72.58 91.75,72.47C91.88,72.37 92.04,72.32 92.2,72.34L97.26,72.62H97.1L102.32,71.81C102.62,71.76 102.92,71.83 103.17,72.01C103.41,72.18 103.58,72.44 103.63,72.74C103.68,73.04 103.61,73.34 103.44,73.59C103.26,73.83 103,74 102.7,74.05L102.54,74.06Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M103.43,77.05L97.28,78.16H97.04L93.18,77.46C93.04,77.43 92.91,77.34 92.82,77.22C92.74,77.09 92.7,76.94 92.73,76.79C92.76,76.65 92.84,76.53 92.96,76.44C93.07,76.36 93.22,76.32 93.36,76.34L97.21,76.81H96.97L103.12,75.52C103.33,75.48 103.54,75.53 103.71,75.64C103.89,75.76 104.01,75.93 104.06,76.14C104.08,76.24 104.08,76.34 104.06,76.45C104.04,76.55 104,76.65 103.94,76.73C103.89,76.82 103.81,76.9 103.73,76.95C103.64,77.01 103.54,77.05 103.44,77.07L103.43,77.05Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M91.76,73.37L96.38,73.85L99.33,73.56"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.37,74.64L96.09,74.98L99.33,74.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.14,72.85C99.46,73.8 99.26,74.54 99.25,76.27"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.44,71.56C101.97,72.15 103.59,72.44 105.22,72.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.55,76.27C100.86,75.35 102.4,74.81 103.99,74.72"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.41,76.27L98.56,76.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M74.22,180.76H68.46L74.49,140.6H84.76L74.22,180.76Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.59,180.76H192.34L186.32,140.6H176.05L186.59,180.76Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M189.21,143.52H73.31C71.14,143.52 69.03,142.74 67.38,141.32C65.74,139.9 64.65,137.94 64.33,135.79L59.42,103.08C59.22,101.78 59.31,100.46 59.67,99.21C60.03,97.95 60.66,96.79 61.52,95.8C62.37,94.81 63.43,94.01 64.62,93.47C65.8,92.93 67.1,92.64 68.4,92.65H194.12C195.43,92.64 196.72,92.92 197.91,93.46C199.1,94.01 200.16,94.8 201.02,95.79C201.87,96.78 202.5,97.95 202.87,99.2C203.23,100.46 203.32,101.78 203.12,103.08L198.21,135.74C197.9,137.91 196.82,139.88 195.16,141.31C193.51,142.74 191.39,143.53 189.21,143.52Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.11,111.54L199.63,144.96C199.29,146.44 198.46,147.76 197.28,148.71C196.09,149.66 194.62,150.18 193.1,150.18C192.76,150.18 192.41,150.15 192.08,150.1C191.77,150.14 191.47,150.16 191.16,150.15H69.02C68.71,150.16 68.41,150.14 68.11,150.1C66.43,150.37 64.72,149.99 63.31,149.03C61.91,148.08 60.92,146.63 60.54,144.98L53.06,111.54C52.83,110.56 52.83,109.54 53.05,108.56C53.27,107.57 53.71,106.65 54.34,105.86C54.96,105.07 55.76,104.44 56.67,104C57.58,103.57 58.57,103.34 59.58,103.34H63.42C65.07,103.35 66.66,103.96 67.88,105.05C69.11,106.15 69.88,107.66 70.06,109.3L72.98,135.55H187.24L190.11,109.35C190.29,107.71 191.07,106.2 192.29,105.1C193.52,104.01 195.1,103.4 196.75,103.39H200.59C201.59,103.39 202.58,103.62 203.49,104.05C204.39,104.48 205.19,105.12 205.81,105.9C206.44,106.68 206.88,107.6 207.1,108.57C207.33,109.55 207.33,110.56 207.11,111.54Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M161.98,76.93L134.01,80.46C133.31,80.58 132.6,80.56 131.91,80.39C131.23,80.23 130.58,79.93 130.01,79.51C129.45,79.09 128.97,78.56 128.62,77.95C128.26,77.34 128.03,76.67 127.94,75.97C127.86,75.27 127.91,74.56 128.1,73.88C128.29,73.2 128.62,72.57 129.07,72.02C129.51,71.47 130.06,71.02 130.68,70.69C131.31,70.36 131.99,70.16 132.69,70.1L160.66,66.57C161.35,66.45 162.06,66.47 162.75,66.64C163.43,66.8 164.07,67.11 164.64,67.52C165.21,67.94 165.68,68.47 166.04,69.08C166.39,69.69 166.62,70.36 166.71,71.06C166.8,71.76 166.74,72.47 166.55,73.14C166.36,73.82 166.04,74.45 165.59,75C165.15,75.55 164.6,76 163.98,76.33C163.36,76.66 162.68,76.87 161.98,76.93Z"
      android:fillColor="#B3B0CF"/>
  <path
      android:pathData="M134.76,80.85L134.08,80.77L109.64,77.14C107.33,76.82 106.76,76.04 106.87,74.16C106.99,72.28 107.81,71.09 110.53,71.21L134.56,70.44H135.35C136.03,70.48 136.7,70.65 137.32,70.95C137.94,71.25 138.49,71.67 138.95,72.18C139.4,72.69 139.75,73.28 139.98,73.93C140.21,74.58 140.3,75.26 140.26,75.95C140.22,76.63 140.05,77.3 139.75,77.92C139.46,78.54 139.04,79.09 138.53,79.55C138.01,80 137.42,80.35 136.77,80.58C136.13,80.8 135.44,80.9 134.76,80.86V80.85Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M134.84,80.69C134.62,80.68 134.39,80.66 134.17,80.62L134.84,80.69Z"
      android:fillColor="#B3B0CF"/>
  <path
      android:pathData="M177.3,102.57H147.49C141.74,102.57 137.08,107.23 137.08,112.98V125.14C137.08,130.89 141.74,135.55 147.49,135.55H177.3C183.05,135.55 187.71,130.89 187.71,125.14V112.98C187.71,107.23 183.05,102.57 177.3,102.57Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.78,116.71L96.8,126.21C96.8,126.21 91.27,129.37 89.41,130.55C87.93,131.48 84.83,132.95 84.83,132.95L82.5,133.19C82.2,133.23 81.92,133.37 81.72,133.59C81.52,133.82 81.41,134.11 81.41,134.41C81.4,134.58 81.44,134.74 81.5,134.89C81.56,135.04 81.65,135.18 81.77,135.29C81.89,135.41 82.03,135.5 82.18,135.56C82.33,135.62 82.49,135.65 82.66,135.64L90.59,135.42L104.2,135.51C104.62,135.51 105.04,135.42 105.43,135.24C105.82,135.07 106.16,134.8 106.43,134.48C106.7,134.15 106.9,133.76 107,133.35C107.11,132.94 107.12,132.5 107.03,132.09L106.6,126.96L112.19,117.37L102.78,116.71Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.17,127.3C130.01,126.8 129.89,126.28 129.79,125.76L117.43,88.33C116.84,86.4 117.02,84.31 117.94,82.51C118.85,80.71 120.42,79.33 122.33,78.66C124.23,77.99 126.32,78.08 128.16,78.92C130,79.75 131.45,81.26 132.2,83.14L150.38,117.83C150.87,118.6 151.26,119.42 151.54,120.28C153.3,125.75 148.7,132.19 143.25,134C134.47,136.89 130.48,128.13 130.48,128.13C130.37,127.86 130.26,127.59 130.17,127.3Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M117.82,82.22L99.14,113.88C96.52,118.1 99.06,120.03 99.91,120.35L109.63,124.39C109.87,124.61 110.16,124.79 110.47,124.9C110.79,125.01 111.12,125.06 111.45,125.04C111.78,125.02 112.11,124.94 112.41,124.79C112.71,124.65 112.97,124.44 113.19,124.2C116.02,120.5 124.33,106.84 127.14,100C129.51,94.18 131.39,90.01 131.39,90.01C131.46,89.9 131.53,89.78 131.6,89.65C132.12,88.74 132.46,87.74 132.59,86.7C132.72,85.65 132.64,84.6 132.35,83.59C132.07,82.58 131.58,81.63 130.92,80.82C130.26,80 129.45,79.32 128.52,78.82C127.6,78.32 126.58,78.02 125.54,77.92C124.49,77.82 123.44,77.94 122.44,78.26C121.44,78.57 120.51,79.09 119.71,79.77C118.91,80.46 118.26,81.29 117.79,82.23L117.82,82.22Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M109.58,122.92L100.86,118.62C100.3,118.35 99.62,118.57 99.35,119.13L98.23,121.4C97.95,121.96 98.18,122.64 98.74,122.91L107.46,127.21C108.02,127.49 108.7,127.26 108.97,126.7L110.09,124.43C110.36,123.87 110.14,123.2 109.58,122.92Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M100.23,119.9L109.63,124.39"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.33,165.1L92.18,176.08C92.18,176.08 87.68,179.4 85.82,180.58C84.32,181.51 81.18,183 81.18,183L78.86,183.25C78.55,183.28 78.28,183.43 78.07,183.65C77.87,183.87 77.75,184.17 77.75,184.47C77.75,184.63 77.78,184.8 77.85,184.95C77.91,185.1 78,185.24 78.12,185.35C78.24,185.46 78.38,185.55 78.53,185.62C78.68,185.68 78.85,185.7 79.01,185.7L86.94,185.47L100.55,185.57C100.97,185.57 101.4,185.48 101.78,185.3C102.17,185.12 102.51,184.86 102.78,184.53C103.05,184.2 103.24,183.82 103.35,183.4C103.45,182.99 103.46,182.56 103.38,182.14L101.8,177.26L106.92,167.62L98.33,165.1Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.22,135.97C159.71,136.01 159.21,136.01 158.7,135.97L121,136.27C119.02,136.26 117.11,135.5 115.66,134.14C114.21,132.77 113.33,130.91 113.2,128.93C113.07,126.95 113.7,124.99 114.96,123.45C116.22,121.91 118.01,120.91 119.98,120.64L156.87,115.71C157.69,115.44 158.55,115.29 159.42,115.26C161.95,115.37 164.36,116.41 166.18,118.17C168.01,119.93 169.13,122.29 169.34,124.82C169.55,127.35 168.83,129.86 167.31,131.9C165.8,133.93 163.59,135.34 161.11,135.87C160.78,135.93 160.5,135.96 160.22,135.97Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M189.15,87.46C184.79,96.45 177.83,119.18 177.82,119.18L148.27,111.92C152.26,103.15 154.42,86.8 159.14,78.11C162.22,72.41 164.78,68.28 170.41,69.29C173.84,69.91 181.01,73.91 184.25,74.91C189.54,76.54 191.76,82.1 189.15,87.46Z"
      android:fillColor="#C9C9C9"/>
  <path
      android:pathData="M165.71,81.67C170.15,81.67 173.74,78.07 173.74,73.64C173.74,69.2 170.15,65.6 165.71,65.6C161.27,65.6 157.68,69.2 157.68,73.64C157.68,78.07 161.27,81.67 165.71,81.67Z"
      android:fillColor="#C9C9C9"/>
  <path
      android:pathData="M178.42,66.59L185.7,68.72L184.76,73.16L176.08,72.68L178.42,66.59Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M172.29,117.74L169.04,129.32L136.53,130.74L148.56,110.78L172.29,117.74Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M113.71,124.84L92.8,164.93C92.56,165.39 92.42,165.89 92.38,166.4C92.34,166.92 92.4,167.43 92.56,167.93C92.73,168.41 92.98,168.87 93.32,169.26C93.67,169.64 94.08,169.96 94.54,170.18L105.25,175C106.17,175.44 107.22,175.5 108.18,175.16C109.14,174.83 109.93,174.13 110.38,173.22C113.97,165.89 121.79,150.21 124.22,143.96C125.58,140.42 127.14,136.95 128.9,133.58C128.9,133.58 131.1,124.01 124.27,121.41C120.19,119.87 115.66,120.99 113.71,124.84Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M204.73,97.56C205.39,98.79 205.53,100.22 205.13,101.55C204.73,102.88 203.81,103.99 202.59,104.64C201.37,105.3 199.93,105.44 198.61,105.04C197.28,104.64 196.16,103.73 195.51,102.5L182.19,77.65C181.53,76.43 181.39,75 181.8,73.67C182.2,72.34 183.11,71.23 184.33,70.58C185.56,69.93 186.99,69.78 188.32,70.19C189.64,70.59 190.76,71.5 191.41,72.73L204.73,97.56Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.24,119.7L150.09,117.34"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M182.94,89.36C188.64,89.36 193.26,84.74 193.26,79.04C193.26,73.35 188.64,68.73 182.94,68.73C177.24,68.73 172.63,73.35 172.63,79.04C172.63,84.74 177.24,89.36 182.94,89.36Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M104.22,172.48L95.34,168.5C94.78,168.25 94.11,168.5 93.86,169.07L92.82,171.38C92.57,171.95 92.82,172.62 93.39,172.87L102.26,176.85C102.83,177.1 103.5,176.85 103.75,176.28L104.79,173.97C105.04,173.4 104.79,172.74 104.22,172.48Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M94.66,169.58L103.69,174.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.1,88.52C185.43,97.21 177.81,119.31 177.81,119.31L148.55,110.78C152.41,102.31 154.31,87.92 158.66,79.02"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.03,79.68C158.03,79.68 143.54,82.42 138.67,82.58C133.79,82.75 122.7,81.56 119.78,81.45C117.27,81.32 116.07,80.23 116.05,78.37L115.92,68.93L137.59,69.47C142.82,68.78 154.86,65.03 160.05,64.79C166.43,64.51 168.46,66.44 168.46,66.44C166.33,70.83 164.48,75.34 162.91,79.96C158.63,92.86 154.82,108.55 154.82,108.55L149,107.02C149,107.02 156.29,83.74 159.76,75.26"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M158.03,79.68C158.03,79.68 143.54,82.42 138.67,82.58C133.79,82.75 122.7,81.56 119.78,81.45C117.27,81.32 116.07,80.23 116.05,78.37L115.92,68.93L137.59,69.47C142.82,68.78 154.86,65.03 160.05,64.79C166.43,64.51 168.46,66.44 168.46,66.44C166.33,70.83 164.48,75.34 162.91,79.96C158.63,92.86 154.82,108.55 154.82,108.55L149,107.02C149,107.02 156.29,83.74 159.76,75.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.58,65.24C156.25,70.02 156.78,74.82 158.15,79.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M164.6,75.31C164.65,73.24 164.9,71.18 165.33,69.16C165.94,67.11 167.05,65.24 168.57,63.73C169.62,62.91 170.95,62.53 172.27,62.67C173.6,62.8 174.82,63.44 175.68,64.46C173.59,65.59 171.68,67.04 170.03,68.75C167.72,71.31 164.6,75.31 164.6,75.31Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.73,52.5C187.93,50.25 186.21,49.94 185.57,47.43C185.46,46.97 185.37,46.51 185.31,46.04C185.25,45.67 185.31,45.3 185.48,44.97C185.07,45.06 184.71,45.28 184.45,45.6C184.26,45.91 184.15,46.26 184.13,46.63C184.3,46.29 183.7,45.6 183.5,45.29C183.15,44.82 182.74,44.41 182.28,44.05C181.34,43.32 180.21,42.88 179.03,42.77C179.16,43.29 179.43,43.75 179.57,44.25C179.37,43.88 179.07,43.58 178.7,43.37C178.34,43.16 177.92,43.05 177.5,43.06C176.68,43.04 175.88,43.23 175.15,43.6C174.4,43.93 173.69,44.34 172.92,44.63C172.16,44.93 171.31,44.96 170.52,44.71C170.46,45.04 170.49,45.39 170.61,45.71C170.73,46.03 170.93,46.31 171.19,46.52C169.6,47.68 169.06,50.11 170.58,53.22C170.62,52.58 170.87,51.98 171.28,51.5C171.27,52.03 171.39,52.55 171.62,53.01C171.86,53.48 172.21,53.88 172.65,54.18C172.68,53.39 172.93,52.62 173.38,51.96C173.85,51.31 174.51,50.81 175.26,50.53C175.11,50.82 175.03,51.14 175.03,51.47C175.03,51.8 175.11,52.12 175.26,52.42C176.12,51.48 177.27,50.87 178.53,50.67C182.37,50.38 186.01,51.84 186.73,52.5Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M184.31,63.43C184.83,61.62 183.74,59.71 181.87,59.17C179.99,58.63 178.05,59.66 177.53,61.47C177.01,63.28 178.1,65.19 179.97,65.73C181.84,66.27 183.78,65.24 184.31,63.43Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M188.78,58.68C188.78,58.82 188.71,58.95 188.65,59.09V59.18C188.08,60.85 186.96,62.29 185.47,63.26C183.99,64.23 182.22,64.67 180.46,64.53L181.36,67.6C181.75,68.63 179.69,70.31 176.78,71.45C173.87,72.6 171.82,72.38 171.44,71.39C171.39,71.22 171.38,71.04 171.41,70.86C171.44,70.68 171.51,70.51 171.62,70.37L170.85,65.81C170.11,65.67 169.36,65.49 168.59,65.3C168.22,65.21 167.87,65.04 167.57,64.81C167.27,64.57 167.02,64.28 166.85,63.94C166.68,63.6 166.58,63.22 166.56,62.84C166.54,62.46 166.61,62.08 166.76,61.73C167.26,60.47 167.66,59.49 168.14,58.1C167.76,57.66 167.32,57.28 166.83,56.97C166.51,56.77 166.57,56.35 166.92,56.14C167.27,55.92 168.56,54.94 169.49,54.17C169.62,53.79 169.85,53.03 169.99,52.65C170.57,50.8 171.84,49.25 173.54,48.31C175.24,47.37 177.23,47.11 179.1,47.59L182.46,48.53V48.62C182.92,48.69 183.36,48.8 183.8,48.95C185.7,49.64 187.28,51.03 188.2,52.83C189.13,54.64 189.33,56.73 188.78,58.68Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M171.07,54.97C171.07,55.33 170.86,55.6 170.72,55.58C170.58,55.56 170.49,55.26 170.55,54.91C170.6,54.55 170.74,54.27 170.88,54.29C171.03,54.31 171.11,54.62 171.07,54.97Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M167.88,59.22C168.35,59.42 168.84,59.55 169.35,59.58C169.85,59.62 170.36,59.57 170.84,59.45"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.84,51.84C171.42,51.92 171.97,52.16 172.43,52.52C172.76,52.78 173.03,53.09 173.24,53.45"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.62,53.65C170.85,53.67 171.07,53.75 171.27,53.87C171.47,54 171.63,54.17 171.76,54.37"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.37,57.99C176.49,57.88 176.64,57.8 176.8,57.76C176.97,57.71 177.14,57.71 177.3,57.74C177.46,57.78 177.62,57.86 177.75,57.96C177.88,58.07 177.99,58.21 178.06,58.36"
      android:strokeAlpha="0.4"
      android:strokeLineJoin="round"
      android:strokeWidth="0.2"
      android:fillColor="#00000000"
      android:strokeColor="#000000"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.68,50.82C170.74,50.76 170.8,50.68 170.88,50.62C172.11,50.04 173.49,49.83 174.84,50.02C175.19,50.07 175.53,50.14 175.87,50.23L176.11,50.28C176.33,50.28 176.54,50.24 176.74,50.15C177.2,50.02 177.68,49.99 178.16,50.06C177.61,50.2 177.11,50.49 176.71,50.89C175.79,52.27 175.12,53.79 174.72,55.4C174.62,55.83 174.72,56.99 175.12,57.11C175.52,57.23 176.48,56.4 177.01,56.16C176.74,56.56 176.39,56.9 175.99,57.18C176.05,58.55 176.09,60.5 176.18,60.71C176.42,61.22 177.03,62.06 177.32,61.92C177.38,61.89 177.43,61.85 177.45,61.79C177.48,61.73 177.49,61.66 177.48,61.6C177.7,62.29 177.99,62.95 178.33,63.58C178.53,64.04 178.86,64.42 179.29,64.67C179.71,64.93 180.21,65.04 180.7,64.99C180.79,64.99 180.78,65.26 180.81,65.35C181.1,66.23 184.91,65.18 187.61,62.16C188.82,60.79 192.04,55.45 187.3,50.58C186.28,47.04 180.94,43.63 173.98,46.12C171.75,46.87 169.47,48.77 170.68,50.82Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M178.65,60.42V56.97L169.52,52.33C169.45,52.3 169.38,52.29 169.3,52.3C169.22,52.31 169.15,52.34 169.09,52.39C169.04,52.44 168.99,52.51 168.97,52.58C168.95,52.65 168.95,52.73 168.97,52.81L170.11,56.43C170.13,56.52 170.19,56.6 170.27,56.65C170.36,56.7 170.45,56.72 170.55,56.71L174.65,56.02L177.84,57.59V60.45L178.65,60.42Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.41,55.81C176.82,55.71 177.25,55.75 177.64,55.91C178.03,56.08 178.35,56.37 178.56,56.74C179.04,57.76 177.85,59.25 176.82,59.81C176.68,59.9 175.34,60.71 174.77,60.2C173.98,59.55 174.65,56.25 176.41,55.81Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M177.62,57.7C177.54,57.53 177.41,57.38 177.24,57.29C177.07,57.21 176.87,57.18 176.68,57.22C176.3,57.31 175.95,57.49 175.66,57.75"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M209.42,127.61H207.72C207.62,127.6 207.53,127.56 207.46,127.49C207.39,127.42 207.35,127.33 207.34,127.23L207.62,124.48C207.62,124.39 207.82,124.3 207.91,124.3H209.14C209.33,124.3 209.42,124.39 209.42,124.48L209.71,127.14C209.9,127.42 209.71,127.61 209.42,127.61Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M208.86,124.2H208.24C208.2,124.19 208.15,124.17 208.11,124.14C208.08,124.11 208.06,124.06 208.05,124.01L208.24,122.55C208.24,122.26 208.63,122.26 208.72,122.55L209,124.06C209.04,124.1 208.95,124.2 208.86,124.2Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M208.86,124.6H208.24C208.05,124.6 207.96,124.5 207.96,124.32C207.95,124.28 207.95,124.24 207.96,124.2C207.98,124.16 208,124.13 208.02,124.1C208.05,124.07 208.09,124.05 208.13,124.04C208.16,124.03 208.21,124.03 208.24,124.03H208.91C209.1,124.03 209.19,124.13 209.19,124.32C209.19,124.51 209.04,124.48 208.86,124.6Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M209.71,145.44H207.82C207.68,145.45 207.54,145.41 207.43,145.32C207.32,145.23 207.26,145.1 207.24,144.97L207.06,127.9C207.04,127.76 207.09,127.62 207.18,127.52C207.26,127.41 207.39,127.35 207.53,127.33H209.42C209.56,127.32 209.7,127.36 209.81,127.45C209.91,127.54 209.98,127.67 210,127.8L210.18,144.87C210.21,144.93 210.21,145.01 210.2,145.08C210.18,145.15 210.15,145.22 210.1,145.28C210.06,145.34 210,145.38 209.93,145.41C209.86,145.44 209.78,145.45 209.71,145.44Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.99,152.93L208.1,152.46C207.72,152.35 207.34,151.98 207.44,151.6L211.8,117.95C211.89,117.57 212.27,117.19 212.65,117.28L216.54,117.75C216.91,117.84 217.29,118.22 217.2,118.6L212.84,152.26C212.84,152.37 212.82,152.47 212.77,152.57C212.73,152.66 212.66,152.75 212.58,152.81C212.5,152.88 212.4,152.92 212.3,152.94C212.19,152.96 212.09,152.96 211.99,152.93Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:fillType="evenOdd"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M216.35,154.31H205.83C205.45,154.3 205.1,154.15 204.83,153.88C204.57,153.62 204.41,153.26 204.4,152.89V135.01C204.41,134.63 204.57,134.28 204.83,134.01C205.1,133.75 205.45,133.59 205.83,133.58H216.35C216.72,133.59 217.08,133.75 217.34,134.01C217.61,134.28 217.76,134.63 217.76,135.01V152.93C217.75,153.29 217.59,153.64 217.33,153.9C217.07,154.15 216.72,154.3 216.35,154.31Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M219.15,180.69H184.85L190.09,158.47H219.15V180.69Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M227.7,153.94H181.1C179.62,153.94 178.43,155.14 178.43,156.62V157.12C178.43,158.6 179.62,159.79 181.1,159.79H227.7C229.18,159.79 230.38,158.6 230.38,157.12V156.62C230.38,155.14 229.18,153.94 227.7,153.94Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M172.89,76.09C172.89,76.09 168.88,90.06 167.89,94.93C166.91,99.8 162.97,110.41 162.97,110.41L183.47,115.2L188.07,89.76"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M172.89,76.09C172.89,76.09 168.88,90.06 167.89,94.93C166.91,99.8 162.97,110.41 162.97,110.41L183.47,115.2L188.07,89.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M199.76,105.37L199.1,105.3L175.01,102.71C174.12,102.67 173.27,102.28 172.66,101.62C172.05,100.96 171.72,100.08 171.76,99.18C171.8,98.28 172.19,97.43 172.85,96.82C173.51,96.21 174.39,95.89 175.29,95.92L199.37,94.96H200.16C201.52,95.04 202.8,95.66 203.72,96.67C204.64,97.68 205.12,99.02 205.07,100.38C205.02,101.75 204.43,103.04 203.43,103.98C202.44,104.92 201.12,105.43 199.75,105.4L199.76,105.37Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M190.24,94.6H197.41"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M168.94,103.3L168.83,103.38L165.83,105.57L166.67,109.95C166.69,110.1 166.65,110.25 166.57,110.37C166.48,110.49 166.35,110.57 166.21,110.61C166.07,110.64 165.91,110.62 165.78,110.55C165.65,110.48 165.55,110.37 165.5,110.23L164.08,105.4C164.03,105.26 164.03,105.11 164.07,104.97C164.11,104.83 164.18,104.7 164.29,104.6L164.37,104.54L167.44,101.68C167.66,101.5 167.94,101.4 168.22,101.42C168.51,101.44 168.77,101.56 168.97,101.77C169.16,101.98 169.27,102.26 169.26,102.54C169.26,102.83 169.15,103.1 168.95,103.3H168.94Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M170.38,103.88L170.3,103.95L167.03,106.52L167.26,111.13C167.26,111.3 167.21,111.47 167.09,111.59C166.98,111.72 166.82,111.8 166.65,111.81C166.49,111.81 166.33,111.76 166.21,111.65C166.08,111.55 166,111.4 165.98,111.24L165.28,106.15C165.26,106.03 165.27,105.9 165.31,105.79C165.35,105.67 165.41,105.56 165.5,105.47L165.57,105.41L168.85,102.25C169.06,102.06 169.34,101.96 169.62,101.98C169.9,101.99 170.17,102.11 170.37,102.31C170.56,102.52 170.68,102.79 170.68,103.07C170.68,103.35 170.57,103.63 170.38,103.84V103.88Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M171.8,104.35L171.69,104.46L168.95,107.11V111.31C168.94,111.46 168.88,111.6 168.77,111.71C168.66,111.82 168.52,111.88 168.36,111.88C168.22,111.88 168.08,111.83 167.97,111.73C167.86,111.63 167.79,111.5 167.78,111.36L167.35,106.8C167.34,106.71 167.35,106.62 167.37,106.53C167.39,106.44 167.44,106.36 167.49,106.29L167.55,106.21L170.07,102.99C170.16,102.86 170.27,102.76 170.4,102.68C170.53,102.61 170.67,102.56 170.82,102.54C170.96,102.52 171.11,102.53 171.26,102.56C171.4,102.6 171.54,102.67 171.65,102.76C171.77,102.85 171.87,102.97 171.94,103.1C172.01,103.23 172.06,103.37 172.07,103.52C172.09,103.67 172.07,103.82 172.03,103.96C171.98,104.1 171.91,104.23 171.82,104.35H171.8Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M173.01,104.9L170.86,107.56L170.58,110.46C170.56,110.61 170.49,110.75 170.37,110.84C170.26,110.94 170.11,110.98 169.96,110.97C169.82,110.95 169.69,110.89 169.6,110.78C169.5,110.67 169.45,110.54 169.45,110.39V107.24C169.46,107.11 169.5,106.99 169.57,106.89L171.69,103.98C171.82,103.81 172,103.7 172.21,103.67C172.41,103.63 172.62,103.68 172.79,103.81C172.88,103.86 172.95,103.93 173.01,104.02C173.07,104.11 173.11,104.2 173.13,104.31C173.16,104.41 173.16,104.51 173.14,104.61C173.12,104.72 173.07,104.82 173.01,104.9Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M166.91,102.25C168.63,100.42 170.46,98.68 172.38,97.06C172.9,96.5 173.58,96.12 174.34,95.98C175.09,95.84 175.86,95.96 176.54,96.31C178.66,97.34 179.44,99.88 175.84,103.08C175.45,103.42 174.07,104.48 173.25,105.13C173.07,105.27 172.84,105.36 172.61,105.37C172.38,105.39 172.14,105.33 171.94,105.21L166.91,102.25Z"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M165.91,110.41L165.34,105.92L167.89,103.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.7,106.01L169.43,103.91"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.32,95.77L175.28,95.92C174.75,95.89 174.21,95.97 173.71,96.17C173.21,96.36 172.76,96.66 172.39,97.05C170.17,98.92 164.32,104.6 164.32,104.6C164.21,104.7 164.13,104.83 164.09,104.97C164.05,105.11 164.06,105.26 164.1,105.4L165.51,110.25C165.53,110.34 165.58,110.43 165.65,110.5C165.72,110.57 165.8,110.62 165.9,110.65L165.98,111.26C166,111.43 166.09,111.57 166.21,111.67C166.33,111.78 166.49,111.83 166.65,111.83C166.82,111.82 166.98,111.74 167.1,111.61C167.21,111.49 167.27,111.32 167.26,111.15L167.03,106.54L167.71,106.01L167.47,106.32C167.42,106.39 167.38,106.47 167.35,106.55C167.33,106.64 167.32,106.73 167.33,106.82L167.76,111.38C167.77,111.52 167.84,111.65 167.95,111.75C168.06,111.85 168.2,111.9 168.34,111.9C168.5,111.9 168.65,111.84 168.75,111.73C168.86,111.63 168.93,111.48 168.93,111.33V107.14L170.66,105.46L169.63,106.95C169.56,107.05 169.52,107.17 169.51,107.3V110.46C169.51,110.6 169.56,110.74 169.66,110.84C169.75,110.95 169.88,111.01 170.02,111.03C170.17,111.04 170.32,111 170.44,110.9C170.55,110.81 170.62,110.68 170.64,110.53L170.93,107.62L172.76,105.35C174.07,104.77 175.23,103.91 176.17,102.84L181.04,103.24"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.33,95.9L180.97,104.04C181.01,104.21 181.1,104.35 181.23,104.46C181.36,104.56 181.53,104.62 181.7,104.61L185.2,104.52L182.28,94.97L180.01,95.04C179.9,95.05 179.8,95.07 179.7,95.12C179.61,95.16 179.53,95.23 179.46,95.31C179.4,95.4 179.35,95.49 179.33,95.59C179.31,95.7 179.31,95.8 179.33,95.9Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.2,82.86L190.24,94.6H182.26L185.04,104.1C185.32,105.01 185.87,105.81 186.62,106.39C187.37,106.97 188.29,107.31 189.24,107.34C193.34,107.48 200.03,107.66 202.07,107.34C205.06,106.83 207.11,102.01 206.33,99.49C205.55,96.97 196.78,72.69 191.35,68.75C187.1,65.68 182.26,68.14 182.26,68.14"
      android:fillColor="#DEDEDE"/>
  <path
      android:pathData="M184.2,82.86L190.24,94.6H182.26L185.04,104.1C185.32,105.01 185.87,105.81 186.62,106.39C187.37,106.97 188.29,107.31 189.24,107.34C193.34,107.48 200.03,107.66 202.07,107.34C205.06,106.83 207.11,102.01 206.33,99.49C205.55,96.97 196.78,72.69 191.35,68.75C187.1,65.68 182.26,68.14 182.26,68.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.24,94.6H197.41"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M172.9,76.09C172.9,76.09 174.01,66.11 179.04,64.43C179.65,64.18 180.31,64.06 180.96,64.08C181.62,64.09 182.27,64.23 182.87,64.5C183.47,64.76 184.01,65.15 184.46,65.63C184.91,66.1 185.26,66.67 185.49,67.28C183.31,67.8 181.25,68.76 179.44,70.08C177.15,71.96 174.96,73.96 172.9,76.09Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#DEDEDE"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.68,83.68C186.51,78.68 191,74.76 195.54,74.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.02,69.26L112.76,69.42C112.59,69.43 112.43,69.5 112.32,69.63C112.2,69.76 112.14,69.93 112.15,70.1L112.69,79.32C112.7,79.47 112.76,79.61 112.86,79.71C112.95,79.82 113.08,79.89 113.22,79.92L116.43,80.16L116.02,69.26Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#C9C9C9"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.14,99.97L123.64,107.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.13,71.16L108.62,71.24C108.46,71.14 107.74,68.27 101.51,69.54C99.76,69.9 97.3,69.9 97.02,70.1C96.75,70.31 96.83,71.13 97.55,71.45C97.93,71.61 99.71,71.45 100.46,71.56L100.21,72.28L97.14,72.65L92.24,72.39C92.09,72.38 91.95,72.43 91.84,72.52C91.72,72.61 91.65,72.74 91.62,72.88C91.61,72.97 91.62,73.06 91.65,73.15C91.68,73.23 91.73,73.31 91.8,73.37H91.38C91.21,73.35 91.04,73.39 90.9,73.5C90.76,73.6 90.67,73.75 90.65,73.92C90.63,74.09 90.67,74.27 90.78,74.4C90.88,74.54 91.03,74.63 91.2,74.65H91.41C91.25,74.65 91.1,74.71 90.99,74.83C90.88,74.94 90.81,75.09 90.81,75.25C90.81,75.4 90.88,75.55 90.99,75.67C91.1,75.78 91.25,75.84 91.41,75.84L96.53,76.51L98.6,76.43L97.13,76.75L93.4,76.29C93.25,76.27 93.11,76.31 92.99,76.4C92.88,76.48 92.8,76.61 92.77,76.75C92.76,76.83 92.76,76.9 92.77,76.97C92.79,77.05 92.82,77.11 92.86,77.18C92.9,77.24 92.95,77.29 93.02,77.33C93.08,77.37 93.15,77.4 93.22,77.41C93.22,77.41 97.18,78.16 97.24,78.14C97.24,78.14 107.16,76.99 108.99,77.11L112.44,77.56"
      android:strokeLineJoin="round"
      android:strokeWidth="0.46"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.64,23.43V24.46C106.43,24.31 106.22,24.18 105.99,24.07C105.24,23.71 104.42,23.48 103.59,23.41C102.81,23.35 101.9,23.35 100.8,23.35H57.31C56.2,23.35 55.26,23.35 54.51,23.41C53.67,23.44 52.85,23.67 52.11,24.07C51.89,24.18 51.67,24.31 51.47,24.46V19.67C51.47,18.89 51.62,18.11 51.92,17.39C52.22,16.66 52.65,16 53.21,15.45C53.76,14.89 54.42,14.45 55.15,14.15C55.87,13.85 56.65,13.7 57.43,13.7H69.38C70.77,13.73 72.12,14.21 73.21,15.06L77.31,18.43H100.95C102.33,18.49 103.64,19.02 104.68,19.93C105.71,20.84 106.41,22.08 106.64,23.43Z"
      android:fillColor="#8D65AC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M109.15,29.41C109.15,30.19 109.09,31.16 109.02,32.2V32.27L107.1,57.65V57.76C107.06,58.59 106.97,59.41 106.83,60.23C106.72,60.98 106.48,61.7 106.13,62.37C105.53,63.42 104.62,64.26 103.53,64.77C102.82,65.06 102.08,65.24 101.32,65.29C100.61,65.36 99.76,65.36 98.85,65.36H59.2C58.22,65.36 57.44,65.36 56.73,65.29C55.97,65.24 55.22,65.07 54.51,64.77C53.44,64.24 52.54,63.41 51.92,62.37C51.57,61.7 51.33,60.98 51.2,60.23C51.07,59.41 50.99,58.59 50.95,57.76V57.7L49.13,32.27V32.2C49.06,31.12 49,30.19 49,29.41C48.99,28.56 49.14,27.73 49.45,26.94C49.88,25.98 50.55,25.16 51.4,24.54C51.6,24.39 51.82,24.25 52.05,24.15C52.8,23.79 53.62,23.57 54.45,23.51C55.23,23.43 56.14,23.43 57.25,23.43H100.74C101.83,23.43 102.79,23.43 103.53,23.51C104.36,23.53 105.19,23.75 105.92,24.15C106.13,24.29 106.38,24.41 106.58,24.54C107.46,25.15 108.17,25.97 108.63,26.94C108.99,27.71 109.17,28.56 109.15,29.41Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.3,42.07H68.81C67.26,42.07 66.01,43.33 66.01,44.87C66.01,46.42 67.26,47.67 68.81,47.67H89.3C90.85,47.67 92.1,46.42 92.1,44.87C92.1,43.33 90.85,42.07 89.3,42.07Z"
      android:fillColor="#B3B0CF"/>
</vector>
