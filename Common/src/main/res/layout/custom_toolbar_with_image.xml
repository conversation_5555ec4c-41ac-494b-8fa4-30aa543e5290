<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.toolbar.COUIToolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    style="@style/COUIToolBarInAppBarLayoutStyle"
    app:supportTitleTextAppearance="@style/textAppearanceSecondTitle">

    <!-- 自定义内容容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 左侧图片 -->
        <ImageView
            android:id="@+id/toolbar_left_image"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_toolbar_logo"
            android:contentDescription="@string/app_name"
            android:scaleType="centerInside" />

        <!-- 标题文本 -->
        <TextView
            android:id="@+id/toolbar_title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/textAppearanceSecondTitle"
            android:textColor="?attr/couiColorPrimary"
            android:singleLine="true"
            android:ellipsize="end" />

        <!-- 右侧图片（可选） -->
        <ImageView
            android:id="@+id/toolbar_right_image"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_toolbar_action"
            android:contentDescription="@string/action_description"
            android:scaleType="centerInside"
            android:visibility="gone"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</com.coui.appcompat.toolbar.COUIToolbar>
