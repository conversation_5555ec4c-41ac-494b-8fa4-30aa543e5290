/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryAppMarketManager.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.appmarketmanager.di

import androidx.annotation.Keep
import com.oplus.filemanager.appmarketmanager.AppMarketManagerApi
import com.oplus.filemanager.interfaze.categoryappmarketamanager.ICategoryAppMarketManagerApi
import org.koin.dsl.module

@Keep
class AutoDIForCategoryAppMarketManager {

    val categoryAppMarketManagerApi = module {
        single<ICategoryAppMarketManagerApi>(createdAtStart = true) {
            AppMarketManagerApi
        }
    }
}