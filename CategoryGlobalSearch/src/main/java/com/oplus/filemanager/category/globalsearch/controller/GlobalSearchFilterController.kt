/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.globalsearch
 * * Version     : 1.0
 * * Date        : 2020/08/03
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.controller

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.annotation.SuppressLint
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.manager.filter.ConnectState
import com.oplus.filemanager.category.globalsearch.manager.filter.ConnectStateHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchFilterPanelAnimHelper
import com.oplus.filemanager.category.globalsearch.view.FlowLayout
import com.oplus.filemanager.category.globalsearch.view.ScrollDividerLayout
import java.lang.ref.WeakReference
import java.util.Locale

class GlobalSearchFilterController(lifecycle: Lifecycle) : BaseLifeController {
    companion object {
        private const val TAG = "GlobalSearchFilterController"
    }

    var mIsShow = false

    private var mWeakRefAct: WeakReference<ComponentActivity>? = null
    private var mRootLayout: ViewGroup? = null
    private var mMaskLayout: View? = null
    private var mPanelScrollView: ScrollView? = null
    private var mFlowLayout: FlowLayout? = null
    private var mParent: ViewGroup? = null
    private var mContainerLayout: ScrollDividerLayout? = null
    private var mContainerTextView: TextView? = null
    private var mContainerFiltrate: TextView? = null
    private var mContainerExpend: ImageView? = null
    private var mSelectTipLine: View? = null
    private var mHasInitView = false
    private var mCategory = GlobalSearchActivity.TAB_ALL
    private var mListener: SearchFilterClickListener? = null
    private var mSupportList: List<FilterCondition>? = null
    private var mFilterSelect: Map<Int, FilterItem>? = null
    private var mFirstShowFilter = true
    private var mResultCount = 0
    private var mPanelDrawable: GradientDrawable? = null
    private val mAnimHelper = GlobalSearchFilterPanelAnimHelper()
    private var mChipViewHelper: ChipViewHelper? = null
    private var navigationBarHeight: Int = 0
    private var isGestureNavMode: Boolean = false
    private var rootHeight: Int = 0
    private var rectTopHeight: Int = 0
    private var showSoftkey: Boolean = false

    // 用于管理 RecyclerView 焦点的变量
    private var mBoundRecyclerView: RecyclerView? = null
    private var mOriginalRecyclerViewImportance: Int = View.IMPORTANT_FOR_ACCESSIBILITY_AUTO

    private var connectStateCallback: ConnectStateHelper.ConnectStateChangeCallback =
        object : ConnectStateHelper.ConnectStateChangeCallback {
            override fun onConnectStateChanaged(connectState: ConnectState) {
                Log.i(TAG, "onConnectStateChanged $connectState controller ${this@GlobalSearchFilterController}, mCategory $mCategory")
                //需要切换线程
                mParent?.post {
                    mSupportList = FilterConditionManager.updateSupportFilter(mCategory)
                    initFilterView()
                    mContainerTextView?.text = getTitleString()
                    reLayoutFilterPanel()
                    //这里需要检测一下，当前由于链接断开导致的消失的筛选项中是否存在内存中
                    val removedFilters = checkNeedRemoveSelectedFilterItem(connectState, mFilterSelect)
                    if (removedFilters.isNotEmpty()) {
                        mListener?.onFileterItemRemoved(removedFilters)
                    }
                    if (!connectState.hasRemotePC) {
                        CustomToast.showLong(com.filemanager.common.R.string.device_offline)
                    }
                }
            }
        }

    init {
        lifecycle.addObserver(this)
        ConnectStateHelper.registerConnectStateChangeCallback(connectStateCallback, true)
    }

    private inline fun withActivity(block: (act: ComponentActivity) -> Unit) {
        mWeakRefAct?.get()?.let { act ->
            if (act.isFinishing || act.isDestroyed) {
                return
            }
            block.invoke(act)
        }
    }

    fun init(act: FragmentActivity, parent: ViewGroup?, category: Int): ViewGroup? {
        if (mParent != null) {
            Log.e(TAG, "init: controller only init for once")
            return null
        }

        if (null == parent) {
            Log.e(TAG, "init:parent is null")
            return null
        }

        mParent = parent
        mWeakRefAct = WeakReference(act)
        mCategory = category
        mSupportList = FilterConditionManager.getSupportFilter(mCategory)

        mPanelDrawable = GradientDrawable()
        mPanelDrawable?.setColor(COUIContextUtil.getAttrColor(act, com.support.appcompat.R.attr.couiColorBackgroundWithCard))

        initContainer(act, parent)

        if (parent.indexOfChild(mContainerLayout) == -1) {
            parent.addView(mContainerLayout)
        } else {
            Log.e(TAG, "init: view is in layout")
        }
        //这里加selectState的处理是为了在页面重建时，重新init的时候，保持上一次重建前的选中状态
        processSelectedState()
        return mContainerLayout
    }

    private fun initContainer(act: FragmentActivity, parent: ViewGroup?) {
        isGestureNavMode = StatusBarUtil.checkIsGestureNavMode(act)
        mContainerLayout = LayoutInflater.from(act).inflate(R.layout.search_filter_container, parent, false) as? ScrollDividerLayout
        mContainerTextView = mContainerLayout?.findViewById(R.id.result)
        mContainerFiltrate = mContainerLayout?.findViewById(R.id.filtrate)
        mContainerLayout?.setAccessibilityDelegate(object : View.AccessibilityDelegate() {
            override fun onInitializeAccessibilityNodeInfo(
                host: View,
                info: AccessibilityNodeInfo
            ) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                info.className = Button::class.java.name
                info.isSelected = mContainerLayout?.isClickable ?: false
                info.addAction(AccessibilityNodeInfo.ACTION_CLICK)
            }
        })
        mContainerLayout?.setAccessibilityTraversalAfter(R.id.filtrate)
        mSelectTipLine = mContainerLayout?.findViewById(R.id.filtrate_line)
        val titleString = getTitleString()
        mContainerTextView?.text = titleString
        setContainerLayoutContentDescription(titleString)
        mContainerExpend = mContainerLayout?.findViewById(R.id.filter_expend)
        mContainerLayout?.setOnClickListener {
            mListener?.onChangeFilterPanel()
            StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.SEARCH_CONDITION_CLICK)
            navigationBarHeight = Utils.getNavigationBarHeight(act)
            val screenHeight = WindowUtils.getScreenHeight(act)
            val rect = Rect()
            act.window.decorView.getWindowVisibleDisplayFrame(rect)
            rectTopHeight = rect.top
            showSoftkey = (rectTopHeight < screenHeight / GlobalSearchActivity.MAX_SPLIT_SCREEN)
            rootHeight = mRootLayout?.height ?: 0
            if (showSoftkey && !isGestureNavMode && rootHeight != 0 &&
                rootHeight == (mPanelScrollView?.height
                    ?: 0) + GlobalSearchActivity.navigationBarHeight
            ) {
                mRootLayout?.updateLayoutParams<ViewGroup.LayoutParams> {
                    height = rootHeight - GlobalSearchActivity.navigationBarHeight
                    rootHeight = height
                }
            }
        }
    }

    fun showOrHideFilter(show: Boolean = true) {
        mContainerLayout?.apply {
            visibility = if (show) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
        }
    }

    fun reLayoutFilterPanel() {
        mRootLayout?.post {
            val originHeight = mPanelScrollView?.height ?: -1
            // In the split screen state, when adjusting the width,
            // the execution result of the Runnable of the first post will be before the layout result,
            // and occasionally interspersed in the middle of the two layouts,
            // and the adjusted real height is obtained through the second post.
            Log.i(TAG, "reLayoutFilterPanel -> originHeight = $originHeight")
            mFlowLayout?.post {
                performAdjustPanel(mPanelScrollView, originHeight)
            }
        }
    }

    private fun performAdjustPanel(
        target: ViewGroup?,
        originHeight: Int
    ) {
        if (target == null) {
            Log.e(TAG, "performAdjustPanel -> target is null")
            return
        }
        target.clearAnimation()
        // same height, no need to adjust the height
        val rootHeight = mRootLayout?.height ?: 0
        Log.i(TAG, "performAdjustPanel -> rootHeight = $rootHeight ; FlowLayout = ${mFlowLayout?.height}")
        val flowLayoutHeight = mFlowLayout?.height ?: originHeight
        val height = if (rootHeight < flowLayoutHeight) {
            rootHeight
        } else {
            mFlowLayout?.height ?: originHeight
        }
        // Adjust the height of the panel so that the content is fully displayed.
        val heightLayoutParams = target.layoutParams
        heightLayoutParams.height = height
        Log.d(TAG, "performAdjustPanel -> height = $height")
        target.layoutParams = heightLayoutParams
    }

    fun showOrHideFilterPanel(show: Boolean? = null) {
        if (Utils.isQuickClick()) {
            Log.d(TAG, "showOrHideFilterPanel: is quick click")
            return
        }
        Log.d(TAG, "showOrHideFilterPanel: mIsShow=$mIsShow")
        if (show != null) {
            mIsShow = !show
        }
        mContainerLayout?.setHasDivider(mIsShow)
        if (mIsShow) {
            hideFilter()
        } else {
            showFilter()
        }
    }

    private fun showFilter() {
        mSelectTipLine?.visibility = View.INVISIBLE
        initFilter()

        mIsShow = true
        mFilterSelect?.let { onFilterSelectChanged(it) }

        blockRecyclerViewFocus()

        // because of lazy init, should use post to get Correct size
        mRootLayout?.post {
            controlFilterPanel(
                isReverse = false,
                animationStart = {
                    mRootLayout?.visibility = View.VISIBLE
                    mContainerLayout?.isClickable = false
                    mContainerLayout?.isEnabled = false
                    mContainerLayout?.isFocusable = false
                },
                animationEnd = {
                    mContainerLayout?.isClickable = true
                    mContainerLayout?.isFocusable = true
                    mContainerLayout?.isEnabled = true
                })
        }
    }

    private fun hideFilter() {
        mIsShow = false

        // 恢复 RecyclerView 的焦点
        restoreRecyclerViewFocus()

        mRootLayout?.post {
            controlFilterPanel(
                isReverse = true,
                animationStart = {
                    mContainerLayout?.isClickable = false
                    mContainerLayout?.isFocusable = false
                    mContainerLayout?.isEnabled = false
                },
                animationEnd = {
                    mRootLayout?.visibility = View.INVISIBLE
                    mSelectTipLine?.visibility = View.VISIBLE
                    mContainerLayout?.isClickable = true
                    mContainerLayout?.isFocusable = true
                    mContainerLayout?.isEnabled = true
                }
            )
        }
    }

    private fun controlFilterPanel(
        isReverse: Boolean,
        animationStart: ((animation: Animator) -> Unit)? = null,
        animationEnd: ((animation: Animator) -> Unit)? = null
    ) {
        Log.i(TAG, "controlFilterPanel -> isReverse = $isReverse; targetHeight = ${mFlowLayout?.height}")
        mFlowLayout?.post {
            val flowLayoutHeight = mFlowLayout?.height ?: -1
            Log.i(TAG, "controlFilterPanel -> FlowLayout Height = $flowLayoutHeight MeasureHeight = ${mFlowLayout?.measuredHeight}")
            rootHeight = mRootLayout?.height ?: 0
            Log.d(TAG, "controlFilterPanel -> rootHeight = $rootHeight")
            mAnimHelper.createAnim(isReverse) {
                buildMaskAnim(mMaskLayout)
                buildPanelContent(mFlowLayout)
                buildPanel(mPanelScrollView, mPanelDrawable, rootHeight, flowLayoutHeight)
            }.play(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    animationStart?.invoke(animation)
                }

                override fun onAnimationEnd(animation: Animator) {
                    animationEnd?.invoke(animation)
                    val panelScrollViewHeight = mPanelScrollView?.measuredHeight ?: 0
                    val rootLayoutHeight = mRootLayout?.measuredHeight ?: 0
                    if (panelScrollViewHeight != 0 && rootLayoutHeight != 0 && panelScrollViewHeight > rootLayoutHeight) {
                        mPanelScrollView?.updateLayoutParams<ViewGroup.LayoutParams> {
                            height = rootLayoutHeight
                        }
                    }
                }
            })
        }
    }

    private fun getTitleString(): String = MyApplication.appContext.resources.run {
        mContainerTextView?.let {
            if (mFirstShowFilter) {
                mContainerLayout?.visibility = View.INVISIBLE
                mFirstShowFilter = false
                return ""
            }
            val sb = StringBuilder()
            sb.append(
                getQuantityString(
                    com.filemanager.common.R.plurals.search_result_count,
                    mResultCount,
                    mResultCount
                )
            )
            val filterTitle = getFilterTitleStringList()
            if (filterTitle.isEmpty()) {
                return sb.toString()
            }
            val separate = when (Locale.getDefault().language) {
                Locale.CHINA.language,
                Locale.CHINESE.language,
                Locale.SIMPLIFIED_CHINESE.language,
                Locale.TRADITIONAL_CHINESE.language -> "、"
                else -> ","
            }
            sb.append("\t(")
            filterTitle.forEach { str ->
                sb.append(str).append(separate)
            }
            sb.replace(sb.lastIndex, sb.length, ")")
            val originTextWidth: Int = it.paint.measureText(sb.toString()).toInt()
            val textViewWidth: Int = it.width
            if (textViewWidth in 1 until originTextWidth) {
                sb.replace(sb.lastIndex - 2, sb.lastIndex, "...")
                // In some languages, exactly the same size will not display the complete final character
                while ((it.paint.measureText(sb.toString()) + 5) > textViewWidth) {
                    sb.replace(sb.lastIndex - 4, sb.lastIndex, "...")
                }
            }
            return sb.toString()
        } ?: let {
            Log.e(TAG, "getTitleString: mContainerTextView is null")
        }
        return ""
    }

    /**
     * 根据选择的filteItem列表，构建相应item的描述字符串list
     */
    private fun getFilterTitleStringList(): ArrayList<String> {
        val filterTitle = arrayListOf<String>()
        mSupportList?.forEach { condition ->
            mFilterSelect?.get(condition.id)?.let { filterItem ->
                val id = filterItem.id
                condition.items?.find { item ->
                    id == item.id
                }?.let { item ->
                    if (item.checkVisibleInCategory(mCategory)) {
                        filterTitle.add(item.desc)
                    } else {
                        //这里选中的时不可见的filterItem的情况下，不将这个item的文案加入到结果中
                        Log.w(TAG, "getFilterTitleStringList item $item not visible in category $mCategory")
                    }
                }
            }
        }
        return filterTitle
    }

    fun updateTitle(result: Int, visible: Boolean = true) {
        mResultCount = result
        val titleString = getTitleString()
        mContainerTextView?.text = getTitleString()
        setContainerLayoutContentDescription(titleString)
        mContainerTextView?.alpha = 1f
        mContainerTextView?.visibility = if (visible) {
            View.VISIBLE
        } else {
            View.INVISIBLE
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initFilter() {
        if (mHasInitView) {
            return
        }

        withActivity { act ->
            mRootLayout = LayoutInflater.from(act).inflate(R.layout.search_filter, mParent, false) as ViewGroup?
            mRootLayout?.setOnTouchListener { _, motionEvent ->
                if (mIsShow && (motionEvent.y >= (mFlowLayout?.bottom ?: 0))) {
                    showOrHideFilterPanel(false)
                    return@setOnTouchListener true
                }
                return@setOnTouchListener false
            }
            mMaskLayout = mRootLayout?.findViewById(R.id.search_filter_mask)
            mPanelScrollView = mRootLayout?.findViewById(R.id.search_filter_panel_scroll_view)
            mFlowLayout = mRootLayout?.findViewById(R.id.search_filter_list)
            mFlowLayout?.apply {
                mGravity = FlowLayout.LEFT
                setOnClickListener {
                    // do not thing
                }
            }

            if (mParent?.indexOfChild(mRootLayout) == -1) {
                mParent?.addView(mRootLayout)
                val lp = mRootLayout?.layoutParams
                if (lp is RelativeLayout.LayoutParams) {
                    mContainerLayout?.id?.let {
                        lp.addRule(RelativeLayout.BELOW, it)
                    }
                }
            }
            initFilterView()

            mRootLayout?.post {
                setupFilterPanelAccessibility()
            }

            mHasInitView = true
            mRootLayout?.visibility = View.INVISIBLE
        }
    }

    fun checkUpdateSupportList() {
        val needAdditionFrom = FilterConditionManager.getNeedAdditionFrom()
        //如果配置项改变才会重新刷新数据，避免频繁重复刷新
        if (FilterConditionManager.isNeedAdditionFrom != needAdditionFrom) {
            mSupportList = FilterConditionManager.updateSupportFilter(mCategory)
            initFilterView()
            val titleString = getTitleString()
            mContainerTextView?.text = getTitleString()
            mContainerLayout?.contentDescription = titleString
            setContainerLayoutContentDescription(titleString)
        }
    }

    private fun initFilterView() {
        if (mSupportList.isNullOrEmpty()) {
            Log.e(TAG, "initFilterView: supportList is empty")
            return
        }
        val titleString = getTitleString()
        mContainerTextView?.text = getTitleString()
        mContainerLayout?.contentDescription = titleString
        setContainerLayoutContentDescription(titleString)
        withActivity { _ ->
            mFlowLayout?.apply {
                removeAllViews()
                mChipViewHelper = ChipViewHelper(this)
                mSupportList?.forEach { condition ->
                    if (condition.items.isNullOrEmpty().not()) {
                        mChipViewHelper!!.inflateTitleView(condition.desc)
                        mChipViewHelper!!.inflateChipGroupView(condition.getVisibleItems(mCategory)) { chip, filterItem ->
                            chip.setTag(R.id.search_filter_keyword, filterItem)
                            chip.setOnClickListener {
                                it.getTag(R.id.search_filter_keyword)?.also { tag ->
                                    if (tag is FilterItem) {
                                        mListener?.onFilterClick(tag)
                                        statisticsFilterItemClick(tag)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun statisticsFilterItemClick(item: FilterItem) {
        var keyName: String? = null
        var keyValue: String? = null
        when (item.parent.id) {
            FilterConstants.FILTER_TIME -> {
                keyName = when (item.id) {
                    FilterConstants.FILTER_TIME_TODAY -> StatisticsUtils.SEARCH_CONDITION_TIME_TODAY
                    FilterConstants.FILTER_TIME_3_DAY -> StatisticsUtils.SEARCH_CONDITION_TIME_3DAYS
                    FilterConstants.FILTER_TIME_7_DAY -> StatisticsUtils.SEARCH_CONDITION_TIME_7DAYS
                    FilterConstants.FILTER_TIME_30_DAY -> StatisticsUtils.SEARCH_CONDITION_TIME_30DAYS
                    else -> null
                }
            }

            FilterConstants.FILTER_FROM -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_SUPERAPP1
                keyValue = item.packageName
            }

            FilterConstants.FILTER_DOC -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_DOC
                keyValue = when (item.id) {
                    FilterConstants.FILTER_DOC_PDF -> "0"
                    FilterConstants.FILTER_DOC_DOC -> "1"
                    FilterConstants.FILTER_DOC_PPT -> "2"
                    FilterConstants.FILTER_DOC_XLS -> "3"
                    FilterConstants.FILTER_DOC_TXT -> "4"
                    FilterConstants.FILTER_DOC_OFD -> "5"
                    else -> null
                }
            }

            FilterConstants.FILTER_AUDIO -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_AUDIO
                keyValue = when (item.id) {
                    FilterConstants.FILTER_AUDIO_MP3 -> "0"
                    FilterConstants.FILTER_AUDIO_M4A -> "1"
                    FilterConstants.FILTER_AUDIO_AMR -> "2"
                    FilterConstants.FILTER_AUDIO_OGG -> "3"
                    FilterConstants.FILTER_AUDIO_AAC -> "4"
                    else -> null
                }
            }

            FilterConstants.FILTER_VIDEO -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_VIDEO
                keyValue = when (item.id) {
                    FilterConstants.FILTER_VIDEO_MP4 -> "0"
                    FilterConstants.FILTER_VIDEO_MKV -> "1"
                    FilterConstants.FILTER_VIDEO_AVI -> "2"
                    FilterConstants.FILTER_VIDEO_WMV -> "3"
                    FilterConstants.FILTER_VIDEO_3GP -> "4"
                    else -> null
                }
            }

            FilterConstants.FILTER_IMG -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_PICTURE
                keyValue = when (item.id) {
                    FilterConstants.FILTER_IMG_JPG -> "1"
                    FilterConstants.FILTER_IMG_PNG -> "2"
                    FilterConstants.FILTER_IMG_GIF -> "3"
                    FilterConstants.FILTER_IMG_BMP -> "4"
                    FilterConstants.FILTER_IMG_HEIF -> "5"
                    else -> null
                }
            }

            FilterConstants.FILTER_COMPRESS -> {
                keyName = StatisticsUtils.SEARCH_CONDITION_ARCHIVE
                keyValue = when (item.id) {
                    FilterConstants.FILTER_COMPRESS_RAR -> "1"
                    FilterConstants.FILTER_COMPRESS_ZIP -> "2"
                    FilterConstants.FILTER_COMPRESS_JAR -> "3"
                    FilterConstants.FILTER_COMPRESS_7ZIP -> "4"
                    else -> null
                }
            }
        }
        if (keyValue.isNullOrEmpty()) {
            if (!keyName.isNullOrEmpty()) {
                StatisticsUtils.onCommon(MyApplication.sAppContext, keyName)
            }
        } else {
            val valueMap = mapOf(keyName to keyValue)
            StatisticsUtils.onCommon(MyApplication.sAppContext, keyName, valueMap)
        }
    }

    fun setFilterTitleEnable(enable: Boolean) {
        mContainerTextView?.isEnabled = enable
        mContainerExpend?.isEnabled = enable
        mContainerFiltrate?.isEnabled = enable
        mContainerLayout?.isClickable = enable
        mContainerLayout?.isEnabled = enable
    }

    private fun processSelectedState() {
        val isSelected = checkSelectState()
        updateFilterIconSelectState(isSelected)
        updateFilterTextApperance(isSelected)
    }

    private fun updateFilterTextApperance(isSelected: Boolean) {
        mContainerFiltrate?.let {
            if (isSelected) {
                it.setTextAppearance(com.support.appcompat.R.style.couiTextButtonM)
            } else {
                it.setTextAppearance(com.support.appcompat.R.style.couiTextBodyM)
            }
            val textColor = it.context?.resources?.getColorStateList(R.color.selector_label_primary_text_color, mWeakRefAct?.get()?.theme)
            if (textColor != null) {
                it.setTextColor(textColor)
            }
        }
    }

    private fun checkSelectState(): Boolean {
        var isSelected = false
        mSupportList?.let { supportList ->
            run loop@{
                for (condition in supportList) {
                    val filterItem = mFilterSelect?.get(condition.id) ?: continue
                    val id = filterItem.id
                    condition.items?.find { item ->
                        id == item.id
                    }?.let { item ->
                        if (item.checkVisibleInCategory(mCategory)) {
                            isSelected = true
                            return@loop
                        } else {
                            //这里选中的时不可见的filterItem的情况下，不将这个item的文案加入到结果中
                            Log.w(TAG, "checkSelectState item $item not visible in category $mCategory")
                        }
                    }
                }
            }
        }
        Log.i(TAG, "checkSelectState mSupportList $mSupportList, mFilterSelect $mFilterSelect, isSelected $isSelected")
        return isSelected
    }

    private fun updateFilterIconSelectState(selected: Boolean) {
        if (selected) {
            mContainerExpend?.setImageResource(R.drawable.selector_filter_icon_selected)
        } else {
            mContainerExpend?.setImageResource(R.drawable.selector_filter_icon)
        }
    }

    fun onFilterSelectChanged(filter: Map<Int, FilterItem>) {
        mFilterSelect = filter
        mFlowLayout?.let {
            mChipViewHelper?.getChipViewList()?.forEach { child ->
                val tag = child.getTag(R.id.search_filter_keyword)
                if (tag is FilterItem) {
                    (child as? COUIChip)?.isChecked = (filter[tag.parent.id]?.id == tag.id)
                }
            }
        }
        //这里添加是用户在筛选面板点击选中或取消选中时，对筛选图标的处理
        processSelectedState()
    }

    fun setFilterClickListener(listener: SearchFilterClickListener) {
        mListener = listener
    }

    fun getLine(): View? {
        return mSelectTipLine
    }


    fun bindRecyclerView(inputRecyclerView: RecyclerView) {
        Log.i(TAG, "bindRecyclerView $this mCategory $mCategory couiRecyclerView $inputRecyclerView")
        mContainerLayout?.bindRecyclerView(inputRecyclerView)
        mBoundRecyclerView = inputRecyclerView
        mOriginalRecyclerViewImportance = inputRecyclerView.importantForAccessibility
    }

    /**
     * 在 TalkBack 模式下屏蔽 RecyclerView 的焦点
     */
    private fun blockRecyclerViewFocus() {
        mBoundRecyclerView?.let { recyclerView ->
            Log.d(TAG, "blockRecyclerViewFocus: Blocking RecyclerView focus for TalkBack")
            recyclerView.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS
        }
    }

    /**
     * 恢复 RecyclerView 的焦点
     */
    private fun restoreRecyclerViewFocus() {
        mBoundRecyclerView?.let { recyclerView ->
            Log.d(TAG, "restoreRecyclerViewFocus: Restoring RecyclerView focus for TalkBack")
            recyclerView.importantForAccessibility = mOriginalRecyclerViewImportance
        }
    }

    /**
     * 设置筛选面板的无障碍属性
     * 让面板本身不获得焦点，但子视图可以获得焦点
     */
    private fun setupFilterPanelAccessibility() {
        mFlowLayout?.let { flowLayout ->
            // FlowLayout 本身不获得焦点，但允许子视图获得焦点
            flowLayout.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            Log.d(TAG, "setupFilterPanelAccessibility: Set flow layout to not focusable")
        }
    }

    override fun onDestroy() {
        mParent = null
        mWeakRefAct?.clear()
        mListener = null
        mAnimHelper.clear()
        mBoundRecyclerView = null
        ConnectStateHelper.unRegisterConnectStateChangeCallback(connectStateCallback)
    }

    /**
     * 检测是否存断开链接导致的筛选选item中是否存在于现在被选中的筛选item中
     * 如果存在，则输出消失的选中的筛选item列表
     * @param connectState 当前的链接状态
     * @param filterSelect 当前的已选择的筛选item的map
     * 输出：消失的选中的筛选item列表
     */
    private fun checkNeedRemoveSelectedFilterItem(
        connectState: ConnectState,
        filterSelect: Map<Int, FilterItem>?
    ): MutableList<FilterItem> {
        val result = mutableListOf<FilterItem>()
        if (filterSelect.isNullOrEmpty()) {
            Log.i(TAG, "checkNeedRemoveSelectedFilterItem filterSelect empty, return false")
            return result
        }
        if (!connectState.hasDfm) {
            val dfmFilterItems = filterSelect.filter { entry ->
                entry.value.id == FilterConstants.FILTER_FROM_DFM
            }.map { it.value }
            if (dfmFilterItems.isNotEmpty()) {
                result.addAll(dfmFilterItems)
            }
        }
        if (!connectState.hasSdcard) {
            val sdcardFilterItems = filterSelect.filter { entry ->
                entry.value.id == FilterConstants.FILTER_FROM_SD
            }.map { it.value }
            if (sdcardFilterItems.isNotEmpty()) {
                result.addAll(sdcardFilterItems)
            }
        }
        if (!connectState.hasOtg) {
            val otgFilterItems = filterSelect.filter { entry ->
                Log.i(TAG, "checkNeedRemoveSelectedFilterItem OTG id ${FilterConstants.FILTER_FROM_OTG}, entry.value.id ${entry.value.id}")
                entry.value.id == FilterConstants.FILTER_FROM_OTG
            }.map { it.value }
            if (otgFilterItems.isNotEmpty()) {
                result.addAll(otgFilterItems)
            }
        }
        Log.i(TAG, "checkNeedRemoveSelectedFilterItem $connectState, filterSelect $filterSelect, result size: ${result.size}")
        return result
    }

    fun processThirdAppSwitchChanged() {
        Log.i(TAG, "processThirdAppSwitchChanged")
        mParent?.post {
            mSupportList = FilterConditionManager.getSupportFilter(mCategory)
            initFilterView()
            val titleString = getTitleString()
            mContainerTextView?.text = getTitleString()
            mContainerLayout?.contentDescription = titleString
            setContainerLayoutContentDescription(titleString)
            reLayoutFilterPanel()
        }
    }

    private fun setContainerLayoutContentDescription(titleString: String) {
        mContainerLayout?.contentDescription = titleString + "\u3000" + mContainerFiltrate?.text
    }
}

interface SearchFilterClickListener {
    fun onFilterClick(filterItem: FilterItem)
    fun onChangeFilterPanel()
    fun onFileterItemRemoved(itemList: List<FilterItem>)
}
