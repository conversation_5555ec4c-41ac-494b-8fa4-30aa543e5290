/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemoteMacFileVH
 ** Description : 远程电脑文件的结果item的ViewHolder
 ** Version     : 1.0
 ** Date        : 2025/02/13 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.adapter.vh

import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.doOnNextLayout
import androidx.core.view.setPadding
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.adapter.GlobalSearchAdapter

class RemoteFileVH(val adapter: GlobalSearchAdapter, convertView: View) : BaseSelectionViewHolder(convertView, true) {

    companion object {

        private const val TAG = "RemoteFileVH"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.search_remote_file_item
        }

        const val CHOICE_MODE_ALPHA_0_2 = 0.2f
        const val CHOICE_MODE_ALPHA_0_3 = 0.3f
        const val CHOICE_MODE_ALPHA_1 = 1f
        const val DESC_TEXT_SIZE = 14.0f
    }

    private var rootView: ConstraintLayout? = null
    private var iconImg: FileThumbView
    private var titleTv: TextViewSnippet
    private var contentLayout: ConstraintLayout
    private var sourceImg: ImageView
    private var pcNameTv: TextView
    private var fileDetailTv: TextView
    private var divider1: View
    private val imgRadius =
        MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    init {
        rootView = convertView.findViewById(R.id.item_layout)
        iconImg = convertView.findViewById(R.id.file_list_item_icon)
        contentLayout = convertView.findViewById(R.id.rl_item_title)
        titleTv = convertView.findViewById(R.id.file_list_item_title)
        sourceImg = convertView.findViewById(R.id.file_list_item_source_img)
        pcNameTv = convertView.findViewById(R.id.file_list_item_pc_name)
        divider1 = convertView.findViewById(R.id.file_list_item_divider_line_1)
        fileDetailTv = convertView.findViewById(R.id.file_list_item_detail)
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
    }

    override fun isInDragRegionImpl(event: MotionEvent): Boolean = false


    fun bindData(
        data: RemoteFileBean,
        deviceName: String,
        keyword: String?,
        choiceMode: Boolean,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        Log.i(TAG, "bindData data $data, keyword: $keyword")
        itemCount = adapter.getRealFileItemCount()
        val fileName = data.mDisplayName
        val type = data.mLocalType
        if (fileName == null) {
            Log.d(TAG, "bindData path is null")
            return
        }
        setItemAlpha(data, adapter as GlobalSearchAdapter, choiceMode)
        showFileIcon(iconImg, type, data, imgRadius)
        showTitle(titleTv, data, keyword)
        showFileDetail(fileDetailTv, data)
        showPCNameTv(pcNameTv, deviceName)
        showCheckBox(adapter, data.isDir(), choiceMode, null)
    }

    private fun showFileIcon(imageView: FileThumbView, type: Int, data: BaseFileBean, radius: Int) {
        val context = imageView.context
        val padding = when (type) {
            MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> {
                MyApplication.sAppContext.resources.getDimension(com.filemanager.common.R.dimen.file_list_image_padding)
                    .toInt()
            }

            else -> 0
        }
        imageView.setPadding(padding)
        if (MimeTypeHelper.IMAGE_TYPE == type || MimeTypeHelper.VIDEO_TYPE == type) {
            imageView.setStrokeStyle(FileThumbView.STROKE_2DP)
        } else {
            imageView.setStrokeStyle(FileThumbView.STROKE_NONE)
        }
        FileImageLoader.sInstance.clear(context, imageView)
        FileImageLoader.sInstance.displayDefault(
            data,
            imageView,
            0,
            radius,
            FileImageLoader.THUMBNAIL_TYPE_LIST,
            loadDocThumbnail = false,
            isCoverError = true,
            isSmallDoc = true
        )
    }

    private fun showTitle(titleTv: TextViewSnippet, data: RemoteFileBean, keyword: String?) {
        val displayName = data.mDisplayName

        if (keyword.isNullOrEmpty()) {
            titleTv.text = displayName
        } else {
            if (Utils.isNeededTargetLanguage(Constants.SNIPPET_LANGUAGE)) {
                titleTv.text = displayName
            } else {
                titleTv.setTextWithPost(displayName, keyword)
            }
        }

        titleTv.post {
            val title = displayName ?: return@post
            val moreOne = titleTv.isMoreThanOneLine(title)
            rootView?.let {
                setIconConstraintSet(it, moreOne)
            }
        }
    }



    private fun showSourceIcon(imageView: ImageView, choiceMode: Boolean) {
        if (choiceMode) {
            imageView.alpha = CHOICE_MODE_ALPHA_0_2
        } else {
            imageView.alpha = CHOICE_MODE_ALPHA_1
        }
    }


    private fun showPCNameTv(textView: TextView, deviceName: String) {
        textView.textSize = DESC_TEXT_SIZE
        COUIChangeTextUtil.adaptFontSize(textView, COUIChangeTextUtil.G2)
        textView.text = deviceName

        fileDetailTv.doOnNextLayout { // 当文件时间显示不全时，将前面的远程电脑名称打点显示
            val detailWidth = fileDetailTv.paint.measureText(fileDetailTv.text.toString())
            val deviceNameWidth = textView.paint.measureText(deviceName).toInt()
            val totalWidth =
                textView.context.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_26dp) + deviceNameWidth + detailWidth
            val titleWidth = contentLayout.width
            val overSize = (totalWidth - titleWidth).toInt()

            Log.d(TAG, "deviceName:$deviceNameWidth detail:$detailWidth total:$totalWidth title:$titleWidth  over:$overSize")
            if (overSize > 0) { // 底部超过了屏幕间距，将前面的名称间距缩短
                textView.width = Math.max(deviceNameWidth - overSize, 0)
            } else {
                textView.width = deviceNameWidth
            }
        }
    }

    private fun showFileDetail(detail: TextView, file: RemoteFileBean) {
        detail.textSize = DESC_TEXT_SIZE
        COUIChangeTextUtil.adaptFontSize(detail, COUIChangeTextUtil.G2)
        val context = detail.context
        val remoteBean = file as RemoteFileBean
        detail.text = ""
        val formatStorageDetail = if (file.mIsDirectory) {
            val fileCount = remoteBean.folderFileNum
            MyApplication.sAppContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.text_x_items,
                fileCount,
                fileCount
            )
        } else {
            KtUtils.formatSize(file)
        }
        val lastModified = file.mDateModified
        val dateAndTime = Utils.getDateFormat(context, lastModified)
        detail.text = Utils.formatDetail(context, formatStorageDetail, dateAndTime)
    }

    private fun showCheckBox(adapter: BaseSelectionRecycleAdapter<*, *>, isDir: Boolean, choiceMode: Boolean, rightView: View?) {
        mCheckBox?.let {
            adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, rightView, it, layoutPosition, userDefault = false))
        }
    }

    private fun setItemAlpha(file: RemoteFileBean, adapter: GlobalSearchAdapter, choiceMode: Boolean) {
        val isIllegal = EmojiUtils.containsIllegalCharFileName(file.mDisplayName)
        val alpha = if (isIllegal) {
            HiddenFileHelper.getAlphaWithHidden(true, adapter.mIsDarkModel)
        } else if (choiceMode) {
            HiddenFileHelper.getAlphaWithHidden(adapter.selectRemoteFile == false, adapter.mIsDarkModel)
        } else {
            HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        }
        rootView?.alpha = alpha
        mCheckBox?.alpha = alpha
    }

    /**
     * 当标题显示一行时，左边的icon在整个item中上下居中
     * 当标题显示两行时，左边的icon顶部和标题顶部对齐
     */
    private fun setIconConstraintSet(rootView: ConstraintLayout, isMoreThanOneLine: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.rl_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    override fun drawDivider(): Boolean {
        return adapter.isNextItemSame(bindingAdapterPosition)
    }

    override fun getDividerStartAlignView(): View {
        return titleTv
    }

    override fun getDividerEndInset(): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp)
    }
}