/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  w9007122                  2020/7/21  1.0        -
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.controller

import android.graphics.drawable.RippleDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.preference.COUIRecommendedDrawable
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.ColorUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.ui.CommonUtil
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.filemanager.category.globalsearch.view.FlowLayout
import com.oplus.filemanager.interfaze.setting.ISetting

class GlobalSearchHistoryController(lifecycle: Lifecycle) : BaseLifeController {
    companion object {
        private const val TAG = "GlobalSearchHistoryController"
    }

    @VisibleForTesting
    var mViewModel: GlobalSearchViewModel? = null
    private var mSearchHistoryLayout: ViewGroup? = null
    private var mSearchTitle: TextView? = null
    private var mSearchClear: ImageView? = null
    private var mSearchFlowLayout: FlowLayout? = null
    private var mCardParentLayout: LinearLayout? = null
    private var mCardLayout: ConstraintLayout? = null
    private var ignoreBotton: TextView? = null
    private var grantBotton: COUIButton? = null
    private var mListener: SearchHistoryInterface? = null
    private var mParent: ViewGroup? = null
    // 判断是否是添加历史
    @VisibleForTesting
    var isAdd = false

    init {
        lifecycle.addObserver(this)
    }

    private fun initController(act: GlobalSearchActivity, parent: ViewGroup) {
        mViewModel = ViewModelProvider(act).get(GlobalSearchViewModel::class.java)
        mViewModel?.mHistoryDataModel?.observe(act, {
            updateView(it)
        })

        mSearchHistoryLayout = LayoutInflater.from(act).inflate(R.layout.search_histroy, parent, false) as ViewGroup?
        mSearchHistoryLayout!!.apply {
            val height = parent.findViewById<View>(R.id.appbar_layout)?.height ?: resources.getDimensionPixelOffset(
                com.filemanager.common.R.dimen.toolbar_height)
            setPadding(paddingStart, height, paddingRight, paddingBottom)
            Log.i(TAG, "initController set paddingTop $height, get PaddingTop ${this.paddingTop}")
        }
        mSearchTitle = mSearchHistoryLayout?.findViewById(R.id.search_history_title)
        ViewHelper.setClassificationTextSize(act, mSearchTitle, COUIChangeTextUtil.G4)

        mSearchClear = mSearchHistoryLayout?.findViewById(R.id.search_history_clear)
        mSearchClear?.setOnClickListener {
            mViewModel?.clearHistory()
            StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.SEARCH_HISTORY_RECORD_CLEARUP)
        }

        mSearchFlowLayout = mSearchHistoryLayout?.findViewById(R.id.search_history_list)
        mSearchFlowLayout?.apply {
            maxLine = 2
            mGravity = FlowLayout.LEFT

            setOnNotAddViewListener { notAddView ->
                if (shouldRemoveHistory(notAddView)) {
                    val removeHistory = mutableListOf<GlobalSearchViewModel.SearchHistoryModel>()
                    notAddView.forEach {
                        val data = it?.getTag(R.id.search_history_keyword)
                        if (data is GlobalSearchViewModel.SearchHistoryModel) {
                            removeHistory.add(data)
                        }
                    }
                    mViewModel?.removeHistory(removeHistory)
                }
            }
        }
        initCardView(act)
    }

    private fun initCardView(act: GlobalSearchActivity) {
        mCardParentLayout = mSearchHistoryLayout?.findViewById(R.id.card_parent_layout)
        showOrHideCardView()
        mCardLayout = mSearchHistoryLayout?.findViewById(R.id.permission_root_layout)
        // 设置背景图片
        val bgColor = ColorUtil.getCouiColorCardBackground(act)
        val radius = COUIContextUtil.getAttrDimens(act, com.support.appcompat.R.attr.couiRoundCornerM)
        mCardLayout?.background = COUIRecommendedDrawable(radius.toFloat(), bgColor)

        val cardLayoutLp = mCardLayout?.layoutParams as MarginLayoutParams
        cardLayoutLp.marginStart = 0
        cardLayoutLp.marginEnd = 0
        ignoreBotton = mSearchHistoryLayout?.findViewById(R.id.button_ignore)
        COUITextViewCompatUtil.setPressRippleDrawable(ignoreBotton)
        grantBotton = mSearchHistoryLayout?.findViewById(R.id.button_grant)
        ignoreBotton?.setOnClickListener {
            mCardParentLayout?.visibility = View.GONE
            PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_CARD_IGNORE, value = true)
            StatisticsUtils.statisticsThirdFileCardClick(ignoreBotton?.context, StatisticsUtils.THIRD_FILE_CARD_CLICK_IGNORE)
        }
        grantBotton?.setOnClickListener {
            val settingApi = Injector.injectFactory<ISetting>()
            settingApi?.jumpToSettingFunctionActivity(act)
            StatisticsUtils.statisticsThirdFileCardClick(grantBotton?.context, StatisticsUtils.THIRD_FILE_CARD_CLICK_OPEN)
        }
    }

    /**
     * 由于添加历史导致历史记录显示超过了最大行数，超过的view被删除，只有这种情况才需要删除
     */
    @VisibleForTesting
    fun shouldRemoveHistory(list: List<View?>): Boolean {
        val isRemove = isAdd && list.isNotEmpty()
        if (isRemove) {
            isAdd = false
        }
        return isRemove
    }

    private fun updateView(historyList: List<GlobalSearchViewModel.SearchHistoryModel>) {
        if (historyList.isNullOrEmpty()) {
            Log.d(TAG, "updateView: history is empty")
            mSearchTitle?.visibility = View.GONE
            mSearchClear?.visibility = View.GONE
            mSearchFlowLayout?.visibility = View.GONE
            mCardParentLayout?.setPadding(0, 0, 0, 0)
            return
        }

        mSearchTitle?.visibility = View.VISIBLE
        mSearchClear?.visibility = View.VISIBLE

        mSearchFlowLayout?.let {
            it.visibility = View.VISIBLE
            updateList(historyList)
        }
    }

    private fun updateList(historyList: List<GlobalSearchViewModel.SearchHistoryModel>) {
        mSearchFlowLayout?.let { flowLayout ->
            flowLayout.removeAllViews()
            val chipViewHelper = ChipViewHelper(flowLayout, chipLayoutRes = R.layout.search_history_keyword_item)
            for (item in historyList) {
                chipViewHelper.inflateChipView(item.mKey ?: "") {
                    it.setOnClickListener { view ->
                        (view.background as? RippleDrawable)?.jumpToCurrentState()
                        mListener?.onHistoryClickListener(item.mKey)
                        StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.SEARCH_HISTORY_RECORD_CLICK)
                    }
                    it.setTag(R.id.search_history_keyword, item)
                }
            }
        }
        setCardLayoutTopPadding()
    }

    /**
     * 这里修改为doOnPreDraw方法是为了修复发现的搜索列表在反色重建时，导致界面一致不显示的问题、
     * （问题原因：重建后onPreviewDraw回调中的mSearchFlowLayout?.viewTreeObserver
     *  跟外部的mSearchFlowLayout?.viewTreeObserver 不是同一个对象，导致无法移除成功，回调
     *  中一致设置padding，padding设置之后一直回调onPreDraw，死循环导致界面不显示）
     */
    private fun setCardLayoutTopPadding() {
        mSearchFlowLayout?.doOnPreDraw {
            val showHeight = mSearchFlowLayout?.getShowedHight()
            Log.i(TAG, "onPreDraw showHeight $showHeight")
            if (showHeight != null && showHeight != -1) {
                mCardParentLayout?.setPadding(0, showHeight, 0, 0)
            }
        }
    }

    fun show(act: GlobalSearchActivity, parent: ViewGroup?): ViewGroup? {
        if (parent == null) {
            Log.w(TAG, "show failed: parent is null")
            return null
        }

        if (mSearchHistoryLayout == null) {
            initController(act, parent)
        }
        if (parent.indexOfChild(mSearchHistoryLayout) == -1) {
            parent.addView(mSearchHistoryLayout)
        }
        mViewModel?.loadHistoryData()
        mParent = parent
        return mSearchHistoryLayout
    }

    fun showOrHideCardView() {
        if (CommonUtil.checkNeedShowCard()) {
            mCardParentLayout?.visibility = View.VISIBLE
        } else {
            mCardParentLayout?.visibility = View.GONE
        }
    }

    fun dismiss() {
        mSearchHistoryLayout?.apply {
            mParent?.removeView(this)
        }
    }

    fun addHistory(txt: String) {
        isAdd = mViewModel?.onQueryTextSubmit(txt) ?: false
        Log.d(TAG, "addHistory result:$isAdd")
    }

    fun setOnHistoryClickListener(listener: SearchHistoryInterface?) {
        mListener = listener
    }

    override fun onDestroy() {
        mParent = null
        mListener = null
        mSearchFlowLayout?.release()
    }
}

interface SearchHistoryInterface {
    fun onHistoryClickListener(txt: String?)
}