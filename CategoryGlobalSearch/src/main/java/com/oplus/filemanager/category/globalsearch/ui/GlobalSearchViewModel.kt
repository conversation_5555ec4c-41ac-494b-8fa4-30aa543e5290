/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search.GlobalSearchViewModel
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.util.ArrayMap
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.base.FileLoader
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.FilterConstants.FILTER_FROM_CURRENT
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.oplus.filemanager.category.globalsearch.bean.SearchCardWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchResultSubList
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.manager.history.GlobalSearchHistoryManager
import com.oplus.filemanager.category.globalsearch.ui.loader.DriveFileSearchLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.GlobalSearchLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.GlobalSearchRecycleBinLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.LocalMixLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.MixSearchLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.RemoteFileSearchLoader
import com.oplus.filemanager.category.globalsearch.ui.loader.ThirdAppFileSearchLoader
import com.oplus.filemanager.dfm.DFMManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

@SuppressLint("NullSafeMutableLiveData")
class GlobalSearchViewModel(internal val mSavedState: SavedStateHandle) : BaseViewModel() {
    private var lastSearchTime = 0L
    private var lastSearchKey = ""

    /**
     * 左右滑动时才检测2秒内是否重复搜索
     */
    var needCheckThisTime: Boolean = false

    @VisibleForTesting
    var mExternalCategory: Int = GlobalSearchActivity.TAB_ALL

    //Map中Key中Int值指的时condition的Id, value中的FilterItem指的是当前选中的的筛选项的fileterItem
    internal val mFilterSelectedDataModel = MutableLiveData<ArrayMap<Int, FilterItem>>()
    internal val mSearchDataModel = MutableLiveData<GlobalSearchResult>()
    internal val mHistoryDataModel = MutableLiveData<MutableList<SearchHistoryModel>>()
    private var mLoaderController: LoaderController? = null
    private val mLoaderCallBack = SearchLoaderCallBack(this)

    companion object {
        private const val TAG = "GlobalSearchViewModel"
        private const val LAST_SELECT_FILTERS = "LAST_SELECT_FILTERS"
        internal const val LAST_SEARCH_KEY = "LAST_SEARCH_KEY"
        internal const val LAST_SELECT_TAB = "LAST_SELECT_TAB"
        private const val SAME_KEYWORD_SEARCH_TIME_INTERVAL = 2000L
    }

    fun init(category: Int, intent: Intent) {
        mExternalCategory = category
        // When change dark mode/font size, the data will be cached, so should clear it first
        mSearchDataModel.value = null
        checkDefaultSelectFilter(intent)
        addCurrentFolderFilter(intent)
    }

    private fun checkDefaultSelectFilter(intent: Intent) {
        val filterMap = mFilterSelectedDataModel.value ?: ArrayMap()
        // First check the default select filter
        Log.d(TAG, "checkDefaultSelectFilter start $mExternalCategory")
        when (mExternalCategory) {
            CategoryHelper.CATEGORY_QQ -> {
                val superAppPackage = IntentUtils.getString(intent, KtConstants.P_PACKAGE)
                if (superAppPackage.isNullOrEmpty().not()) {
                    Log.d(TAG, "checkDefaultSelectFilter: package=$superAppPackage")
                    intent.removeExtra(KtConstants.P_PACKAGE)
                    FilterConditionManager.findFilterItemByPackageName(GlobalSearchActivity.TAB_ALL, superAppPackage!!)
                } else null
            }
            CategoryHelper.CATEGORY_DOWNLOAD -> {
                FilterConditionManager.findFilterItemByDesc(
                    GlobalSearchActivity.TAB_ALL, MyApplication.sAppContext.getString(com.filemanager.common.R.string.download))
            }
            CategoryHelper.CATEGORY_DFM -> {
                val dfmDeviceName = DFMManager.getDFSDeviceName()
                if (dfmDeviceName.isNullOrEmpty()) {
                    Log.e(TAG, "checkDefaultSelectFilter dfm deviceName null, return")
                    null
                } else {
                    val item = FilterConditionManager.findFilterItemByDesc(GlobalSearchActivity.TAB_ALL, dfmDeviceName)
                    Log.d(TAG, "checkDefaultSelectFilter dfmDeviceName $dfmDeviceName, item $item")
                    item
                }
            }
            else -> null
        }?.let {
            Log.d(TAG, "checkDefaultSelectFilter: Find the default filter[$it], filterMap $filterMap, parentConditionId ${it.parent.id}")
            filterMap[it.parent.id] = it
        }

        // Then check the restore filter
        mSavedState.get<ArrayList<FilterItem>>(LAST_SELECT_FILTERS)?.forEach {
            Log.d(TAG, "checkDefaultSelectFilter: Find the restore filter[$it]")
            filterMap[it.parent.id] = it
        }

        mFilterSelectedDataModel.value = filterMap
    }

    @VisibleForTesting
    fun addCurrentFolderFilter(intent: Intent) {
        if (mExternalCategory == CategoryHelper.CATEGORY_FILE_BROWSER || mExternalCategory == CategoryHelper.CATEGORY_OTG_BROWSER) {
            val filterList = FilterConditionManager.getSupportFilter(mExternalCategory)
            val filterCondition = filterList?.get(1) // FILTER_FROM FilterCondition
            filterCondition?.let {
                val desc = MyApplication.sAppContext.getString(com.filemanager.common.R.string.current_folder)
                val path = IntentUtils.getString(intent, KtConstants.CURRENT_DIR)
                (it.items as MutableList).add(0, FilterItem(FILTER_FROM_CURRENT, it, desc, path))
            }
        }
    }

    fun getLoadController(): LoaderController {
        if (mLoaderController == null) {
            mLoaderController = LoaderController()
        }
        return mLoaderController!!
    }

    override fun onCleared() {
        Log.i(TAG, "onCleared")
        super.onCleared()
        FilterConditionManager.recycle()
        mLoaderController?.onDestroy()
    }

    class SearchLoaderCallBack(viewModel: GlobalSearchViewModel) : OnLoaderListener<GlobalSearchResult> {
        var mSearchKey: String? = null
        private var mLoader: GlobalSearchLoader? = null
        private var mViewModel: WeakReference<GlobalSearchViewModel> = WeakReference(viewModel)
        private var mLoading = false

        internal fun readyLoadData() {
            mLoading = true
        }

        internal fun loadData(word: String) {
            mLoading = true
            mSearchKey = word
            if (mLoader == null) {
                mViewModel.get()?.apply {
                    getLoadController().initLoader(mExternalCategory, this@SearchLoaderCallBack)
                }
            } else {
                mLoader?.setSearchKey(word)
                mLoader?.forceLoad()
            }
        }

        internal fun cancelLoad() {
            mLoading = false
            mLoader?.cancelLoad()
        }

        internal fun isLoading() = mLoading

        private var startTime = 0L

        override fun onCreateLoader(): FileLoader<GlobalSearchResult> {

            fun getGlobalSearchLoader(category: Int): GlobalSearchLoader {
                Log.d(TAG, "getGlobalSearchLoader category:$category")
                startTime = System.currentTimeMillis()
                return if (category == CategoryHelper.CATEGORY_RECYCLE_BIN) {
                    GlobalSearchRecycleBinLoader()
                } else {
                    //这里传入的DFM类型（从分布式文件管理过来时传入的DFM类型）和其他的类型都使用MixSearchLoader，采用混合搜索
                    val loaders = mutableListOf<GlobalSearchLoader>()
                    val localLoader = LocalMixLoader()
                    loaders.add(localLoader)
                    loaders.add(RemoteFileSearchLoader())
                    loaders.add(DriveFileSearchLoader())
                    loaders.add(ThirdAppFileSearchLoader())
                    MixSearchLoader(loaders)
                }
            }

            val viewModel = mViewModel.get()
            return if (viewModel != null) {
                mLoader = getGlobalSearchLoader(viewModel.mExternalCategory)
                mSearchKey?.let { mLoader!!.setSearchKey(it) }
                return mLoader as GlobalSearchLoader
            } else {
                FileLoader(MyApplication.sAppContext)
            }
        }

        override fun onLoadComplete(data: GlobalSearchResult?) {
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "SearchLoaderCallBack onLoadFinished size=" + data?.uriLoadResult?.mResultList?.size)
            mLoading = false
            val viewModel = mViewModel.get()
            viewModel?.onSearchDataReturn(data) ?: Log.w(TAG, "onLoadComplete: viewModel is null")
            OptimizeStatisticsUtil.searchSuccess(startTime, endTime, endTime - startTime)
        }
    }

    fun onThirdAppCardIgnore() {
        //需要验证
        val tempResult = mSearchDataModel.value
        val allTableFiles = tempResult?.searchResultSubList?.allTableFiles ?: return
        Log.i(TAG, "onThirdAppCardIgnore rangeMap")
        allTableFiles.removeIf {
            it is SearchCardWrapper
        }
        this.onSearchDataReturn(tempResult)
    }

    internal fun onSearchDataReturn(data: GlobalSearchResult?) {
        data?.let {
            mSearchDataModel.postValue(it)
        } ?: Log.w(TAG, "onSearchDataReturn: data is null")
    }

    fun onQueryTextChange(word: String?) {
        Log.d(TAG, "onQueryTextChange word $word")
        val timeNow = System.currentTimeMillis()
        Log.d(TAG, "onQueryTextChange word  lastSearchTime - timeNow${lastSearchTime - timeNow}")
        //相同关键词距离上一次搜索不超过两秒，则不进行搜索
        if (lastSearchKey == word && needCheckThisTime
            && (timeNow - lastSearchTime) in 0L until SAME_KEYWORD_SEARCH_TIME_INTERVAL
        ) {
            Log.i(TAG, "onQueryTextChange search return")
            needCheckThisTime = false
            return
        }
        needCheckThisTime = false
        lastSearchKey = word ?: ""
        lastSearchTime = timeNow
        if (word.isNullOrEmpty()) {
            mSearchDataModel.value = null
            mLoaderCallBack.cancelLoad()
        } else {
            OptimizeStatisticsUtil.searchWord(word)
            mLoaderCallBack.loadData(word)
        }
        mSavedState[LAST_SEARCH_KEY] = word
    }

    fun readyLoadData() {
        mLoaderCallBack.readyLoadData()
    }

    fun isLoadingData() = mLoaderCallBack.isLoading()

    fun loadHistoryData() {
        launch {
            withContext(Dispatchers.IO) {
                val ls = GlobalSearchHistoryManager.loadHistory()
                mHistoryDataModel.postValue(ls)
            }
        }
    }

    @VisibleForTesting
    fun getHistoryDataModel(): MutableLiveData<MutableList<SearchHistoryModel>> {
        return mHistoryDataModel
    }

    /**
     * 提交搜索内容
     * @param word 搜索词
     * @return true:添加成功，false:添加失败
     */
    fun onQueryTextSubmit(word: String): Boolean {
        if (word.isBlank()) {
            return false
        }
        val mHistoryDataModel = getHistoryDataModel()
        val ls = mHistoryDataModel.value ?: mutableListOf()
        var updateItem = -1
        for (index in 0 until ls.size) {
            if (word == ls[index].mKey) {
                updateItem = index
                break
            }
        }
        var isAdd = false
        if (updateItem >= 0) {
            val item = ls[updateItem]
            updateHistory(item)
            isAdd = false
        } else {
            mHistoryDataModel.postValue(ls)
            launch {
                withContext(Dispatchers.IO) {
                    val id: Long = GlobalSearchHistoryManager.addHistory(word)
                    withContext(Dispatchers.Main) {
                        if (id < 0) {
                            Log.e(TAG, "onQueryTextSubmit: add history fail")
                            isAdd = false
                        } else {
                            ls.add(0, SearchHistoryModel(id, word, System.currentTimeMillis()))
                            mHistoryDataModel.postValue(ls)
                            isAdd = true
                        }
                    }
                }
            }
        }
        return isAdd
    }

    fun removeHistory(historyList: List<SearchHistoryModel>) {
        val ls = mHistoryDataModel.value
        ls?.removeAll(historyList)
        mHistoryDataModel.postValue(ls)
        launch {
            withContext(Dispatchers.IO) {
                GlobalSearchHistoryManager.removeHistory(historyList)
            }
        }
    }

    private fun updateHistory(item: SearchHistoryModel) {
        val ls = mHistoryDataModel.value ?: mutableListOf()
        ls.remove(item)
        item.mTime = System.currentTimeMillis()
        ls.add(0, item)
        mHistoryDataModel.postValue(ls)
        launch {
            withContext(Dispatchers.IO) {
                GlobalSearchHistoryManager.updateHistory(item)
            }
        }
    }

    fun clearHistory() {
        val v = mHistoryDataModel.value
        v?.clear()
        mHistoryDataModel.postValue(v)
        launch {
            withContext(Dispatchers.IO) {
                GlobalSearchHistoryManager.clearHistory()
            }
        }
    }

    fun updateSelectFilter(filterItem: FilterItem?) {
        val map = mFilterSelectedDataModel.value ?: ArrayMap()
        // Save the selected filters
        val saveData = mSavedState.get<ArrayList<FilterItem>>(LAST_SELECT_FILTERS) ?: ArrayList()
        saveData.clear()
        filterItem?.parent?.id?.let {
            if (map[it]?.id == filterItem.id) {
                map.remove(it)
            } else {
                map[it] = filterItem
                saveData.add(filterItem)
            }
        }
        mSavedState.set(LAST_SELECT_FILTERS, saveData)
        mFilterSelectedDataModel.postValue(map)
    }

    /**
     * 当dfm跨端,otg,sdcard等可变数据链接断开时，之前如果存存在dfm跨端，otg，sdcard中被选中的筛选卡片，这里需要将相关卡片从ViewModule中移除
     */
    fun removeSelectFilter(filterItems: List<FilterItem>) {
        val map = mFilterSelectedDataModel.value ?: ArrayMap()
        //修改viewModule中的map中存储的正在被选择的filterItem
        filterItems.forEach { filterItem ->
            filterItem.parent.id.let {
                if (map[it]?.id == filterItem.id) {
                    map.remove(it)
                }
            }
        }
        mFilterSelectedDataModel.postValue(map)
    }

    data class GlobalSearchResult(var uriLoadResult: UriLoadResult<Int, BaseFileBean>) {
        // The index range of each type file where in  uriLoadResult.mResultList.
        // map的Key值是分类，比如(GlobalSearchActivity.TAB_ALL, CategoryHelper.CATEGORY_IMAGE, CategoryHelper.CATEGORY_DOC等)
        // map的value值是该类型数据在uriLoadResult.mResultList列表中的开始的index和结束Index组成的Pair
        // Pair.first -> start index, Pair.second -> end index
        // 这里去掉了rangeMap
        // var dataRangeMap: MutableMap<Int, Pair<Int, Int>> = ArrayMap()

        var searchResultSubList: SearchResultSubList = SearchResultSubList()

        //搜索的关键字
        var searchKey: String? = null
        //当前loader中一次loadinbackground的的当前分页
        var currentPage: Int = -1

        fun reset() {
            if (uriLoadResult.mResultList is MutableList) {
                (uriLoadResult.mResultList as MutableList<BaseFileBean>).clear()
            }
            uriLoadResult.mResultMap.clear()
            //dataRangeMap.clear()
            searchKey = null
            currentPage = -1
        }
        //这里的allResultData代表的是原始所有的item数据，uriLoadResult中在首页ALL_TABLE页时做了最多15个的截断处理
        var allResultData: MutableList<BaseFileBean> = mutableListOf()
        var fromLoaderClassString: String? = null
    }

    data class SearchHistoryModel(
            var mId: Long,
            var mKey: String?,
            var mTime: Long?
    )

    class SearchResultData {
        var time: Long = 0
        var labelFiles = ArrayList<BaseFileBean>()
        var folderFiles = ArrayList<BaseFileBean>()
        var imageFiles = ArrayList<BaseFileBean>()
        var videoFiles = ArrayList<BaseFileBean>()
        var audioFiles = ArrayList<BaseFileBean>()
        var docFiles = ArrayList<BaseFileBean>()
        var appFiles = ArrayList<BaseFileBean>()
        var compressFiles = ArrayList<BaseFileBean>()
        var otherFiles = ArrayList<BaseFileBean>()
        var listDirectory: MutableList<BaseFileBean>? = null
        var listDirectoryIds: MutableSet<Int>? = null
        var listTitleAndContent: MutableList<BaseFileBean>? = null
        var listNameAndTitleIds: MutableSet<Int>? = null
        var listJustTitle: MutableList<BaseFileBean>? = null
        var listContent: MutableList<BaseFileBean>? = null
        var listTitleFuzzy: MutableList<BaseFileBean>? = null
        var listTitleFuzzyIds: MutableList<BaseFileBean>? = null
        var listOldFMQuery: MutableList<BaseFileBean>? = null
    }


    class NormalResultData {
        var listDirectory: MutableList<BaseFileBean> = mutableListOf()
        var listFileBean: MutableList<BaseFileBean> = mutableListOf()

        fun getDirListSize(): Int {
            return listDirectory.size
        }

        fun getFileListSize(): Int {
            return listFileBean.size
        }

        fun getTotalSize(): Int {
            return getDirListSize() + getFileListSize()
        }
    }
}