/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ScrollDividerLayout.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.view

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnLayoutChangeListener
import android.view.ViewGroup
import androidx.annotation.Nullable
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.scrollview.COUINestedScrollView
import com.oplus.filemanager.category.globalsearch.R
import com.support.appcompat.R.attr
import com.support.toolbar.R.dimen
import com.support.toolbar.R.styleable
import kotlin.math.max
import kotlin.math.min

class ScrollDividerLayout(
    context: Context,
    @Nullable var attrs: AttributeSet?,
    var defStyleAttr: Int
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "ScrollDividerLayout"

        const val SUPER_STATE_KEY = "SUPER_STATE_KEY"
        const val OFFSET_DY_SCROLL_STATE_KEY = "OFFSET_DY_SCROLL_STATE_KEY"
        const val SCROLL_DY_SCROLL_STATE_KEY = "SCROLL_DY_SCROLL_STATE_KEY"
        const val DIVIDER_FRACTION_STATE_KEY = "DIVIDER_FRACTION_STATE_KEY"
        const val OVERSCROLL_DY_SCROLL_STATE_KEY = "OVERSCROLL_DY_SCROLL_STATE_KEY"
        const val MAX_FRACTION = 1.0f
    }

    private var debug = true
    private var mCollapsable: Boolean = false
    private var mScrollDyByOffset: Int = 0
    private var mScrollDyByScroll: Int = 0
    private var mScrollDyByOverScroll: Int = 0
    private var mTargetViewState: Int = 0
    private var mTargetView: RecyclerView? = null
    private var mDividerView: View? = null
    private var mDividerFraction = 0f
    private var mHasDivider = false
    private var mDividerEndAlpha = 0f
    private var mDividerStartAlpha = 0f
    private var mDividerEndMarginHorizontal = 0
    private var mDividerStartMarginHorizontal = 0
    private var mOnDividerProgressChangedListener: OnDividerProgressChangedListener? = null
    private var mOnScrollListener: RecyclerView.OnScrollListener? = null
    private var mOnLayoutChangeListener: OnLayoutChangeListener? = null

    init {
        this.mCollapsable = false
        this.mTargetViewState = 0
        this.mTargetView = null
        this.mDividerFraction = 0.0f
        this.mOnDividerProgressChangedListener = null
        this.mOnScrollListener = null
        this.mOnLayoutChangeListener = null
        this.initAttrs(attrs)
    }


    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context) : this(context, null, 0)


    override fun onSaveInstanceState(): Parcelable {
        val bundle = Bundle()
        bundle.putParcelable(SUPER_STATE_KEY, super.onSaveInstanceState())
        bundle.putInt(OFFSET_DY_SCROLL_STATE_KEY, mScrollDyByOffset)
        bundle.putInt(SCROLL_DY_SCROLL_STATE_KEY, mScrollDyByScroll)
        bundle.putInt(OVERSCROLL_DY_SCROLL_STATE_KEY, mScrollDyByOverScroll)
        bundle.putFloat(DIVIDER_FRACTION_STATE_KEY, mDividerFraction)
        return bundle
    }

    override fun onRestoreInstanceState(state: Parcelable?) {
        var newState = state
        if (state is Bundle) {
            mScrollDyByOffset = state.getInt(OFFSET_DY_SCROLL_STATE_KEY)
            mScrollDyByScroll = state.getInt(SCROLL_DY_SCROLL_STATE_KEY)
            mScrollDyByOverScroll = state.getInt(OVERSCROLL_DY_SCROLL_STATE_KEY)
            mDividerFraction = state.getFloat(DIVIDER_FRACTION_STATE_KEY)
            newState = state.getParcelable(SUPER_STATE_KEY)
        }
        super.onRestoreInstanceState(newState)
    }


    private fun initAttrs(attrs: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attrs, styleable.COUIDividerAppBarLayout)
        mHasDivider = typedArray.getBoolean(styleable.COUIDividerAppBarLayout_hasDivider, true)
        mDividerStartAlpha = typedArray.getFloat(styleable.COUIDividerAppBarLayout_dividerStartAlpha, 0.0f)
        mDividerEndAlpha = typedArray.getFloat(
            styleable.COUIDividerAppBarLayout_dividerEndAlpha,
            if (mHasDivider) MAX_FRACTION else 0.0f
        )
        mDividerStartMarginHorizontal = typedArray.getDimensionPixelOffset(
            styleable.COUIDividerAppBarLayout_dividerStartMarginHorizontal,
            context.resources.getDimensionPixelOffset(dimen.coui_appbar_divider_expanded_margin_horizontal)
        )
        mDividerEndMarginHorizontal = typedArray.getDimensionPixelOffset(
            styleable.COUIDividerAppBarLayout_dividerEndMarginHorizontal,
            context.resources.getDimensionPixelOffset(dimen.coui_appbar_divider_collapsed_margin_horizontal)
        )
        typedArray.recycle()
        mDividerEndAlpha = max(0.0, min(mDividerEndAlpha.toDouble(), 1.0)).toFloat()
        mDividerStartAlpha = max(0.0, min(mDividerStartAlpha.toDouble(), 1.0)).toFloat()
        mOnScrollListener = object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                <EMAIL> =
                    recyclerView.computeVerticalScrollOffset()
                Log.i(
                    TAG,
                    "onScrolled recyclerView $recyclerView, dx $dx, dy $dy, scrollByScroll $mScrollDyByScroll"
                )
                <EMAIL>()
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                Log.i(TAG, "onScrollStateChanged recyclerView $recyclerView, newState $newState")
                <EMAIL> = newState
            }
        }
        this.mOnLayoutChangeListener =
            OnLayoutChangeListener { v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom ->
                <EMAIL>(
                    v
                )
            }
        if (debug) {
            Log.i(
                TAG,
                "initAttrs mHasDivider ${hasDivider()}, mDividerStartAlpha $mDividerStartAlpha, " +
                        "mDividerEndAlpha $mDividerEndAlpha, mDividerStartMarginHorizontal $mDividerStartMarginHorizontal, " +
                        "mDividerEndMarginHorizontal $mDividerEndMarginHorizontal"
            )
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (mDividerView == null) {
            mDividerView = LayoutInflater.from(this.context).inflate(
                R.layout.divider_layout,
                this, false
            )
            val lastChildView = this.getChildAt(this.childCount - 1)
            val layoutHeight = context.resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_divider_height)
            Log.i(
                TAG,
                "onAttachedToWindow lastChild $lastChildView, lastChild ${lastChildView.id}, layoutHeight $layoutHeight"
            )
            val params = LayoutParams(
                CoordinatorLayout.LayoutParams.MATCH_PARENT,
                layoutHeight
            )
            params.startToStart = LayoutParams.PARENT_ID
            params.endToEnd = LayoutParams.PARENT_ID
            params.bottomToBottom = LayoutParams.PARENT_ID
            addView(mDividerView, params)
            mDividerView?.alpha = this.mDividerStartAlpha
        }
        mDividerView?.setBackgroundColor(
            COUIContextUtil.getAttrColor(
                this.context,
                attr.couiColorDivider
            )
        )
        this.refreshDivider()
        mDividerView?.visibility = if (this.mHasDivider) View.VISIBLE else View.GONE
        COUIDarkModeUtil.setForceDarkAllow(this.mDividerView, false)
        this.findRecyclerView()
        this.bindListener()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (this.mTargetView != null) {
            mOnScrollListener?.let {
                mTargetView?.removeOnScrollListener(it)
            }
            mTargetView?.removeOnLayoutChangeListener(mOnLayoutChangeListener)
        } else if (debug) {
            Log.d(TAG, "Can not find RecyclerView")
        }
    }

    private fun findRecyclerView() {
        val parent = this.parent as ViewGroup
        for (i in 0 until parent.childCount) {
            if (parent.getChildAt(i) is RecyclerView) {
                this.mTargetView = parent.getChildAt(i) as RecyclerView
                return
            }
        }
    }

    fun bindRecyclerView(rv: RecyclerView?) {
        if (rv != null) {
            mOnScrollListener?.let {
                mTargetView?.removeOnScrollListener(it)
            }
            mTargetView?.removeOnLayoutChangeListener(mOnLayoutChangeListener)
            mTargetView = rv
            mOnScrollListener?.let {
                mTargetView?.addOnScrollListener(it)
            }
            mTargetView?.addOnLayoutChangeListener(mOnLayoutChangeListener)
        }
    }

    private fun bindListener() {
        if (mTargetView != null) {
            mTargetView?.addOnScrollListener(mOnScrollListener!!)
            mTargetView?.addOnLayoutChangeListener(mOnLayoutChangeListener)
        } else if (debug) {
            Log.d(TAG, "Can not find RecyclerView")
        }
    }

    fun onDividerChanged() {
        val totalScrollDy = (max(0.0, mScrollDyByScroll.toDouble()) + this.mScrollDyByOffset + this.mScrollDyByOverScroll).toInt()
        if (debug) {
            Log.d(
                TAG,
                "onDividerChanged: mScrollDyByScroll = " + this.mScrollDyByScroll +
                        ", mScrollDyByOffset = " + this.mScrollDyByOffset +
                        ", mScrollDyByOverScroll = " + this.mScrollDyByOverScroll + ", totalScrollDy $totalScrollDy, " +
                        "dividerAnimaEnable ${isDividerAnimEnable()}"
            )
        }
        if (totalScrollDy >= 0 && isDividerAnimEnable()) {
            val oldFraction = mDividerFraction
            val t = getDividerScrollRange().toFloat()
            if (t == 0.0f) {
                mDividerFraction = t
            } else {
                mDividerFraction = min((totalScrollDy.toFloat() / t).toDouble(), 1.0).toFloat()
            }

            val alpha =
                mDividerStartAlpha + (mDividerEndAlpha - mDividerStartAlpha) * mDividerFraction
            val margin =
                mDividerStartMarginHorizontal + ((mDividerEndMarginHorizontal - mDividerStartMarginHorizontal).toFloat() * mDividerFraction).toInt()
            mDividerView?.alpha = alpha
            if (debug) {
                Log.i(
                    TAG,
                    "onDividerChanged mDividerFraction $mDividerFraction, alpha $alpha, margin $margin"
                )
            }
            this.setDividerHorizontalMargin(margin)
            if (this.mOnDividerProgressChangedListener != null && oldFraction != mDividerFraction) {
                mOnDividerProgressChangedListener?.onDividerProgressChanged(mDividerFraction)
            }
        }
    }

    fun refreshDivider() {
        val alpha = mDividerStartAlpha + (mDividerEndAlpha - mDividerStartAlpha) * mDividerFraction
        val margin =
            mDividerStartMarginHorizontal + ((mDividerEndMarginHorizontal - mDividerStartMarginHorizontal).toFloat() * mDividerFraction).toInt()
        if (mDividerView != null) {
            mDividerView?.setBackgroundColor(
                COUIContextUtil.getAttrColor(
                    this.context,
                    attr.couiColorDivider
                )
            )
            mDividerView?.setAlpha(alpha)
        }

        this.onDividerChanged()
        this.setDividerHorizontalMargin(margin)
        if (this.mOnDividerProgressChangedListener != null) {
            mOnDividerProgressChangedListener?.onDividerProgressChanged(mDividerFraction)
        }
    }

    private fun getDividerScrollRange(): Int {
        return this.measuredHeight
    }

    private fun isDividerAnimEnable(): Boolean {
        val isChangeable = mDividerStartAlpha != mDividerEndAlpha || mDividerStartMarginHorizontal != mDividerEndMarginHorizontal
        val hasDivider = mDividerView != null && mHasDivider
        return hasDivider && isChangeable
    }

    private fun setDividerHorizontalMargin(horizontalMargin: Int) {
        val layoutParams = mDividerView?.layoutParams as? MarginLayoutParams
        layoutParams?.leftMargin = horizontalMargin
        layoutParams?.rightMargin = horizontalMargin
        mDividerView?.layoutParams = layoutParams
    }

    fun setOnDividerProgressChangedListener(onDividerProgressChangedListener: OnDividerProgressChangedListener?) {
        this.mOnDividerProgressChangedListener = onDividerProgressChangedListener
    }

    fun hasDivider(): Boolean {
        return mHasDivider
    }

    fun setHasDivider(hasDivider: Boolean) {
        mHasDivider = hasDivider
        if (this.mDividerView != null) {
            val visibility = if (mHasDivider) View.VISIBLE else View.GONE
            mDividerView?.setVisibility(visibility)
        }
    }

    fun getDividerEndAlpha(): Float {
        return this.mDividerEndAlpha
    }

    fun setDividerEndAlpha(dividerEndAlpha: Float) {
        this.mDividerEndAlpha = dividerEndAlpha
    }

    fun getDividerStartAlpha(): Float {
        return this.mDividerStartAlpha
    }

    fun setDividerStartAlpha(dividerStartAlpha: Float) {
        this.mDividerStartAlpha = dividerStartAlpha
    }

    fun getDividerEndMarginHorizontal(): Int {
        return this.mDividerEndMarginHorizontal
    }

    fun setDividerEndMarginHorizontal(dividerEndMarginHorizontal: Int) {
        this.mDividerEndMarginHorizontal = dividerEndMarginHorizontal
    }

    fun getDividerStartMarginHorizontal(): Int {
        return this.mDividerStartMarginHorizontal
    }

    fun setDividerStartMarginHorizontal(dividerStartMarginHorizontal: Int) {
        this.mDividerStartMarginHorizontal = dividerStartMarginHorizontal
    }

    fun refreshAppBar(v: View): Boolean {
        val newValue: Int
        if (v is COUIRecyclerView) {
            newValue = v.computeVerticalScrollOffset()
        } else {
            if (v !is COUINestedScrollView) {
                return false
            }

            newValue = v.getScrollY()
        }
        if (debug) {
            Log.i(TAG, "refreshAppBar newValue $newValue, mScrollDyByScroll $mScrollDyByScroll")
        }
        if (newValue == mScrollDyByScroll) {
            return false
        } else {
            mScrollDyByScroll = newValue
            return true
        }
    }

    fun reset() {
        mScrollDyByScroll = 0
        mScrollDyByOffset = 0
        mScrollDyByOverScroll = 0
    }

    fun setDebug(v: Boolean) {
        debug = v
    }

    interface OnDividerProgressChangedListener {
        fun onDividerProgressChanged(var1: Float)
    }
}