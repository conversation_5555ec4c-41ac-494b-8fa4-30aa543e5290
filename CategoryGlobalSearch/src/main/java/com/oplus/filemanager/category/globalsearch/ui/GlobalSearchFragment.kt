/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search.GlobalSearchFragment
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.util.ArrayMap
import android.view.DragEvent
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.FileManagerDefaultItemAnimator
import com.filemanager.common.base.RecyclerPercentSelectionVMFragment
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.base.SearchFileWrapper
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.navigation.IMenuEnable
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileManagerPercentWidthRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.filemanager.fileoperate.base.ACTION_DONE
import com.google.android.material.appbar.AppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.adapter.GlobalSearchAdapter
import com.oplus.filemanager.category.globalsearch.adapter.SearchSpaceItemDecoration
import com.oplus.filemanager.category.globalsearch.bean.SearchCardWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchDFMFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.controller.GlobalSearchFilterController
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.ui.navigation.SearchMenuEnableImpl
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.touchshare.TouchShareFragmentSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class GlobalSearchFragment : RecyclerPercentSelectionVMFragment<GlobalSearchFragmentViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener, View.OnTouchListener {
    companion object {
        private const val TAG = "GlobalSearchFragment"
        private const val DELAY_NOTIFY_TIME = 100L
        private const val ACTION_CLOUD_FILE_DOWNLOAD = 100 // 云文档下载成功的code

        val downloadPath: String by lazy {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
        }
    }

    @VisibleForTesting
    var mLastListMode: Int = LIST_NORMAL_MODE
    private var mRootView: ViewGroup? = null
    private var mAppBarLayout: AppBarLayout? = null
    private var mFragmentCategory = GlobalSearchActivity.TAB_ALL
    private var mAdapter: GlobalSearchAdapter? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private var mFileOperateController: IFileOperate? = null
    private val mFilterController by lazy { GlobalSearchFilterController(lifecycle) }
    private var mHandler: Handler? = Handler(Looper.getMainLooper())
    private var mTabActivityListener: TabActivityListener<BaseFileBean>? = null
    private var mLastLoadTime: Long = 0
    private var mIsFirstLoad = true
    private var scrollHelper: DragScrollHelper? = null

    private var menuEnableImpl: IMenuEnable = SearchMenuEnableImpl {
        mViewModel?.getSelectItems()
    }

    override fun getLayoutResId(): Int {
        return R.layout.search_fragment
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView(view: View) {
        mAppBarLayout = baseVMActivity?.findViewById(R.id.appbar_layout)
        mRootView = view.findViewById(R.id.root_view)
        mRecyclerViewFastScroller = view.findViewById(R.id.fastScroller)
        mRecyclerView = view.findViewById<FileManagerPercentWidthRecyclerView>(R.id.recycler_view).apply {
            isNestedScrollingEnabled = true
            clipToPadding = false
            layoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            val animator = FileManagerDefaultItemAnimator()
            itemAnimator = animator
            animator.supportsChangeAnimations = false
            setHasFixedSize(true)
            setPadding(paddingLeft, 0, paddingRight, resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom))
            setOnTouchListener(this@GlobalSearchFragment)
            setOnGenericMotionListener(PCConsumeOnGenericMotionListener())
            isForceDarkAllowed = false
            //UI核查，平板上每个列表延伸到页面左右边缘0边距，需要这样设置
            setPercentIndentEnabled(false)
        }
        baseVMActivity?.let {
            mRecyclerViewFastScroller?.post {
                mRootView?.setPadding(0, mAppBarLayout?.height ?: 0, 0, 0)
                mFilterController.init(it, mRootView, mFragmentCategory)?.apply {
                    val lp = mRecyclerViewFastScroller?.layoutParams as? RelativeLayout.LayoutParams
                    lp?.addRule(RelativeLayout.BELOW, this.id)
                    //这里加入bindRecyclerView的逻辑，保证上滑时有分割线
                    val recyclerView = mRootView?.findViewById<RecyclerView>(R.id.recycler_view)
                    if (recyclerView != null) {
                        mFilterController.bindRecyclerView(recyclerView)
                    }
                }
            }

            if (it is GlobalSearchActivity) {
                mFilterController.setFilterClickListener(it)
                mAdapter = GlobalSearchAdapter(
                    it, <EMAIL>,
                    it.getExternalCategory()
                ).apply {
                    setHasStableIds(true)
                    mRecyclerView?.adapter = this
                }
                mRecyclerView?.apply {
                    setRecycledViewPool(it.getRecycleViewPool())
                    addItemDecoration(SearchSpaceItemDecoration(context = baseVMActivity))
                    this.let {
                        scrollHelper = DragScrollHelper(it)
                    }
                }
            }
        }
    }

    override fun createViewModel(): GlobalSearchFragmentViewModel {
        mFragmentCategory = arguments?.getInt(KtConstants.P_CATEGORY_TYPE)
                ?: GlobalSearchActivity.TAB_ALL
        val category = (activity as? GlobalSearchActivity)?.getExternalCategory()
        val isRecycleBin = category == CategoryHelper.CATEGORY_RECYCLE_BIN
        val viewModel = if (isRecycleBin) {
            ViewModelProvider(this)[mFragmentCategory.toString(), GlobalSearchRecyclebinViewModel::class.java]
        } else {
            ViewModelProvider(this)[mFragmentCategory.toString(), GlobalSearchNormalViewModel::class.java]
        }
        viewModel.init({
            (baseVMActivity as? GlobalSearchActivity)?.reloadData()
        }, mFragmentCategory)

        val listener = FileOperatorListenerImpl(viewModel)
        mFileOperateController = when (category) {
            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                val recycleBinAction = Injector.injectFactory<IRecycleBin>()
                recycleBinAction?.getRecycleFileOperatorController(lifecycle, viewModel)?.apply {
                    setInterceptor(viewModel)
                    setResultListener(listener)
                }
            }
            CategoryHelper.CATEGORY_DFM -> {
                NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_DFM, viewModel).apply {
                    setInterceptor(viewModel)
                    setResultListener(listener)
                    checkHasDynamicBeans = true
                }
            }
            else -> {
                NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_SEARCH, viewModel).apply {
                    setInterceptor(viewModel)
                    setResultListener(listener)
                    checkHasDynamicBeans = true
                }
            }
        }
        return viewModel
    }

    override fun initData(savedInstanceState: Bundle?) {
        // no need init data
        var category = (activity as? GlobalSearchActivity)?.getExternalCategory() ?: CategoryHelper.CATEGORY_SEARCH
        Log.d(TAG, "initData category:$category fragment:$mFragmentCategory")
        if (category == GlobalSearchActivity.TAB_ALL) {
            category = mFragmentCategory
        }
        TouchShareSupplier.attach(this, TouchShareFragmentSupplier(category, baseVMActivity, mViewModel, mFileOperateController))
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        Log.i(TAG, "onViewCreated this $this")
        super.onViewCreated(view, savedInstanceState)
        view.post {
            (activity as? GlobalSearchActivity)?.getViewModel()?.also {
                it.mFilterSelectedDataModel.value?.let { data ->
                    onSelectedFilterChange(data, false)
                }
                it.mSearchDataModel.value?.let { data ->
                    mViewModel?.onSearchDataUpdate(data)
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        if (event?.actionMasked == MotionEvent.ACTION_DOWN) {
            hideKeyboard()
        }
        return false
    }

    override fun onResume() {
        super.onResume()
        if ((mAdapter?.itemCount ?: 0) > 0) {
            hideKeyboard()
        }
        //首次进入页面不在onresume里刷新；onResumeLoadData刚加载过时也不在onresume里刷新
        if (!mIsFirstLoad) {
            if (SystemClock.elapsedRealtime() - mLastLoadTime > KtConstants.SEARCH_LOAD_TIME_GAP) {
                Log.i(TAG, "onResume reloadData")
                (baseVMActivity as? GlobalSearchActivity)?.reloadData()
                /*
                 * 通过穿透搜索引导可以进入设置打开穿透搜索开关，返回到搜索页面时需要刷新筛选面板的选项。
                 */
                mFilterController.checkUpdateSupportList()
            }
        } else {
            mIsFirstLoad = false
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onConfigurationChanged(newConfig: Configuration) {
        Log.i(TAG, "onConfigurationChanged")
        super.onConfigurationChanged(newConfig)
        activity?.let {
            if (mFilterController.mIsShow) {
                mFilterController.reLayoutFilterPanel()
            }
        }
        mRecyclerView?.postDelayed({
            mAdapter?.notifyDataSetChanged()
        }, DELAY_NOTIFY_TIME)
        if (mFileOperateController is NormalFileOperateController) {
            (mFileOperateController as NormalFileOperateController).onConfigurationChanged(newConfig)
        }
    }

    fun notifySearchStart() {
        mFileEmptyController.hideFileEmptyView()
        mFilterController.showOrHideFilter(false)
        mViewModel?.apply {
            if (mModeState.listModel.value != LIST_NORMAL_MODE) {
                changeListMode(LIST_NORMAL_MODE)
            }
        }
    }

    private fun isCurrentFragment(): Boolean {
        val superActivity = (baseVMActivity as? GlobalSearchActivity)
        superActivity?.let {
            return it.getCurrentFragment() == this
        }
        return false
    }

    @SuppressLint("ClickableViewAccessibility")
    fun onSearchDataUpdate(result: GlobalSearchViewModel.GlobalSearchResult?, searchKey: String? = result?.searchKey) {
        mViewModel?.onSearchDataUpdate(result, searchKey)
        mRecyclerView?.setOnTouchListener(this)
    }

    fun onShowFilter() {
        mFilterController.showOrHideFilter()
    }

    fun onSelectedFilterChange(select: ArrayMap<Int, FilterItem>, filterData: Boolean = true) {
        mFilterController.onFilterSelectChanged(select)
        mViewModel?.onFilterSelectUpdate(select, filterData)
    }

    fun showOrHidePanel() {
        Log.d(TAG, "showOrHidePanel: listMode=${mViewModel?.mModeState?.listModel?.value}")
        mViewModel?.apply {
            if (mModeState.listModel.value == LIST_NORMAL_MODE) {
                mFilterController.showOrHideFilterPanel()
            }
        }
    }

    fun getFilterPanelState(): Boolean {
        return mFilterController.mIsShow
    }

    override fun startObserve() {
        mRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startFileItemClickObserver()
                startUIDataStateObserver()
            }
        }
    }

    private fun startListSelectModeObserver() {
        mViewModel?.apply {
            mModeState.mListModel.observe(this@GlobalSearchFragment, object : Observer<Int> {
                override fun onChanged(value: Int) {
                    if (!mViewModel!!.mModeState.mInitState || !isCurrentFragment()) {
                        return
                    }
                    Log.d(TAG, "startListSelectModeObserver: mListModel=$value")
                    val selectModel = (value == LIST_SELECTED_MODE)
                    setRecyclerViewSelectModel(selectModel)
                    setBarSelectModel(selectModel)
                    mFilterController.setFilterTitleEnable(selectModel.not())
                    hideKeyboardByListModel(value)
                }
            })
        }
    }

    /**
     * 设置recyclerView选中模式的表现
     * @param selectModel 是否选中
     */
    private fun setRecyclerViewSelectModel(selectModel: Boolean) {
        mAdapter?.let {
            it.selectRemoteFile = mViewModel?.isRemoteFileSelected()
            it.setSelectEnabled(selectModel)
            it.setChoiceModeAnimFlag(selectModel)
        }
        mRecyclerView?.let {
            val res = appContext.resources
            val bottom = if (selectModel) {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
            } else {
                res.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            }
            it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, bottom)
            mRecyclerViewFastScroller?.apply { trackMarginBottom = bottom }
            if (selectModel) {
                it.setFadingEdgeLength(res.getDimensionPixelSize(com.filemanager.common.R.dimen.list_fading_edge_height))
            }
        }
    }

    /**
     * 设置顶部toolbar和底部导航栏选中模式下的表现
     * @param selectModel 是否选中
     */
    private fun setBarSelectModel(selectModel: Boolean) {
        if (selectModel) {
            showNavigation(false)
        } else {
            hideNavigation()
        }
        Log.i(TAG, "notifySelectModel FROM setBarSelectModel")
        if (selectModel) {
            mTabActivityListener?.initToolbarSelectedMode(
                true,
                mViewModel!!.getCanSelectFileSize(),
                mViewModel!!.mUiState.value?.mSelectedList?.size ?: 0,
                mViewModel!!.getSelectItems()
            )
        } else {
            mTabActivityListener?.initToolbarNormalMode(needInit = true, empty = false)
        }
        (baseVMActivity as? GlobalSearchActivity)?.showOrHideSearchController(selectModel)
    }

    /**
     * 文件item的点击监听：点击搜索结果的item后，会把当前搜索词添加到历史记录中
     */
    private fun startFileItemClickObserver() {
        mViewModel?.apply {
            mItemClickState.observe(this@GlobalSearchFragment) {
                (baseVMActivity as? GlobalSearchActivity)?.apply {
                    getSearchController().getQueryText().let { text ->
                        if (text.isNullOrBlank().not()) {
                            addHistory(text.toString())
                        }
                    }
                }
            }
        }
    }

    private fun startUIDataStateObserver() {
        val viewModule = mViewModel ?: return
        viewModule.uiState.observe(this) { uiModel ->
            Log.d(
                TAG, "category $mFragmentCategory $ startUIDataStateObserver: total=${uiModel.fileList.size},"
                        + "select=${uiModel.selectedList.size}, keyword=${uiModel.keyWord}"
            )
            val selectModel = (uiModel.stateModel.listModel.value == LIST_SELECTED_MODE)
            if (isCurrentFragment()) {
                Log.i(TAG, "startUIDataStateObserver category $mFragmentCategory selectModel $selectModel")
                if (selectModel) {
                    showNavigation(true)
                    mTabActivityListener?.initToolbarSelectedMode(
                        false,
                        viewModule.getCanSelectFileSize(),
                        viewModule.uiState.value?.selectedList?.size ?: 0,
                        viewModule.getSelectItems()
                    )
                } else {
                    mTabActivityListener?.initToolbarNormalMode(needInit = false, empty = false)
                }
                mFilterController.setFilterTitleEnable(selectModel.not())
                if (uiModel.fileList.isEmpty() && selectModel) {
                    viewModule.mModeState.listModel.value = LIST_NORMAL_MODE
                }
            }

            checkNeedShowEmptyView()
            updateResultCountShow(mViewModel?.getTotalFileSize() ?: 0)
            if (uiModel.mFileList is ArrayList<BaseFileBean>) {
                mAdapter?.let {
                    it.selectRemoteFile = mViewModel?.isRemoteFileSelected()
                    it.setKeyWord(uiModel.keyWord)
                    it.setData(uiModel.fileList as ArrayList<BaseFileBean>, uiModel.selectedList)
                }
            }
        }
    }

    private fun updateResultCountShow(size: Int) {
        mFilterController.updateTitle(size)
        if ((baseVMActivity as? GlobalSearchActivity)?.getViewModel()?.isLoadingData() == false) {
            onShowFilter()
        }
    }

    override fun onResumeLoadData() {
        Log.i(TAG, "onResumeLoadData")
        mLastLoadTime = SystemClock.elapsedRealtime()
        (baseVMActivity as? GlobalSearchActivity)?.reloadData()
    }

    fun checkNeedShowEmptyView() {
        mViewModel?.let {
            val loading = (baseVMActivity as? GlobalSearchActivity)?.getViewModel()?.isLoadingData()
                    ?: false
            if (it.uiState.value?.fileList.isNullOrEmpty() && !loading) {
                // When switching zoomWindows, a delay is required to ensure that view height can be obtained
                mHandler?.postDelayed({
                    if (it.uiState.value?.fileList.isNullOrEmpty()) {
                        mFileEmptyController.showFileEmptyView(
                            baseVMActivity!!, mRootView!!,
                              SEARCH_EMPTY_ANIMATION_FILE, com.filemanager.common.R.string.no_search_results)
                    }
                }, 30)
            } else {
                mFileEmptyController.hideFileEmptyView()
            }
        }
    }

    override fun pressBack(): Boolean {
        if (mFilterController.mIsShow) {
            mFilterController.showOrHideFilterPanel()
            return true
        }
        return mViewModel?.pressBack() ?: false
    }

    private fun showNavigation(onlyUpdate: Boolean) {
        val navImpl = (mActivity as? GlobalSearchActivity)
        // 只有云文档，显示云文档NavigationType
        val category = (activity as? GlobalSearchActivity)?.getExternalCategory() ?: GlobalSearchActivity.TAB_ALL
        val navType = mViewModel?.getNavigationType(category) ?: NavigationType.DEFAULT
        Log.d(TAG, "showNavigation category:$category -> navType:$navType")
        if (onlyUpdate) {
            navImpl?.updateMenuType(navType)
        } else {
            navImpl?.showNavigation(navType, true)
        }
        //设置每个item的Enable状态
        navImpl?.processNavigationItemEnable(menuEnableImpl)
    }

    private fun hideNavigation() {
        val navImpl = (mActivity as? GlobalSearchActivity)
        navImpl?.hideNavigation()
    }


    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (!Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return activity?.let {
                val selectFiles = mViewModel?.getSelectItems()
                if (handleSelectDriveFile(it, item, selectFiles)) {
                    Log.d(TAG, "onNavigationItemSelected select drive file")
                    return true
                }
                if (handleSelectRemoteFile(it, item, selectFiles)) {
                    Log.d(TAG, "onNavigationItemSelected select remote file")
                    return true
                }
                //这里要求在复选多个数据时，也需要显示压缩菜单
                mFileOperateController?.onNavigationItemSelected(it, item)
            } ?: false
        }
        return false
    }

    /**
     * 选中云文件的事件：重命名和下载
     */
    private fun handleSelectDriveFile(activity: ComponentActivity, item: MenuItem, selectFiles: ArrayList<BaseFileBean>?): Boolean {
        if (selectFiles == null) {
            return false
        }
        if (selectFiles.size != 1) {
            return false
        }
        val file = selectFiles.get(0)
        Log.d(TAG, "handleSelectDriveFile select : $file")
        if (file !is DriveFileWrapper) {
            return false
        }
        when (item.itemId) {
            com.filemanager.common.R.id.navigation_download -> downloadCloudFile(activity, file)
            com.filemanager.common.R.id.navigation_rename -> renameCloudFile(activity, file)
            else -> return false
        }
        return true
    }

    private fun handleSelectRemoteFile(activity: ComponentActivity, item: MenuItem, selectFiles: ArrayList<BaseFileBean>?): Boolean {
        Log.d(TAG, "handleSelectRemoteFile select : ${selectFiles?.size}")
        if (selectFiles == null) {
            return false
        }
        if (selectFiles.size == 0) {
            return false
        }
        val containOtherFile = selectFiles.filterNot {
            it is RemoteFileBean
        }.isNotEmpty()
        if (containOtherFile) {
            return false
        }
        when (item.itemId) {
            com.filemanager.common.R.id.navigation_download -> mViewModel?.downloadRemoteFile(activity, false, selectFiles)
            else -> return false
        }
        return true
    }

    /**
     * 下载云文档
     */
    private fun downloadCloudFile(activity: ComponentActivity, downloadFile: DriveFileWrapper) {
        Log.d(TAG, "downloadCloudFile $downloadFile")
        if (downloadFile.isDir()) {
            Log.d(TAG, "downloadCloudFile -> selected file is folder, not support download.")
            CustomToast.showShort(com.filemanager.common.R.string.download_folder_not_supported)
            return
        }
        if (!downloadFile.isSupportDownload()) {
            Log.d(TAG, "downloadCloudFile -> selected file format not support download.")
            CustomToast.showShort(com.filemanager.common.R.string.download_format_not_supported)
            return
        }
        //产品没有定义这个埋点,这里先加上
        OptimizeStatisticsUtil.onDownloadDrive("", listOf(downloadFile), true)
        if (activity is TransformNextFragmentListener) {
            (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(
                MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD,
                downloadPath
            )
        }
    }

    /**
     *  真正下载云文档
     */
    private fun realDownloadCloudFile(targetPath: String?) {
        val activity = activity ?: return
        if (targetPath == null) {
            Log.w(TAG, "realDownloadCloudFile targetPath is null")
            return
        }
        val selectFiles = mViewModel?.getSelectItems() ?: return
        val selectLocalFile = mViewModel?.isSelectLocalFile(selectFiles) ?: false
        Log.d(TAG, "realDownloadCloudFile select localFile: $selectLocalFile")
        if (selectLocalFile) {
            Log.w(TAG, "realDownloadCloudFile local file don't need download")
            return
        }
        val fleCloudBrowserApi = Injector.injectFactory<IFileCloudBrowser>()
        val list = selectFiles.map { it as DriveFileWrapper }
        fleCloudBrowserApi?.downloadCloudFile(activity, list.get(0), targetPath) { code ->
            if (code == ACTION_DONE || code == ACTION_CLOUD_FILE_DOWNLOAD) { //
                // 下载到本地后，搜索结果需要更新
                onResumeLoadData()
            }
        }
    }

    /**
     * 重命名文件
     */
    private fun renameCloudFile(activity: ComponentActivity, file: DriveFileWrapper) {
        Log.d(TAG, "renameCloudFile $file")
        OptimizeStatisticsUtil.onRename("", listOf(file), true)
        val fileCloudBrowserAction = Injector.injectFactory<IFileCloudBrowser>()
        fileCloudBrowserAction?.renameCloudFile(activity, file) { result ->
            if (result.first == ACTION_DONE) {
                mViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                mAdapter?.onRenameFile(file, result.second)
            }
        }
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.finish()
            }

            R.id.action_select_all -> mViewModel?.clickToolbarSelectAll()

            com.filemanager.common.R.id.action_select_cancel -> {
                if (mViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                    mViewModel?.changeListMode(LIST_NORMAL_MODE)
                }
            }
        }
        return true
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        Log.d(TAG, "fromSelectPathResult requestCode:$requestCode path: $path")
        activity?.let {
            if (requestCode == MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD) {
                realDownloadCloudFile(path?.getOrNull(0))
            } else {
                mFileOperateController?.onSelectPathReturn(it, requestCode, path)
            }
        }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            mViewModel?.changeListMode(LIST_NORMAL_MODE)
        }
    }

    private fun hideKeyboard() {
        (activity as? GlobalSearchActivity)?.hideKeyboard()
    }

    @VisibleForTesting
    fun hideKeyboardByListModel(mListModel: Int?) {
        mListModel?.let {
            if (mListModel != mLastListMode) {
                mLastListMode = mListModel
                hideKeyboard()
            }
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        mViewModel?.uiState?.value?.let { uiModel ->
            //这里加这个是在选择模式下，点击授权卡片时，需要返回为false要将卡片的点击事件下发到卡片内部，卡片内部的点击事件才能响应
            val checkClickCard = checkClickCard(item)
            if (checkClickCard) {
                return false
            }
            if (uiModel.stateModel.listModel.value != LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey]?.apply {
                Log.d(TAG, "onItemClick 1 baseFile=$this, uri ${this.mLocalFileUri}")
                //这里改为返回false是为了卡片中需要点击事件，返回为true的话，卡片中的子view点击事件会被拦截
            } ?: return false
            activity?.let {
                val mediaImgIds: ArrayList<String> = ArrayList()
                // 判断当前点击是否是媒体库中的图片
                if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                    // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                    mViewModel?.uiState?.value?.fileList?.forEach { file ->
                        file.takeIf { curFile ->
                            !curFile.isDir() && curFile.mLocalType == MimeTypeHelper.IMAGE_TYPE && curFile.mSize > 0
                        }?.takeIf {
                            file !is SearchDFMFileWrapper && file !is SearchDMPFileWrapper
                        }?.let {
                            if (file is SearchFileWrapper) mediaImgIds.add(file.id.toString())
                        }
                    }
                    // 限制数据大小
                    FileMediaHelper.limitNumberOfFileList(baseFile, mediaImgIds)
                }
                mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                if (baseFile.mLocalType != MimeTypeHelper.DIRECTORY_TYPE && baseFile !is SearchLabelWrapper) {
                    //复用之前的点击埋点
                    OptimizeStatisticsUtil.clickSearchResultFileType(baseFile, mFragmentCategory)
                }
            }
        }
        return true
    }

    private fun getClickItemBean(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>): BaseFileBean? {
        val position = item.position
        val baseFileBean = mAdapter?.getItem(position)
        Log.i(TAG, "getClickItemBean positin $position, bean $baseFileBean")
        return baseFileBean
    }

    private fun checkClickCard(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>): Boolean {
        val baseFileBean = getClickItemBean(item)
        val result = (baseFileBean is SearchCardWrapper)
        Log.i(TAG, "checkClickCard result $result")
        return result
    }

    fun switchToNormalMode() {
        Log.i(TAG, "switchToNormalMode")
        mViewModel?.changeListMode(LIST_NORMAL_MODE)
    }

    fun openOrCloseSwitchForFilter() {
        mFilterController.processThirdAppSwitchChanged()
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy this $this")
        mHandler?.removeCallbacksAndMessages(null)
        super.onDestroy()
    }

    fun setTabActivityListener(tabListener: TabActivityListener<BaseFileBean>) {
        mTabActivityListener = tabListener
    }

    fun handleDragEvent(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = mViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = mViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_SEARCH
    }

    fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = mViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }
}