/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.controller

import android.app.Activity
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.TypedValue
import android.view.Gravity
import android.view.KeyEvent
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi

class SearchController(lifecycle: Lifecycle) : BaseLifeController {
    companion object {
        private const val TAG = "SearchController"
        private const val SEARCH_HIDE_POST_TIME = 100L
        private const val SEARCH_CONTENT_MAX_LEN = 500
        private const val SEARCH_DMP_QUERY_LENGTH = 50
    }

    private var mSearchListener: SearchListener? = null
    private var mOppoSearchView: COUISearchBar? = null
    private var mIsClearFocus = false
    private var mSearchHideRunnable: Runnable? = null
    private var mIsShowing = false
    private val mOnQueryTextListener = object : TextWatcher, OnEditorActionListener {

        override fun onEditorAction(textView: TextView?, actionId: Int, event: KeyEvent?): Boolean {
            if (EditorInfo.IME_ACTION_SEND == actionId || EditorInfo.IME_ACTION_SEARCH == actionId) {
                mSearchListener?.onSearchSubmit(textView?.text.toString())
                return true
            }
            return false
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(newText: CharSequence?, start: Int, before: Int, count: Int) {
            newText?.let { mSearchListener?.onSearch(it.toString()) }
        }

        override fun afterTextChanged(s: Editable?) {
            mOppoSearchView?.quickDeleteButton?.contentDescription =
                MyApplication.appContext.resources.getString(com.filemanager.common.R.string.search_history_clear)
        }
    }

    init {
        lifecycle.addObserver(this)
    }

    fun showAnimateSearchView(
        activity: Activity,
        toolbar: COUIToolbar,
        menuId: Int,
        searchListener: SearchListener,
        showBackArrow: Boolean = false,
        showSoftInput: Boolean = true
    ) {
        Log.d(TAG, "showAnimateSearchView")
        mSearchListener = searchListener
        if (mOppoSearchView == null) {
            toolbar.menu?.findItem(menuId)?.let { menu ->
                mOppoSearchView = (menu.actionView as? COUISearchBar)?.apply {
                    if (showBackArrow) {
                        setNavigationView(activity, this)
                    }
                    inputMethodAnimationEnabled = showSoftInput
                    showInToolBar()
                    setAtBehindToolBar(toolbar, Gravity.TOP, menu)

                    searchEditText.setHint(com.filemanager.common.R.string.search_hide_text)
                    functionalButton.setOnClickListener { mSearchListener?.onExitSearch() }
                    setPaddingRelative(
                        context.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.coui_search_view_padding_start_in_toolbar),
                        0,
                        context.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.coui_search_view_padding_end_in_toolbar),
                        0
                    )
                    searchEditText.setTextSize(
                        TypedValue.COMPLEX_UNIT_PX,
                        resources.getDimensionPixelSize(com.filemanager.common.R.dimen.font_size_14).toFloat()
                    )
                    searchEditText?.addTextChangedListener(mOnQueryTextListener)
                    searchEditText?.setOnEditorActionListener(mOnQueryTextListener)
                    setInputTextLengthFilter(searchEditText)
                    findViewById<ImageView>(com.support.toolbar.R.id.animated_search_icon)?.apply {
                        isClickable = false
                        isEnabled = false
                    }
                }
            }
        }
        mOppoSearchView?.also {
            Log.d(TAG, "showAnimateSearchView: toolHeight=${toolbar.height}")
            setSearchHeaderVisible(true)
            mSearchListener?.onShowSearch()
        }
    }

    /**
     * 设置导航箭头
     */
    private fun setNavigationView(activity: Activity, searchView: COUISearchBar) {
        Log.d(TAG, "setSearchAnimateType $mOppoSearchView")
        // 设置搜索样式为非及时搜索
        searchView.setSearchAnimateType(COUISearchBar.TYPE_NON_INSTANT_SEARCH)
        // 设置返回箭头
        searchView.setNavigationViewDrawable(ContextCompat.getDrawable(activity, com.support.appcompat.R.drawable.coui_back_arrow))
        searchView.navigationView?.setOnClickListener {
            activity.finish()
        }
    }

    /**
     * 设置输入的文件长度过滤器
     */
    private fun setInputTextLengthFilter(editText: EditText) {
        val maxLength = if (FeatureCompat.sIsExpRom) {
            val exportDmpSearchApi = Injector.injectFactory<IExportDmpSearchApi>()
            if (exportDmpSearchApi?.isShouldLoadDMP() == true) {
                SEARCH_DMP_QUERY_LENGTH
            } else {
                SEARCH_CONTENT_MAX_LEN
            }
        } else {
            val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
            if (dmpSearchApi?.isShouldLoadDMP() == true) {
                SEARCH_DMP_QUERY_LENGTH
            } else {
                SEARCH_CONTENT_MAX_LEN
            }
        }
        val maxLenFilter = InputFilter.LengthFilter(maxLength)
        val filters = editText.filters
        val len = (filters?.size ?: 0)
        val tmpFilter: Array<InputFilter?> = arrayOfNulls(len + 1)
        System.arraycopy(filters, 0, tmpFilter, 0, len)
        tmpFilter[len] = maxLenFilter
        editText.filters = tmpFilter
    }


    fun setQuery(query: CharSequence, submit: Boolean) {
        getSearchView()?.let {
            it.setText(query)
            it.setSelection(query.length)
        }
        if (submit && query.isNotEmpty()) {
            mSearchListener?.onSearchSubmit(query.toString())
        }
    }

    fun getSearchView() = mOppoSearchView?.searchEditText

    fun getQueryText(): CharSequence? {
        return mOppoSearchView?.searchEditText?.text
    }

    fun showSoftInput() {
        mOppoSearchView?.openSoftInput(true)
    }

    fun hideSoftInput() {
        mOppoSearchView?.openSoftInput(false)
    }

    fun clearFocus() {
        mOppoSearchView?.clearFocus()
    }

    fun setSearchHeaderVisible(isVisible: Boolean) {
        fun showInToolBar() {
            mIsShowing = true
            mOppoSearchView?.let {
                it.inputMethodAnimationEnabled = false
                it.showInToolBar()
                it.inputMethodAnimationEnabled = true
            }
        }

        Log.d(TAG, "setSearchHeaderVisible isVisible = $isVisible")
        mOppoSearchView?.let {
            if (isVisible) {
                if (!mIsShowing) {
                    showInToolBar()
                }
                if (mIsClearFocus) {
                    enableColorSearchView(false)
                    mSearchHideRunnable = Runnable {
                        enableColorSearchView(true)
                    }
                    it.postDelayed(mSearchHideRunnable, /*it.animatorDuration +*/ SEARCH_HIDE_POST_TIME)
                }
                Log.d(TAG, "setSearchHeaderVisible layoutParams=${it.layoutParams}")
                if (it.layoutParams == null) {
                    it.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT)
                } else {
                    it.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT
                }
                mIsClearFocus = false
            } else {
                it.hideInToolBar()
                mIsShowing = false
            }
        }
    }

    private fun enableColorSearchView(enable: Boolean) {
        if (enable) {
            clearFocus()
        }
        mOppoSearchView?.isEnabled = enable
    }

    fun setNextSearchClearFocus() {
        mIsClearFocus = true
    }

    override fun onDestroy() {
        try {
            mOppoSearchView?.removeCallbacks(mSearchHideRunnable)
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        mOppoSearchView = null
        mSearchListener = null
    }
}

interface SearchListener {
    /**
     * 当显示搜索
     */
    fun onShowSearch()

    /**
     * 退出搜索
     */
    fun onExitSearch()

    /**
     * 开始搜索内容
     */
    fun onSearch(text: String)

    /**
     * 搜索内容提交
     */
    fun onSearchSubmit(text: String) {}
}