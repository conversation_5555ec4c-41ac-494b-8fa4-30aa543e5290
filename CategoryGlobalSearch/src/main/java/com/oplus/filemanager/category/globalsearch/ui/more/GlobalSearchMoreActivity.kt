/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchMoreActivity
 ** Description : 搜索更多结果的Activity
 ** Version     : 1.0
 ** Date        : 2024/05/16 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui.more

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.MenuItem
import android.view.ViewGroup
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.controller.navigation.IMenuEnable
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.category.globalsearch.controller.SearchController
import com.oplus.filemanager.category.globalsearch.controller.SearchListener
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchNormalViewModel.Companion.DELETE_OPERATION
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController

class GlobalSearchMoreActivity : EncryptActivity(), NavigationInterface, SearchListener, TabActivityListener<BaseFileBean>,
    TransformNextFragmentListener, NavigationBarView.OnItemSelectedListener {

    companion object {
        private const val TAG = "GlobalSearchMoreActivity"
        private const val TAG_SEARCH_MORE_FRAGMENT = "SearchMoreFragment"
        private const val WHAT_READY_SEARCH = 100
        private const val WHAT_START_SEARCH = 101
        private const val SHOW_SOFT_INPUT_DELAY = 500L
        private const val READY_SEARCH_DELAY = 400L

        fun start(context: Context, category: Int, searchWord: String) {
            Log.d(TAG, "start category:$category searchWord:$searchWord")
            val intent = Intent(context, GlobalSearchMoreActivity::class.java)
            intent.putExtra(KtConstants.P_CATEGORY_TYPE, category)
            intent.putExtra(KtConstants.SEARCH_WORD, searchWord)
            context.startActivity(intent)
        }
    }

    private var category: Int = 0
    private var searchWord: String = ""
    private var lastSelectModel: Boolean = true
    private var toolbar: COUIToolbar? = null
    private var appBarLayout: COUIDividerAppBarLayout? = null
    private var rootView: ViewGroup? = null
    private var mViewModel: GlobalSearchMoreViewModel? = null
    private var fragment: GlobalSearchMoreFragment? = null
    private var navigationController: NavigationController? = null
    private val searchController: SearchController by lazy { SearchController(lifecycle) }
    private var loadingController: LoadingController? = null
    private val selectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(
            lifecycle, CategoryHelper.CATEGORY_SEARCH,
            model, SortHelper.FILE_TIME_REVERSE_ORDER
        ).also {
            it.setResultListener(FileOperatorListenerImpl(model))
            it.checkHasDynamicBeans = true
        }
    }

    private var mHandler: Handler? = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                WHAT_READY_SEARCH -> readySearch(msg.obj?.toString())
                WHAT_START_SEARCH -> reloadData()
            }
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_global_search_more
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        category = IntentUtils.getInt(intent, KtConstants.P_CATEGORY_TYPE, 0)
        searchWord = IntentUtils.getString(intent, KtConstants.SEARCH_WORD) ?: ""
        Log.d(TAG, "initView category:$category searchWord:$searchWord")
        appBarLayout = findViewById(R.id.appbar_layout)
        toolbar = findViewById(R.id.toolbar)
        rootView = findViewById(R.id.coordinator_layout)
        initNavigationController()
        initToolbar()
    }

    /**
     * 初始化底部工具栏类型
     */
    private fun initNavigationController() {
        navigationController = if (category == CategoryFileWrapper.TYPE_LOCAL_FILE) {
            NavigationController(lifecycle, NavigationType.DEFAULT, id = R.id.navigation_tool)
        } else if (category == CategoryFileWrapper.TYPE_REMOTE_FILE) {
            NavigationController(lifecycle, NavigationType.REMOTE_MAC, id = R.id.navigation_tool)
        } else {
            NavigationController(lifecycle, NavigationType.FILE_DRIVE, id = R.id.navigation_tool)
        }
    }


    override fun initData() {
        fragment = (supportFragmentManager.findFragmentByTag(TAG_SEARCH_MORE_FRAGMENT) as? GlobalSearchMoreFragment)
            ?: GlobalSearchMoreFragment.newInstance(category, searchWord)
        supportFragmentManager.beginTransaction().replace(
            R.id.fragment_container, fragment!!, TAG_SEARCH_MORE_FRAGMENT
        ).commit()
    }

    private fun initToolbar() {
        toolbar?.apply {
            menu.clear()
            title = ""
            isTitleCenterStyle = false
        }
        setSupportActionBar(toolbar)
        rootView?.apply {
            setPadding(
                paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(this@GlobalSearchMoreActivity), paddingRight, paddingBottom
            )
        }
        appBarLayout?.post {
            notifySelectModel(false, init = true)
            restoreState()
        }
    }

    private fun restoreState() {
        mViewModel?.let { vm ->
            var restoreSearchKey: String? = null
            try {
                restoreSearchKey = vm.mSavedState.remove<String>(GlobalSearchViewModel.LAST_SEARCH_KEY)
            } catch (e: UnsupportedOperationException) {
                Log.d(TAG, "restoreState failed: ${e.message}")
            }
            if (restoreSearchKey.isNullOrEmpty().not()) {
                /**
                 * Set it in advance to avoid the loading state is false when checked in
                 * GlobalSearchMoreFragment.checkNeedShowEmptyView when setCurrentItem below.
                 */
                vm.readyLoadData()
                searchController.setQuery(restoreSearchKey!!, false)
            }
        }
    }

    override fun startObserve() {
        mViewModel = ViewModelProvider(this, SavedStateViewModelFactory(application, this))
            .get(GlobalSearchMoreViewModel::class.java).apply {
                init(category)
                mSearchDataModel.observe(this@GlobalSearchMoreActivity) {
                    Log.d(TAG, "startObserve -> mSearchDataModel: size=${it?.uriLoadResult?.mResultList?.size}")
                    fragment?.let { frg ->
                        frg.onSearchDataUpdate(it)
                        frg.onShowFilter()
                    }
                    hideSearchLoading()
                }
                mFilterSelectedDataModel.observe(this@GlobalSearchMoreActivity) {
                    Log.d(TAG, "startObserve -> mFilterSelectedDataModel, selected size =${it.size}")
                    fragment?.onSelectedFilterChange(it)
                }
            }
    }

    fun isLoadingData(): Boolean? {
        return mViewModel?.isLoadingData()
    }


    override fun refreshCurrentPage(action: String?, data: String?) {
    }


    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return fragment?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }


    override fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize: Int,
        selectedFileSize: Int,
        selectItems: ArrayList<BaseFileBean>
    ) {
        toolbar?.let {
            if (needInit) {
                it.menu.clear()
                it.isTitleCenterStyle = true
                it.inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            }
            val isSelectAll = (realFileSize == selectedFileSize)
            Log.i(TAG, "initToolbarSelectedMode realFileSize $realFileSize, selectedFileSize $selectedFileSize, isSelectAll $isSelectAll")
            ToolbarUtil.updateToolbarTitle(it, selectedFileSize, isSelectAll)
        }
        supportActionBar?.setDisplayHomeAsUpEnabled(false)
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        toolbar?.let {
            it.menu.clear()
            it.isTitleCenterStyle = false
            it.inflateMenu(R.menu.global_search_menu)
            title = ""
        }
    }

    override fun refreshScanModeItemIcon(withAnimation: Boolean) {
    }

    override fun updateNeedSkipAnimation(withAnimation: Boolean) {
    }

    override fun showNavigation() {
        navigationController?.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        navigationController?.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        navigationController?.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
    }

    /**
     * 设置menu中的enable和disable
     */
    fun processNavigationItemEnable(iMenuEnable: IMenuEnable) {
        navigationController?.setNavigateItemAble(iMenuEnable)
    }

    override fun onNavigationItemSelected(p0: MenuItem): Boolean {
        return fragment?.onNavigationItemSelected(p0) ?: false
    }


    private fun readySearch(text: String?) {
        Log.d(TAG, "readySearch: text=$text")
        mViewModel?.apply {
            searchController.getQueryText().let {
                readyLoadData()
            }
            mSearchDataModel.value?.reset()
        }
        fragment?.apply {
            onSearchDataUpdate(null, text)
            notifySearchStart()
        }
    }

    override fun onResume() {
        if (PrivacyPolicyController.hasAgreePrivacy().not() || PermissionUtils.hasStoragePermission().not()) {
            hideKeyboard()
        }
        super.onResume()
    }

    /**
     * 分页加载更多
     */
    fun loadMore(): Boolean? {
        return mViewModel?.loadMore()
    }

    override fun onRefreshDataAfterFileOperation(
        operationType: Int,
        operationFiles: List<BaseFileBean>
    ) {
        super.onRefreshDataAfterFileOperation(operationType, operationFiles)
        Log.i(TAG, "onRefreshDataAfterFileOperation ")
        mViewModel?.let {
            if (operationType == DELETE_OPERATION) {
                it.mSearchDataModel.value.let { result ->
                    operationFiles.forEach { deleteItem ->
                        val removeResult = result?.searchResultSubList?.allTableFiles?.removeIf {
                            it == deleteItem
                        }.apply {
                            Log.i(TAG, "onRefreshDataAfterFileOperation removeResult $this, deleteItem $deleteItem")
                        }
                    }
                }
            }
            if (operationFiles.find { it is DriveFileWrapper && it.isKDocs() } != null) {
                Log.i(TAG, "onRefreshDataAfterFileOperation delete file contain kdocs, not reload again")
            } else {
                reloadData()
            }
        }
    }

    fun reloadData() {
        Log.w(TAG, "reloadData $searchWord")
        searchController.getQueryText().let {
            Log.d(TAG, "reloadData: start=$it")
            if (it.isNullOrEmpty().not()) {
                showSearchLoading()
            } else {
                //搜索后切换暗色模式时query值为null 做返回处理
                if (it == null) {
                    return
                }
            }
            mViewModel?.onQueryTextChange(it?.toString())
            notifySelectModel(false)
        }
    }

    private fun showSearchLoading() {
        mViewModel?.let { vm ->
            vm.mSearchDataModel.value.let {
                if ((it == null) || (it.searchKey != searchController.getQueryText().toString())) {
                    if (loadingController == null) {
                        loadingController = LoadingController(this, this)
                    }
                    loadingController?.showLoading()
                }
            }
        }
    }

    private fun hideSearchLoading() {
        loadingController?.dismissLoading(true)
    }


    override fun onShowSearch() {
    }

    override fun onExitSearch() {
        hideKeyboard()
        finish()
    }

    fun hideKeyboard() {
        searchController.let {
            it.clearFocus()
            it.hideSoftInput()
            it.setNextSearchClearFocus()
        }
    }

    override fun onSearch(text: String) {
        mHandler?.let {
            // To prevent frequent searches set delay;
            it.sendMessage(it.obtainMessage(WHAT_READY_SEARCH, text))
            it.removeMessages(WHAT_START_SEARCH)
            it.sendEmptyMessageDelayed(WHAT_START_SEARCH, READY_SEARCH_DELAY)
        }
    }

    override fun onSearchSubmit(text: String) {
        if (mViewModel?.isLoadingData() != true) {
            onSearch(text)
        }
    }

    @SuppressLint("RestrictedApi")
    fun notifySelectModel(selectModel: Boolean, init: Boolean = false) {
        Log.d(TAG, "notifySelectModel select:$selectModel last:$lastSelectModel init:$init")
        if (selectModel) {
            searchController.setSearchHeaderVisible(false)
        } else {
            if (lastSelectModel != selectModel) {
                toolbar?.let {
                    it.menu.clear()
                    it.inflateMenu(R.menu.global_search_menu)
                    searchController.let { sc ->
                        if (init) {
                            sc.showAnimateSearchView(
                                this,
                                it,
                                R.id.actionbar_search_view,
                                this,
                                true,
                                false
                            )
                            sc.setQuery(searchWord, true)
                            readySearch(searchWord)
                        }
                        sc.setSearchHeaderVisible(true)
                    }
                }
            }
        }
        lastSelectModel = selectModel
    }

    fun updateSelectFilter(filterItem: FilterItem) {
        mViewModel?.updateSelectFilter(filterItem)
    }

    fun removeSelectFilter(itemList: List<FilterItem>) {
        mViewModel?.removeSelectFilter(itemList)
    }

    override fun backtoTop() {
        super.backtoTop()
        fragment?.let {
            if (it.isResumed) {
                it.getRecyclerView()?.fastSmoothScrollToTop()
            }
        }
    }

    override fun onBackPressed() {
        if ((fragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun transformToNextFragment(path: String?) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mViewModel?.let {
            selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
        }
    }

    override fun showSelectPathFragmentDialog(code: Int, path: String?) {
        mViewModel?.let {
            selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path)
        }
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        hideKeyboard()
        if (fragment == null) {
            fileOperateController.onSelectPathReturn(this, code, paths)
        } else {
            fragment?.fromSelectPathResult(code, paths)
        }
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        Log.i(TAG, "onUpdatedLabel $fragment")
        fragment?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return selectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun updateNavigationToolPadding() {
        navigationController?.updateNavigationToolPadding(navPaddingBottom)
    }
}