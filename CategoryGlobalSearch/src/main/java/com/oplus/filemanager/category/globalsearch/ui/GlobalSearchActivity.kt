/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search.GlobalSearchActivity
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.DragEvent
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.tablayout.COUITab
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.tablayout.COUITabLayoutMediator
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.appcompat.viewpager.COUIViewPager2
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseViewPagerFragmentAdapter
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.bean.SwitchChangeEvent
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.controller.navigation.IMenuEnable
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.databus.EventConstants
import com.filemanager.common.databus.LiteEventBus
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.view.ViewPagerWrapperForPC
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.bean.SearchCardWrapper
import com.oplus.filemanager.category.globalsearch.controller.GlobalSearchHistoryController
import com.oplus.filemanager.category.globalsearch.controller.SearchController
import com.oplus.filemanager.category.globalsearch.controller.SearchFilterClickListener
import com.oplus.filemanager.category.globalsearch.controller.SearchHistoryInterface
import com.oplus.filemanager.category.globalsearch.controller.SearchListener
import com.oplus.filemanager.category.globalsearch.manager.filter.ConnectStateHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class GlobalSearchActivity : EncryptActivity(), NavigationInterface, SearchListener,
    NavigationBarView.OnItemSelectedListener, SearchFilterClickListener,
    COUITabLayout.OnTabSelectedListener, TransformNextFragmentListener, SearchHistoryInterface,
    TabActivityListener<BaseFileBean>, BaseVMActivity.PermissonCallBack, DragDropInterface,
    IDraggingActionOperate {

    companion object {
        private const val TAG = "GlobalSearchActivity"
        private const val WHAT_READY_SEARCH = 100
        private const val WHAT_START_SEARCH = 101
        const val TAB_ALL = Integer.MIN_VALUE
        const val MAX_SPLIT_SCREEN = 3
        var navigationBarHeight: Int = 0

        fun start(context: Activity?, category: Int = TAB_ALL, supperAppPackage: String? = null, dirPath: String? = null) {
            context?.apply {
                if (actionCheckPermission().not()) {
                    //其他页面跳转到搜索,如果没有所有文件权限。弹窗提示
                    (context as? BaseVMActivity)?.showSettingGuildDialog()
                    return
                }
                startActivity(Intent(context, GlobalSearchActivity::class.java).also {
                    it.putExtra(KtConstants.P_CATEGORY_TYPE, category)
                    it.putExtra(KtConstants.P_PACKAGE, supperAppPackage)
                    it.putExtra(KtConstants.CURRENT_DIR, dirPath)
                })
                overridePendingTransition(R.anim.search_push_up_enter, com.filemanager.common.R.anim.app_zoom_fade_exit)
            }
        }

        private fun actionCheckPermission(): Boolean {
            return PermissionUtils.hasStoragePermission()
        }
    }

    private val mTabCategory = arrayListOf(
        TAB_ALL,
        CategoryHelper.CATEGORY_IMAGE,
        CategoryHelper.CATEGORY_VIDEO,
        CategoryHelper.CATEGORY_AUDIO,
        CategoryHelper.CATEGORY_DOC,
        CategoryHelper.CATEGORY_APK,
        CategoryHelper.CATEGORY_COMPRESS
    )

    private var mToolbar: COUIToolbar? = null
    private var mAppBarLayout: COUIDividerAppBarLayout? = null
    private var mTabView: COUITabLayout? = null
    private var mViewPager: COUIViewPager2? = null
    private var mViewPagerWrapper: ViewPagerWrapperForPC? = null
    private var mRootView: ViewGroup? = null
    private var mIsFirstChangeTab = true
    private var mViewModel: GlobalSearchViewModel? = null
    private var mExternalCategory: Int = TAB_ALL
    private var mPages: ArrayList<GlobalSearchFragment> = ArrayList()
    private var mTabIndex: Int = 0
    private var mNavigationController: NavigationController? = null
    private var mLoadingController: LoadingController? = null
    private val mSearchController: SearchController by lazy { SearchController(lifecycle) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mHistoryController by lazy { GlobalSearchHistoryController(lifecycle) }
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private val mRecycleViewPool: RecyclerView.RecycledViewPool by lazy { RecyclerView.RecycledViewPool() }
    private var mStartTime = 0L
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_SEARCH,
            model, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(model))
            it.checkHasDynamicBeans = true
        }
    }

    private var mHandler: Handler? = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                WHAT_READY_SEARCH -> readySearch(msg.obj?.toString())
                WHAT_START_SEARCH -> reloadData()
            }
        }
    }
    private var keyword: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        //放在这里是为了先让FileConcondifionManager中的列表第一次初始化正确
        ConnectStateHelper.initAndRegister()
        super.onCreate(savedInstanceState)
        Log.i(TAG, "onCreate")
        mStartTime = System.currentTimeMillis()
        navigationBarHeight = KtViewUtils.getSoftNavigationBarSize(this)
        if (FeatureCompat.sIsExpRom) {
            val dmpSearchApi = Injector.injectFactory<IExportDmpSearchApi>()
            val shouldLoadDmp = dmpSearchApi?.isShouldLoadDMP() ?: false
            if (shouldLoadDmp) {
                dmpSearchApi?.checkState(this@GlobalSearchActivity)
            }
        } else {
            val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
            val shouldLoadDmp = dmpSearchApi?.isShouldLoadDMP() ?: false
            if (shouldLoadDmp) {
                dmpSearchApi?.checkState(this@GlobalSearchActivity)
            }
        }
        checkNeedDfmP2P()
        initRemoteDevice()
    }

    override fun onStart() {
        super.onStart()
        if (mViewModel?.mSearchDataModel?.value?.searchResultSubList?.allTableFiles.isNullOrEmpty() != false) {
            mSelectPathController.hideSelectPathFragmentDialog(supportFragmentManager)
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.search_activity
    }

    fun getExternalCategory() = mExternalCategory

    override fun initView() {
        try {
            mExternalCategory = IntentUtils.getInt(intent, KtConstants.P_CATEGORY_TYPE, TAB_ALL)
            getDataFromDeepLink(intent)
        } catch (e: Exception) {
            Log.e(TAG, "initData failed: ${e.message}")
        }

        registerVmChangedReceiver(null)
        mAppBarLayout = findViewById(R.id.appbar_layout)
        mToolbar = findViewById(R.id.toolbar)
        mTabView = findViewById(R.id.tab_layout)
        mViewPager = findViewById(R.id.viewPager)
        mViewPagerWrapper = findViewById(R.id.view_pager_wrapper)
        mRootView = findViewById(R.id.coordinator_layout)
    }

    fun checkNeedDfmP2P() {
        GlobalScope.launch {
            Log.d(TAG, "checkNeedDfmP2P start")
            CommonUtil.checkAndOpenP2PForDfm(CommonUtil.OPEN_P2P_TYPE_INTO_SEARCH_ACTVITY)
            Log.d(TAG, "checkNeedDfmP2P end")
        }
    }

    private fun initRemoteDevice() {
        Log.d(TAG, "initRemoteDevice")
        val remoteApi: IRemoteDevice? = Injector.injectFactory<IRemoteDevice>()
        remoteApi?.init(this)
    }



    /**
     * 从deeplink中获取Category的值和搜索词
     */
    private fun getDataFromDeepLink(intent: Intent) {
        if (Intent.ACTION_VIEW == intent.action) {
            val uri = intent.data
            uri?.apply {
                keyword = this.getQueryParameter(KtConstants.P_KEY_WORD) ?: ""
                val category = this.getQueryParameter(KtConstants.P_CATEGORY_TYPE) ?: mExternalCategory.toString()
                Log.d(TAG, "getDataFromDeepLink category:$category keyword:$keyword")
                try {
                    mExternalCategory = category.toInt()
                    StatisticsUtils.statisticsEntryLaunch(this@GlobalSearchActivity, Constants.PKG_SPEECH_ASSIST, "", Constants.PAGE_SEARCH)
                } catch (e: NumberFormatException) {
                    Log.e(TAG, "getDataFromDeepLink error", e)
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        Log.i(TAG, "onConfigurationChanged")
        super.onConfigurationChanged(newConfig)
        getFragmentList().forEach {
            it.checkNeedShowEmptyView()
        }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            title = ""
            inflateMenu(R.menu.global_search_menu)
            Log.i(TAG, "initToolbar INFLATE SERACH MENU")
        }

        mTabView?.visibility = View.GONE
        setSupportActionBar(mToolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        mRootView?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(this@GlobalSearchActivity), paddingRight, paddingBottom)
        }

        mAppBarLayout?.apply {
            post {
                Log.i(TAG, "notifySelectModel FROM initToolbar")
                initSearchController()
                showOrHideSearchController(false)
                restoreState()
            }
        }
        if ((mTabView != null) && (mViewPager != null)) {
            COUITabLayoutMediator(mTabView!!, mViewPager!!) { tab, position ->
                val title = when (mTabCategory[position]) {
                    TAB_ALL -> com.filemanager.common.R.string.total
                    CategoryHelper.CATEGORY_IMAGE -> com.filemanager.common.R.string.string_photos
                    CategoryHelper.CATEGORY_VIDEO -> com.filemanager.common.R.string.string_videos
                    CategoryHelper.CATEGORY_AUDIO -> com.filemanager.common.R.string.string_audio
                    CategoryHelper.CATEGORY_DOC -> com.filemanager.common.R.string.string_documents
                    CategoryHelper.CATEGORY_APK -> com.filemanager.common.R.string.string_apk
                    CategoryHelper.CATEGORY_COMPRESS -> com.filemanager.common.R.string.string_compress
                    else -> com.filemanager.common.R.string.total
                }
                tab.setText(getString(title).trim())
            }.apply {
                attach()
            }
            mTabView?.isUpdateindicatorposition = true
            mTabView?.clearOnTabSelectedListeners()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initData() {
        initFragment()
        initToolbar()
        mNavigationController = if (mExternalCategory == CategoryHelper.CATEGORY_RECYCLE_BIN) {
            NavigationController(lifecycle, NavigationType.RECYCLE_EDIT, id = R.id.navigation_tool)
        } else if (mExternalCategory == CategoryHelper.CATEGORY_DFM) {
            NavigationController(lifecycle, type = NavigationType.DFM, id = R.id.navigation_tool)
        } else if (CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC == mExternalCategory) {
            NavigationController(lifecycle, type = NavigationType.REMOTE_MAC, id = R.id.navigation_tool)
        } else {
            NavigationController(lifecycle, id = R.id.navigation_tool)
        }
        mHistoryController.setOnHistoryClickListener(this)
    }

    private fun initFragment() {
        mTabCategory.forEachIndexed { index, category ->
            if (mExternalCategory == category) {
                mTabIndex = index
            }
        }
        for (i in mTabCategory.indices) {
            var fragment = supportFragmentManager.findFragmentByTag("f$i")
            if (fragment == null) {
                fragment = GlobalSearchFragment()
                val bundle = Bundle()
                bundle.putInt(KtConstants.P_CATEGORY_TYPE, mTabCategory[i])
                bundle.putInt(KtConstants.P_CATEGORY_TYPE, mTabCategory[i])
                fragment.arguments = bundle
            }
            if (fragment is GlobalSearchFragment) {
                fragment.setTabActivityListener(this)
                mPages.add(fragment)
            }
        }
        mViewPager?.let {
            it.offscreenPageLimit = mTabCategory.size
            it.overScrollMode = View.OVER_SCROLL_NEVER
            it.adapter = ViewPagerFragmentStateAdapter(this, mTabCategory)
            it.post {
                try {
                    it.setCurrentItem(mTabIndex, false)
                    mTabView?.addOnTabSelectedListener(this)
                } catch (e: Exception) {
                    Log.e(TAG, "ViewPager setCurrentItem failed in init: tab=$mTabIndex, ${e.message}")
                }
            }
        }
    }

    private fun restoreState() {
        mViewModel?.let { vm ->
            var restoreSearchKey: String? = null
            var restoreTab: Int? = null
            try {
                restoreSearchKey = vm.mSavedState.remove<String>(GlobalSearchViewModel.LAST_SEARCH_KEY)
                restoreTab = vm.mSavedState.remove<Int>(GlobalSearchViewModel.LAST_SELECT_TAB)
            } catch (e: Exception) {
                Log.d(TAG, "restoreState failed: ${e.message}")
            }
            if (restoreSearchKey.isNullOrEmpty().not()) {
                // Set it in advance to avoid the loading state is false when checked in
                // GlobalSearchFragment.checkNeedShowEmptyView when setCurrentItem below.
                vm.readyLoadData()
                mSearchController.setQuery(restoreSearchKey!!, false)
            }
            if (restoreTab != null) {
                mViewPager?.setCurrentItem(restoreTab, false)
            }
            if (restoreSearchKey.isNullOrEmpty()) {
                mSearchController.showSoftInput()
            } else {
                hideKeyboard()
            }
        }
    }

    override fun onShowSearch() {
    }

    override fun onExitSearch() {
        hideKeyboard()
        finish()
    }

    override fun onSearch(text: String) {
        mHandler?.let {
            // To prevent frequent searches set delay;
            it.sendMessage(it.obtainMessage(WHAT_READY_SEARCH, text))
            it.removeMessages(WHAT_START_SEARCH)
            it.sendEmptyMessageDelayed(WHAT_START_SEARCH, 500)
        }
    }

    override fun onSearchSubmit(text: String) {
        mViewModel?.needCheckThisTime = false
        if (mViewModel?.isLoadingData() != true) {
            onSearch(text)
        }
        addHistory(text)
    }

    internal fun getToolbar() = mToolbar

    internal fun getSearchController() = mSearchController

    fun notifySelectTextCount(text: String) {
        mToolbar?.title = text
    }

    fun addHistory(text: String) {
        mHistoryController.addHistory(text)
    }

    override fun onTabReselected(p0: COUITab?) {
    }

    override fun onTabUnselected(tab: COUITab?) {
        if (mIsFirstChangeTab) {
            hideKeyboard()
            mIsFirstChangeTab = false
        }
    }

    override fun onTabSelected(tab: COUITab?) {
        tab?.let {
            mTabIndex = it.position
            Log.d(TAG, "onTabSelected mTabIndex $mTabIndex,selectedByClick ${it.selectedByClick}")
            mViewModel?.needCheckThisTime = true
            if (it.selectedByClick) {
                mViewPager?.setCurrentItem(mTabIndex, false)
            } else {
                refreshCurrentPage()
            }

            StatisticsUtils.onCommon(
                MyApplication.sAppContext, when (mTabCategory[mTabIndex]) {
                    CategoryHelper.CATEGORY_IMAGE -> StatisticsUtils.SEARCH_TYPE_TAB_PICTURE_CLICK
                    CategoryHelper.CATEGORY_VIDEO -> StatisticsUtils.SEARCH_TYPE_TAB_VIDEO_CLICK
                    CategoryHelper.CATEGORY_AUDIO -> StatisticsUtils.SEARCH_TYPE_TAB_AUDIO_CLICK
                    CategoryHelper.CATEGORY_DOC -> StatisticsUtils.SEARCH_TYPE_TAB_DOC_CLICK
                    CategoryHelper.CATEGORY_APK -> StatisticsUtils.SEARCH_TYPE_TAB_APK_CLICK
                    CategoryHelper.CATEGORY_COMPRESS -> StatisticsUtils.SEARCH_TYPE_TAB_ARCHIVES_CLICK
                    else -> StatisticsUtils.SEARCH_TYPE_TAB_ALL_CLICK
                }
            )
        }
    }

    override fun startObserve() {
        mViewModel = ViewModelProvider(this, SavedStateViewModelFactory(application, this))
            .get(GlobalSearchViewModel::class.java).apply {
                init(<EMAIL>, intent)
                mSearchDataModel.observe(this@GlobalSearchActivity) {
                    Log.d(
                        TAG,
                        "mSearchDataModel observe: size=${it?.uriLoadResult?.mResultList?.size}"
                    )
                    getFragmentList().forEach { frg ->
                        frg.onSearchDataUpdate(it)
                        frg.onShowFilter()
                    }
                    hideSearchLoading()
                    if (mTabIndex != mViewPager?.currentItem) {
                        try {
                            mViewPager?.setCurrentItem(mTabIndex, false)
                        } catch (e: Exception) {
                            Log.e(
                                TAG,
                                "ViewPager setCurrentItem failed in observe: tab=$mTabIndex, ${e.message}"
                            )
                        }
                    }
                }
                mFilterSelectedDataModel.observe(this@GlobalSearchActivity) {
                    Log.d(TAG, "mFilterSelectedDataModel observer, selected size =${it.size}")
                    getFragmentList().forEach { frg ->
                        frg.onSelectedFilterChange(it)
                    }
                    if (it.size == 1) {
                        it?.keys?.forEach { key ->
                            val type = getConditionType(it[key]?.parent?.desc)
                            OptimizeStatisticsUtil.searchConditionType(type)
                        }
                    } else if (it.size > 1) {
                        OptimizeStatisticsUtil.searchConditionType(OptimizeStatisticsUtil.SEARCH_CONDITION_MIX)
                    }
                }
            }
        startEventObserver()
    }

    private fun startEventObserver() {
        Log.i(TAG, "startEventObserver")
        //列表item的卡片的ingore处理，在设置页打开关闭开关之后，onResume中会重新触发加载，loader中会判定是否添加wrapper，这里不用处理
        LiteEventBus.instance.with(
            EventConstants.EVENT_THIRD_APP_SEARCH_CARD_IGNORE,
            EventConstants.OBSERVERID_THIRD_APP_SEARCH_CARD_IGNORE
        )?.observe(this) { searchCardWrapper ->
                Log.d(TAG, "eventChange EVENT_THIRD_APP_SEARCH_CARD_IGNORE searchCardWrapper $searchCardWrapper")
                if (searchCardWrapper is SearchCardWrapper) {
                    mViewModel?.onThirdAppCardIgnore()
                }
            }
        //搜索历史中的卡片的显示和不显示处理，在设置页面打开关闭开关之后，发送这个事件，这里监听这个事件之后处理
        LiteEventBus.instance.with(
            EventConstants.EVENT_THIRD_APP_SEARCH_SWITCH_CHANGE,
            EventConstants.OBSERVERID_THIRD_APP_SERRCH_SWITCH_CHANGE
        )?.observe(this) { searchCardEvent ->
            Log.d(TAG, "eventChange EVENT_THIRD_APP_SERRCH_SWITCH_CHANGE searchCardEvent $searchCardEvent")
            if (searchCardEvent is SwitchChangeEvent) {
                mHistoryController.showOrHideCardView()
                //这里加入这个代码是在搜索界面在选择模式下从三方应用授权卡片点击去授权之后，回到搜索列表页面变为非选择模式，规避选择模式下重新搜索显示混乱的问题
                if (mPages.size > 0 && searchCardEvent.eventType == SwitchChangeEvent.EVENT_TYPE_ON) {
                    val allFragment = mPages[0]
                    allFragment.switchToNormalMode()
                }
                Log.d(TAG, "eventChange EVENT_THIRD_APP_SERRCH_SWITCH_CHANGE filter change")
                FilterConditionManager.recycleAndInitFilter()
                getFragmentList().forEach { fragment ->
                    fragment.openOrCloseSwitchForFilter()
                }
            }
        }
    }

    private fun releaseEventObserver() {
        Log.i(TAG, "releaseEventObserver")
        LiteEventBus.instance.releaseObserver(EventConstants.OBSERVERID_THIRD_APP_SEARCH_CARD_IGNORE, this)
        LiteEventBus.instance.releaseObserver(EventConstants.OBSERVERID_THIRD_APP_SERRCH_SWITCH_CHANGE, this)
    }


    private fun getConditionType(desc: String?): Int = when (desc) {
        getString(com.filemanager.common.R.string.search_filtrate_time) -> OptimizeStatisticsUtil.SEARCH_CONDITION_TIME
        getString(com.filemanager.common.R.string.search_filtrate_source) -> OptimizeStatisticsUtil.SEARCH_CONDITION_SOURCE
        getString(com.filemanager.common.R.string.search_filtrate_format) -> OptimizeStatisticsUtil.SEARCH_CONDITION_FORMAT
        else -> -1
    }

    override fun onResume() {
        if (PrivacyPolicyController.hasAgreePrivacy().not() || PermissionUtils.hasStoragePermission().not()) {
            hideKeyboard()
        }
        Log.i(TAG, "onResume")
        super.onResume()
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        mSearchController.getQueryText().let {
            if (it.isNullOrEmpty()) {
                mHistoryController.show(this, mRootView)
                mHandler?.postDelayed({mSearchController.showSoftInput()},500)
            }
        }
    }

    fun reloadData() {
        mSearchController.getQueryText().let {
            Log.d(TAG, "reloadData: start=$it")
            if (it.isNullOrEmpty().not()) {
                showSearchLoading()
            } else {
                //搜索后切换暗色模式时query值为null 做返回处理
                if (it == null) {
                    return
                }
                mHistoryController.show(this, mRootView)
            }
            mViewModel?.onQueryTextChange(it?.toString())
            Log.i(TAG, "notifySelectModel FROM reloadData")
           /* fixBug7496474
            otifySelectModel(false)*/
        }
    }

    private fun readySearch(text: String?) {
        Log.d(TAG, "readySearch: start=$text")
        mViewModel?.apply {
            mSearchController.getQueryText()?.let {
                readyLoadData()
            }
            mSearchDataModel.value?.reset()
        }
        getFragmentList().forEach { frg ->
            frg.onSearchDataUpdate(null, text)
            frg.notifySearchStart()
        }
        val visibility = if (text.isNullOrEmpty()) View.GONE else View.VISIBLE
        mTabView?.visibility = visibility
        mViewPager?.let {
            it.post {
                it.visibility = visibility
            }
        }
        if (text.isNullOrEmpty()) {
            mHistoryController.show(this, mRootView)
            hideSearchLoading()
        } else {
            mHistoryController.dismiss()
            showSearchLoading()
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        getCurrentFragment()?.onResumeLoadData()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    fun getCurrentFragment(): GlobalSearchFragment? {
        return getFragment(mTabIndex)
    }

    private fun getFragmentList(): ArrayList<GlobalSearchFragment> {
        val list = arrayListOf<GlobalSearchFragment>()
        for (i in mTabCategory.indices) {
            getFragment(i)?.also { list.add(it) }
        }
        return list
    }

    private fun getFragment(index: Int): GlobalSearchFragment? {
        return supportFragmentManager.findFragmentByTag("f$index")?.let {
            if (it is GlobalSearchFragment) {
                it
            } else null
        } ?: null
    }

    override fun showNavigation() {
        mNavigationController?.showNavigation(this)
        updateNavigationToolPadding()
    }

    fun updateMenuType(type: NavigationType) {
        mNavigationController?.let {
            if (it.getNavMenuType() == type) {
                return
            }
            it.modifyMenuType(type, this)
            it.adjustToolItemsVisible()
        }
    }

    fun showNavigation(type: NavigationType, showTab: Boolean) {
        mNavigationController?.modifyMenuType(type, this)
        showNavigation()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController?.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController?.hideNavigation(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onNavigationItemSelected(p0: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(p0) ?: false
    }

    override fun onHistoryClickListener(txt: String?) {
        if (!txt.isNullOrEmpty()) {
            mSearchController.setQuery(txt, true)
        }
    }

    fun showOrHideSearchController(selectModel: Boolean) {
        Log.i(TAG, "showOrHideSearchController selectModel $selectModel")
        mSearchController.setSearchHeaderVisible(!selectModel)
    }

    override fun onBackPressed() {
        if ((getCurrentFragment() as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        getCurrentFragment()?.let {
            if (it.isResumed) {
                it.getRecyclerView()?.fastSmoothScrollToTop()
            }
        }
    }

    private fun showSearchLoading() {
        mViewModel?.let { vm ->
            vm.mSearchDataModel.value.let {
                if ((it == null) || (it.searchKey != mSearchController.getQueryText().toString())) {
                    if (mLoadingController == null) {
                        mLoadingController = LoadingController(this, this)
                    }
                    mLoadingController?.showLoading()
                }
            }
        }
    }

    private fun hideSearchLoading() {
        mLoadingController?.dismissLoading(true)
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mViewModel?.let { getCurrentFragment()?.onResumeLoadData() }
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mViewModel?.let {
            mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
        }
    }

    override fun showSelectPathFragmentDialog(code: Int, path: String?) {
        mViewModel?.let {
            mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path)
        }
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        hideKeyboard()
        if (getCurrentFragment() == null) {
            fileOperateController.onSelectPathReturn(this, code, paths)
        } else {
            getCurrentFragment()?.fromSelectPathResult(code, paths)
        }
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        super.onPermissionReject(alwaysReject)
        hideKeyboard()
        mNavigationController?.hideNavigation(this)
        finish()
    }

    fun hideKeyboard() {
        mSearchController.let {
            it.clearFocus()
            it.hideSoftInput()
            it.setNextSearchClearFocus()
        }
    }

    fun getViewModel() = mViewModel

    fun getRecycleViewPool() = mRecycleViewPool

    override fun onFilterClick(filterItem: FilterItem) {
        //点击事件
        Log.d(TAG, "onFilterClick: ${filterItem.desc}")
        mViewModel?.updateSelectFilter(filterItem)
    }

    override fun onFileterItemRemoved(itemList: List<FilterItem>) {
        //当dfm跨端,otg,sdcard等可变数据链接断开时，之前如果存存在dfm跨端，otg，sdcard中被选中的筛选卡片，这里需要将相关卡片从ViewModule中移除
        Log.d(TAG, "onFileterItemRemoved: $itemList")
        mViewModel?.removeSelectFilter(itemList)
    }

    override fun onChangeFilterPanel() {
        hideKeyboard()
        getCurrentFragment()?.showOrHidePanel()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (getCurrentFragment()?.getFilterPanelState() == true) {
            if ((ev?.y ?: 0f) <= (mTabView?.bottom ?: 0)) {
                onChangeFilterPanel()
                return true
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mViewModel?.loadHistoryData()
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                (mViewPager?.adapter as? BaseViewPagerFragmentAdapter)?.scrollToPosition(mTabIndex)
                return
            }
        }
    }

    private fun showEditMode() {
        mViewPager?.isUserInputEnabled = false
        mTabView?.isEnabled = false
        mViewPagerWrapper?.setEditMode(true)
    }

    private fun exitEditMode() {
        mViewPager?.isUserInputEnabled = true
        mTabView?.isEnabled = true
        mViewPagerWrapper?.setEditMode(false)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, com.support.appcompat.R.anim.coui_push_down_exit)
    }

    override fun onDestroy() {
        Log.i(TAG, "onDestroy")
        mViewModel?.apply {
            mSavedState.set(GlobalSearchViewModel.LAST_SELECT_TAB, mTabIndex)
        }

        val endTime = (System.currentTimeMillis() - mStartTime) / 1000
        val valueMap = mapOf(StatisticsUtils.SEARCH_DURATION_TIME to endTime.toString())
        StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.SEARCH_DURATION_TIME, valueMap)
        releaseEventObserver()
        mRecycleViewPool.clear()
        mHandler?.removeCallbacksAndMessages(null)
        mHandler = null
        unregisterVmChangedReceiver()
        mPages.clear()
        ConnectStateHelper.release()
        super.onDestroy()
    }

    inner class ViewPagerFragmentStateAdapter(private val activity: GlobalSearchActivity, private var categoryList: ArrayList<Int>) :
        BaseViewPagerFragmentAdapter(activity) {
        @NonNull
        override fun createFragment(position: Int): Fragment {
            return mPages[position]
        }

        override fun getItemCount(): Int {
            return mTabCategory.size
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }
    }

    /**
     * 初始化mSearchController，mSearchController找到searchView的menu，并完成相应处理
     */
    private fun initSearchController() {
        Log.i(TAG, "initSearchController")
        mToolbar?.let {
            mSearchController.let { sc ->
                sc.showAnimateSearchView(this, it, R.id.actionbar_search_view, this)
                if (keyword.isNotEmpty()) {
                    sc.setQuery(keyword, true)
                    readySearch(keyword)
                }
            }
        }
    }

    /**
     * 这个方法只做toolbar中的MENU操作，其他的底部navigation相关处理放在其他地方
     */
    override fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize: Int,
        selectedFileSize: Int,
        selectItems: ArrayList<BaseFileBean>
    ) {
        mToolbar?.let {
            if (needInit && (mTabView?.isInEditMode != true)) {
                it.menu.clear()
                it.isTitleCenterStyle = true
                it.inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
                Log.i(TAG, "initToolbarSelectedMode INFLATE SELECT MENU")
            }
            showEditMode()
            val isSelectAll = (realFileSize == selectedFileSize)
            Log.i(TAG, "initToolbarSelectedMode needInit $needInit, mTabView?.isInEditMode ${mTabView?.isInEditMode} realFileSize $realFileSize, " +
                    "selectedFileSize $selectedFileSize, isSelectAll $isSelectAll, selectItems size ${selectItems.size}")
            ToolbarUtil.updateToolbarTitle(it, selectedFileSize, isSelectAll)
            ToolbarUtil.setToolbarChildViewTag(it, this)
            val selectAllMenu = it.menu?.findItem(com.filemanager.common.R.id.action_select_all)
            selectAllMenu?.isEnabled = selectedFileSize > 0
        }
        supportActionBar?.setDisplayHomeAsUpEnabled(false)
    }

    fun processNavigationItemEnable(
        iMenuEnable: IMenuEnable
    ) {
        mNavigationController?.setNavigateItemAble(iMenuEnable)
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        Log.i(TAG, "initToolbarNormalMode needInit $needInit, empty $empty")
        mToolbar?.let {
            if (needInit) {
                it.menu.clear()
                it.inflateMenu(R.menu.global_search_menu)
            }
            Log.i(TAG, "initToolbarNormalMode INFLATE SERACH MENU")
            mSearchController.setSearchHeaderVisible(true)
            ToolbarUtil.setToolbarChildViewTag(it, this)
        }
        exitEditMode()
    }

    override fun refreshScanModeItemIcon(withAnimation: Boolean) {
    }

    override fun updateNeedSkipAnimation(withAnimation: Boolean) {
    }

    override fun onRefreshData() {
        mViewModel?.let { getCurrentFragment()?.onResumeLoadData() }
    }

    /**
     * 这个回调只有在选择模式下删除文件后才会调用
     */
    override fun onRefreshDataAfterFileOperation(
        operationType: Int,
        operationFiles: List<BaseFileBean>
    ) {
        Log.i(TAG, "onRefreshDataAfterFileOperation operationType $operationType, operationFiles $operationFiles")
        mViewModel?.let {
            if (operationType == GlobalSearchNormalViewModel.DELETE_OPERATION) {
                it.mSearchDataModel.value.let { result ->
                    operationFiles.forEach { deleteItem ->
                        val removeResult = result?.searchResultSubList?.allTableFiles?.removeIf {
                            it == deleteItem
                        }.apply {
                            Log.i(TAG, "onRefreshDataAfterFileOperation removeResult $this, deleteItem $deleteItem")
                        }
                    }
                }
            }
            if (operationFiles.find { it is DriveFileWrapper && it.isKDocs() } != null) {
                Log.i(TAG, "onRefreshDataAfterFileOperation delete file contain kdocs, not reload again")
            } else {
                mViewModel?.let { getCurrentFragment()?.onResumeLoadData() }
            }
        }
    }

    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission")
        showSettingGuildDialog()
    }

    override fun updateNavigationToolPadding() {
        mNavigationController?.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        if (event != null) {
            return getFragment(mTabIndex)?.handleDragEvent(event)
        }
        return false
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return getFragment(mTabIndex)?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        getFragment(mTabIndex)?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }
}