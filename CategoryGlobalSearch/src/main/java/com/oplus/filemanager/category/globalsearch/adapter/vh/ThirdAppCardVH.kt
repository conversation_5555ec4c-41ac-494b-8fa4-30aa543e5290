/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ThirdAppFileVH
 ** Description : 授权卡片的ViewHolder
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.adapter.vh

import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import androidx.annotation.LayoutRes
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.databus.EventConstants
import com.filemanager.common.databus.LiteEventBus
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.THIRD_FILE_CARD_CLICK_IGNORE
import com.filemanager.common.utils.StatisticsUtils.THIRD_FILE_CARD_CLICK_OPEN
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.bean.SearchCardWrapper
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.interfaze.setting.ISetting

class ThirdAppCardVH(convertView: View) : BaseSelectionViewHolder(convertView, false) {

    companion object {

        private const val TAG = "ThirdAppCardVH"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.third_app_permission_card_item
        }
    }


    private var discriptionTv: TextView? = null
    private var ignoreBotton: TextView? = null
    private var grantBotton: COUIButton? = null


    init {
        discriptionTv = convertView.findViewById(R.id.card_discription)
        ignoreBotton = convertView.findViewById(R.id.button_ignore)
        grantBotton = convertView.findViewById(R.id.button_grant)
        COUITextViewCompatUtil.setPressRippleDrawable(ignoreBotton)
        grantBotton?.setOnClickListener {
            Log.i(TAG, "grantBotton onClick jumpToSettingFunctionActivity")
            val settingApi = Injector.injectFactory<ISetting>()
            settingApi?.jumpToSettingFunctionActivity(grantBotton?.context as GlobalSearchActivity)
            StatisticsUtils.statisticsThirdFileCardClick(grantBotton?.context, THIRD_FILE_CARD_CLICK_OPEN)
        }
    }


    override fun isInDragRegionImpl(event: MotionEvent): Boolean = true


    fun bindData(
        data: SearchCardWrapper,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        position: Int
    ) {
        ignoreBotton?.setOnClickListener {
            Log.i(TAG, "ignoreBotton onClick ")
            PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_CARD_IGNORE, value = true)
            LiteEventBus.instance.send(EventConstants.EVENT_THIRD_APP_SEARCH_CARD_IGNORE, data)
            StatisticsUtils.statisticsThirdFileCardClick(ignoreBotton?.context, THIRD_FILE_CARD_CLICK_IGNORE)
        }
    }


    override fun drawDivider(): Boolean {
        return false
    }


    override fun getDividerEndInset(): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
    }
}