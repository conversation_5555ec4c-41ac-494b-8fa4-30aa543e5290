/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search.GlobalSearchNormalViewModel
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.app.Activity
import android.content.Intent
import android.view.MotionEvent
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.ThirdAppFileWrapper
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.Constants.LABEL_FILTER_MAPPING_CONTENT
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.detail.FileDetailDialogAdapter
import com.google.gson.Gson
import com.oplus.filemanager.category.globalsearch.bean.MoreFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchDFMFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.operate.MixFileDeleteAction
import com.oplus.filemanager.category.globalsearch.operate.MixFileDeleteObserver
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.simulateclick.ISimulateClickApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class GlobalSearchNormalViewModel : GlobalSearchFragmentViewModel() {
    companion object {
        private const val TAG = "GlobalSearchNormalViewModel"
        private const val OTG_ACTIVITY_NAME = "com.oplus.filebrowser.otg.OtgFileBrowserActivity"
        private const val FILE_BROWSER_ACTION = "oplus.intent.action.filemanager.BROWSER_FILE"
        const val DELETE_OPERATION = 1
        private const val EXTRACHAR = 4
    }

    /**
     * 获取navigation类型
     * 1. 所有文件中没有云文档，显示默认工具栏
     * 2. 所有文件中有云文档，没有本地文件，显示云文档工具栏
     * 3. 有云文档和本地文件，只选中了云文档，显示云文档工具栏
     * 4. 有云文档和本地文件，选中了云文档和本地文件，显示默认工具栏
     * 5. 有远程电脑文件的情况下，只选中远程电脑文件，除远程电脑文件外的类型都置灰，显示远程电脑文件功能栏
     */
    override fun getNavigationType(category: Int): NavigationType {
        if (category == CategoryHelper.CATEGORY_RECYCLE_BIN || category == CategoryHelper.CATEGORY_DFM) {
            return super.getNavigationType(category)
        }
        val allFile = uiState.value?.fileList ?: return NavigationType.DEFAULT
        val selectItems = getSelectItems()
        // 统计所有文件和选中文件的类型及个数
        val allFileCount = countLocalAndDriveFile(allFile)
        val selectFileCount = countLocalAndDriveFile(selectItems)
        val containLocalFile = allFileCount.first > 0
        val containDriveFile = allFileCount.second > 0
        val selectLocalFile = selectFileCount.first > 0
        val selectDriveFile = selectFileCount.second > 0
        val onlySelectRemoteFile = isSelectRemoteFile(selectItems)
        Log.d(TAG, "getNavigationType all:$allFileCount； select:$selectFileCount onlySelectRemoteFile:$onlySelectRemoteFile")
        if (onlySelectRemoteFile) { // 只选中远程电脑文件
            return NavigationType.REMOTE_MAC
        }
        if (!containDriveFile) { // 所有文件中没有云文档,直接用默认工具栏
            return NavigationType.DEFAULT
        }
        if (!containLocalFile) { // 所有文件中有云文档,但没有本地文件，用云文档工具栏
            return NavigationType.FILE_DRIVE
        }
        // 所有文件中有本地文件和云文件
        return if (selectDriveFile && !selectLocalFile) {  // 只选中云文档
            NavigationType.FILE_DRIVE
        } else {
            NavigationType.DEFAULT
        }
    }

    /**
     * 统计本地文档和云文档个数
     * @return 本地文件个数，云文档个数
     */
    private fun countLocalAndDriveFile(list: List<BaseFileBean>): Pair<Int, Int> {
        var localFileCount = 0
        var driveFileCount = 0
        list.forEach {
            if (it is DriveFileWrapper) {
                driveFileCount++
            } else if (it is SearchLabelWrapper || it is UriFileWrapper) {
                localFileCount++
            }
        }
        return Pair(localFileCount, driveFileCount)
    }

    override fun onFileClick(
        activity: ComponentActivity,
        file: BaseFileBean,
        event: MotionEvent?,
        mediaIds: ArrayList<String>?
    ): Boolean {
        super.onFileClick(activity, file, event, mediaIds)
        if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
            onClickDirFile(activity, file)
            return true
        } else if (file is SearchLabelWrapper) {
            onClickLabelFile(activity, file)
            return true
        } else if (file is DriveFileWrapper) {
            onClickDriveFile(activity, file)
            return true
        } else if (file is ThirdAppFileWrapper) {
            onClickThirdAppFile(activity, file)
            return true
        } else if (file is RemoteFileBean) {
            onClickRemoteFile(activity, file)
            return true
        } else {
            return false
        }
    }

    /**
     * 打开目录文件
     */
    private fun onClickDirFile(activity: ComponentActivity, file: BaseFileBean) {
        launch {
            if (file.checkExist().not()) {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                return@launch
            }
            file.mData?.let {
                val intent = Intent()
                val isOTGPath = VolumeEnvironment.isOTGPath(activity, it)
                val isSdcardPath = VolumeEnvironment.isSdcardPath(activity, it)
                if (isOTGPath || isSdcardPath) {
                    val otgList: ArrayList<String> = ArrayList()
                    intent.setClassName(
                        activity.packageName, OTG_ACTIVITY_NAME
                    )
                    otgList.add(it)
                    intent.putStringArrayListExtra(FileDetailDialogAdapter.OTG_LIST_PATH, otgList)
                    intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_otg)
                    intent.putExtra(Constants.TITLE, activity.getString(com.filemanager.common.R.string.storage_otg))
                } else {
                    intent.action = FILE_BROWSER_ACTION
                    intent.putExtra(
                        KtConstants.P_CURRENT_PATH, it
                    )
                }
                intent.putExtra(KtConstants.FROM_DETAIL, true)
                activity.startActivity(intent)
            }
        }
    }

    /**
     * 打开带标签的文件
     */
    private fun onClickLabelFile(activity: ComponentActivity, file: SearchLabelWrapper) {
        launch {
            var isFilter = false
            withContext(Dispatchers.IO) {
                isFilter = file.fileListFilter.size < file.fileList.size
                val fileListFilterContent: String?
                if (isFilter) {
                    fileListFilterContent = Gson().toJson(file.fileListFilter)
                    fileListFilterContent.let {
                        val filterMappingFile = File(appContext.filesDir, LABEL_FILTER_MAPPING_CONTENT)
                        if (filterMappingFile.exists().not()) {
                            filterMappingFile.createNewFile()
                        }
                        filterMappingFile.writeText(fileListFilterContent)
                    }
                }
            }
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.LABEL_SEARCH_CLICK
            )
            withContext(Dispatchers.Main) {
                val mainAction = Injector.injectFactory<IMain>()
                //wwh找到对应displayName的sideCategoryType
                mainAction?.startSubLabelListActivity(
                    activity, file.labelEntity.id, file.labelEntity.name, isFilter, true, sideCategoryType =
                    CategoryHelper.CATEGORY_LABEL_GROUP
                )
            }
        }
    }

    /**
     * 打开云文档
     */
    private fun onClickDriveFile(activity: Activity, file: DriveFileWrapper) {
        Log.d(TAG, "onClickDriveFile $file")
        if (file.isDir()) {
            Log.w(TAG, "onClickDriveFile file is dir!!!")
            return
        }
        val fileCloudBrowserApi = Injector.injectFactory<IFileCloudBrowser>()
        if (file.isTencentDocs()) { // 腾讯文档
            fileCloudBrowserApi?.openTencentDocs(activity, file.url, file.type)
        } else if (file.isKDocs()) { // 金山文档
            fileCloudBrowserApi?.openKDocsFile(activity, file.id, file.title, file.type)
        }
    }


    fun onClickThirdAppFile(activity: ComponentActivity, fileItem: ThirdAppFileWrapper) {
        val simulateClickApi = Injector.injectFactory<ISimulateClickApi>() ?: run {
            Log.i(TAG, "jumpToThirdAppAuto: SimulateClickApi is not integrated")
            return
        }

        CollectPrivacyUtils.collectInstalledAppList(fileItem.sourcePackageName)
        simulateClickApi.jumpToOtherApp(
            activity,
            fileItem.mDisplayName ?: "",
            fileItem.sourcePackageName
        )
    }

    fun onClickRemoteFile(activity: ComponentActivity, file: RemoteFileBean) {
        downloadRemoteFile(activity, true, listOf(file))
    }

    override fun getRealFileSize(): Int {
        var canSelectItemCount = 0
        mUiState.value?.mFileList?.forEach {
            if (isRealFile(it) && (it !is ThirdAppFileWrapper)) {
                canSelectItemCount += 1
            }
        }
        Log.i(TAG, "getRealFileSize canSelectItemCount $canSelectItemCount")
        return canSelectItemCount
    }

    override fun getTotalFileSize(): Int {
        var canSelectItemCount = 0
        mUiState.value?.mFileList?.forEach {
            val isValidFile =
                it is SearchLabelWrapper || it is DriveFileWrapper || it is UriFileWrapper || it is ThirdAppFileWrapper || it is RemoteFileBean
            if (isValidFile) {
                canSelectItemCount += 1
            }
            if (it is MoreFileWrapper) {
                canSelectItemCount += it.moreCount
            }
        }
        Log.i(TAG, "getTotalFileSize $canSelectItemCount")
        return canSelectItemCount
    }

    override fun getCanSelectFileSize(): Int {
        val isSelectRemoteFile = isRemoteFileSelected()
        val remoteFileCount = mUiState.value?.mFileList?.filter { it is RemoteFileBean }?.size ?: 0
        val realSize = getRealFileSize()
        val canSelectCount = if (isSelectRemoteFile == true) { // 选中远程电脑
            remoteFileCount
        } else if (isSelectRemoteFile == false) { // 有选中，但是没有选中远程电脑
            realSize - remoteFileCount
        } else { // 什么都没有选
            realSize
        }
        Log.d(TAG, "getCanSelectFileSize remote Select：$isSelectRemoteFile count:$remoteFileCount, real:$realSize canSelect:$canSelectCount")
        return canSelectCount
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
    }

    override fun onDelete(activity: ComponentActivity): Boolean {
        val selectFiles = getSelectItems()
        val localFiles = mutableListOf<BaseFileBean>()
        val tencentFiles = mutableListOf<DriveFileWrapper>()
        val kDocsFiles = mutableListOf<DriveFileWrapper>()
        val dfmFiles = mutableListOf<SearchDFMFileWrapper>()
        // 将文件进行分类
        classifySelectFiles(selectFiles, localFiles, dfmFiles, tencentFiles, kDocsFiles)
        // 上传埋点删除菜单按钮埋点
        OptimizeStatisticsUtil.onDelete("", selectFiles, true)
        Log.d(TAG, "onDelete select:${selectFiles.size} local:${localFiles.size} dfm:${dfmFiles.size}, " +
                "tencent:${tencentFiles.size} kDocs:${kDocsFiles.size}")
        // 选中的文件只包含本地文件，采用默认处理
        if (selectFiles.size == localFiles.size) {
            Log.w(TAG, "onDelete -> only contains local files!!")
            return false
        }
        // 只包含云文档,并且云文档的类型相同
        if (selectFiles.size == tencentFiles.size || selectFiles.size == kDocsFiles.size) {
            Log.w(TAG, "onDelete -> only contains same category cloud files!!")
            deleteSameCloudFiles(activity, selectFiles)
            return true
        }
        // 选择的数据只包含dfm的时候，拦截使用dfm的删除弹窗
        if (selectFiles.size == dfmFiles.size) {
            Log.w(TAG, "onDelete -> only contains same category dfm files!!")
            deleteDfmFiles(activity, selectFiles, checkSelectAll())
            return true
        }
        // 处理混合类型的文件
        Log.w(TAG, "onDelete -> contains mix file !!")
        MixFileDeleteAction(activity, selectFiles).execute(object : MixFileDeleteObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "onDelete -> Delete mix file onActionDone result:$result data:$data")
                if (result) {
                    changeListMode(KtConstants.LIST_NORMAL_MODE)
                    if (activity is BaseVMActivity) {
                        //这里删除文件之后，需要触发loader重新load（云文档分页加载时，如果被删除所有文件，需要重新load下一页数据这种场景，）
                        processDeleteBean(selectFiles)
                        activity.onRefreshDataAfterFileOperation(DELETE_OPERATION, selectFiles)
                    }
                }
            }
        })
        return true
    }

    private fun processDeleteBean(deletedFileBean: List<BaseFileBean>) {
        val oldBaseUiModel = uiState.value as BaseUiModel<BaseFileBean>
        Log.i(TAG, "processDeleteBean deletedFileBean $deletedFileBean, oldBaseUiModel $oldBaseUiModel")
        val oldFileList = mutableListOf<BaseFileBean>()
        oldFileList.addAll(oldBaseUiModel.fileList)
        oldFileList.removeIf {
            deletedFileBean.contains(it)
        }
        oldBaseUiModel.fileList = oldFileList
        //这里刷新UI
        uiState.value = oldBaseUiModel
        Log.i(TAG, "processDeleteBean deletedFileBean $deletedFileBean, oldFileList $oldFileList, oldBaseUiModel.fileList ${oldBaseUiModel.fileList}")
    }


    /**
     * 将选中的文件进行分类：分为本地文件，腾讯文达，金山文档
     * @param selectFiles 选中的所有文件
     * @param localFiles 本地文件
     * @param tencentFiles 腾讯文档
     * @param kDocsFiles 金山文档
     */
    private fun classifySelectFiles(
        selectFiles: List<BaseFileBean>,
        localFiles: MutableList<BaseFileBean>,
        dfsFiles: MutableList<SearchDFMFileWrapper>,
        tencentFiles: MutableList<DriveFileWrapper>,
        kDocsFiles: MutableList<DriveFileWrapper>
    ) {
        selectFiles.forEach {
            if (NewFunctionSwitch.isSupportDfmSearch && it is SearchDFMFileWrapper) {
                dfsFiles.add(it)
            } else if (it is SearchLabelWrapper || it is UriFileWrapper) {
                localFiles.add(it)
            } else if (it is DriveFileWrapper) {
                if (it.isTencentDocs()) {
                    tencentFiles.add(it)
                } else if (it.isKDocs()) {
                    kDocsFiles.add(it)
                }
            }
        }
    }

    /**
     * 删除相同类型的云文档
     */
    private fun deleteSameCloudFiles(activity: ComponentActivity, selectFiles: List<BaseFileBean>) {
        val cloudFiles = selectFiles.map { it as DriveFileWrapper }
        val fileCloudBrowserApi = Injector.injectFactory<IFileCloudBrowser>()
        fileCloudBrowserApi?.deleteSameCategoryCloudFile(activity, cloudFiles) { code ->
            if (code == ACTION_DONE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                if (activity is BaseVMActivity) {
                    //这里删除文件之后，需要触发loader重新load（云文档分页加载时，如果被删除所有文件，需要重新load下一页数据这种场景，）
                    processDeleteBean(cloudFiles)
                    activity.onRefreshDataAfterFileOperation(DELETE_OPERATION, cloudFiles)
                }
            }
        }
    }

    /**
     * 检查是否是全选
     */
    private fun checkSelectAll(): Boolean {
        val selectFileSize = getSelectItems().size
        val allItemSize = uiState.value?.fileList?.size ?: 0
        val selectAll = selectFileSize == allItemSize
        Log.d(TAG, "checkSelectAll $selectAll")
        return selectAll
    }

    /**
     * 删除跨设备文件的弹窗以及相应的删除操作
     */
    private fun deleteDfmFiles(activity: ComponentActivity, selectFiles: List<BaseFileBean>, isSelectAll: Boolean) {
        val recycleBinAction = Injector.injectFactory<IRecycleBin>()
        val action = recycleBinAction?.getFileActionDelete(
                activity,
                selectFiles,
                isSelectAll,
                CategoryHelper.CATEGORY_DFM,
            )
        val listener = FileOperatorListenerImpl(this)
        action?.let {
            StatisticsUtils.onCommon(activity, StatisticsUtils.DELETE_MENU_PRESSED)
            OptimizeStatisticsUtil.onDelete(OptimizeStatisticsUtil.getOptionPage(activity, ""), selectFiles, true)
            it.execute(object : IFileActionObserver {
                override fun onActionDone(result: Boolean, data: Any?) {
                    //这里使用listenner是触发重新load刷新数据
                    Log.d(TAG, "deleteDfmFiles actionDone result $result")
                    listener.onActionDone(IFileOperate.OP_DELETE_TO_RECYCLE, result, data)
                    val info = OptimizeStatisticsUtil.OperationInfo(
                        selectFiles.size.toString(),
                        OptimizeStatisticsUtil.OP_DELETE,
                        Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                        "",
                        selectFiles
                    )
                    OptimizeStatisticsUtil.allOperation(info)
                }

                override fun onActionCancelled() {
                    Log.d(TAG, "deleteDfmFiles actionCanceled")
                    listener.onActionCancelled(IFileOperate.OP_DELETE_TO_RECYCLE)
                }

                override fun onActionReloadData() {
                    Log.d(TAG, "deleteDfmFiles actionReload")
                    listener.onActionReloadData(IFileOperate.OP_DELETE_TO_RECYCLE)
                }

                override fun onActionReShowDialog() {
                }

                override fun isShowDialog(): Boolean {
                    return false
                }
            })
        }
    }
}