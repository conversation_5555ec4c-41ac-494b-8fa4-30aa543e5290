/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchResultSubList
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/8/3      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.bean

import android.util.Log
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.category.globalsearch.util.Util
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain

class SearchResultSubList {

    companion object {
        const val TAG = "SearchResultSubList"
    }

    //全部tab页的数据, 包含标签列表labelFiles + 文件夹列表folderFiles
    var allTableFiles = ArrayList<BaseFileBean>()
    var labelFiles = ArrayList<SearchLabelWrapper>()
    var folderFiles = ArrayList<BaseFileBean>()
    //新增全部tab页数据，包含标签列表labelFiles + 文件夹列表folderFiles，忽略超过5个只去前五个的逻辑
    var allTableAllFiles = ArrayList<BaseFileBean>()

    var imageFiles = ArrayList<BaseFileBean>()
    var videoFiles = ArrayList<BaseFileBean>()
    var audioFiles = ArrayList<BaseFileBean>()
    var docFiles = ArrayList<BaseFileBean>()
    var appFiles = ArrayList<BaseFileBean>()
    var compressFiles = ArrayList<BaseFileBean>()
    var otherFiles = ArrayList<BaseFileBean>()


    private val mTabCategory = arrayListOf(
        GlobalSearchActivity.TAB_ALL,
        CategoryHelper.CATEGORY_IMAGE,
        CategoryHelper.CATEGORY_VIDEO,
        CategoryHelper.CATEGORY_AUDIO,
        CategoryHelper.CATEGORY_DOC,
        CategoryHelper.CATEGORY_APK,
        CategoryHelper.CATEGORY_COMPRESS
    )

    fun parseSubItemList(inputItems: List<BaseFileBean>) {
        inputItems.forEach { item ->
            SortHelper.classifyFileByMimeType(
                item, folderFiles, imageFiles,
                videoFiles, audioFiles, docFiles,
                appFiles, compressFiles, otherFiles
            )
        }
        Injector.injectFactory<IDocumentExtensionType>()
            ?.classifyOtherFileByMimeType(docFiles, otherFiles)
    }


    fun getSubListByCategoryType(category: Int): List<BaseFileBean> {
        val result = when (category) {
            GlobalSearchActivity.TAB_ALL -> allTableFiles
            CategoryHelper.CATEGORY_IMAGE -> imageFiles
            CategoryHelper.CATEGORY_VIDEO -> videoFiles
            CategoryHelper.CATEGORY_AUDIO -> audioFiles
            CategoryHelper.CATEGORY_DOC -> docFiles
            CategoryHelper.CATEGORY_APK -> appFiles
            CategoryHelper.CATEGORY_COMPRESS -> compressFiles
            else -> allTableFiles
        }
        Log.i(TAG, "getSubListByCategoryType category $category, result ${result.size}")
        return result
    }

    private fun sortSubItemList() {
        SortHelper.sortByNameWhenSameTime(folderFiles)
        SortHelper.sortByNameWhenSameTime(imageFiles)
        SortHelper.sortByNameWhenSameTime(videoFiles)
        SortHelper.sortByNameWhenSameTime(audioFiles)
        SortHelper.sortByNameWhenSameTime(docFiles)
        SortHelper.sortByNameWhenSameTime(appFiles)
        SortHelper.sortByNameWhenSameTime(compressFiles)
        SortHelper.sortByNameWhenSameTime(otherFiles)
    }

    fun sortAndMergeSubItemList(sortByTime: Boolean = true): List<BaseFileBean> {
        if (sortByTime) {
            sortSubItemList()
        }
        val mergeResult = ArrayList<BaseFileBean>()
        mergeResult.addAll(folderFiles)
        mergeResult.addAll(imageFiles)
        mergeResult.addAll(videoFiles)
        mergeResult.addAll(audioFiles)
        mergeResult.addAll(docFiles)
        mergeResult.addAll(appFiles)
        mergeResult.addAll(compressFiles)
        mergeResult.addAll(otherFiles)
        return mergeResult
    }

    fun mergeNewResult(inputResult: SearchResultSubList?) {
        if (inputResult == null) {
            Log.i(TAG, "mergeNewResult input NULL return")
            return
        }
        mTabCategory.forEach { category ->
            val categoryList = getSubListByCategoryType(category).toMutableList()
            val inputCategoryList = inputResult.getSubListByCategoryType(category).toMutableList()
            if (category == GlobalSearchActivity.TAB_ALL || category == CategoryHelper.CATEGORY_DOC) {
                Log.i(TAG, "mergeNewResult start category $category, old size ${categoryList.size}, add size ${inputCategoryList.size}")
            }
            val distinctList = ArrayList(getDistinctList(categoryList, inputCategoryList))
            when (category) {
                GlobalSearchActivity.TAB_ALL -> allTableFiles = distinctList
                CategoryHelper.CATEGORY_IMAGE -> imageFiles = distinctList
                CategoryHelper.CATEGORY_VIDEO -> videoFiles = distinctList
                CategoryHelper.CATEGORY_AUDIO -> audioFiles = distinctList
                CategoryHelper.CATEGORY_DOC -> docFiles = distinctList
                CategoryHelper.CATEGORY_APK -> appFiles = distinctList
                CategoryHelper.CATEGORY_COMPRESS -> compressFiles = distinctList
                else -> allTableFiles = distinctList
            }
            if (category == GlobalSearchActivity.TAB_ALL || category == CategoryHelper.CATEGORY_DOC) {
                Log.i(TAG, "mergeNewResult end category $category, old categoryList size ${distinctList.size}")
            }
        }
    }

    private fun getDistinctList(
        oldList: List<BaseFileBean>,
        newList: List<BaseFileBean>
    ): List<BaseFileBean> {
        //老的数据中的itemKey形成的set
        val oldKeySet = oldList.map { Util.getItemKey(it) ?: 0 }.toSet()
        //来自newList中getItemKey跟老的list中的getItemKey相同的数据
        val newCommonList = newList.filter { oldKeySet.contains(Util.getItemKey(it) ?: 0) }
        //转换为map
        val newCommonKeySet = newCommonList.associateBy { Util.getItemKey(it) ?: 0 }
        //来自newList中的多余的数据
        val newDiffList = newList.minus(newCommonList.toSet())
        val result = mutableListOf<BaseFileBean>()
        result.addAll(oldList)
        if (newDiffList.isNotEmpty()) {
            result.addAll(newDiffList)
        }
        //使用new列表中替换之前old列表中新的列表中itemKey相同的数据
        if (newCommonKeySet.isNotEmpty()) {
            result.forEachIndexed { index, baseFileBean ->
                val resultKey = Util.getItemKey(baseFileBean) ?: 0
                val commonKeyItem = newCommonKeySet[resultKey]
                if (newCommonKeySet.containsKey(resultKey) && commonKeyItem != null) {
                    result[index] = commonKeyItem
                }
            }
        }
        return result
    }


    fun processFileLabelIfHad(iMan: IMain) {
        iMan.findFileLabelIfHad(allTableFiles)
        iMan.findFileLabelIfHad(folderFiles)
        iMan.findFileLabelIfHad(imageFiles)
        iMan.findFileLabelIfHad(videoFiles)
        iMan.findFileLabelIfHad(audioFiles)
        iMan.findFileLabelIfHad(docFiles)
        iMan.findFileLabelIfHad(appFiles)
        iMan.findFileLabelIfHad(compressFiles)
        iMan.findFileLabelIfHad(otherFiles)
    }

    fun sortLabels() {
        Util.sortLabels(labelFiles)
    }
}