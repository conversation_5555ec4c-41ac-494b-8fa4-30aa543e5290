/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MixFileDeleteObserver
 ** Description : 混合文件删除的Observer
 ** Version     : 1.0
 ** Date        : 2024/05/14 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.operate

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.URLSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.core.text.HtmlCompat
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.statement.COUILinkMovementMethod
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.oplus.filemanager.interfaze.feedback.IFeedback

const val SHOW_DELETE_CONFIRM_DIALOG = 0

open class MixFileDeleteObserver(val activity: Activity) : BaseFileActionObserver(activity) {

    companion object {
        private const val TAG = "MixFileDeleteObserver"
    }

    private var deleteConfirmDialog: Dialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        Log.d(TAG, "onChanged $result")
        when (result.first) {
            SHOW_DELETE_CONFIRM_DIALOG -> {
                if (result.second is FileDeleteBean) {
                    showDeleteConfirmDialog(activity, result.second as FileDeleteBean)
                }
            }

            ACTION_FAILED -> {
                dismissDialog()
                // 删除失败的提示
                CustomToast.showShort(com.filemanager.common.R.string.delete_docs_failure)
                return false
            }

            ACTION_DONE, ACTION_CANCELLED -> {
                dismissDialog()
                return false
            }

            else -> return false
        }
        return true
    }

    /**
     * 显示删除确认弹窗
     */
    @VisibleForTesting
    fun showDeleteConfirmDialog(context: Activity, deleteBean: FileDeleteBean) {
        Log.d(TAG, "showDeleteConfirmDialog $deleteBean")
        deleteConfirmDialog = COUIAlertDialogBuilder(context)
            .setCancelable(false)
            .setOnCancelListener { deleteBean.onClickListener }
            .setTitle(getDeleteDialogTitle(context, deleteBean.count))
            .setMessage(getDeleteDialogMessage(context, deleteBean))
            .setNeutralButton(com.filemanager.common.R.string.menu_file_list_delete, deleteBean.onClickListener)
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, deleteBean.onClickListener)
            .show()
        val msgTv = deleteConfirmDialog?.findViewById<TextView>(android.R.id.message)
        msgTv?.movementMethod = COUILinkMovementMethod
    }

    /**
     * 获取删除弹窗的标题
     */
    private fun getDeleteDialogTitle(context: Context, count: Int): String {
        return if (count == 1) {
            context.getString(com.filemanager.common.R.string.delete_docs_confirm_title)
        } else {
            context.resources.getQuantityString(
                com.filemanager.common.R.plurals.delete_several_docs_confirm_title,
                count,
                count
            )
        }
    }

    /**
     * 获取删除弹窗的message
     */
    private fun getDeleteDialogMessage(context: Activity, deleteBean: FileDeleteBean): CharSequence {
        val count = deleteBean.count
        if (FeatureCompat.sIsExpRom) {
            val content = context.resources.getQuantityString(com.filemanager.common.R.plurals.delete_several_file_confirm_msg_exp, count, count)
            return content
        } else {
            val content = context.resources.getQuantityString(com.filemanager.common.R.plurals.delete_several_file_confirm_msg, count, count)
            val span = HtmlCompat.fromHtml(content, HtmlCompat.FROM_HTML_MODE_LEGACY, null, null) as SpannableStringBuilder
            val urlSpans = span.getSpans(0, span.length, URLSpan::class.java)
            urlSpans.forEach {
                val start = span.getSpanStart(it)
                val end = span.getSpanEnd(it)
                val flags = span.getSpanFlags(it)
                val clickSpan = object : COUIClickableSpan(context) {
                    override fun onClick(widget: View) {
                        super.onClick(widget)
                        Log.d(TAG, "click FAQ")
                        val feedbackApi = Injector.injectFactory<IFeedback>()
                        feedbackApi?.launchFeedBack(context)
                    }
                }
                span.removeSpan(it)
                span.setSpan(clickSpan, start, end, flags)
            }
            return span
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || deleteConfirmDialog?.isShowing ?: false
    }

    override fun recycle() {
        dismissDialog()
        super.recycle()
    }

    @VisibleForTesting
    fun dismissDialog() {
        if (deleteConfirmDialog?.isShowing == true) {
            deleteConfirmDialog?.dismiss()
        }
    }

    data class FileDeleteBean(var count: Int, var onClickListener: DialogInterface.OnClickListener?)
}