/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MixFileDeleteAction
 ** Description : 混合文件删除的Action ：区分为2种情况：1. 云文档和本地文档混合；2.只包含云文档，但是同时包含腾讯文档和金山文档
 ** Version     : 1.0
 ** Date        : 2024/05/14 14:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.operate

import android.content.DialogInterface
import androidx.activity.ComponentActivity
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.stringResource
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.oplus.filemanager.category.globalsearch.bean.SearchDFMFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class MixFileDeleteAction(val activity: ComponentActivity, val selectFiles: List<BaseFileBean>) :
    BaseFileAction<MixFileDeleteObserver>(activity) {

    companion object {
        private const val TAG = "MixFileDeleteAction"
        private const val DELAY_INTERNAL = 200L
    }

    @VisibleForTesting
    val lockObj = Object()

    @VisibleForTesting
    var fileDeleteBean: MixFileDeleteObserver.FileDeleteBean? = null

    private var localFiles = mutableListOf<BaseFileBean>()
    private var dfmFiles = mutableListOf<BaseFileBean>()
    private var cloudFiles = mutableListOf<DriveFileWrapper>()

    @VisibleForTesting
    fun initFileDeleteBean(count: Int) {
        fileDeleteBean = MixFileDeleteObserver.FileDeleteBean(count) { _, which ->
            Log.d(TAG, "onDialogClick $which")
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> notifyLockReleased()
                else -> cancel()
            }
        }
    }

    override fun onCancelled() {
        notifyLockReleased()
        super.onCancelled()
    }

    @VisibleForTesting
    fun notifyLockReleased() {
        Log.d(TAG, "notifyLockReleased")
        synchronized(lockObj) {
            lockObj.notify()
        }
    }

    override fun beforeRun() {
        super.beforeRun()
        classifySelectFiles()
    }

    /**
     * 将选中的文件进行分类：分为本地文件，云文档
     */
    private fun classifySelectFiles() {
        selectFiles.forEach {
            if (it is SearchDFMFileWrapper) {
                dfmFiles.add(it)
            } else if (it is SearchLabelWrapper || it is UriFileWrapper) {
                localFiles.add(it)
            } else if (it is DriveFileWrapper) {
                cloudFiles.add(it)
            }
        }
    }

    override fun run(): Boolean {
        initFileDeleteBean(selectFiles.size)
        notifyObserver(SHOW_DELETE_CONFIRM_DIALOG, fileDeleteBean)
        while (!isCancelled()) {
            try {
                synchronized(lockObj) {
                    lockObj.wait()
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "run interrupted", e)
                return false
            }
            val canceled = isCancelled()
            Log.d(TAG, "Continue to execute: isCancelled=$canceled")
            if (canceled) {
                return false
            }
            return reallyExecuteAction(selectFiles)
        }
        return false
    }


    @VisibleForTesting
    fun reallyExecuteAction(files: List<BaseFileBean>): Boolean {
        Log.d(TAG, "reallyExecuteAction")
        // 显示loading 弹窗
        showProgressDialog()
        val count = files.size
        val result = runBlocking(Dispatchers.IO) {
            // 删除本地
            val delLocalSize = withContext(Dispatchers.Main) {
                directDeleteLocalFiles()
            }
            // 删除云文件
            val delCloudSize = directDeleteCloudFiles()
            // 删除dfm文件
            val delDfmSize = withContext(Dispatchers.Main) {
                directDeleteDfmFiles()
            }
            Log.w(TAG, "reallyExecuteAction result-> all:$count local:$delLocalSize cloud:$delCloudSize, dfm:$delDfmSize")
            count == delLocalSize + delCloudSize + delDfmSize
        }
        // 取消loading弹窗
        cancelShowProgressDialog()
        return result
    }

    /**
     * 直接删除本地文档
     */
    @MainThread
    private fun directDeleteLocalFiles(): Int {
        val recyclerBinApi = Injector.injectFactory<IRecycleBin>()
        val action = recyclerBinApi?.getFileActionDelete(activity, localFiles, false, CategoryHelper.CATEGORY_SEARCH, bgDelete = true)
        action?.execute(object : BaseFileActionObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "directDeleteLocalFiles result:$result data:$data")
            }
        })
        return localFiles.size
    }


    /**
     * 直接DFM跨端文档，这里是后台删除，不弹窗
     */
    @MainThread
    private fun directDeleteDfmFiles(): Int {
        val recyclerBinApi = Injector.injectFactory<IRecycleBin>()
        val action = recyclerBinApi?.getFileActionDelete(activity, dfmFiles, false, CategoryHelper.CATEGORY_DFM, bgDelete = true)
        action?.execute(object : BaseFileActionObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "directDeleteLocalFiles result:$result data:$data")
            }
        })
        return dfmFiles.size
    }

    /**
     * 直接删除云文档
     */
    private fun directDeleteCloudFiles(): Int {
        val fileCloudBrowserApi = Injector.injectFactory<IFileCloudBrowser>()
        return fileCloudBrowserApi?.deleteCloudFiles(cloudFiles) ?: 0
    }

    /**
     * 显示loading弹窗
     */
    @VisibleForTesting
    fun showProgressDialog() {
        notifyObserver(
            SHOW_PROGRESS,
            BaseFileActionObserver.ProgressDialogBean(stringResource(com.filemanager.common.R.string.dialog_deleting), true, 0),
            DELAY_INTERNAL
        )
    }

    /**
     * 取消显示删除进度弹窗
     */
    @VisibleForTesting
    fun cancelShowProgressDialog() {
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)
    }

    override fun afterRun(result: Boolean) {
        super.afterRun(result)
        if (result) {
            notifyObserver(ACTION_DONE)
        } else {
            notifyObserver(ACTION_FAILED)
        }
    }
}