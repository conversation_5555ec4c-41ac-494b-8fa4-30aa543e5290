<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.ViewPagerWrapperForPC
        android:id="@+id/view_pager_wrapper"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.coui.appcompat.viewpager.COUIViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:visibility="gone"
            />
    </com.filemanager.common.view.ViewPagerWrapperForPC>

    <include layout="@layout/appbar_with_search_layout_secondary" />

    <com.filemanager.common.view.NavigationView
        android:id="@+id/navigation_tool"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:visibility="gone"
        app:navigationType="tool" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>