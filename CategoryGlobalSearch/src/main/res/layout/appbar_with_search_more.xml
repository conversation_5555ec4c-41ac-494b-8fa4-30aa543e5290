<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.COUIDividerAppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorBackgroundWithCard"
    android:orientation="vertical"
    app:hasDivider="false"
    app:elevation="0dp">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        style="@style/COUIToolBarInAppBarLayoutStyle"
        app:supportContentInsetEnd="0dp"
        app:supportContentInsetStart="0dp" />

</com.google.android.material.appbar.COUIDividerAppBarLayout>