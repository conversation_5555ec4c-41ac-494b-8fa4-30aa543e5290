/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.apk
 * * Version     : 1.0
 * * Date        : 2020/5/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.apk.ui

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.SubPageAdMgr
import com.oplus.filemanager.category.apk.R
import com.oplus.filemanager.category.apk.adapter.CategoryApkAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier

class ApkFragment : RecyclerSelectionVMFragment<ApkViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener {
    companion object {
        private const val TAG = "ApkFragment"
    }
    private var sortEntryView: SortEntryView? = null
    private var mTabPosition = ApkParentFragment.APK_TAB_UN_INSTALL
    private var mTempSortType = -1
    private var mUri: Uri? = null
    private var mSql: String? = null
    private var mIsNeedFilter = false
    private var mTitle: String? = null
    private var mAdapter: CategoryApkAdapter? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mApkParentViewModel: ApkParentViewModel? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_APK)
    }
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    internal var mFileOperateController: NormalFileOperateController? = null
        private set
    private var mLoadingController: LoadingController? = null
    private var mAdEntryName: String? = null
    private var mTabActivityListener: TabActivityListener<MediaFileWrapper>? = null
    private var mIsAllowedInstalledPermission = PreferencesUtils.getBoolean(
        key = InstalledPermissionCallback.LAST_INSTALLED_APPS_PERMISSION,
        default = true
    )
    private var hasFoundMoreApp = false
    private var foundMoreView: View? = null
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var hasShowEmpty: Boolean = false
    private var tempSortDesc = -1

    /**
     * onResumeLoadData的已执行次数
     */
    private var loadTimes = 0

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.apk_list_fragment
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_APK
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            val uri = bundle.getString(KtConstants.P_URI)
            mSql = bundle.getString(KtConstants.P_SQL)
            mTempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            mIsNeedFilter = bundle.getBoolean(KtConstants.P_IS_NEED_FILTER)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            mTabPosition = bundle.getInt(KtConstants.P_TAB_POSITION)
            hasFoundMoreApp = bundle.getBoolean(KtConstants.P_HAS_FOUND_MORE_APP, false)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
            if ((null == uri) || (null == mSql)) {
                return
            }
            mUri = Uri.parse(uri)
            mAdapter = CategoryApkAdapter(
                it,
                mTabPosition == ApkParentFragment.APK_TAB_UN_INSTALL,
                hasFoundMoreApp,
                <EMAIL>,
                getApkParentFragment()?.mAdManager
            ).apply {
                setHasStableIds(true)
            }
        }
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.root_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentRecyclerView?.setOnGenericMotionListener(PCConsumeOnGenericMotionListener())
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        sortEntryView = getApkParentFragment()?.sortEntryView
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_APK))
        if (mTempSortType != -1) {
            sortEntryView?.setSortOrder(mTempSortType, tempSortDesc == 0)
        } else {
            sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_APK))
        }
    }

    fun setToolbarNew(toolbarParam: COUIToolbar?, title: String) {
        toolbar = toolbarParam
        mTitle = title
    }

    private fun isCurrentFragment(): Boolean {
        val fragment = getApkParentFragment() ?: return false
        return fragment.getCurrentTabPosition() == mTabPosition
    }

    private fun getApkParentFragment(): ApkParentFragment? {
        return parentFragment as? ApkParentFragment
    }

    override fun createViewModel(): ApkViewModel {
        val vm = ViewModelProvider(parentFragment ?: (baseVMActivity ?: this))[mTabPosition.toString(), ApkViewModel::class.java]
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_APK, vm).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let {
            it.addItemDecoration(mSpacesItemDecoration)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.layoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType: Int? = mAdapter?.getItemViewType(position)
                        return if (isTheGridViewType(viewType)) spanCount else 1
                    }
                }
            }
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { ad ->
                fragmentRecyclerView!!.adapter = ad
            }
        }
        setRecyclerMargin(mIsAllowedInstalledPermission)
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun isTheGridViewType(viewType: Int?): Boolean {
        return ((viewType == BaseFileBean.TYPE_FILE_LIST_HEADER) || (viewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)
                || (viewType == BaseFileBean.TYPE_LABEL_FILE) || (viewType == BaseFileBean.TYPE_FILE_AD))
    }

    override fun startObserve() {
        baseVMActivity?.apply { mApkParentViewModel =
            ViewModelProvider(this)[ApkParentViewModel::class.java]
        }
        fragmentRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
                startScrollToPositionObserver()
                startScanModeObserver()
                startObserveLoadState()
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    val isNotEmpty = (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                    Log.d(TAG, "isNotEmpty $isNotEmpty")
                    if (isNotEmpty) {
                        hasShowEmpty = false
                        mFileEmptyController.hideFileEmptyView()
                    }
                    isNotEmpty
                }
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun startScrollToPositionObserver() {
        fragmentViewModel?.mPositionModel?.observe(this) { positionModel ->
            positionModel?.let {
                (fragmentRecyclerView?.layoutManager as GridLayoutManager).scrollToPosition(it)
            }
        }
    }

    private fun startListSelectModeObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.mModeState.initState || !isCurrentFragment()) {
                    toolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    return
                }
                Log.d(TAG, "startListSelectModeObserver: mListModel=$value")
                val selectModel = (value == KtConstants.LIST_SELECTED_MODE)
                mAdapter?.setSelectEnabled(selectModel)
                if (selectModel) {
                    getApkParentFragment()?.disableViewPager()
                    (parentFragment as? ApkParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                } else {
                    (parentFragment as? ApkParentFragment)?.previewClickedFile(
                        fragmentViewModel?.previewClickedFileLiveData?.value,
                        fragmentViewModel?.previewClickedFileLiveData
                    )
                }
                fragmentRecyclerView?.let {
                    val bottom = if (selectModel) {
                        val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                        KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                    } else {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    }
                    it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, bottom)
                    fragmentFastScroller?.apply { trackMarginBottom = bottom }
                }
                (baseVMActivity as? NavigationInterface)?.let {
                    if (selectModel) {
                        it.showNavigation()
                        viewModule.setNavigateItemAble(it)
                    } else {
                        it.hideNavigation()
                    }
                }
                toolbar?.let {
                    changeActionModeAnim(it, {
                        if (selectModel) {
                            mTabActivityListener?.initToolbarSelectedMode(
                                true,
                                viewModule.getRealFileSize(),
                                viewModule.uiState.value?.selectedList?.size ?: 0,
                                viewModule.getSelectItems())
                        } else {
                            mTabActivityListener?.apply {
                                val empty = (viewModule.getApkDataSize() == 0)
                                initToolbarNormalMode(true, empty)
                                refreshScanModeItemIcon()
                            }
                        }
                    }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                    it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                }
            }
        })
    }

    private fun startUIDataStateObserver() {
        fragmentViewModel?.uiState!!.observe(this) { uiModel ->
            val apkSize = fragmentViewModel!!.getApkDataSize()
            Log.d( TAG, "startUIDataStateObserver apkSize=$apkSize, selectSize="
                    + uiModel.selectedList.size + ", keyWord=${uiModel.keyWord} ")
            updateActivityApkCount(uiModel.fileList)
            if (apkSize == 0) {
                showEmptyView()
                sortEntryView?.visibility = View.GONE
            } else {
                hasShowEmpty = false
                mFileEmptyController.hideFileEmptyView()
                sortEntryView?.visibility = View.VISIBLE
            }
            val selectModel = (uiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE)
            if (selectModel) {
                mTabActivityListener?.initToolbarSelectedMode(
                    false,
                    fragmentViewModel!!.getRealFileSize(),
                    uiModel.selectedList.size,
                    fragmentViewModel!!.getSelectItems())
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    mAdapter?.setData(uiModel.fileList as ArrayList<MediaFileWrapper>,
                        uiModel.selectedList
                    )
                    getApkParentFragment()?.mAdManager?.refreshIfListChanged()
                    (parentFragment as? ApkParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                }
            } else {
                (parentFragment as? ApkParentFragment)?.previewClickedFile(
                    fragmentViewModel?.previewClickedFileLiveData?.value,
                    fragmentViewModel?.previewClickedFileLiveData
                )
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    mAdapter?.let {
                        it.setKeyWord(uiModel.keyWord)
                        it.setData(
                            uiModel.fileList as ArrayList<MediaFileWrapper>,
                            uiModel.selectedList
                        )
                        startRequestAd(uiModel.fileList as ArrayList<MediaFileWrapper>)
                    }
                }
                mTabActivityListener?.apply {
                    initToolbarNormalMode(false, apkSize == 0)
                    refreshScanModeItemIcon()
                }
                fragmentRecyclerView?.layoutParams?.height = if (apkSize == 0) {
                    ViewGroup.LayoutParams.WRAP_CONTENT
                } else {
                    ViewGroup.LayoutParams.MATCH_PARENT
                }
            }
        }
        fragmentViewModel?.previewClickedFileLiveData?.observe(this) {
            mAdapter?.setPreviewClickedFile(it)
        }
    }

    @SuppressLint("InflateParams")
    private fun createFoundMoreAppView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.category_apk_footer_view, null)
        val tv = view.findViewById<TextView>(R.id.head_tv_find_more_apps)
        tv.setOnClickListener {
            val oapsAction = Injector.injectFactory<IOapsLib>()
            oapsAction?.openAppStoreDetail(it.context, mTabPosition == ApkParentFragment.APK_TAB_UN_INSTALL)
        }
        return view
    }

    private fun startScanModeObserver() {
        mApkParentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            updateScanModeView(scanMode)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = mApkParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    fun updateLeftRightMargin() {
        val scanMode = mApkParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    fun getFileList(): List<MediaFileWrapper>? {
        return fragmentViewModel?.uiState?.value?.fileList
    }

    fun getSelectItems(): List<MediaFileWrapper>? {
        return fragmentViewModel?.getSelectItems()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode, ItemDecorationFactory.GRID_ITEM_DECORATION_APK)
        (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            notifyDataSetChanged()
            getApkParentFragment()?.mAdManager?.refreshByScanModeChanged()
        }
    }

    private fun updateScanModeView(scanMode: Int) {
        fragmentViewModel?.mScanModeValue = scanMode
        toolbar?.let {
            val needSkipAnimation = mNeedSkipAnimation
            if (needSkipAnimation) {
                refreshScanModeAdapter(scanMode)
            } else {
                fragmentRecyclerView?.let { recyclerView ->
                    recyclerView.mTouchable = false
                    recyclerView.stopScroll()
                }
                mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                    override fun onSpanChangeCallback() {
                        (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.scrollToPosition(0)
                        refreshScanModeAdapter(scanMode)
                    }
                }, object : OnAnimatorEndListener {
                    override fun onAnimatorEnd() {
                        fragmentRecyclerView?.mTouchable = true
                    }
                })
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        // 首次进mApkDataList对象是空，这个时候不能认为apkSize是0，因为数据还没有回调，list对象是空
        val tempEmpty = fragmentViewModel?.mApkDataList != null && fragmentViewModel?.getApkDataSize() == 0
        Log.d(TAG, "onResume tempEmpty:$tempEmpty")
        if (tempEmpty && isShowPermissionEmptyView.not()) {
            mLoadingController?.dismissLoading(true)
            showEmptyView()
        }
        if (mApkParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            if (hasFoundMoreApp && foundMoreView == null) {
                foundMoreView = createFoundMoreAppView()
                foundMoreView?.let {
                    mFileEmptyController.addExtraView(it)
                }
            }
            (parentFragment as? ApkParentFragment)?.listEmptyFile()
        }
        //only normal mode need bring recyclerView to front,because of softmarket adv may be add to recyclerView in ExpRom
        fragmentFastScroller?.bringToFront()
        Log.d(TAG, "showEmptyView")
    }

    override fun onResumeLoadData() {
        loadTimes += 1
        if (!isAdded) {
            return
        }
        if (Utils.isQuickClick() && loadTimes != 1) {
            if ((mAdapter?.itemCount ?: 0) > 0) {
                if (sortEntryView?.visibility == View.GONE) {
                    sortEntryView?.visibility = View.VISIBLE
                    sortEntryView?.setFileCount(mAdapter?.itemCount ?: 0)
                }
            } else {
                sortEntryView?.visibility = View.GONE
            }
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            return
        }
        fragmentViewModel?.let {
            val allowedGetInstalledApps = PermissionUtils.hasGetInstalledAppsPermission()
            if (mIsAllowedInstalledPermission != allowedGetInstalledApps) {
                mIsAllowedInstalledPermission = allowedGetInstalledApps
                //已经初始化后的，不再初始化标题栏及隐藏底部文件操作栏。否则在选择状态，切换权限后返回，会切换标题栏及底部文件操作栏的状态
                if (it.uiState.value?.stateModel?.initState != true) {
                    mTabActivityListener?.initToolbarNormalMode(true, empty = false)
                    (baseVMActivity as? NavigationInterface)?.hideNavigation()
                }
                setRecyclerMargin(allowedGetInstalledApps)
            }
            it.initLoader(LoaderViewModel.getLoaderController(baseVMActivity ?: this), mUri, mSql, mTabPosition, mIsNeedFilter)
            it.setSort(mTempSortType, tempSortDesc)
        }
    }

    override fun pressBack(): Boolean {
        return fragmentViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_APK)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.APK)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_APK)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.APK)
                true
            }
            R.id.navigation_sort -> {
                baseVMActivity?.let {
                    StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                    OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.APK)
                    val bundle = Bundle()
                    bundle.putInt(Constants.TEMP_SORT_TYPE, mTempSortType)
                    bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
                    bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_APK))
                    val anchorView: View? = parentFragment?.view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                    mSortPopupController.showSortPopUp(
                        it,
                        anchorView,
                        bundle,
                        object : SelectItemListener {

                            override fun onDismiss() {
                                sortEntryView?.rotateArrow()
                            }

                            override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                if (flag) {
                                    sortEntryView?.setSortOrder(sortMode, isDesc)
                                    mViewModel?.setSort(-1, -1)
                                    fragmentViewModel?.loadData()
                                }
                            }
                        })
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                mTabActivityListener?.updateNeedSkipAnimation(true)
                mApkParentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.APK)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_APK)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    fun setTabActivityListener(tabListener: TabActivityListener<MediaFileWrapper>) {
        mTabActivityListener = tabListener
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, path) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = (parentFragment as? ApkParentFragment)?.previewClickedFile(
                    baseFile, fragmentViewModel?.previewClickedFileLiveData
                )
                if (previewResult != true) {
                    mFileOperateController?.onFileClick(it, baseFile, e)
                }
            }
        }
        return true
    }

    private fun startRequestAd(data: java.util.ArrayList<MediaFileWrapper>) {
        Log.i(TAG, "startRequestAd:  mTabPosition:$mTabPosition   $data")
        baseVMActivity?.let { activity ->
            getApkParentFragment()?.mAdManager?.let {
                val isHideAd = mTabPosition == ApkParentFragment.APK_TAB_INSTALL
                        && SubPageAdMgr.isHideApkInstallAd()
                //Oppo 外销，已安装TAB 不加载广告
                if (isHideAd) {
                    return
                }
                if (mAdEntryName.isNullOrEmpty()) {
                    mAdEntryName = it.makeName(TAG)
                    mAdapter?.mEntryName = mAdEntryName
                }
                it.requestSubAd(activity, mAdEntryName!!, mAdapter!!, data, false)
                it.requestCommonAd(activity, mAdapter!!, data)
            }
        }
    }

    private fun setRecyclerMargin(isHasTab: Boolean = true) {
        fragmentRecyclerView?.let {
            toolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    it.setPadding(
                        it.paddingLeft,
                        getRecyclerViewTopPadding(isHasTab),
                        it.paddingRight,
                        paddingBottom
                    )
                }
            }
        }
    }

    @SuppressLint("PrivateResource")
    private fun getRecyclerViewTopPadding(isHasTab: Boolean = true): Int {
        val sortEntryHeight = sortEntryView?.measuredHeight ?: 0
        sortEntryView?.updateLayoutParams<LinearLayout.LayoutParams> {
            if (isHasTab) {
                this.topMargin = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_12dp)
            } else {
                this.topMargin = 0
            }
        }
        val sortEntryMarginTop = sortEntryView?.marginTop ?: 0
        Log.i(TAG, "getRecyclerViewTopPadding sortView:$sortEntryHeight, sortEntryMarginTop:$sortEntryMarginTop")
        val dividerLineHeight = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.divider_background_height)
        return (toolbar?.measuredHeight ?: 0) + dividerLineHeight + sortEntryHeight + sortEntryMarginTop +
                isHasTab.let {
                    if (it) {
                        appContext.resources.getDimensionPixelSize(com.support.tablayout.R.dimen.tablayout_small_layout_height)
                    } else {
                        0
                    }
                }
    }

    private fun updateActivityApkCount(fileList: List<MediaFileWrapper>) {
        val count = fileList.size - AdvertManager.getAdViewCount(fileList as ArrayList<MediaFileWrapper>)
        sortEntryView?.setFileCount(count)
    }

    /**
     * 判断当前是否处于选中编辑模式
     */
    fun isInSelectMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() == true
    }

    override fun bringToFront(visible: Int) {
        //显示的时候在appbarlayout初始化后在执行 因为高度依赖appbarlayout
        if (View.GONE == visible) {
            super.bringToFront(visible)
        }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyViewPaddingForChild()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getInstallPerMission() {
        Log.d(TAG, "getInstallPerMission")
        (baseVMActivity as BaseVMActivity).checkGetInstalledAppsPermission()
    }

    fun exitSelectionMode() {
        if (isInSelectMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (mApkParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val slideWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            slideWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_APK
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    fun getFragmentViewModel(): ApkViewModel? {
        return fragmentViewModel
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }
}