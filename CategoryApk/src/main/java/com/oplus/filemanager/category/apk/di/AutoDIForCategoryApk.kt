/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryApk.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.category.apk.di

import com.oplus.filemanager.category.apk.CategoryApkApi
import com.oplus.filemanager.interfaze.categoryapk.ICategoryApkApi
import org.koin.dsl.module

object AutoDIForCategoryApk {

    val categoryApkApi = module {
        single<ICategoryApkApi>(createdAtStart = true) {
            CategoryApkApi
        }
    }
}