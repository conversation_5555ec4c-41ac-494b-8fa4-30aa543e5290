/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilePathScannerTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/5 16:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 ** <author>        <data>       <version>  <desc>
 ** ternence        2022/12/5       1.0      create
 *********************************************************************/
package com.oplus.fileservice.filelist.scanner

import org.junit.Assert
import org.junit.Test

class FilePathScannerTest {

    @Test
    fun testCreateFromPath() {
        val filePathScanner = FilePathScanner(PATH, 1, 1, 1)
        val dirResult = filePathScanner.createFromPath("", "", "")
        Assert.assertNotNull(dirResult)
    }

    @Test
    fun testGetPath() {
        val filePathScanner = FilePathScanner(PATH, 1, 1, 1)
        val result = filePathScanner.getPath()
        Assert.assertEquals(1, result.size)
        Assert.assertEquals(PATH, result[0])
    }

    @Test
    fun testGetVolume() {
        val filePathScanner = FilePathScanner(PATH, 1, 1, 1)
        val result = filePathScanner.getVolume()
        Assert.assertNull(result)
    }

    @Test
    fun testGetFilterList() {
        val filePathScanner = FilePathScanner(PATH, 1, 1, 1)
        val result = filePathScanner.getFilterList()
        Assert.assertNull(result)
    }

    companion object {
        private const val PATH = "/storage/emulated/0/Ternence"
    }
}