/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BasePathScannerTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/11/30 17:36
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/11/30       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BasePathScannerTest {
    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `should verify when pageData with getVolume null`() {
        val basePathScanner = object : BasePathScanner(1, 30) {
            override fun createFromPath(volume: String, parentPath: String, path: String): List<BaseFileBean>? {
                return mockk<List<BaseFileBean>>()
            }

            override fun getPath(): Array<String> {
                return mockk<Array<String>>()
            }

            override fun getVolume(): List<String>? {
                return null
            }

            override fun getFilterList(): List<Int>? {
                return null
            }
        }
        val imageFileWrappers = mutableListOf<BaseFileBean>()
        repeat(100) {
            imageFileWrappers.add(BaseFileBean())
        }

        val result = basePathScanner.pageData(imageFileWrappers)
        Assert.assertEquals(30, result.size)
    }
}