/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AudioFilesScannerTest
 * Description :
 * Version     : 1.0
 * Date        : 2022/11/30 15:54
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2022/11/30       1.0      create
 *********************************************************************/
package com.oplus.fileservice.filelist.scanner

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.wrapper.AudioFileWrapper
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AudioFilesScannerTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testPageData() {
        val nullAudioFilesScanner = AudioFilesScanner(1, 0, 1, 1)
        val nullResult = nullAudioFilesScanner.pageData(null)
        Assert.assertNull(nullResult)
        val zeroPageSizeAudioFilesScanner = AudioFilesScanner(pageNo = 1, pageSize = 0, 1, 1)
        val audioFileWrappers = mutableListOf<AudioFileWrapper>()
        repeat(100) {
            audioFileWrappers.add(AudioFileWrapper())
        }
        val result = zeroPageSizeAudioFilesScanner.pageData(audioFileWrappers)
        Assert.assertEquals(WebFileConstant.DEFAULT_PAGE_SIZE, result?.size)
        audioFileWrappers.add(AudioFileWrapper())
        val zeroPageNoAudioFilesScanner = AudioFilesScanner(pageNo = 0, pageSize = 0, 1, 1)
        val zeroPageNoResult = zeroPageNoAudioFilesScanner.pageData(audioFileWrappers)
        Assert.assertEquals(WebFileConstant.DEFAULT_PAGE_SIZE, zeroPageNoResult?.size)

        val largerPageNoAudioFilesScanner = AudioFilesScanner(pageNo = 4, pageSize = 0, 1, 1)
        val largerPageNoResult = largerPageNoAudioFilesScanner.pageData(audioFileWrappers)
        Assert.assertEquals(1, largerPageNoResult?.size)
    }

    @Test
    fun testConvertImageFileToWebFileBean() {
        val audioFilesScanner = AudioFilesScanner(1, 1, 1, 1)
        val imageFileWrapper = AudioFileWrapper()
        val imageFileWrappers = listOf(imageFileWrapper)
        imageFileWrapper.mDisplayName = DISPLAY_NAME
        mockkStatic(WebMimeTypeHelper::class)
        every { WebMimeTypeHelper.getFileTypeFromExtension(any()) } returns MIME_TYPE
        mockkStatic("com.oplus.fileservice.filelist.utils.DateModifyUtils")
        every { getDateAndTimeByDefaultFormat(any()) } returns LAST_MODIFY_DATE
        val result = audioFilesScanner.convertAudioFileToWebFileBean(imageFileWrappers)[0]
        Assert.assertEquals(LAST_MODIFY_DATE, result.mLastModifyDate)
        Assert.assertEquals(MIME_TYPE, result.mFileType)
        Assert.assertEquals(EXTENSION, result.mFileFormat)
        Assert.assertEquals(FILE_NAME, result.mFileName)
    }

    companion object {
        private const val DISPLAY_NAME = "Utopia.txt"
        private const val FILE_NAME = "Utopia"
        private const val EXTENSION = "txt"
        private const val MIME_TYPE = "txt"
        private const val LAST_MODIFY_DATE = "2022-11-30"
    }
}