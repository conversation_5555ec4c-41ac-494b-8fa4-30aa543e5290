/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AlbumFilesScannerTest
 * Description :
 * Version     : 1.0
 * Date        : 2022/11/30 15:25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2022/11/30       1.0      create
 *********************************************************************/
package com.oplus.fileservice.filelist.scanner

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.wrapper.ImageFileWrapper
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebMimeTypeHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AlbumFilesScannerTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testConvertImageFileToWebFileBean() {
        val albumFilesScanner = AlbumFilesScanner(1, 1, 1)
        val imageFileWrapper = ImageFileWrapper()
        imageFileWrapper.mDisplayName = DISPLAY_NAME
        mockkStatic(WebMimeTypeHelper::class)
        every { WebMimeTypeHelper.getFileTypeFromExtension(any()) } returns MIME_TYPE
        mockkStatic("com.oplus.fileservice.filelist.utils.DateModifyUtils")
        every { getDateAndTimeByDefaultFormat(any()) } returns LAST_MODIFY_DATE
        val result = albumFilesScanner.convertImageFileToWebFileBean(imageFileWrapper)
        Assert.assertEquals(LAST_MODIFY_DATE, result.mLastModifyDate)
        Assert.assertEquals(MIME_TYPE, result.mFileType)
        Assert.assertEquals(EXTENSION, result.mFileFormat)
        Assert.assertEquals(FILE_NAME, result.mFileName)
    }

    @Test
    fun testGetOrderWithType() {
        val albumFilesScanner = AlbumFilesScanner(1, 1, 1)
        val dateResult = albumFilesScanner.getOrderWithType(SortHelper.FILE_TIME_REVERSE_ORDER)
        Assert.assertEquals(AlbumFilesScanner.DATE_MODIFIED, dateResult)
        val sizeResult = albumFilesScanner.getOrderWithType(SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)
        Assert.assertEquals(AlbumFilesScanner.SIZE_DESC, sizeResult)
        val typeResult = albumFilesScanner.getOrderWithType(SortHelper.FILE_TYPE_ORDER)
        Assert.assertEquals(AlbumFilesScanner.TYPE_DESC, typeResult)
        val nameResult = albumFilesScanner.getOrderWithType(SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(AlbumFilesScanner.NAME_DESC, nameResult)
        val otherResult = albumFilesScanner.getOrderWithType(INVALID_SORT_ORDER)
        Assert.assertNull(otherResult)
    }

    companion object {
        private const val INVALID_SORT_ORDER = -1
        private const val DISPLAY_NAME = "Utopia.txt"
        private const val FILE_NAME = "Utopia"
        private const val EXTENSION = "txt"
        private const val MIME_TYPE = "txt"
        private const val LAST_MODIFY_DATE = "2022-11-30"
    }
}