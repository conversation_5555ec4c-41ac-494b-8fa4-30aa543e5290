/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RequestConvertKtTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/19 20:54
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 ** <author>        <data>       <version>  <desc>
 ** ternence        2022/7/19       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.convert

import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.OPERATE_COPY
import com.oplus.fileservice.operate.internal.OPERATE_CREATE_DIR
import com.oplus.fileservice.operate.internal.OPERATE_CUT
import com.oplus.fileservice.operate.internal.OPERATE_DELETE
import com.oplus.fileservice.operate.internal.OPERATE_RENAME
import com.oplus.fileservice.operate.internal.OPERATE_RESTORE
import org.junit.Assert
import org.junit.Test

class RequestConvertKtTest {

    @Test
    fun testConvertOperateInfoByRequestWithInvalidInfo() {
        val request = null
        val result = convertOperateInfoByRequest(request)
        Assert.assertEquals(-1, result.operateType)
        Assert.assertEquals(0, result.sourceFileInfo?.size)
        Assert.assertNull(result.targetFileInfo)
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result.conflictPolicy)
        Assert.assertNull(result.option)
        val invalidRequest = mutableListOf<String>()
        val invalidResult = convertOperateInfoByRequest(invalidRequest)
        Assert.assertEquals(-1, invalidResult.operateType)
        Assert.assertEquals(0, invalidResult.sourceFileInfo?.size)
        Assert.assertNull(invalidResult.targetFileInfo)
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, invalidResult.conflictPolicy)
        Assert.assertNull(result.option)
    }

    @Test
    fun testConvertOperateInfoByRequestWithoutOptions() {
        val operateInfo = mutableMapOf<String, Any>()
        // operate type
        operateInfo[KEY_OPERATE_TYPE] = 1

        // operate target info
        val targetInfo = mutableMapOf<String, String>()
        targetInfo[DATA] = TARGET_INFO_DATA
        targetInfo[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        operateInfo[KEY_TARGET_FILE_INFO] = targetInfo

        // operate source info
        val fileInfoLists = arrayListOf<Map<*, *>>()
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap[DATA] = SOURCE_INFO
        fileInfoMap[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        fileInfoLists.add(fileInfoMap)
        operateInfo[KEY_SOURCE_FILE_INFO] = fileInfoLists

        // put map to serializable
        val result = convertOperateInfoByRequest(operateInfo)
        Assert.assertEquals(1, result.operateType)
        Assert.assertEquals(1, result.sourceFileInfo?.size)
        Assert.assertNotNull(result.targetFileInfo)
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result.conflictPolicy)
        Assert.assertNull(result.option)
    }

    @Test
    fun testConvertOperateInfoByRequestWithOptions() {
        val operateInfo = mutableMapOf<String, Any>()
        // operate type
        operateInfo[KEY_OPERATE_TYPE] = OPERATE_RESTORE

        // operate target info
        val targetInfo = mutableMapOf<String, String>()
        targetInfo[DATA] = TARGET_INFO_DATA
        targetInfo[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        operateInfo[KEY_TARGET_FILE_INFO] = targetInfo

        // operate source info
        val fileInfoLists = arrayListOf<Map<*, *>>()
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap[DATA] = SOURCE_INFO
        fileInfoMap[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        fileInfoLists.add(fileInfoMap)
        operateInfo[KEY_SOURCE_FILE_INFO] = fileInfoLists

        // operate options
        operateInfo[KEY_OPTION] = true

        // put map to serializable
        val result = convertOperateInfoByRequest(operateInfo)
        Assert.assertEquals(6, result.operateType)
        Assert.assertEquals(1, result.sourceFileInfo?.size)
        Assert.assertNotNull(result.targetFileInfo)
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result.conflictPolicy)
        Assert.assertNull(result.option)
    }

    @Test
    fun testParseFileInfoListsWithEmptyList() {
        val fileInfoLists = arrayListOf<String>()
        Assert.assertEquals(0, parseFileInfoLists(fileInfoLists).size)
    }

    @Test
    fun testParseFileInfoListsWithInvalidInfo() {
        val fileInfoLists = arrayListOf<String>()
        Assert.assertEquals(0, parseFileInfoLists(fileInfoLists).size)
        val fileInfos = mutableMapOf<String, String>()
        Assert.assertEquals(0, parseFileInfoLists(fileInfos).size)
    }

    @Test
    fun testParseFileInfoLists() {
        val fileInfoLists = arrayListOf<Map<*, *>>()
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap[DATA] = TARGET_INFO_DATA
        fileInfoMap[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        fileInfoLists.add(fileInfoMap)
        val result = parseFileInfoLists(fileInfoLists)
        Assert.assertEquals(1, result.size)
        Assert.assertEquals(TARGET_INFO_DATA, result[0].data)
        Assert.assertEquals(TARGET_INFO_DISPLAY_NAME, result[0].displayName)
        Assert.assertNull(result[0].recycleId)
    }

    @Test
    fun testParseFileInfoWithEmptyMap() {
        val fileInfoMap = mutableMapOf<String, String>()
        val fileInfo = parseFileInfo(fileInfoMap)
        Assert.assertNull(fileInfo.data)
        Assert.assertNull(fileInfo.displayName)
        Assert.assertNull(fileInfo.recycleId)
    }

    @Test
    fun testParseFileInfoWithInvalidInfo() {
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap["$DATA-data"] = TARGET_INFO_DATA
        fileInfoMap["$DISPLAY_NAME-display"] = TARGET_INFO_DISPLAY_NAME
        val fileInfo = parseFileInfo(fileInfoMap)
        Assert.assertNull(fileInfo.data)
        Assert.assertNull(fileInfo.displayName)
        val fileInfoLists = mutableListOf<String>()
        val fileInfoListsResult = parseFileInfo(fileInfoLists)
        Assert.assertNull(fileInfoListsResult.data)
        Assert.assertNull(fileInfoListsResult.displayName)
        Assert.assertNull(fileInfo.recycleId)
    }

    @Test
    fun testParseFileInfo() {
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap[DATA] = TARGET_INFO_DATA
        fileInfoMap[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        val fileInfo = parseFileInfo(fileInfoMap)
        Assert.assertEquals(TARGET_INFO_DATA, fileInfo.data)
        Assert.assertEquals(TARGET_INFO_DISPLAY_NAME, fileInfo.displayName)
        Assert.assertNull(fileInfo.recycleId)
    }

    @Test
    fun testParseFileInfoWithRecycleId() {
        val fileInfoMap = mutableMapOf<String, String>()
        fileInfoMap[DATA] = TARGET_INFO_DATA
        fileInfoMap[DISPLAY_NAME] = TARGET_INFO_DISPLAY_NAME
        fileInfoMap[RECYCLE_ID] = RECYCLE_NUMBER_ID
        val fileInfo = parseFileInfo(fileInfoMap)
        Assert.assertEquals(TARGET_INFO_DATA, fileInfo.data)
        Assert.assertEquals(TARGET_INFO_DISPLAY_NAME, fileInfo.displayName)
        Assert.assertEquals(RECYCLE_NUMBER_ID, fileInfo.recycleId)
    }

    @Test
    fun testParseConflictPolicyWithKeepBoth() {
        val operateType = OPERATE_COPY
        val conflictPolicy = 1
        val operateRequest = OperateRequest(operateType)
        parseConflictPolicy(operateRequest, conflictPolicy)
        val result = operateRequest.conflictPolicy
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result)
    }

    @Test
    fun testParseConflictPolicyWithCutType() {
        val operateType = OPERATE_CUT
        val conflictPolicy = 1
        val operateRequest = OperateRequest(operateType)
        parseConflictPolicy(operateRequest, conflictPolicy)
        val result = operateRequest.conflictPolicy
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result)
    }

    @Test
    fun testParseConflictPolicyWithReplace() {
        val operateType = OPERATE_COPY
        val conflictPolicy = 2
        val operateRequest = OperateRequest(operateType)
        parseConflictPolicy(operateRequest, conflictPolicy)
        val result = operateRequest.conflictPolicy
        Assert.assertEquals(ConflictPolicy.REPLACE, result)
    }

    @Test
    fun testParseConflictPolicyWithSkip() {
        val operateType = OPERATE_COPY
        val conflictPolicy = 3
        val operateRequest = OperateRequest(operateType)
        parseConflictPolicy(operateRequest, conflictPolicy)
        val result = operateRequest.conflictPolicy
        Assert.assertEquals(ConflictPolicy.SKIP, result)
    }

    @Test
    fun testParseConflictPolicyWithDefault() {
        val operateType = OPERATE_COPY
        val conflictPolicy = 0
        val operateRequest = OperateRequest(operateType)
        parseConflictPolicy(operateRequest, conflictPolicy)
        val result = operateRequest.conflictPolicy
        Assert.assertEquals(ConflictPolicy.KEEP_BOTH, result)
    }

    @Test
    fun testParseOptionsWithRestoreType() {
        val operateType = OPERATE_DELETE
        val options = 1
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertTrue(result is Int)
        if (result is Int) {
            Assert.assertEquals(1, result)
        }
    }

    @Test
    fun testParseOptionsWithDeleteType() {
        val operateType = OPERATE_RESTORE
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    @Test
    fun testParseOptionsWithRenameType() {
        val operateType = OPERATE_RENAME
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    @Test
    fun testParseOptionsWithCreateDirType() {
        val operateType = OPERATE_CREATE_DIR
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    @Test
    fun testParseOptionsWithCopyType() {
        val operateType = OPERATE_COPY
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    @Test
    fun testParseOptionsWithCutType() {
        val operateType = OPERATE_CUT
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    @Test
    fun testParseOptionsWithOtherType() {
        val operateType = -1
        val options = true
        val operateRequest = OperateRequest(operateType = operateType)
        parseOptions(operateRequest, options)
        val result = operateRequest.option
        Assert.assertNull(result)
    }

    companion object {
        private const val KEY_OPERATE_TYPE = "operateType"
        private const val KEY_TARGET_FILE_INFO = "targetFileInfo"
        private const val KEY_SOURCE_FILE_INFO = "sourceFileInfo"
        private const val KEY_OPTION = "option"
        private const val DATA = "data"
        private const val DISPLAY_NAME = "displayName"
        private const val RECYCLE_ID = "recycleId"
        private const val SOURCE_INFO = "/storage/emulated/0/"
        private const val TARGET_INFO_DATA = "/storage/emulated/0/Ternence"
        private const val TARGET_INFO_DISPLAY_NAME = "Ternence"
        private const val RECYCLE_NUMBER_ID = "181"
    }
}