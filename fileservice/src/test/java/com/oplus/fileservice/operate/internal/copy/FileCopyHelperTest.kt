/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCopyHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/11/28 16:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 ** <author>        <data>       <version>  <desc>
 ** ternence        2022/11/28       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.copy

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.fileutils.JavaFileHelper
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.utils.OnFileOperateListener
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class FileCopyHelperTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `test check in same folder`() {
        val sourceLists = mutableListOf<FileInfo>()
        val destDir = FileInfo()
        val destDirNotExistResult = FileCopyHelper.checkInSameFolder(sourceLists, destDir)
        Assert.assertFalse(destDirNotExistResult.first)
        Assert.assertEquals(DEST_FOLDER_NOT_EXIST, destDirNotExistResult.second)
        destDir.data = DEST_DIR
        val notSameFolderResult = FileCopyHelper.checkInSameFolder(sourceLists, destDir)
        Assert.assertFalse(notSameFolderResult.first)
        Assert.assertEquals(NOT_SAME_FOLDER, notSameFolderResult.second)
    }

    @Test
    fun `test check in same folder source and target`() {
        val sourceLists = mutableListOf<FileInfo>()
        val destDir = FileInfo()
        destDir.data = DEST_DIR
        sourceLists.add(FileInfo(data = FILE_INFO_DATA, displayName = FILE_INFO_DISPLAY_NAME))
        val result = FileCopyHelper.checkInSameFolder(sourceLists, destDir)
        Assert.assertTrue(result.first)
        Assert.assertEquals(SAME_FOLDER, result.second)
    }

    @Test
    fun `test check in same folder else branch`() {
        val sourceLists = mutableListOf<FileInfo>()
        val destDir = FileInfo()
        destDir.data = NOT_IN_SAME_DEST_DIR
        sourceLists.add(FileInfo(data = FILE_INFO_DATA, displayName = FILE_INFO_DISPLAY_NAME))
        val result = FileCopyHelper.checkInSameFolder(sourceLists, destDir)
        Assert.assertFalse(result.first)
        Assert.assertEquals(NOT_SAME_FOLDER, result.second)
    }

    @Test
    fun `test check total size`() {
        mockkStatic(JavaFileHelper::class)
        val file = mockk<File>()
        every { file.isDirectory } returns false
        every { file.length() } returns FILE_LENGTH
        val totalSize = FileCopyHelper.checkTotalSize(file)
        Assert.assertEquals(FILE_LENGTH, totalSize)

        val directory = mockk<File>()
        every { directory.isDirectory } returns true
        every { JavaFileHelper.listFiles(directory) } returns null
        every { directory.length() } returns FILE_LENGTH
        val dirTotalSize = FileCopyHelper.checkTotalSize(directory)
        Assert.assertEquals(FILE_LENGTH, dirTotalSize)

        every { JavaFileHelper.listFiles(directory) } returns arrayOf(file).toList()
        val dirWithFileTotalSize = FileCopyHelper.checkTotalSize(directory)
        Assert.assertEquals(2 * FILE_LENGTH, dirWithFileTotalSize)

        unmockkStatic(JavaFileHelper::class)
    }

    @Test
    fun `test copy file`() {
        mockkStatic(JavaFileHelper::class)
        val file = mockk<File>()
        val sourceFile = mockk<File>()
        every { sourceFile.isDirectory } returns true
        every { sourceFile.length() } returns FILE_LENGTH
        every { JavaFileHelper.listFiles(sourceFile) } returns null

        val destFile = mockk<File>()
        every { destFile.exists() } returns false
        every { destFile.mkdir() } returns false
        val listener = object : OnFileOperateListener() {
            override var isCancel: () -> Boolean = {
                true
            }

            override fun onSuccess(sourceFile: File, destFile: File) {
                return
            }

            override fun onFailure(sourceFile: File, destFile: File): Boolean {
                return false
            }
        }
        val result = FileCopyHelper.copyFile(sourceFile, destFile, listener = listener)
        Assert.assertFalse(result)

        every { destFile.exists() } returns true
        every { destFile.mkdir() } returns true
        val destFileResult = FileCopyHelper.copyFile(sourceFile, destFile, listener = listener)
        Assert.assertTrue(destFileResult)

        every { JavaFileHelper.listFiles(sourceFile) } returns arrayOf(file).toList()
        val fileListResult = FileCopyHelper.copyFile(sourceFile, destFile, listener = listener)
        Assert.assertTrue(fileListResult)

        unmockkStatic(JavaFileHelper::class)
    }

    companion object {
        private const val NOT_SAME_FOLDER = -1
        private const val DEST_FOLDER_NOT_EXIST = 1 shl 0
        private const val SAME_FOLDER = 1 shl 2

        private const val FILE_INFO_DATA = "/storage/emulated/0/Ternence"
        private const val FILE_INFO_DISPLAY_NAME = "Ternence"

        private const val FILE_LENGTH = 1024L
        private const val DEST_DIR = "/storage/emulated/0/Ternence"
        private const val NOT_IN_SAME_DEST_DIR = "/storage/emulated/0/Terrence"
    }
}