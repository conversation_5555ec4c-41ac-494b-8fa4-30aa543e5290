/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AsyncOperationInfoKtTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/10 16:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 ** <author>        <data>       <version>  <desc>
 ** ternence        2022/9/10       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.reply

import android.content.Context
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.OPERATE_DELETE
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AsyncOperationInfoKtTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk()
        every { context.packageName } returns PACKAGE_NAME
    }

    @Test
    fun `test generate reply info with id`() {
        val paths = HashSet<String?>()
        paths.add(FILE_PATH)
        val result = generateReplyInfoById(REQUEST_ID, OPERATE_TYPE, paths)
        Assert.assertEquals(RESULT_WITH_ID, result)
    }

    @Test
    fun `test generate reply info by operationResult`() {
        val operateResult = OperationResult(false)
        val result = generateReplyInfoByOperateResult(REQUEST_ID, OPERATE_REQUEST, operateResult)
        Assert.assertEquals(RESULT_WITH_FAILURE_OPERATION_RESULT, result)
    }

    @Test
    fun `test generate reply info by success operationResult`() {
        val operateResult = OperationResult(true)
        val result = generateReplyInfoByOperateResult(REQUEST_ID, OPERATE_REQUEST, operateResult)
        Assert.assertEquals(RESULT_WITH_OPERATION_RESULT, result)
    }

    @Test
    fun `test generate reply info by delete operation`() {
        val operateResult = OperationResult(true)
        val operateRequest = OperateRequest(OPERATE_DELETE, option = 1)
        val result = generateReplyInfoByOperateResult(REQUEST_ID, operateRequest, operateResult)
        Assert.assertEquals(RESULT_WITH_DELETE_OPERATION, result)
    }

    companion object {
        private const val PACKAGE_NAME = "com.oplus.filemanager"
        private const val FILE_PATH = "/storage/emulated/0/"
        private const val REQUEST_ID = "88ac0abb-b8cb-4edc-b1dc-da9f048d73f7-1659076955482"
        private val OPERATE_REQUEST = OperateRequest(operateType = 1)
        private const val OPERATE_TYPE = 1

        private const val RESULT_WITH_OPERATION_RESULT =
            "{\"id\":\"88ac0abb-b8cb-4edc-b1dc-da9f048d73f7-1659076955482\",\"appId\":\"file\"," +
                    "\"category\":3,\"operation\":\"1\",\"target\":\"/storage/emulated/0/\",\"state\":0}"

        private const val RESULT_WITH_FAILURE_OPERATION_RESULT =
            "{\"id\":\"88ac0abb-b8cb-4edc-b1dc-da9f048d73f7-1659076955482\",\"appId\":\"file\"," +
                    "\"category\":3,\"operation\":\"1\",\"target\":\"/storage/emulated/0/\",\"state\":1}"

        private const val RESULT_WITH_ID =
            "{\"id\":\"88ac0abb-b8cb-4edc-b1dc-da9f048d73f7-1659076955482\",\"appId\":\"file\"," +
                    "\"category\":3,\"operation\":\"1\",\"target\":\"/storage/emulated/0/\",\"state\":0}"

        private const val RESULT_WITH_DELETE_OPERATION =
            "{\"id\":\"88ac0abb-b8cb-4edc-b1dc-da9f048d73f7-1659076955482\",\"appId\":\"file\"," +
                    "\"category\":3,\"operation\":\"5\",\"target\":\"/storage/emulated/0/\",\"state\":0}"
    }
}