/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 19:25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.recent.entity.recent.ExpandGroupItemEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.task.RecentLoadCallback
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.recent.utils.RecentFileObserver
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils

class RecentFileScanner(pageNo: Int, pageSize: Int, private var isNeedThumbnail: Boolean = true) : BaseUriScanner() {
    companion object {
        const val TAG = "RecentFileScanner"
    }

    private var mPageNo: Int = pageNo
    private var mPageSize: Int = pageSize
    private var mTotal: Int = 0
    private var mRecentWebFiles = ArrayList<WebFileBean>()
    private val mRecentFileObserver = RecentFileObserver()

    override fun scannerFiles(): WebScannerResult? {
        mRecentFileObserver.loadRecentData(RecentDataHelper.TYPE_MEDIA, null, ServiceRecentlyCallBack())
        return null
    }

    inner class ServiceRecentlyCallBack : RecentLoadCallback {

        override fun loadSucc(type: Int, data: MutableList<ExpandGroupItemEntity>?) {
            if (data == null) {
                mTotal = 0
                mPageNo = 1
                mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, mRecentWebFiles))
            } else {
                val pageRecentFiles = pageData(getAllRecentFileEntity(data))
                mRecentWebFiles = transformRecentFileEntityToWebFileBean(pageRecentFiles)
                fileServiceScope.launch(Dispatchers.IO) {
                    val thumbnailsWebBeans = mRecentWebFiles.filter {
                        isNeedThumbnail && ((it.mLocalType == MimeTypeHelper.APPLICATION_TYPE) ||
                                (it.mLocalType == MimeTypeHelper.IMAGE_TYPE) ||
                                (it.mLocalType == MimeTypeHelper.VIDEO_TYPE))
                    }
                    WebThumbCacheManager.setThumbnailData(thumbnailsWebBeans)
                    mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, mRecentWebFiles))
                }
            }
        }

        override fun loadFail(type: Int, msgObj: Any?) {
            mScannerCallback?.scannerFail(null)
        }

        override fun loadInvalid() {
            mScannerCallback?.scannerFail(null)
        }
    }

    @VisibleForTesting
    fun getAllRecentFileEntity(data: MutableList<ExpandGroupItemEntity>): List<RecentFileEntity> {
        val recentFileEntities = ArrayList<RecentFileEntity>()
        for (expandGroupItemEntity in data) {
            val recentContentEntities = expandGroupItemEntity.getChildList()
            for (recentFileEntity in recentContentEntities) {
                recentFileEntity?.apply {
                    recentFileEntities.add(recentFileEntity)
                }
            }
        }
        return recentFileEntities
    }

    @VisibleForTesting
    fun pageData(result: List<RecentFileEntity>): List<RecentFileEntity> {
        mTotal = result.size
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        return result.subList(start, end)
    }

    private fun transformRecentFileEntityToWebFileBean(entities: List<RecentFileEntity>): ArrayList<WebFileBean> {
        val resultWebFileBean = ArrayList<WebFileBean>()
        for (recentFile in entities) {
            recentFile.apply {
                val webFileBean = WebFileBean()
                webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                webFileBean.mFilePath = mAbsolutePath
                webFileBean.mFileSize = mSize
                webFileBean.mFileFormat = FileTypeUtils.getExtension(mDisplayName)
                webFileBean.mFileType =
                    WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
                webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
                webFileBean.mLocalType = mLocalType
                resultWebFileBean.add(webFileBean)
            }
        }
        Log.d(TAG, "transformRecentFileEntityToWebFileBean finish()")
        return resultWebFileBean
    }

    override fun destroy() {
        mRecentFileObserver.destroy()
    }
}