/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebFileBean
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/5 16:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/5       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.bean

import com.filemanager.common.helper.MimeTypeHelper
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.oplus.fileservice.utils.WebMimeTypeHelper

sealed class WebFileItemBean

class WebAlbumSetBean : WebFileItemBean() {

    @SerializedName("bucketName")
    @Expose
    var bucketName: String? = null

    @SerializedName("count")
    @Expose
    var count: Long? = null

    @SerializedName("coverPath")
    @Expose
    var coverPath: String? = null

    @SerializedName("key")
    @Expose
    var key: String? = null

    @SerializedName("bucketId")
    @Expose
    var bucketId: String? = null

    @SerializedName("type")
    @Expose
    var type: Int = 0

    @SerializedName("dataModified")
    @Expose
    var dataModified: Long = 0

    @SerializedName("thumbnailData")
    @Expose
    var mThumbnailData: ByteArray? = null
    override fun toString(): String {
        return "WebAlbumSetBean(" +
                "bucketName=$bucketName, " +
                "count=$count, " +
                "coverPath=$coverPath, " +
                "key=$key, " +
                "bucketId=$bucketId, " +
                "type=$type, " +
                "dataModified=$dataModified" +
                ")"
    }
}

open class WebFileBean : WebFileItemBean() {
    companion object {
        const val TAG = "WebFileBean"
    }

    @SerializedName("thumbnailData")
    @Expose
    var mThumbnailData: ByteArray? = null

    @SerializedName("fileName")
    @Expose
    var mFileName: String? = null

    @SerializedName("lastModifyDate")
    @Expose
    var mLastModifyDate: String? = null

    @SerializedName("fileType")
    @Expose
    var mFileType: String = WebMimeTypeHelper.OTHER_TYPE

    @SerializedName("fileFormat")
    @Expose
    var mFileFormat: String? = null

    @SerializedName("fileSize")
    @Expose
    var mFileSize: Long? = null

    @SerializedName("filePath")
    @Expose
    var mFilePath: String? = null

    @SerializedName("fileId")
    @Expose
    open var mFileId: Int? = null

    @Expose(serialize = false, deserialize = false)
    var mLocalType: Int = MimeTypeHelper.UNKNOWN_TYPE

    @SerializedName("folderItem")
    @Expose
    var mFolderItem = 0

    @Expose(serialize = false, deserialize = false)
    var mDateModified: Long = 0

    override fun toString(): String {
        return "WebFileBean(" +
                "mFileName=$mFileName," +
                "mLastModifyDate=$mLastModifyDate," +
                "mFileType=$mFileType" +
                ",mFileFormat=$mFileFormat," +
                "mFileSize=$mFileSize," +
                "mFilePath=$mFilePath," +
                "mFileId=$mFileId," +
                "mLocalType=$mLocalType," +
                "mFolderItem=$mFolderItem" +
                ")"
    }
}