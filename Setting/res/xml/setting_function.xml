<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.coui.appcompat.preference.COUIPreferenceCategory app:isFirstCategory="true">
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_cloud"
            android:persistent="false"
            android:title="@string/settings_function_menu_cloud" />
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_cleanup"
            android:persistent="false"
            android:title="@string/settings_function_menu_cleanup" />
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_owork"
            android:persistent="false"
            android:title="@string/owork_space_new" />
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_private_safe"
            android:persistent="false"
            android:title="@string/private_safe" />
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_tencent_docs"
            android:persistent="false"
            android:title="@string/tencent_docs" />
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_kdocs"
            android:persistent="false"
            android:title="@string/kdocs" />
        <com.filemanager.setting.ui.function.ThirdAppSearchSwitchPreference
            android:key="setting_pref_third_app_search"
            android:persistent="false"
            android:title="@string/third_app_search_preference_title_new" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

</PreferenceScreen>