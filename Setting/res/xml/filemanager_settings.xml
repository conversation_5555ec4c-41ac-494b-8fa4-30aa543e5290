<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.coui.appcompat.preference.COUIPreferenceCategory app:isFirstCategory="true">
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_file_open_mode"
            android:persistent="false"
            android:title="@string/doc_viewer_setting_title" />

        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_hidden_files"
            android:persistent="false"
            android:title="@string/hidden_file_switch"
            android:summary="@string/hide_file_switch_details"/>

        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_recent_camera_screenshot"
            android:persistent="false"
            android:title="@string/recent_camera_screenshot_display_switch"
            android:summary="@string/recent_camera_screenshot_display_switch_details"/>

        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_encrypt_box_switch"
            android:persistent="false"
            app:isPreferenceVisible="false"
            android:title="@string/encrypt_box_switch" />

        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="setting_pref_advert_service_switch"
            android:persistent="false"
            android:summary="@string/advert_service_close_tips"
            android:title="@string/advert_service"
            app:isPreferenceVisible="false" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_function"
            android:persistent="false"
            app:isPreferenceVisible="false"
            android:title="@string/settings_function" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory>

        <com.coui.appcompat.preference.COUIPreference
            android:key="setting_pref_app_version"
            android:title="@string/apk_version"
            app:isBackgroundAnimationEnabled="false" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_about"
            android:persistent="true"
            android:title="@string/about_file_manager"/>

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_data_list"
            android:persistent="true"
            android:title="@string/personal_information_collection_list"/>

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_privacy"
            android:persistent="true"
            android:title="@string/privacy_string"/>

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_help_feedback"
            android:persistent="false"
            android:title="@string/help_and_feedback"/>
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.filemanager.common.view.preference.SpacePreferenceCategory />
</PreferenceScreen>