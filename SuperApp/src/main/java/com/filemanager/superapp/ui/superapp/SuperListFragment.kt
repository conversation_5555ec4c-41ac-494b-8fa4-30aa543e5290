/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.constants.KtConstants.SUPER_DIR_DEPTH
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnRefreshDataCallback
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.SortAnimationHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch.SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.dp2px
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.filemanager.superapp.R
import com.filemanager.superapp.databinding.GuidePanelLayoutBinding
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.feedback.IFeedback
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.QQ
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.WECHAT
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier

class SuperListFragment : RecyclerSelectionVMFragment<SuperListViewModel>(), OnBackPressed, NavigationBarView.OnItemSelectedListener,
    OnDragStartListener, OnItemClickListener<Int> {
    companion object {
        private const val TAG = "SuperListFragment"
        const val SUPER_LIST_PATH = "super_list_path"
        private const val DELAY_REFRESH = 100L      //onUiConfigChange 时需要延迟更新页面，加载的图片才能获取正确的宽度
        private const val SP_KEY_WECHAT = "key_wechat"
        private const val SP_KEY_QQ = "key_qq"
        private const val GUIDE_SHOWING_STATE = "guide_showing_state"
    }
    private var sortEntryView: SortEntryView? = null
    private var mPath: Array<String>? = null
    private var mDirDepth: Int = 0
    private var mTabPosition = Constants.TAB_ALL
    private var mTitleResId: Int = -1
    private var mTempSortType = -1
    private var mTitle: String? = null
    private var mPackage: String? = null
    private var externalPath: String? = ""
    private var sideCategoryType: Int = -1
    private var mAdapter: SuperListAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mTabActivityListener: TabActivityListener<BaseFileBean>? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER, mTabPosition)
    }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    internal var mFileOperateController: NormalFileOperateController? = null
        private set
    private var mLoadingController: LoadingController? = null
    private var mNeedLoadData = false
    private var hasShowEmpty: Boolean = false
    private var curPackage = ""
    private var guideDialogBuilder: COUIAlertDialogBuilder? = null
    private var guideDialog: AlertDialog? = null
    private var lastGuideShowing = false
    private var tempSortDesc = -1
    private var tempSortType = -1
    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null
    private var mFolderTransformAnimator: FolderTransformAnimator? = null
    private var sortAnimationHelper: SortAnimationHelper? = null

    enum class Source {
        WECHAT,
        QQ
    }

    override fun getLayoutResId(): Int {
        return R.layout.super_app_list_fragment
    }

    override fun createViewModel(): SuperListViewModel {
        val vm = ViewModelProvider(this)[mTabPosition.toString(), SuperListViewModel::class.java]
        val sortMode = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getSuperKey(mPath))
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_QQ, vm, sortMode).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mTabPosition = bundle.getInt(KtConstants.P_TAB_POSITION)
            mPackage = bundle.getString(KtConstants.P_PACKAGE)
            mPath = bundle.getStringArray(SUPER_LIST_PATH)
            mDirDepth = bundle.getInt(SUPER_DIR_DEPTH)
            mTitleResId = bundle.getInt(Constants.TITLE_RES_ID)
            mTempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA)
            tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
            tempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            sideCategoryType = bundle.getInt(Constants.SIDE_CATEGORY_TYPE, -1)
            externalPath = bundle.getString(KtConstants.FILE_PATH)
            val isOWork = mTitleResId == com.filemanager.common.R.string.owork_space
            mAdapter = SuperListAdapter(it, mTabPosition, <EMAIL>, mPackage, isOWork).apply {
                setHasStableIds(true)
            }
            checkShowGuide()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated")
        lastGuideShowing = savedInstanceState?.getBoolean(GUIDE_SHOWING_STATE, false) == true
        Log.d(TAG, "onViewCreated lastGuideShowing $lastGuideShowing mPackage $mPackage")
        if (lastGuideShowing) {
            checkShowGuide()
            lastGuideShowing = false
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged")
        if (guideDialog?.isShowing == true) {
           updateGuideDialog()
        }
    }

    private fun checkShowGuide() {
        if (!SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT) {
            Log.d(TAG, "support private directory entry prompt false")
            return
        }
        val parent = parentFragment as? SuperAppParentFragment
        val isOtherPkg = mPackage != WECHAT && mPackage != QQ
        if (!lastGuideShowing && (parent?.isFirstEnter == false || isOtherPkg)) {
            Log.d(TAG, "checkShowGuide return")
            return
        }
        parent?.isFirstEnter = false
        curPackage = mPackage ?: ""
        Log.d(TAG, "checkShowGuide lastGuideShowing:$lastGuideShowing")
        when (curPackage) {
            WECHAT -> {
                val hasShowWechatGuide = PreferencesUtils.getBoolean(key = SP_KEY_WECHAT)
                if (hasShowWechatGuide.not() || lastGuideShowing) {
                    showGuideDialog(Source.WECHAT)
                }
            }

            QQ -> {
                val hasShowQQGuide = PreferencesUtils.getBoolean(key = SP_KEY_QQ)
                if (hasShowQQGuide.not() || lastGuideShowing) {
                    showGuideDialog(Source.QQ)
                }
            }
        }
    }


    private fun showGuideDialog(source: Source) {
        if (guideDialog?.isShowing == true) {
            Log.d(TAG, "mBottomAlertDialog isShowing return")
            return
        }
        activity?.let {
            val guideLayoutBinding = GuidePanelLayoutBinding.inflate(LayoutInflater.from(it), null, false)
            COUIAlertDialogBuilder(it).apply {
                guideDialogBuilder = this
                //设置内容
                setGuideContent(source, guideLayoutBinding, it)
                //左边btn
                setNegativeButton(com.filemanager.common.R.string.menu_file_list_detail) { _, _ ->
                    val feedBack = Injector.injectFactory<IFeedback>()
                    activity?.let { act -> feedBack?.launchFeedBack(act) }
                }
                //右边btn
                setPositiveButton(com.filemanager.common.R.string.positive_ok) { _, _ ->
                    guideDialog?.dismiss()
                }
                setView(guideLayoutBinding.root)
                guideDialog = show()
                guideDialog?.setCanceledOnTouchOutside(true)
            }
        }
        when (source) {
            Source.WECHAT -> PreferencesUtils.put(key = SP_KEY_WECHAT, value = true)
            Source.QQ -> PreferencesUtils.put(key = SP_KEY_QQ, value = true)
        }
    }

    private fun updateGuideDialog() {
        val source = when (curPackage) {
            WECHAT -> Source.WECHAT
            QQ -> Source.QQ
            else -> return
        }
        activity?.let {
            val guideLayoutBinding =
                GuidePanelLayoutBinding.inflate(LayoutInflater.from(it), null, false)
            setGuideContent(source, guideLayoutBinding, it)
            guideDialogBuilder?.apply {
                setView(guideLayoutBinding.root)
                Log.d(TAG, "updateGuideDialog success")
            }
        }
    }

    private fun setGuideContent(
        source: Source,
        guideLayoutBinding: GuidePanelLayoutBinding,
        activity: FragmentActivity
    ) {
        /*1.大屏居中显示 中屏居中显示2.小屏底部显示3.中大屏使用中大屏专用切图4.小屏分屏也使用大图*/
        val isMiddleAndLargeScreen = WindowUtils.isMiddleAndLargeScreen(activity)
        val isInMultiWindowMode = COUIPanelMultiWindowUtils.isInMultiWindowMode(activity)
        val currentWindowType = WindowUtils.getCurrentWindowType(activity)
        Log.d(TAG, "isMiddleAndLargeScreen $isMiddleAndLargeScreen currentWindowType $currentWindowType isInMultiWindowMode $isInMultiWindowMode")
        when (source) {
            Source.WECHAT -> {
                // 1大中屏 || 中小屏分屏时 使用大图
                if (isMiddleAndLargeScreen || (isInMultiWindowMode && currentWindowType != WindowUtils.LARGE)) {
                    guideLayoutBinding.ivGuide.setImageResource(R.drawable.ic_guide_wechat_big)
                    // 大屏单独处适配顶部高度
                    dealWindowLarge(guideLayoutBinding)
                } else {
                    guideLayoutBinding.ivGuide.setImageResource(R.drawable.ic_guide_wechat)
                }
                guideLayoutBinding.tvDescription.text = resources.getString(
                    com.filemanager.common.R.string.app_guide_description,
                    getString(com.filemanager.common.R.string.string_wechat),
                    getString(com.filemanager.common.R.string.app_name),
                    getString(com.filemanager.common.R.string.string_wechat)
                )
            }

            Source.QQ -> {
                if (isMiddleAndLargeScreen || (isInMultiWindowMode && currentWindowType  != WindowUtils.LARGE)) {
                    guideLayoutBinding.ivGuide.setImageResource(R.drawable.ic_guide_qq_big)
                    dealWindowLarge(guideLayoutBinding)
                } else {
                    guideLayoutBinding.ivGuide.setImageResource(R.drawable.ic_guide_qq)
                }
                guideLayoutBinding.tvDescription.text = resources.getString(
                    com.filemanager.common.R.string.app_guide_description,
                    getString(com.filemanager.common.R.string.string_qq),
                    getString(com.filemanager.common.R.string.app_name),
                    getString(com.filemanager.common.R.string.string_qq)
                )
            }
        }
    }

    private fun dealWindowLarge(guideLayoutBinding: GuidePanelLayoutBinding) {
        if (WindowUtils.getCurrentWindowType(context) == WindowUtils.LARGE) {
            //大屏pad适配时高度高了,需要单独处理
            Log.d(TAG, "dealWindowLarge")
            guideLayoutBinding.guideRoot.apply {
                val top = dp2px(context, resources.getDimension(com.filemanager.common.R.dimen.dimen_11dp).toInt())
                setPadding(paddingLeft, top, paddingRight, paddingBottom)
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        Log.d(TAG, "onSaveInstanceState")
        if (guideDialog?.isShowing == true) {
            lastGuideShowing = true
            outState.putBoolean(GUIDE_SHOWING_STATE, true)
        }
        super.onSaveInstanceState(outState)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        if (guideDialog?.isShowing == true) {
            guideDialog?.dismiss()
            guideDialog = null
        }
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        super.onDestroy()
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator?.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.root_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentRecyclerView?.setOnGenericMotionListener(PCConsumeOnGenericMotionListener())
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        sortEntryView = (parentFragment as? SuperAppParentFragment)?.sortEntryView
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getSuperKey(mPath))
        if (tempSortType != -1) {
            sortEntryView?.setSortOrder(tempSortType, tempSortDesc == 0)
        } else {
            sortEntryView?.setDefaultOrder(SortRecordModeFactory.getSuperKey(mPath))
        }
        /**
         * 视频音频动画和分类图片一样，其他类型tab和全部文件动画一样
         * 需要通过mTabPosition区分是哪个页面
         */
        if (mTabPosition == Constants.TAB_IMAGE || mTabPosition == Constants.TAB_VIDEO) {
            sortAnimationHelper = SortAnimationHelper(SortRecordModeFactory.getSuperKey(mPath))
        } else {
            mFolderTransformAnimator = FolderTransformAnimator()
        }
    }

    fun setToolbarNew(toolbar: COUIToolbar?, title: String) {
        this.toolbar = toolbar
        mTitle = title
    }

    private fun isCurrentFragment(): Boolean {
        (parentFragment as? SuperAppParentFragment)?.let {
            if (it.getCurrentTabPosition() == mTabPosition) {
                return true
            }
        }
        return false
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentViewModel?.mTabPosition = mTabPosition
        fragmentRecyclerView?.let {
            it.addItemDecoration(mSpacesItemDecoration)
            val scanMode = getParentViewModel()?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.getGridItemCount(activity,
                scanMode, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER, mTabPosition)).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType: Int? = mAdapter?.getItemViewType(position)
                        return if (viewType == SuperListAdapter.VIEW_TYPE_ITEM_FOOTER) spanCount else 1
                    }
                }
            }
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.layoutManager = mLayoutManager!!
            it.itemAnimator = if (mTabPosition == Constants.TAB_IMAGE || mTabPosition == Constants.TAB_VIDEO) {
                sortAnimationHelper?.sortAnimator
            } else {
                mFolderTransformAnimator
            }
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { adapter ->
                it.adapter = adapter
            }
            toolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    val appbar = (parentFragment as? SuperAppParentFragment)?.mAppBarLayout
                    val footLayout = baseVMActivity?.findViewById<View>(R.id.footer_layout)
                    var footHeight = 0
                    if (footLayout?.visibility == View.VISIBLE) {
                        footHeight = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_64dp)
                    }
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appbar, 0), it.paddingRight, paddingBottom + footHeight)
                    fragmentFastScroller?.apply { trackMarginBottom = (paddingBottom + footHeight) }
                }
            }
            it.setLoadStateForScroll(this)
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    @VisibleForTesting
    fun getParentViewModel(): SuperAppParentFragmentViewModel? {
        return try {
            ViewModelProvider(baseVMActivity ?: this)[SuperAppParentFragmentViewModel::class.java]
        } catch (e: IllegalStateException) {
            Log.d(TAG, "getParentViewModel IllegalStateException：${e.message}")
            null
        }
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModuleChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { superUiModel ->
                onSuperUiModelChanged(superUiModel, viewModule)
            }
            startScrollToPositionObserver()
            startScanModeObserver()
            startObserveLoadState()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun onListModuleChanged(viewModule: SuperListViewModel, listModel: Int) {
        if (!viewModule.mModeState.initState || !isCurrentFragment()) {
            toolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "mListModel=$listModel")
        val footLayout = baseVMActivity?.findViewById<View>(R.id.footer_layout)
        var footHeight = 0
        if (footLayout?.visibility == View.VISIBLE) {
            footHeight =
                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_64dp)
        }
        if (listModel == LIST_SELECTED_MODE) {
            if (baseVMActivity is NavigationInterface) {
                (baseVMActivity as NavigationInterface).showNavigation()
                viewModule.setNavigateItemAble(baseVMActivity as NavigationInterface)
            }
            mAdapter?.setSelectEnabled(true)
            (parentFragment as? SuperAppParentFragment)?.previewEditedFiles(
                fragmentViewModel?.getSelectItems()
            )
            (parentFragment as? SuperAppParentFragment)?.disableViewPager()
            fragmentRecyclerView?.let {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                val paddingBottom =
                    KtViewUtils.getSelectModelPaddingBottom(it, bottomView) + footHeight
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.apply { trackMarginBottom = paddingBottom }
            }
            toolbar?.let {
                changeActionModeAnim(it, {
                    mTabActivityListener?.initToolbarSelectedMode(
                        true,
                        viewModule.getRealFileSize(),
                        viewModule.uiState.value?.selectedList?.size ?: 0,
                        viewModule.getSelectItems()
                    )
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
        } else {
            (parentFragment as? SuperAppParentFragment)?.previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            mAdapter?.setSelectEnabled(false)
            fragmentRecyclerView?.let {
                val paddingBottom = appContext.resources
                    .getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom) + footHeight
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.trackMarginBottom = appContext.resources
                    .getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom) + footHeight
            }
            toolbar?.let {
                changeActionModeAnim(it, {
                    mTabActivityListener?.apply {
                        val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
                        initToolbarNormalMode(true, empty)
                        refreshScanModeItemIcon()
                    }
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            (baseVMActivity as? NavigationInterface)?.hideNavigation()
        }
    }

    private fun onSuperUiModelChanged(
        superUiModel: BaseUiModel<BaseFileBean>,
        viewModule: SuperListViewModel
    ) {
        Log.d(
            TAG, "SuperListUiModel mUiState =" + superUiModel.fileList.size + ","
                    + superUiModel.selectedList.size + "," + superUiModel.keyWord
        )
        val fileSize = viewModule.getRealFileSize()
        sortEntryView?.setFileCount(fileSize)
        if (superUiModel.stateModel.listModel.value == LIST_SELECTED_MODE) {
            toolbar?.let {
                mTabActivityListener?.initToolbarSelectedMode(
                    false, fileSize,
                    superUiModel.selectedList.size,
                    viewModule.getSelectItems()
                )
            }
            sortEntryView?.visibility = View.VISIBLE
            if (superUiModel.fileList is ArrayList<BaseFileBean>) {
                if (getParentViewModel()?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST &&
                    (mTabPosition == Constants.TAB_IMAGE || mTabPosition == Constants.TAB_VIDEO)) {
                    listDataRefresh(superUiModel, true)
                } else {
                    mAdapter?.setData(
                        superUiModel.fileList as ArrayList<BaseFileBean>,
                        superUiModel.selectedList,
                        externalPath
                    )
                    externalPath = null
                }
            }
        } else {
            (parentFragment as? SuperAppParentFragment)?.previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            val empty = superUiModel.fileList.isEmpty()
            if (empty) {
                showEmptyView()
                sortEntryView?.visibility = View.GONE
            } else {
                mFileEmptyController.hideFileEmptyView()
                sortEntryView?.visibility = View.VISIBLE
            }
            mTabActivityListener?.apply {
                initToolbarNormalMode(false, empty)
                refreshScanModeItemIcon()
            }
            if (superUiModel.fileList is ArrayList<BaseFileBean>) {
                if (getParentViewModel()?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST &&
                    (mTabPosition == Constants.TAB_IMAGE || mTabPosition == Constants.TAB_VIDEO)) {
                    listDataRefresh(superUiModel, true)
                } else {
                    mAdapter?.let {
                        it.setKeyWord(superUiModel.keyWord)
                        it.setData(
                            superUiModel.fileList as ArrayList<BaseFileBean>,
                            superUiModel.selectedList,
                            externalPath
                        )
                        externalPath = null
                    }
                }
            }
        }
    }

    private fun listDataRefresh(superUiModel: BaseUiModel<BaseFileBean>, isSelectMode: Boolean) {
        val baseFileBeans = superUiModel.fileList as ArrayList<BaseFileBean>
        val needDoListAnimate = sortAnimationHelper?.needDoListAnimate() ?: false
        Log.d(TAG, "list setData animate $needDoListAnimate")
        if (mGridSpanAnimationHelper != null && needDoListAnimate) {
            fragmentRecyclerView?.let { recyclerView ->
                recyclerView.mTouchable = false
                recyclerView.stopScroll()
            }
            mGridSpanAnimationHelper?.startListSortAnimation(object : OnRefreshDataCallback {
                override fun onRefreshDataCallback() {
                    mAdapter?.setData(baseFileBeans, superUiModel.selectedList, externalPath)
                    externalPath = null
                    if (isSelectMode) {
                        (parentFragment as? SuperAppParentFragment)?.previewEditedFiles(
                            fragmentViewModel?.getSelectItems()
                        )
                    }
                }
            }, object : OnAnimatorEndListener {
                override fun onAnimatorEnd() {
                    fragmentRecyclerView?.mTouchable = true
                }
            })
        } else {
            if (!isSelectMode) {
                mAdapter?.setKeyWord(superUiModel.keyWord)
            }
            mAdapter?.setData(baseFileBeans, superUiModel.selectedList, externalPath)
            externalPath = null
            if (isSelectMode) {
                (parentFragment as? SuperAppParentFragment)?.previewEditedFiles(
                    fragmentViewModel?.getSelectItems()
                )
            }
        }
    }

    private fun startScanModeObserver() {
        getParentViewModel()?.mBrowseModeState?.observe(this) { scanMode ->
            sortAnimationHelper?.setAnimatorDurationWithScanModeChange(fragmentRecyclerView, scanMode)
            updateScanModeView(scanMode)
        }
    }

    private fun updateScanModeView(scanMode: Int) {
        fragmentViewModel?.mBrowseModeState = scanMode
        toolbar?.let {
            val needSkipAnimation = mNeedSkipAnimation
            if (needSkipAnimation) {
                refreshScanModeAdapter(scanMode)
            } else {
                fragmentRecyclerView?.let { recyclerView ->
                    recyclerView.mTouchable = false
                    recyclerView.stopScroll()
                }
                mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                    override fun onSpanChangeCallback() {
                        mLayoutManager?.scrollToPosition(0)
                        refreshScanModeAdapter(scanMode)
                    }
                }, object : OnAnimatorEndListener {
                    override fun onAnimatorEnd() {
                        fragmentRecyclerView?.mTouchable = true
                    }
                })
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            if (!isDetached) {
                val scanMode = getParentViewModel()?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
                if (scanMode == KtConstants.SCAN_MODE_GRID) {
                    refreshScanModeAdapter(scanMode)
                }
                baseVMActivity?.let {
                    mFileEmptyController.changeEmptyFileIcon()
                }
                mSortPopupController.hideSortPopUp()
                mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
                if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
                }
                updateLeftRightMargin()
            }
        }
    }

    fun updateLeftRightMargin() {
        val scanMode = getParentViewModel()?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER, mTabPosition
                                                              )
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator?.mSkipAddRemoveAnimation = true
            fragmentRecyclerView?.postDelayed({ notifyDataSetChanged() }, DELAY_REFRESH)
        }
    }

    private fun startScrollToPositionObserver() {
        fragmentViewModel?.mPositionModel?.observe(this) { positionModel ->
            positionModel?.let {
                mLayoutManager?.scrollToPosition(it)
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    mFileEmptyController.hideFileEmptyView()
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator?.registerNeedSkipAnimator(this)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
        if (getParentViewModel()?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            (parentFragment as? SuperAppParentFragment)?.listEmptyFile()
        }
        mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
        Log.d(TAG, "showEmptyView")
    }

    fun getFileList(): List<BaseFileBean>? {
        return fragmentViewModel?.uiState?.value?.fileList
    }

    fun getSelectItems(): List<BaseFileBean>? {
        return fragmentViewModel?.getSelectItems()
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData")
        if (!isAdded) {
            Log.e(TAG, "onResumeLoadData fragment don't add")
            return
        }
        if (checkShowPermissionEmpty()) {
            sortEntryView?.setFileCount(0)
            return
        }
        fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(parentFragment ?: this), mTabPosition, mPath, mDirDepth)
        if (curPackage != mPackage) {
            (parentFragment as? SuperAppParentFragment)?.isFirstEnter = true
            checkShowGuide()
        }
        mViewModel?.setSort(tempSortType, tempSortDesc)
    }

    override fun pressBack(): Boolean {
        return fragmentViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.let { controller ->
                controller.operationPage = OptimizeStatisticsUtil.signalToPage(toolbar?.title?.toString() ?: "")
                controller.onNavigationItemSelected(it, item)
            }
        } ?: false
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_QQ, mPackage)
                val page = OptimizeStatisticsUtil.signalToPage(toolbar?.title?.toString() ?: "")
                OptimizeStatisticsUtil.pageSearch(page)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, page)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.signalToPage(toolbar?.title?.toString() ?: ""))
                true
            }
            R.id.navigation_sort -> {
                onNavigationSortSelected()
                true
            }
            R.id.actionbar_scan_mode -> {
                mTabActivityListener?.updateNeedSkipAnimation(true)
                getParentViewModel()?.clickScanModeItem()
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                val page = OptimizeStatisticsUtil.signalToPage(toolbar?.title?.toString() ?: "")
                OptimizeStatisticsUtil.pageSetting(page)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, page)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    private fun onNavigationSortSelected() {
        baseVMActivity?.let {
            StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
            OptimizeStatisticsUtil.pageSort(
                OptimizeStatisticsUtil.signalToPage(
                    toolbar?.title?.toString() ?: ""
                )
            )
            val bundle = Bundle()
            bundle.putInt(Constants.TEMP_SORT_TYPE, tempSortType)
            bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
            bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, SortRecordModeFactory.getSuperKey(mPath))
            val anchorView: View? = parentFragment?.view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
            mSortPopupController.showSortPopUp(it,
                anchorView,
                bundle,
                object : SelectItemListener {

                    override fun onDismiss() {
                        sortEntryView?.rotateArrow()
                    }

                    override fun onPopUpItemClick(
                        flag: Boolean,
                        sortMode: Int,
                        isDesc: Boolean
                    ) {
                        if (flag) {
                            sortEntryView?.setSortOrder(sortMode, isDesc)
                            mViewModel?.setSort(-1, -1)
                            fragmentViewModel?.sortReload()
                        }
                    }
                })
        }
    }

    fun setTabActivityListener(tabListener: TabActivityListener<BaseFileBean>) {
        mTabActivityListener = tabListener
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, path) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = (parentFragment as? SuperAppParentFragment)?.previewClickedFile(
                    baseFile, fragmentViewModel?.previewClickedFileLiveData
                )
                if (previewResult != true) {
                    mFileOperateController?.operationPage = OptimizeStatisticsUtil.signalToPage(toolbar?.title?.toString() ?: "")
                    var mediaImgIds: ArrayList<String>? = null
                    // 判断当前点击是否是媒体库中的图片
                    if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                        // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                        mediaImgIds = FileMediaHelper.getMediaImgIds(baseFile, fragmentViewModel?.uiState?.value?.fileList)
                    }
                    mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                }
            }
        }
        return true
    }

    fun updatePathAndPackage(pathArray: Array<String>?, superAppPackage: String?) {
        mPath = pathArray
        mPackage = superAppPackage
        fragmentViewModel?.updateLoaderPath(mPath)
        mAdapter?.let { adapter ->
            adapter.isOWork = toolbar?.title == getString(com.filemanager.common.R.string.owork_space)
            adapter.mSuperAppPackage = superAppPackage
        }
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getSuperKey(mPath))
    }

    /**
     * 判断当前是否处于选中编辑模式
     */
    fun isInSelectMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() == true
    }

    fun exitSelectionMode() {
        if (isInSelectMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (getParentViewModel()?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val slideWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            slideWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
            mTabPosition
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun bringToFront(visible: Int) {
        //显示的时候在appbarlayout初始化后在执行 因为高度依赖appbarlayout
        if (View.GONE == visible) {
            super.bringToFront(visible)
        }
    }
    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyViewPaddingForChild()
    }

    override fun getFragmentCategoryType(): Int {
        return if (sideCategoryType != -1) {
            sideCategoryType
        } else {
            CategoryHelper.CATEGORY_SOURCE_GROUP
        }
    }

    fun updateSideCategoryType(type: Int) {
        sideCategoryType = type
        rootView?.apply {
            super.setFragmentViewDragTag(this)
        }
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }
}