/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ViewDataSizeRunnableTest
 ** Description : ViewDataSizeRunnable Unit Test
 ** Version     : 1.0
 ** Date        : 2022/8/11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/8/11      1.0        create
 ***********************************************************************/
package com.oplus.recyclebin.viewmodel

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.interfaze.recyclebin.RecycleBinTotalSizeCallback
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.filemanager.recyclebin.viewmodel.ViewDataSizeRunnable
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Assert
import org.junit.Test
import java.io.File

class ViewDataSizeRunnableTest {


    @Test
    fun should_return_when_calculateSizeAndCallback() {
        mockkObject(RecycleBinUtils)
        val runnable = mockk<ViewDataSizeRunnable>(relaxed = true)
        every { runnable.calculateSizeAndCallback(any(), any(), any(), any()) }.answers { callOriginal() }
        every { runnable.setQuerySizeListener(any()) }.answers { callOriginal() }
        every { RecycleBinUtils.getFileOrFolderSize(any<File>()) }.returns(10L)

        runnable.setQuerySizeListener(object : RecycleBinTotalSizeCallback {
            override fun onTotalSizeReturn(size: Pair<Long, Long>) {
                Assert.assertEquals(100, size.first)
            }
        })
        val files = arrayListOf<BaseFileBean>()
        var size = runnable.calculateSizeAndCallback(100, files, 0, files.size.toLong())
        Assert.assertEquals(100, size)

        runnable.setQuerySizeListener(object : RecycleBinTotalSizeCallback {
            override fun onTotalSizeReturn(size: Pair<Long, Long>) {
                Assert.assertEquals(100, size.first)
            }
        })
        files.add(BaseFileBean())
        size = runnable.calculateSizeAndCallback(100, files, 0, files.size.toLong())
        Assert.assertEquals(100, size)

        runnable.setQuerySizeListener(object : RecycleBinTotalSizeCallback {
            override fun onTotalSizeReturn(size: Pair<Long, Long>) {
                Assert.assertEquals(110, size.first)
            }
        })
        files.add(BaseFileBean().apply { mData = "/sdcard/1.mp3" })
        size = runnable.calculateSizeAndCallback(100, files, 0, files.size.toLong())
        Assert.assertEquals(110, size)
    }
}