/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinRecordHelperTest.kt
 * Description:
 * The test cases for RecycleBinRecordHelper
 *
 * Version: 1.0
 * Date: 2024-04-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-04-11   1.0    Create this module
 *********************************************************************************/
package com.filemanager.recyclebin.db

import android.content.ContentValues
import com.filemanager.common.RecycleStore
import com.filemanager.common.RecycleStore.Files.FileColumns
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.CONSTRUCT_INJECT_CLASS
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.constructFile
import com.oplus.recyclebin.test.mockContentValues
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test
import java.io.File

/**
 * [RecycleBinRecordHelper.extractContentValues] is already tested in [RecycleBinExportHelperTest]
 * So do not test it at here.
 */
class RecycleBinRecordHelperTest : Assert() {

    private companion object {
        private const val MIMETYPE_DEFAULT = "application/octet-stream"
        private const val TEST_ORIGIN_PATH = "/storage/emulated/0/Download/TEST_README.md"
        private const val TEST_IS_FILE = true
        private const val TEST_FILE_SIZE = 2881L
        private const val TEST_RECYCLE_DATE = 1712836751571L
        private const val TEST_RECYCLE_DIR = "/storage/emulated/0/.FileManagerRecycler"
        private const val TEST_DISPLAY_NAME = "TEST_README.md"
        private const val TEST_MODIFY_DATE = 1712738407L
        private const val TEST_RECYCLE_PATH = "$TEST_RECYCLE_DIR/746604632_TEST_README.md_temp"
        private const val TEST_RELATIVE = "Download/"
        private const val TEST_PARENT_ID = 4L
    }

    private fun prepareMockValues(exterior: Boolean = true): ContentValues =
        mockContentValues().apply {
            put(FileColumns.VOLUME_NAME, RecycleStore.VOLUME_EXTERNAL_PRIMARY)
            put(FileColumns.DATE_MODIFIED, TEST_MODIFY_DATE)
            put(FileColumns.DISPLAY_NAME, TEST_DISPLAY_NAME)
            put(FileColumns.RECYCLE_DATE, TEST_RECYCLE_DATE)
            put(FileColumns.RECYCLE_PATH, TEST_RECYCLE_PATH)
            put(FileColumns.ORIGIN_PATH, TEST_ORIGIN_PATH)
            put(FileColumns.RELATIVE_PATH, TEST_RELATIVE)
            if (exterior) {
                put(FileColumns.SIZE, TEST_FILE_SIZE)
                put(RecycleBinExportHelper.ExportParams.IS_FILE, TEST_IS_FILE)
                put(RecycleBinExportHelper.ExportParams.IS_CALL_FROM_EXTERIOR, true)
            }
        }

    @Test
    fun `should true when ensureNonNulFileColumns if has all data`() {
        // Given
        val values = prepareMockValues()

        // When & Then
        assertTrue(RecycleBinRecordHelper.ensureNonNulFileColumns(values))
    }

    @Test
    fun `should false when ensureNonNulFileColumns if missing data`(): Unit = arrayOf(
        FileColumns.RECYCLE_DATE,
        FileColumns.RECYCLE_PATH,
        FileColumns.RELATIVE_PATH,
        FileColumns.DISPLAY_NAME,
        FileColumns.VOLUME_NAME,
        FileColumns.DATE_MODIFIED
    ).forEach { missingKey ->
        // Given
        val values = prepareMockValues()
        values.remove(missingKey)

        // When & Then
        assertFalse(RecycleBinRecordHelper.ensureNonNulFileColumns(values))
    }

    @Test
    fun `should add media data when computerMediaTypeValues if from exterior`() {
        // SetUp
        mockkStatic(MimeTypeHelper::class, SdkUtils::class)
        mockkObject(MediaFileCompat, MimeTypeHelper)
        mockkStatic(CONSTRUCT_INJECT_CLASS)

        // Given
        val values = prepareMockValues(exterior = true)
        val recyclePath = values.getAsString(FileColumns.RECYCLE_PATH)
        every { SdkUtils.isAtLeastS() }.returns(true)
        every { MediaFileCompat.getMimeTypeForFile(recyclePath) } returns MIMETYPE_DEFAULT
        every { MimeTypeHelper.getTypeFromPath(recyclePath) } returns MimeTypeHelper.UNKNOWN_TYPE
        every { constructFile(recyclePath) } returns mockk()

        // When
        RecycleBinRecordHelper.computerMediaTypeValues(values, recyclePath)

        // Then
        assertEquals(FileColumns.MEDIA_TYPE_NONE, values.getAsInteger(FileColumns.MEDIA_TYPE))
        assertFalse(values.containsKey(FileColumns.IS_DRM))
        assertEquals(MIMETYPE_DEFAULT, values.getAsString(FileColumns.MIME_TYPE))
        verify(inverse = true) {
            constructFile(recyclePath)
        }

        // TearDown
        unmockkStatic(MimeTypeHelper::class, SdkUtils::class)
        unmockkObject(MimeTypeHelper, MediaFileCompat)
        unmockkStatic(CONSTRUCT_INJECT_CLASS)
    }

    @Test
    fun `should add media data when computerMediaTypeValues if from inner`() {
        // SetUp
        mockkStatic(MimeTypeHelper::class, SdkUtils::class)
        mockkObject(MediaFileCompat, MimeTypeHelper)
        mockkStatic(CONSTRUCT_INJECT_CLASS)

        // Given
        val values = prepareMockValues(exterior = false)
        val recyclePath = values.getAsString(FileColumns.RECYCLE_PATH)
        val originPath = values.getAsString(FileColumns.ORIGIN_PATH)
        every { SdkUtils.isAtLeastS() }.returns(true)
        every { MediaFileCompat.getMimeTypeForFile(recyclePath) } returns MIMETYPE_DEFAULT
        every { MimeTypeHelper.getTypeFromPath(recyclePath) } returns MimeTypeHelper.UNKNOWN_TYPE
        val testFile = mockk<File> {
            every { isFile } returns true
            every { isDirectory } returns false
            every { exists() } returns true
            every { length() } returns TEST_FILE_SIZE
        }
        every { constructFile(recyclePath) } returns testFile
        every { constructFile(originPath) } returns testFile

        // When
        RecycleBinRecordHelper.computerMediaTypeValues(values, recyclePath)

        // Then
        assertEquals(FileColumns.MEDIA_TYPE_NONE, values.getAsInteger(FileColumns.MEDIA_TYPE))
        assertFalse(values.containsKey(FileColumns.IS_DRM))
        assertEquals(MIMETYPE_DEFAULT, values.getAsString(FileColumns.MIME_TYPE))
        assertEquals(TEST_FILE_SIZE, values.getAsLong(FileColumns.SIZE))
        verify {
            constructFile(recyclePath)
        }

        // TearDown
        unmockkStatic(MimeTypeHelper::class, SdkUtils::class)
        unmockkObject(MediaFileCompat, MimeTypeHelper)
        unmockkStatic(CONSTRUCT_INJECT_CLASS)
    }

    @Test
    fun `should add parent data when computerDataValues`() {
        // Given
        val values = prepareMockValues()
        val recyclePath = values.getAsString(FileColumns.RECYCLE_PATH)
        val helper = mockk<DatabaseOpenHelper> {
            every { getParent(values, recyclePath) } returns TEST_PARENT_ID
        }

        // When
        RecycleBinRecordHelper.computerDataValues(helper, values, recyclePath)

        // Then
        assertEquals(TEST_PARENT_ID, values.getAsLong(FileColumns.RECYCLE_PARENT))
        assertTrue(values.containsKey(FileColumns.RECYCLE_BUCKET_ID))
    }
}