/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IExportedRecycleMethodTest.kt
 * Description:
 *     The test cases for IExportedRecycleMethod
 *
 * Version: 1.0
 * Date: 2024-04-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-04-11   1.0    Create this module
 *********************************************************************************/
package com.filemanager.recyclebin.db

import android.content.ContentValues
import android.os.Bundle
import com.filemanager.common.RecycleStore
import com.filemanager.recyclebin.utils.RootAdbCallingHelper
import com.oplus.recyclebin.test.mockBundle
import com.oplus.recyclebin.test.mockConstructBundle
import com.oplus.recyclebin.test.unmockConstructBundle
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class IExportedRecycleMethodTest : Assert() {

    private companion object {
        private const val TEST_RECYCLE_DIR = "/storage/emulated/0/.FileManagerRecycler"
        private const val TEST_ORIGIN_PATH = "/storage/emulated/0/Download/TEST_README.md"
        private const val TEST_IS_FILE = true
        private const val TEST_FILE_SIZE = 2881L
        private const val TEST_LAST_MODIFY = 1712738407000L
    }

    @Test
    fun `should has result when getRecycleDirectory`() {
        // SetUp
        mockkStatic(RecycleStore::getRecycleDirectory)
        mockConstructBundle()

        // Given
        every { RecycleStore.getRecycleDirectory(false) } returns TEST_RECYCLE_DIR

        // When
        val result = MethodGetRecycleDirectory.callMethod(mockk(), null, null)

        // Then
        assertEquals(
            TEST_RECYCLE_DIR,
            result?.getString(MethodGetRecycleDirectory.RESULT_RECYCLE_DIR)
        )

        // TearDown
        unmockkStatic(RecycleStore::getRecycleDirectory)
        unmockConstructBundle()
    }

    @Test
    fun `should call obtainRecycleContentValues when obtainRecycleContentValues if all args`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        val params = slot<RecycleBinExportHelper.RecycleContentParams>()
        val resultValues = mockk<ContentValues>()
        every {
            RecycleBinExportHelper.obtainRecycleContentValues(capture(params))
        } returns resultValues
        val resultBundle = mockk<Bundle>()
        every {
            RootAdbCallingHelper.obtainCallProviderResult(
                any(),
                MethodObtainRecycleContent.RESULT_CONTENT_VALUES,
                resultValues
            )
        } returns resultBundle
        val extras = mockBundle().apply {
            putString(MethodObtainRecycleContent.ARG_ORIGIN_PATH, TEST_ORIGIN_PATH)
            putBoolean(MethodObtainRecycleContent.ARG_IS_FILE, TEST_IS_FILE)
            putLong(MethodObtainRecycleContent.ARG_FILE_SIZE, TEST_FILE_SIZE)
            putLong(MethodObtainRecycleContent.ARG_LAST_MODIFIED, TEST_LAST_MODIFY)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertEquals(resultBundle, result)
        params.captured.run {
            assertEquals(TEST_ORIGIN_PATH, originPath)
            assertEquals(TEST_IS_FILE, isFile)
            assertEquals(TEST_FILE_SIZE, fileSize)
            assertEquals(TEST_LAST_MODIFY, lastModified)
            assertEquals(emptySet<String>(), existsRecycledFileNames)
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }

    @Test
    fun `should return null when obtainRecycleContentValues if no originPath`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        every { RecycleBinExportHelper.obtainRecycleContentValues(any()) } returns mockk()
        every { RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any()) } returns mockk()
        val extras = mockBundle().apply {
            putBoolean(MethodObtainRecycleContent.ARG_IS_FILE, TEST_IS_FILE)
            putLong(MethodObtainRecycleContent.ARG_FILE_SIZE, TEST_FILE_SIZE)
            putLong(MethodObtainRecycleContent.ARG_LAST_MODIFIED, TEST_LAST_MODIFY)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertNull(result)
        verify(inverse = true) {
            RecycleBinExportHelper.obtainRecycleContentValues(any())
            RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any())
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }

    @Test
    fun `should return null when obtainRecycleContentValues if no lastModified`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        every { RecycleBinExportHelper.obtainRecycleContentValues(any()) } returns mockk()
        every { RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any()) } returns mockk()
        val extras = mockBundle().apply {
            putString(MethodObtainRecycleContent.ARG_ORIGIN_PATH, TEST_ORIGIN_PATH)
            putBoolean(MethodObtainRecycleContent.ARG_IS_FILE, TEST_IS_FILE)
            putLong(MethodObtainRecycleContent.ARG_FILE_SIZE, TEST_FILE_SIZE)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertNull(result)
        verify(inverse = true) {
            RecycleBinExportHelper.obtainRecycleContentValues(any())
            RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any())
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }

    @Test
    fun `should return null when obtainRecycleContentValues if no isFile`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        every { RecycleBinExportHelper.obtainRecycleContentValues(any()) } returns mockk()
        every { RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any()) } returns mockk()
        val extras = mockBundle().apply {
            putString(MethodObtainRecycleContent.ARG_ORIGIN_PATH, TEST_ORIGIN_PATH)
            putLong(MethodObtainRecycleContent.ARG_FILE_SIZE, TEST_FILE_SIZE)
            putLong(MethodObtainRecycleContent.ARG_LAST_MODIFIED, TEST_LAST_MODIFY)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertNull(result)
        verify(inverse = true) {
            RecycleBinExportHelper.obtainRecycleContentValues(any())
            RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any())
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }

    @Test
    fun `should return null when obtainRecycleContentValues if isFile but no fileSize`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        every { RecycleBinExportHelper.obtainRecycleContentValues(any()) } returns mockk()
        every { RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any()) } returns mockk()
        val extras = mockBundle().apply {
            putString(MethodObtainRecycleContent.ARG_ORIGIN_PATH, TEST_ORIGIN_PATH)
            putBoolean(MethodObtainRecycleContent.ARG_IS_FILE, TEST_IS_FILE)
            putLong(MethodObtainRecycleContent.ARG_LAST_MODIFIED, TEST_LAST_MODIFY)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertNull(result)
        verify(inverse = true) {
            RecycleBinExportHelper.obtainRecycleContentValues(any())
            RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any())
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }

    @Test
    fun `should return null when obtainRecycleContentValues if isDir but has fileSize`() {
        // SetUp
        mockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )

        // Given
        every { RecycleBinExportHelper.obtainRecycleContentValues(any()) } returns mockk()
        every { RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any()) } returns mockk()
        val extras = mockBundle().apply {
            putString(MethodObtainRecycleContent.ARG_ORIGIN_PATH, TEST_ORIGIN_PATH)
            putBoolean(MethodObtainRecycleContent.ARG_IS_FILE, false)
            putLong(MethodObtainRecycleContent.ARG_FILE_SIZE, TEST_FILE_SIZE)
            putLong(MethodObtainRecycleContent.ARG_LAST_MODIFIED, TEST_LAST_MODIFY)
        }

        // When
        val result = MethodObtainRecycleContent.callMethod(mockk(), null, extras)

        // Then
        assertNull(result)
        verify(inverse = true) {
            RecycleBinExportHelper.obtainRecycleContentValues(any())
            RootAdbCallingHelper.obtainCallProviderResult(any(), any(), any())
        }

        // TearDown
        unmockkStatic(
            RecycleBinExportHelper::obtainRecycleContentValues,
            RootAdbCallingHelper::obtainCallProviderResult
        )
    }
}