/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.recyclebin.operation.listener

import android.content.Context
import android.content.res.Resources
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AlertDialog
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.CustomToast
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_NOT_ENOUGH_SPACE
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_REACH_LIMIT
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_SUCCESS
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class RestoreOperationListenerTest {

    private val context = mockk<Context>()

    @Before
    fun setUp() {
        mockkStatic(CustomToast::class)
        mockkStatic(MyApplication::class)
        every { MyApplication.appContext } returns context
        val resources = mockk<Resources>()
        every { context.resources } returns resources
        every { resources.getString(any<Int>(), any<String>(), any<String>()) } returns ""
        every { context.getString(any()) } returns ""
        every { CustomToast.showLong(any<String>()) } just runs
    }

    @After
    fun teardown() {
        unmockkStatic(MyApplication::class)
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call onProgressComplete`() {
        //given
        val parameters = mockk<OperationParameters>()
        val activity = mockk<ComponentActivity>()
        val listener = mockk<RecycleBinOperationListener>()
        val mainHandler = mockk<MainHandler>()
        val restoreOperationListener =
            spyk(RestoreOperationListener(activity, listener), recordPrivateCalls = true)
        every { restoreOperationListener.mMainHandler } returns mainHandler
        every { mainHandler.removeCallbacksAndMessages(any()) } just runs
        val progressDialog = mockk<AlertDialog>()
        every { restoreOperationListener.mProgressDialog } returns progressDialog
        every { progressDialog.isShowing } returns true
        every { progressDialog.dismiss() } just runs
        every { listener.onOperationCompleted(any(), any()) } just runs
        val result = OperationResult(1, 0, STATUS_NOT_ENOUGH_SPACE)
        //when
        restoreOperationListener.onProgressComplete(parameters, result)
        //then
        verify { CustomToast.showLong(any<String>()) }
    }

    @Test
    fun `should call right when call onProgressComplete if STATUS_REACH_LIMIT`() {
        //given
        val parameters = mockk<OperationParameters>()
        val activity = mockk<ComponentActivity>()
        val listener = mockk<RecycleBinOperationListener>()
        val mainHandler = mockk<MainHandler>()
        val restoreOperationListener =
            spyk(RestoreOperationListener(activity, listener), recordPrivateCalls = true)
        every { restoreOperationListener.mMainHandler } returns mainHandler
        every { mainHandler.removeCallbacksAndMessages(any()) } just runs
        val progressDialog = mockk<AlertDialog>()
        every { restoreOperationListener.mProgressDialog } returns progressDialog
        every { progressDialog.isShowing } returns true
        every { progressDialog.dismiss() } just runs
        every { listener.onOperationCompleted(any(), any()) } just runs
        val result = OperationResult(1, 0, STATUS_REACH_LIMIT)
        every { CustomToast.showLong(any<Int>()) } just runs
        //when
        restoreOperationListener.onProgressComplete(parameters, result)
        //then
        verify { CustomToast.showLong(any<Int>()) }
    }

    @Test
    fun `should call right when call onProgressComplete if STATUS_SUCCESS`() {
        //given
        val parameters = mockk<OperationParameters>()
        val activity = mockk<ComponentActivity>()
        val listener = mockk<RecycleBinOperationListener>()
        val mainHandler = mockk<MainHandler>()
        val restoreOperationListener =
            spyk(RestoreOperationListener(activity, listener), recordPrivateCalls = true)
        every { restoreOperationListener.mMainHandler } returns mainHandler
        every { mainHandler.removeCallbacksAndMessages(any()) } just runs
        val progressDialog = mockk<AlertDialog>()
        every { restoreOperationListener.mProgressDialog } returns progressDialog
        every { progressDialog.isShowing } returns true
        every { progressDialog.dismiss() } just runs
        every { listener.onOperationCompleted(any(), any()) } just runs
        val result = OperationResult(1, 0, STATUS_SUCCESS)
        every { CustomToast.showLong(any<Int>()) } just runs
        //when
        restoreOperationListener.onProgressComplete(parameters, result)
        //then
        verify(inverse = true) { CustomToast.showLong(any<Int>()) }
    }
}