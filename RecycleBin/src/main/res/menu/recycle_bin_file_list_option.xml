<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="AlwaysShowAction">

    <item
        android:id="@+id/actionbar_search"
        android:icon="@drawable/color_tool_menu_ic_search"
        android:title="@string/search_item"
        android:visible="true"
        app:showAsAction="always|collapseActionView" />
    <item
        android:id="@+id/actionbar_edit"
        android:title="@string/menu_recent_file_edit"
        android:icon="@drawable/color_tool_menu_ic_edit"
        android:visible="false"
        app:showAsAction="always|collapseActionView" />
    <item
        android:id="@+id/actionbar_sort"
        android:enabled="false"
        android:title="@string/menu_file_list_sort"
        android:visible="false"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_setting"
        android:title="@string/set_button_text"
        android:enabled="true"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_privacy"
        android:title="@string/privacy_button_text"
        android:enabled="true"
        app:showAsAction="never" />
</menu>