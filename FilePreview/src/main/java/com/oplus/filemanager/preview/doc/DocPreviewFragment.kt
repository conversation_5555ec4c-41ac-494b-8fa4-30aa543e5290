/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/10/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.preview.doc

import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class DocPreviewFragment : AbsPreviewFragment<FilePreviewViewModel>(R.layout.fragment_preview_doc) {

    private companion object {
        private const val TAG = "DocPreviewFragment"
    }

    override val fragmentInstance: Fragment = this
    override val logTag: String = TAG
    override val viewModelClass: Class<FilePreviewViewModel> = FilePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar
    private var filePreview: IDocFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
    FilePreviewTypeCheckHelper(context).isDocType(fileBean, TAG)

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }

    override fun onViewModelCreated(view: View, viewModel: FilePreviewViewModel) {
        operationsBar?.setOpenButtonText(com.filemanager.common.R.string.open)
        filePreview = prepareFilePreview(view, viewModel)
    }

    private fun prepareFilePreview(view: View, viewModel: FilePreviewViewModel): IDocFilePreview {
        val previewImpl = IDocFilePreview.obtain(viewLifecycleOwner, viewModel)
        val containerManager = DocPreviewContainerManager(view.findViewById(R.id.root_view))
        previewImpl.attachToContainer(containerManager)
        return previewImpl
    }

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }
}