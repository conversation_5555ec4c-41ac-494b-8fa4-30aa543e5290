<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:parentTag="android.widget.LinearLayout">

    <View
        android:id="@+id/preview_operations_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_background_height"
        android:layout_marginBottom="16dp"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/preview_operations_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/preview_operation_icon_size">

        <LinearLayout
            android:id="@+id/preview_operations_group"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentBottom="true"
            android:layout_toStartOf="@id/preview_open_button"
            android:orientation="horizontal"
            android:gravity="start|center_vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/preview_operation_label"
                android:layout_width="@dimen/preview_operation_icon_size"
                android:layout_height="@dimen/preview_operation_icon_size"
                android:layout_marginEnd="@dimen/preview_operation_icon_margin"
                android:background="@drawable/preview_operation_icon_bg"
                android:contentDescription="@string/menu_file_list_label"
                android:forceDarkAllowed="false"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/color_tool_menu_ic_label_new" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/preview_operation_send"
                android:layout_width="@dimen/preview_operation_icon_size"
                android:layout_height="@dimen/preview_operation_icon_size"
                android:layout_marginHorizontal="@dimen/preview_operation_icon_margin"
                android:background="@drawable/preview_operation_icon_bg"
                android:contentDescription="@string/doc_viewer_share_menu_title"
                android:forceDarkAllowed="false"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/color_tool_menu_ic_send" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/preview_operation_more"
                android:layout_width="@dimen/preview_operation_icon_size"
                android:layout_height="@dimen/preview_operation_icon_size"
                android:layout_marginHorizontal="@dimen/preview_operation_icon_margin"
                android:background="@drawable/preview_operation_icon_bg"
                android:contentDescription="@string/menu_file_list_more"
                android:forceDarkAllowed="false"
                android:padding="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/color_tool_menu_ic_more" />
        </LinearLayout>

        <com.coui.appcompat.button.COUIButton
            style="@style/Widget.COUI.Button.Small.ButtonNew"
            android:id="@+id/preview_open_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:minWidth="120dp"
            android:minHeight="@dimen/preview_operation_icon_size"
            android:gravity="center"
            android:text="@string/open"
            android:textSize="14sp"/>
    </RelativeLayout>

    <Space
        android:id="@+id/preview_operations_extend_nav_gesture_margin"
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:visibility="gone"/>
</merge>