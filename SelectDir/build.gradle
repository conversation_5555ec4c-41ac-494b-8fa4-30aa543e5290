plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.selectdir"

    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.google.material
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.responsiveui
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.input
    implementation libs.oplus.appcompat.button
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.rotateview
    implementation libs.oplus.appcompat.card
    implementation libs.oplus.appcompat.preference
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':FileOperate')
    implementation project(':framework:DFM')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}