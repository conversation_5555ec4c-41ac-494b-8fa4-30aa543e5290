/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/5/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.material.navigation.NavigationBarView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.isNetworkAvailable
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.SubPageAdMgr
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import java.io.File

class FileBrowserActivity : EncryptActivity(), NavigationInterface, NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, InstalledPermissionCallback, BaseVMActivity.PermissonCallBack, DragDropInterface,
    IRefreshActivityDataForCreateDir, IDraggingActionOperate, PerformClickDir {
    companion object {
        private const val TAG = "FileBrowserActivity"
        private const val TAG_FILE_BROWSER = "file_browser_tag"
    }

    var mAdManager: SubPageAdMgr? = null
    private var mCurrentPath: String? = null
    private var mTitle: String? = ""
    private var mRootView: ViewGroup? = null
    private var mFileBrowserFragmentNew: FileBrowserFragment? = null
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mIsSDPage = false
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private var mIsFromDetail = false
    var isFromShortcutFolder = false
    private val fileBrowserActivityCompanion: FileBrowserActivityCompanion by lazy {
        FileBrowserActivityCompanion(this)
    }

    override fun getLayoutResId(): Int {
        return R.layout.filebrowser_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        val intent = intent
        if (null == intent) {
            Log.v(TAG, "intent null")
            finish()
            return
        }
        mCurrentPath = fileBrowserActivityCompanion.getCurrentPath(intent)
        mTitle = fileBrowserActivityCompanion.getTitle(intent)
        mIsSDPage = KtUtils.checkIsSDPath(this, mCurrentPath + File.separator)
        mIsFromDetail = IntentUtils.getBoolean(intent, KtConstants.FROM_DETAIL, false)
        isFromShortcutFolder = IntentUtils.getBoolean(intent, KtConstants.FROM_SHORTCUT_FOLDER, false)

        mRootView = findViewById(R.id.coordinator_layout)
        setFragment()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        return if (mFileBrowserFragmentNew != null) {
            mFileBrowserFragmentNew?.onCreateOptionsMenu(menu, menuInflater)
            true
        } else {
            super.onCreateOptionsMenu(menu)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (mFileBrowserFragmentNew != null) {
            mFileBrowserFragmentNew!!.onMenuItemSelected(item)
        } else {
            super.onOptionsItemSelected(item)
        }
    }

    override fun initData() {
        if (AdvertManager.isAdEnabled() && SubPageAdMgr.isSupportPhotoStorageAd()
            && isNetworkAvailable(appContext)//无网络、网络返回超时，不展示广告位
        ) {
            mAdManager = SubPageAdMgr(<EMAIL>)
        }
    }

    override fun startObserve() {
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
        cloudConfigApi?.initCloudConfig()
        mRootView?.post {
            mFileBrowserFragmentNew?.onResumeLoadData()
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage  $action-->$data ;  $mCurrentPath")
        // File system not need to care the media scan result
        if (Intent.ACTION_MEDIA_SCANNER_FINISHED == action) {
            return
        }

        fun refreshCurrentPage() {
            mFileBrowserFragmentNew?.onResumeLoadData()
        }

        if ((mCurrentPath != null) && mIsSDPage) {
            if ((Intent.ACTION_MEDIA_BAD_REMOVAL == action) || (Intent.ACTION_MEDIA_REMOVED == action)
                    || (Intent.ACTION_MEDIA_UNMOUNTED == action) || (Intent.ACTION_MEDIA_EJECT == action)) {
                var uriPath: String? = null
                try {
                    data?.let {
                        uriPath = Uri.parse(it).path
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "refreshCurrentPage data err: ${e.message}")
                }
                if (uriPath.isNullOrEmpty() || (mCurrentPath?.startsWith(uriPath!!) == true)) {
                    finish()
                    return
                }
            }
        }
        refreshCurrentPage()
    }

    private fun setFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_FILE_BROWSER)
        if ((fragment == null) || (fragment !is FileBrowserFragment)) {
            fragment = FileBrowserFragment()
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.P_CURRENT_PATH, mCurrentPath)
        bundle.putString(KtConstants.P_TITLE, mTitle)
        bundle.putBoolean(KtConstants.FROM_DETAIL, mIsFromDetail)
        bundle.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, isFromShortcutFolder)
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.content, fragment, TAG_FILE_BROWSER)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mFileBrowserFragmentNew = fragment

    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mFileBrowserFragmentNew?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        mSelectPathController.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onBackPressed() {
        if ((mFileBrowserFragmentNew as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mFileBrowserFragmentNew?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mFileBrowserFragmentNew?.onNavigationItemSelected(menuItem) ?: false
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val path = fileBrowserActivityCompanion.getCurrentPath(intent)
        Log.d(TAG, "onNewIntent = $path")
        mCurrentPath = path
        mFileBrowserFragmentNew?.setCurrentFromOtherSide(path)
    }

    fun exitShortcutFolder() {
        mFileBrowserFragmentNew?.resetIsFromShortcut()
        isFromShortcutFolder = false
        intent.extras?.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, false)
        mFileBrowserFragmentNew?.setCurrentFromOtherSide(getCurrentPath())
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mFileBrowserFragmentNew?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mFileBrowserFragmentNew?.fromSelectPathResult(code, paths)
    }

    override fun getActivityType(): Int {
        return InstalledPermissionCallback.FILE_BROWSER_ACTIVITY
    }

    fun getCurrentPath(): String {
        return mFileBrowserFragmentNew?.getCurrentPath() ?: ""
    }

    override fun onRefreshData() {
        Log.d(TAG, "onRefreshData")
        mFileBrowserFragmentNew?.onRefreshData()
    }

    override fun handleNoStoragePermission() {
        mFileBrowserFragmentNew?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun onRefreshDataForDir(path: String) {
        mFileBrowserFragmentNew?.refreshDataForDir(path, CategoryHelper.CATEGORY_FILE_BROWSER)
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean) {}
    override fun renameToLabel(newName: String, labelId: Long) {}
    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mFileBrowserFragmentNew?.handleDragScroll(event) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mFileBrowserFragmentNew?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mFileBrowserFragmentNew?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return getCurrentPath()
    }

    override fun onClickDir(path: String) {
        mFileBrowserFragmentNew?.onClickDir(path)
    }
}

