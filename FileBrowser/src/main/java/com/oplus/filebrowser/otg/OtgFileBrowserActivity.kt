/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser.otg
 * * Version     : 1.0
 * * Date        : 2020/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.otg

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filebrowser.R
import com.oplus.filebrowser.morestorage.MoreStorageFragment
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController

class OtgFileBrowserActivity : EncryptActivity(), NavigationInterface, NavigationBarView.OnItemSelectedListener, TransformNextFragmentListener,
    DragDropInterface, IRefreshActivityDataForCreateDir, IDraggingActionOperate, PerformClickDir {
    companion object {
        const val OTG_LIST_PATH = "OTG_LIST_PATH"
        private const val TAG = "OtgFileBrowserActivity"
        private const val TAG_FILE_BROWSER = "file_browser_tag"
        private const val CATEGORY_OTG_BROWSER = "category_otg_browser"
    }

    private var mTitle: String? = ""
    private var mRootView: ViewGroup? = null
    private var mOtgFileFragment: OtgFileFragment? = null
    private var mMoreStorageFragment: MoreStorageFragment? = null
    private var mOtgPaths: ArrayList<String>? = null
    private var mCurrentFragment: BaseVMFragment<*>? = null
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mCurrentOtgPath: String? = null
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }

    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private var mIsFromDetail = false

    override fun getLayoutResId(): Int {
        return R.layout.filebrowser_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        val intent = intent
        if (null == intent) {
            Log.v(TAG, "intent null")
            finish()
            return
        }
        mTitle = IntentUtils.getString(intent, Constants.TITLE)
        mOtgPaths = IntentUtils.getStringArrayList(intent, OTG_LIST_PATH)
        if (mOtgPaths.isNullOrEmpty()) {
            Log.v(TAG, "mOtgPaths isNullOrEmpty")
            finish()
            return
        }
        val resId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
        mTitle = if (resId > 0) {
            getString(resId)
        } else {
            IntentUtils.getString(intent, Constants.TITLE)
        }
        if (TextUtils.isEmpty(mTitle)) {
            mTitle = resources.getString(com.filemanager.common.R.string.storage_otg)
        }
        mRootView = findViewById(R.id.coordinator_layout)
        mIsFromDetail = IntentUtils.getBoolean(intent, KtConstants.FROM_DETAIL, false)
        setFragment()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        return when (mCurrentFragment) {
            is OtgFileFragment -> {
                (mCurrentFragment as OtgFileFragment).onCreateOptionsMenu(menu, menuInflater)
                true
            }
            is MoreStorageFragment -> {
                (mCurrentFragment as MoreStorageFragment).onCreateOptionsMenu(menu, menuInflater)
                true
            }
            else -> {
                super.onCreateOptionsMenu(menu)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (mCurrentFragment) {
            is OtgFileFragment -> {
                (mCurrentFragment as OtgFileFragment).onMenuItemSelected(item)
            }
            is MoreStorageFragment -> {
                (mCurrentFragment as MoreStorageFragment).onMenuItemSelected(item)
            }
            else -> {
                super.onOptionsItemSelected(item)
            }
        }
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        mRootView?.post {
            refreshLoadFragment()
        }
    }

    private fun refreshLoadFragment() {
        if (mCurrentFragment is OtgFileFragment) {
            mOtgFileFragment?.onResumeLoadData()
        } else if (mCurrentFragment is MoreStorageFragment) {
            mOtgFileFragment?.onResumeLoadData()
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage $action-->$data ; $mCurrentOtgPath")
        if ((Intent.ACTION_MEDIA_BAD_REMOVAL == action) || (Intent.ACTION_MEDIA_REMOVED == action)
                || (Intent.ACTION_MEDIA_UNMOUNTED == action) || (Intent.ACTION_MEDIA_EJECT == action)) {
            var uriPath: String? = null
            try {
                data?.let {
                    uriPath = Uri.parse(it).path
                }
            } catch (e: Exception) {
                Log.w(TAG, "refreshCurrentPage data err: ${e.message}")
            }
            if (uriPath.isNullOrEmpty() || (mCurrentOtgPath?.startsWith(uriPath!!) == true)) {
                finish()
                return
            }
        }
        refreshLoadFragment()
    }

    private fun setFragment() {
        Log.d(TAG, "setFragment mOtgPaths ${mOtgPaths?.size}")
        if (mOtgPaths!!.size > 1) {
            val mFragment = MoreStorageFragment()
            val bundle = Bundle()
            bundle.putStringArrayList(KtConstants.P_PATH_LIST, mOtgPaths)
            bundle.putString(KtConstants.P_TITLE, MyApplication.sAppContext.getString(com.filemanager.common.R.string.storage_otg))
            mFragment.arguments = bundle
            val ft = supportFragmentManager.beginTransaction()
            ft.replace(R.id.content, mFragment, TAG_FILE_BROWSER)
            ft.show(mFragment)
            ft.commitAllowingStateLoss()
            mMoreStorageFragment = mFragment
            mCurrentFragment = mMoreStorageFragment
        } else {
            var mFragment = supportFragmentManager.findFragmentByTag(TAG_FILE_BROWSER)
            if ((mFragment == null) || (mFragment !is OtgFileFragment)) {
                mFragment = OtgFileFragment()
            }
            val bundle = Bundle()
            mCurrentOtgPath = mOtgPaths!![0]
            bundle.putString(KtConstants.P_CURRENT_PATH, mCurrentOtgPath)
            bundle.putString(KtConstants.P_TITLE, mTitle)
            bundle.putBoolean(KtConstants.FROM_DETAIL, mIsFromDetail)
            mFragment.arguments = bundle
            val ft = supportFragmentManager.beginTransaction()
            ft.replace(R.id.content, mFragment, TAG_FILE_BROWSER)
            ft.show(mFragment)
            ft.commitAllowingStateLoss()
            mOtgFileFragment = mFragment
            mCurrentFragment = mOtgFileFragment
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mOtgFileFragment?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        mSelectPathController.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }


    override fun onBackPressed() {
        if ((mCurrentFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mOtgFileFragment?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mOtgFileFragment?.onNavigationItemSelected(menuItem) ?: false
    }


    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        mOtgPaths = IntentUtils.getStringArrayList(intent, OTG_LIST_PATH)
        mOtgPaths?.let {
            if (it.size > 0) {
                mOtgFileFragment?.setCurrentFromOtherSide(it[0])
            }
        }
    }

    fun toOtgFragment(path: String?) {
        mCurrentOtgPath = path
        val ft = supportFragmentManager.beginTransaction()
        if (mOtgFileFragment == null) {
            mOtgFileFragment = OtgFileFragment()
            val bundle = Bundle()
            bundle.putString(KtConstants.P_CURRENT_PATH, mCurrentOtgPath)
            bundle.putString(KtConstants.P_TITLE, mTitle)
            bundle.putBoolean(KtConstants.P_INIT_LOAD, true)
            bundle.putBoolean(KtConstants.FROM_OTG_LIST, true)
            mOtgFileFragment!!.arguments = bundle
            ft.add(R.id.content, mOtgFileFragment!!, TAG_FILE_BROWSER).addToBackStack(CATEGORY_OTG_BROWSER)
        }
        mOtgFileFragment?.let {
            if (mCurrentFragment != null) {
                ft.hide(mCurrentFragment!!)
            }
            ft.show(it)
            ft.commitAllowingStateLoss()
            mCurrentFragment = it
            mOtgFileFragment?.onResumeLoadData()
        }
    }

    fun backToMoreStorage() {
        Log.d(TAG, "backToOTGMoreStorage")
        supportFragmentManager.popBackStack()
        mOtgFileFragment = null
        mCurrentFragment = mMoreStorageFragment
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mOtgFileFragment?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mOtgFileFragment?.fromSelectPathResult(code, paths)
    }

    fun getCurrentPath(): String {
        return mOtgFileFragment?.getCurrentPath() ?: ""
    }

    override fun onRefreshData() {
        Log.d(TAG, "onRefreshData")
        mOtgFileFragment?.onRefreshData()
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun onRefreshDataForDir(path: String) {
        mOtgFileFragment?.refreshDataForDir(path, CategoryHelper.CATEGORY_OTG_BROWSER)
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean) {}

    override fun renameToLabel(newName: String, labelId: Long) {}
    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mOtgFileFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mOtgFileFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mOtgFileFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return getCurrentPath()
    }

    override fun onClickDir(path: String) {
        mOtgFileFragment?.onClickDir(path)
    }
}
