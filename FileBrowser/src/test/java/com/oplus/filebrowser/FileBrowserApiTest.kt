/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileBrowserApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.app.Activity
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtUtils
import com.oplus.filebrowser.morestorage.MoreStorageFragment
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.categorydfm.ICategoryDFMApi
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkConstructor
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class FileBrowserApiTest {

    private val mainAction = mockk<IMain>()
    private val categoryDFMApi = mockk<ICategoryDFMApi>()

    private val koinApp = koinApplication {
        modules(module {
            single { mainAction }
            single { categoryDFMApi }
        })
    }

    @Before
    fun setUp() {
        startKoin(koinApp)
    }

    @After
    fun rearDown() {
        stopKoin()
    }

    @Test
    fun `should call startFragment when call startOtgBrowserFragment`() {
        //given
        val activity = mockk<Activity>()
        val pathList = ArrayList<String>()
        pathList.add("sdcard/path1")
        pathList.add("sdcard/path2")
        every { mainAction.startFragment(activity, any(), any()) } returns true
        every { activity.getString(any()) } returns ""
        //when
        FileBrowserApi.startOtgBrowserFragment(
            activity,
            pathList,
            fromDetail = false,
            fromOTGList = false
        )
        //then
        verify { mainAction.startFragment(activity, CategoryHelper.CATEGORY_MORE_STORAGE, any()) }
    }

    @Test
    fun `should call startFragment when call startOtgBrowserFragment if has only path`() {
        //given
        val activity = mockk<Activity>()
        val pathList = ArrayList<String>()
        pathList.add("sdcard/path1")
        every { mainAction.startFragment(activity, any(), any()) } returns true
        every { activity.getString(any()) } returns ""
        //when
        FileBrowserApi.startOtgBrowserFragment(
            activity,
            pathList,
            fromDetail = false,
            fromOTGList = false
        )
        //then
        verify { mainAction.startFragment(activity, CategoryHelper.CATEGORY_OTG_BROWSER, any()) }
    }

    @Test
    fun `should call startFragment when call startSdCardBrowserFragment`() {
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { mainAction.startFragment(activity, any(), any()) } returns true
        every { activity.getString(any()) } returns ""
        //when
        FileBrowserApi.startSdCardBrowserFragment(activity, path, fromDetail = false)
        //then
        verify { mainAction.startFragment(activity, CategoryHelper.CATEGORY_SDCARD_BROWSER, any()) }
    }

    @Test
    fun `should call startFragment when call startFileBrowserFragment`() {
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { mainAction.startFragment(activity, any(), any()) } returns true
        every { activity.getString(any()) } returns ""
        //when
        FileBrowserApi.startFileBrowserFragment(activity, path, fromDetail = false)
        //then
        verify { mainAction.startFragment(activity, CategoryHelper.CATEGORY_FILE_BROWSER, any()) }
    }

    @Test
    fun `should call startActivity when call startOtgBrowserActivity`() {
        //given
        val activity = mockk<Activity>()
        val pathList = ArrayList<String>()
        pathList.add("sdcard/path1")
        pathList.add("sdcard/path2")
        every { activity.getString(any()) } returns ""
        every { activity.startActivity(any()) } just runs
        //when
        FileBrowserApi.startOtgBrowserActivity(
            activity,
            pathList,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify { activity.startActivity(any()) }
    }

    @Test
    fun `should call startActivity when call startOtgBrowserActivity if path is null`() {
        //given
        val activity = mockk<Activity>()
        val pathList = ArrayList<String>()
        every { activity.getString(any()) } returns ""
        every { activity.startActivity(any()) } just runs
        //when
        FileBrowserApi.startOtgBrowserActivity(
            activity,
            pathList,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify(inverse = true) { activity.startActivity(any()) }
    }

    @Test
    fun `should call showShort when call toFileBrowserActivity if is not Mounted`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns false
        every { CustomToast.showShort(any<Int>()) } just runs
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify(inverse = true) { activity.finish() }
        verify { CustomToast.showShort(any<Int>()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
    }

    @Test
    fun `should call startOtgBrowserFragment when call toFileBrowserActivity if is otg path and ParentChild mode`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        mockkStatic(KtUtils::class)
        mockkStatic(FileBrowserApi::class)
        mockkObject(FileBrowserApi)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns true
        every { KtUtils.checkIsOTGPath(activity, path) } returns true
        every { mainAction.isParentChildActivity(activity) } returns true
        every { mainAction.getMainTab(activity) } returns 1
        every { mainAction.isMainActivity(activity) } returns false
        every { FileBrowserApi.isFileBrowserActivity(activity) } returns true
        every { FileBrowserApi.startOtgBrowserFragment(activity, any(), any(), any()) } just runs
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify { activity.finish() }
        verify { FileBrowserApi.startOtgBrowserFragment(activity, any(), any(), any()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
        unmockkStatic(FileBrowserApi::class)
        unmockkObject(FileBrowserApi)
    }

    @Test
    fun `should call startOtgBrowserActivity when call toFileBrowserActivity if is otg path and not ParentChild mode`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        mockkStatic(KtUtils::class)
        mockkStatic(FileBrowserApi::class)
        mockkObject(FileBrowserApi)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns true
        every { KtUtils.checkIsOTGPath(activity, path) } returns true
        every { mainAction.isParentChildActivity(activity) } returns false
        every { mainAction.getMainTab(activity) } returns 0
        every { mainAction.isMainActivity(activity) } returns false
        every { FileBrowserApi.isFileBrowserActivity(activity) } returns true
        every { FileBrowserApi.startOtgBrowserActivity(activity, any(), any(), any()) } just runs
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify { activity.finish() }
        verify(inverse = true) { FileBrowserApi.startOtgBrowserFragment(activity, any(), any(), any()) }
        verify { FileBrowserApi.startOtgBrowserActivity(activity, any(), any(), any()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
        mockkStatic(KtUtils::class)
        unmockkStatic(FileBrowserApi::class)
        unmockkObject(FileBrowserApi)
    }

    @Test
    fun `should call startDFM when call toFileBrowserActivity if is dfm path`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        mockkStatic(KtUtils::class)
        mockkStatic(FileBrowserApi::class)
        mockkObject(FileBrowserApi)
        mockkStatic(DFMManager::class)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns true
        every { KtUtils.checkIsOTGPath(activity, path) } returns false
        every { KtUtils.checkIsDfmPath(path) } returns true
        every { categoryDFMApi.startDFM(any(), any(), any(), any()) } just runs
        every { mainAction.isMainActivity(activity) } returns false
        every { DFMManager.getDFSDeviceName() } returns "test"
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify(inverse = true) { activity.finish() }
        verify { categoryDFMApi.startDFM(any(), any(), any(), any()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
        unmockkStatic(FileBrowserApi::class)
        unmockkObject(FileBrowserApi)
        unmockkStatic(DFMManager::class)
    }

    @Test
    fun `should call startFileBrowserFragment when call toFileBrowserActivity if is normal and parent child mode`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        mockkStatic(KtUtils::class)
        mockkObject(KtUtils)
        mockkStatic(FileBrowserApi::class)
        mockkObject(FileBrowserApi)
        mockkStatic(DFMManager::class)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns true
        every { KtUtils.checkIsOTGPath(activity, path) } returns false
        every { KtUtils.checkIsDfmPath(path) } returns false
        every { FileBrowserApi.startFileBrowserFragment(activity, any(), any()) } just runs
        every { DFMManager.getDFSDeviceName() } returns "test"
        every { FileBrowserApi.isFileBrowserActivity(activity) } returns true
        every { mainAction.isParentChildActivity(activity) } returns true
        every { mainAction.getMainTab(activity) } returns 1
        every { mainAction.isMainActivity(activity) } returns false
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify(inverse = true) { activity.finish() }
        verify { FileBrowserApi.startFileBrowserFragment(activity, any(), any()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
        unmockkStatic(FileBrowserApi::class)
        unmockkObject(FileBrowserApi)
        unmockkObject(KtUtils)
        unmockkStatic(DFMManager::class)
    }

    @Test
    fun `should call startFileBrowserActivity when call toFileBrowserActivity if is normal and parent child mode`() {
        //setup
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(CustomToast::class)
        mockkStatic(KtUtils::class)
        mockkObject(KtUtils)
        mockkStatic(FileBrowserApi::class)
        mockkObject(FileBrowserApi)
        mockkStatic(DFMManager::class)
        //given
        val activity = mockk<Activity>()
        val path = "sdcard/path1"
        every { activity.getString(any()) } returns ""
        every { activity.finish() } just runs
        every { VolumeEnvironment.isMounted(activity, path) } returns true
        every { KtUtils.checkIsOTGPath(activity, path) } returns false
        every { KtUtils.checkIsDfmPath(path) } returns false
        every { FileBrowserApi.startFileBrowserActivity(activity, any(), any()) } just runs
        every { DFMManager.getDFSDeviceName() } returns "test"
        every { FileBrowserApi.isFileBrowserActivity(activity) } returns true
        every { mainAction.isParentChildActivity(activity) } returns false
        every { mainAction.getMainTab(activity) } returns 0
        every { mainAction.isMainActivity(activity) } returns false
        every { mainAction.getPreviewListFragment(activity) } returns null
        //when
        FileBrowserApi.toFileBrowserActivity(
            activity,
            path,
            needClearTop = false,
            fromDetail = false
        )
        //then
        verify(inverse = true) { activity.finish() }
        verify { FileBrowserApi.startFileBrowserActivity(activity, any(), any()) }
        unmockkStatic(CustomToast::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
        unmockkStatic(FileBrowserApi::class)
        unmockkObject(FileBrowserApi)
        unmockkObject(KtUtils)
        unmockkStatic(DFMManager::class)
    }

    @Test
    fun `should return right fragment when call getOTGFragment if CATEGORY_MORE_STORAGE`() {
        //given
        val activity = mockk<Activity>()
        mockkConstructor(MoreStorageFragment::class)
        val type = CategoryHelper.CATEGORY_MORE_STORAGE
        //when
        val result = FileBrowserApi.getOTGFragment(activity, type)
        //then
        Assert.assertTrue(result is MoreStorageFragment)
        unmockkConstructor(MoreStorageFragment::class)
    }

    @Test
    fun `should return right fragment when call getOTGFragment`() {
        //given
        val activity = mockk<Activity>()
        mockkConstructor(MoreStorageFragment::class)
        val type = 0
        //when
        val result = FileBrowserApi.getOTGFragment(activity, type)
        //then
        Assert.assertTrue(result is PreviewCombineFragment)
        unmockkConstructor(MoreStorageFragment::class)
    }
}