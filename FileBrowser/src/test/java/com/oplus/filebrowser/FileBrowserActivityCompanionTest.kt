/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : FileBrowserActivityCompanion
 * * Version     : 1.0
 * * Date        : 2024/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.ResourceUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class FileBrowserActivityCompanionTest {

    private lateinit var activity: FileBrowserActivity
    private lateinit var appContext: Context

    @Before
    fun setUp() {
        activity = mockk()
        appContext = mockk()
        MyApplication.init(appContext)

        mockkStatic(IntentUtils::class)
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(HiddenFileHelper::class)
    }

    @After
    fun after() {
        unmockkStatic(IntentUtils::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(HiddenFileHelper::class)
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is null`() {
        //given
        val currentPath = null
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is empty`() {
        //given
        val currentPath = ""
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is illegal`() {
        //given
        val currentPath = "/storage/emulated/0/../Download"
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is hidde but not allow show`() {
        //given
        val currentPath = "/storage/emulated/0/.Download"
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        mockkStatic(HiddenFileHelper::class)
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns false
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is hidde and allow show`() {
        //given
        val currentPath = "/storage/emulated/0/.Download"
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns true
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is not exit`() {
        //given
        val fileBrowserActivityCompanion = spyk(FileBrowserActivityCompanion(activity), recordPrivateCalls = true)
        val currentPath = "/storage/emulated/0/Download"
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        val file = mockk<File>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        every { fileBrowserActivityCompanion invoke "file" withArguments listOf(currentPath) } returns file
        every { file.exists() } returns false
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns true
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is exit and is dir`() {
        //given
        val fileBrowserActivityCompanion = spyk(FileBrowserActivityCompanion(activity), recordPrivateCalls = true)
        val currentPath = "/storage/emulated/0/Download"
        val internalSdPath = "/storage/emulated/0"
        val intent = mockk<Intent>()
        val file = mockk<File>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        every { fileBrowserActivityCompanion invoke "file" withArguments listOf(currentPath) } returns file
        every { file.exists() } returns true
        every { file.isDirectory } returns true
        every { file.absolutePath } returns currentPath
        every { fileBrowserActivityCompanion.formatFilePath(currentPath) } returns currentPath
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns true
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, currentPath)
    }


    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is exit and is file`() {
        //given
        val fileBrowserActivityCompanion = spyk(FileBrowserActivityCompanion(activity), recordPrivateCalls = true)
        val currentPath = "/storage/emulated/0/Download/test.doc"
        val internalSdPath = "/storage/emulated/0"
        val parent = "/storage/emulated/0/"
        val intent = mockk<Intent>()
        val file = mockk<File>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        every { fileBrowserActivityCompanion invoke "file" withArguments listOf(currentPath) } returns file
        every { file.exists() } returns true
        every { file.isDirectory } returns false
        every { file.parent } returns parent
        every { fileBrowserActivityCompanion.formatFilePath(any()) } returns parent
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns true
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, parent)
    }

    @Test
    fun `should call getInternalSdPath when call getCurrentPath if currentPath is exit and is file parent is null`() {
        //given
        val fileBrowserActivityCompanion = spyk(FileBrowserActivityCompanion(activity), recordPrivateCalls = true)
        val currentPath = "/storage/emulated/0/Download/test.doc"
        val internalSdPath = "/storage/emulated/0"
        val parent = "/storage/emulated/0/"
        val intent = mockk<Intent>()
        val file = mockk<File>()
        every { IntentUtils.getString(intent, KtConstants.CURRENT_DIR) } returns currentPath
        every { VolumeEnvironment.getInternalSdPath(appContext) } returns internalSdPath
        every { fileBrowserActivityCompanion invoke "file" withArguments listOf(currentPath) } returns file
        every { file.exists() } returns true
        every { file.isDirectory } returns false
        every { file.parent } returns null
        every { fileBrowserActivityCompanion.formatFilePath(currentPath) } returns parent
        every { HiddenFileHelper.isNeedShowHiddenFile() } returns true
        //when
        val result = fileBrowserActivityCompanion.getCurrentPath(intent)
        //then
        Assert.assertEquals(result, internalSdPath)
        verify { VolumeEnvironment.getInternalSdPath(appContext) }
    }

    @Test
    fun `should return right when call getTitle if resId is -1`() {
        //given
        val expect = "title"
        val intent = mockk<Intent>()
        mockkStatic(ResourceUtils::class)
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        every { IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1) } returns -1
        every { IntentUtils.getString(intent, Constants.TITLE) } returns expect
        //when
        val result = fileBrowserActivityCompanion.getTitle(intent)
        //then
        Assert.assertEquals(result, expect)
        verify { IntentUtils.getString(intent, Constants.TITLE) }
        //teardown
        unmockkStatic(ResourceUtils::class)
    }

    @Test
    fun `should return right when call getTitle if resId is valid`() {
        //given
        val expect = "title"
        val intent = mockk<Intent>()
        mockkStatic(ResourceUtils::class)
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        every { IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1) } returns 111
        every { ResourceUtils.getString(activity, 111) } returns expect
        //when
        val result = fileBrowserActivityCompanion.getTitle(intent)
        //then
        Assert.assertEquals(result, expect)
        verify { ResourceUtils.getString(activity, 111) }
        //teardown
        unmockkStatic(ResourceUtils::class)
    }

    @Test
    fun `should return right when call getTitle if title is null`() {
        //given
        val expect = "test"
        val intent = mockk<Intent>()
        mockkStatic(ResourceUtils::class)
        val resource = mockk<Resources>()
        every { activity.resources } returns resource
        every { resource.getString(com.filemanager.common.R.string.string_all_files) } returns expect
        val fileBrowserActivityCompanion = FileBrowserActivityCompanion(activity)
        every { IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1) } returns -1
        every { IntentUtils.getString(intent, Constants.TITLE) } returns null
        //when
        val result = fileBrowserActivityCompanion.getTitle(intent)
        //then
        Assert.assertEquals(result, expect)
        //teardown
        unmockkStatic(ResourceUtils::class)
    }
}