/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : MockDownloadMgr 模拟下载测试类
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.remotedevice.mock

import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.LockSupport
import kotlin.concurrent.thread

object MockDownloadMgr {

    private const val TAG = "MockDownloadMgr"
    private const val PROGRESS_100 = 100
    private const val ONE_SECOND = 1000L

    private var downloadThread: Thread? = null
    private var loop: AtomicBoolean = AtomicBoolean(true)
    private var lastProgress = 0
    private var exitDownload = false

    @JvmStatic
    fun startDownload(downloadFilePaths: List<String>, callback: DownloadRemoteFileCallback? = null): Int {
        Log.e(TAG, "startDownload")
        thread {
            downloadThread = Thread.currentThread()
            while (!exitDownload && lastProgress < PROGRESS_100) {
                if (!loop.get()) {
                    LockSupport.park()
                } else {
                    lastProgress++
                    callback?.onProgress(lastProgress)
                    progressEachFile(lastProgress, downloadFilePaths, callback)
                    Thread.sleep(ONE_SECOND)
                }
            }
            if (!exitDownload) {
                callback?.onResult(OperateCallback.OPERATE_BEGIN_DOWNLOAD, ResultConstant.RESULT_CODE_FAIL_NORMAL)
                lastProgress = 0
            }
        }
        return 0
    }

    @JvmStatic
    private fun progressEachFile(progress: Int, downloadFilePaths: List<String>, callback: DownloadRemoteFileCallback?) {
        val size = downloadFilePaths.size
        val eachFileProgress = PROGRESS_100 / size
        var currentFileProgress = progress % eachFileProgress
        var currentFileIndex = progress / eachFileProgress
        if (currentFileProgress == 0) {
            currentFileIndex--
            currentFileProgress = eachFileProgress
        }
        currentFileIndex = Math.min(currentFileIndex, size - 1)
        Log.d(TAG, "progressEachFile progress:$progress size:$size each:$eachFileProgress -> current:$currentFileIndex x $currentFileProgress %")
        callback?.onProgressEachFile(
            downloadFilePaths[currentFileIndex],
            DownloadRemoteFileCallback.DOWNLOADING,
            currentFileProgress.toFloat()
        )
    }

    @JvmStatic
    fun pauseDownload(callback: OperateCallback? = null) {
        Log.e(TAG, "pauseDownload")
        loop.set(false)
        callback?.onResult(OperateCallback.OPERATE_PAUSE_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS)
    }

    @JvmStatic
    fun continueDownload(callback: OperateCallback? = null) {
        Log.e(TAG, "continueDownload")
        loop.set(true)
        LockSupport.unpark(downloadThread)
        callback?.onResult(OperateCallback.OPERATE_CONTINUE_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS)
    }

    @JvmStatic
    fun cancelDownload(callback: OperateCallback?) {
        Log.e(TAG, "cancelDownload")
        LockSupport.unpark(downloadThread)
        exitDownload = true
        callback?.onResult(OperateCallback.OPERATE_CANCEL_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS)
    }
}