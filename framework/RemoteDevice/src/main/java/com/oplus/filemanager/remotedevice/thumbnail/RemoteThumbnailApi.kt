/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteThumbnailApi.kt
 ** Description: RemoteThumbnailApi
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.remotedevice.thumbnail

import android.net.Uri
import com.oplus.filemanager.interfaze.remotedevice.IRemotePicListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnailListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnailsListener
import com.oplus.filemanager.remotedevice.RemoteDeviceManager

object RemoteThumbnailApi : IRemoteThumbnail {
    override fun getFileThumbnails(
        deviceID: String,
        thumbnailPaths: List<String>,
        width: Int,
        height: Int,
        callback: IRemoteThumbnailsListener
    ) {
        RemoteDeviceManager.getFileThumbnails(deviceID, thumbnailPaths, width, height, callback)
    }

    override fun getFileThumbnails(
        deviceID: String,
        thumbnailPath: String,
        width: Int,
        height: Int,
        callback: IRemoteThumbnailListener
    ) {
        RemoteDeviceManager.getFileThumbnails(deviceID, thumbnailPath, width, height, callback)
    }

    override fun getPicThumbnails(
        devicesId: String,
        picPath: String,
        picUri: Uri,
        packageName: String,
        width: Int,
        height: Int,
        callback: IRemotePicListener
    ) {
        RemoteDeviceManager.getFilePicture(
            devicesId, picPath, picUri, packageName, width, height, callback
        )
    }
}