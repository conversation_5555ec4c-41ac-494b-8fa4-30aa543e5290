/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchFileCallbackWrapper
 ** Description : remote mac file search callback
 ** Version     : 1.0
 ** Date        : 2025/02/14 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.remotedevice.search

import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.utils.Log
import com.oplus.filemanager.remotedevice.ResultConstant
import com.oplus.filemanager.remotedevice.util.mapRemoteFileInfo
import com.oplus.remotecontrol.remotecontrolsdk.RemoteControlServiceManager
import com.oplus.remotecontrol.remotecontrolsdk.bean.RemoteFileInfoInternal
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcQueryFileListCallback
import java.util.concurrent.atomic.AtomicBoolean

class SearchFileCallbackWrapper : IRcQueryFileListCallback {

    companion object {
        private const val TAG = "SearchFileCallbackWrapper"

        //远控服务器设置超时时间为20s,20s超时之后，显示空数据
        const val WAIT_TIME_OUT = 20 * 1000L
    }

    private val lock = Object()
    private var resultList = mutableListOf<RemoteFileData>()
    private var returnResult = AtomicBoolean(false)

    /**
     * 通知等待waitLock的监听方，结束等待
     */
    private fun notifyLockReleased() {
        Log.d(TAG, "notifyLockReleased")
        synchronized(lock) {
            lock.notify()
        }
    }

    private fun lockWait(timeout: Long = WAIT_TIME_OUT) {
        Log.d(TAG, "lockWait")
        try {
            synchronized(lock) {
                lock.wait(timeout)
            }
        } catch (e: InterruptedException) {
            Log.e(TAG, "lockWait interrupted", e)
        }
    }

    /**
     * 开始搜索
     */
    fun search(remoteMgr: RemoteControlServiceManager?, deviceId: String, keyword: String, pageNum: Int, pageSize: Int): List<RemoteFileData> {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "search mgr:$remoteMgr device:$deviceId keyword:$keyword page:$pageNum start...")

        // 开始搜索
        remoteMgr?.searchKeyWordFileList(deviceId, keyword, pageSize, pageNum, this)
        if (!returnResult.get()) {
            // 等待结果返回
            lockWait()
        }

        val endTime = System.currentTimeMillis()
        Log.d(TAG, "search page:$pageNum end result:${resultList.size}, cost ${endTime - startTime} ms")
        return resultList
    }

    override fun onResult(code: Int, fileListInfo: List<RemoteFileInfoInternal>) {
        Log.d(TAG, "onResult code:$code list size:${fileListInfo.size}")
        returnResult.set(true)
        resultList.clear()
        if (ResultConstant.isSuccess(code)) { // 成功
            val list = mapRemoteFileInfo(fileListInfo)
            resultList.addAll(list)
        }
        notifyLockReleased()
    }
}