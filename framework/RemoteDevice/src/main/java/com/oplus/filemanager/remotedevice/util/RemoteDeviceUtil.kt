/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteDeviceUtil.kt
 ** Description: Remote device util
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.remotedevice.util

import android.util.Log
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.RemoteDeviceConstants
import com.oplus.filemanager.interfaze.remotedevice.IRemoteFileLoadCallback
import com.oplus.filemanager.remotedevice.RemoteDeviceManager
import com.oplus.remotecontrol.remotecontrolsdk.bean.RemoteFileInfoInternal
import com.oplus.remotecontrol.remotecontrolsdk.constants.ResultConstant
import com.oplus.remotecontrol.remotecontrolsdk.db.DeviceInfo

fun mapRemoteDeviceInfoList(list: List<DeviceInfo>): List<RemoteDeviceInfo> {
    val deviceInfoList = arrayListOf<RemoteDeviceInfo>()
    for (device in list) {
        val deviceInfo = RemoteDeviceInfo().apply {
            deviceId = device.deviceId
            deviceName = device.name
            //设备状态 采用 设备文件传输能力的连接状态
            val connectingStatus = deviceId?.let {
                RemoteDeviceManager.connectingStatus[it] ?: false
            } ?: false
            deviceStatus = if (device.deviceFileState == RemoteDeviceConstants.DISCOVERED && connectingStatus) {
                Log.d(TAG, "keep device status: connecting, status=${RemoteDeviceConstants.CONNECTING}")
                RemoteDeviceConstants.CONNECTING
            } else {
                deviceId?.let { RemoteDeviceManager.connectingStatus[it] = false }
                Log.d(TAG, "not connecting status, status=${device.deviceFileState}")
                device.deviceFileState
            }
            deviceType = device.deviceType
            sameAccount = device.sameAccount
            deviceCode = device.deviceCode
        }
        deviceInfoList.add(deviceInfo)
    }
    Log.d(TAG, "deviceInfoList=$deviceInfoList")
    return deviceInfoList
}

const val TAG = "RemoteDeviceUtil"

fun mapRemoteFileInfo(list: List<RemoteFileInfoInternal>): List<RemoteFileData> {
    val fileDataList = arrayListOf<RemoteFileData>()
    for (file in list) {
        val remoteFileData = RemoteFileData().apply {
            remoteId = file.id
            fileName = file.name.toString()
            path = file.path.toString()
            alternativeName = file.alias
            size = file.size
            //这里后面需要修改 暂时屏蔽
            createDate = file.creationDate
            modifyDate = file.modificationDate
            lastOpenDate = file.lastOpenedDate

            isDir = file.isDirectory
            fileNum = file.fileNum

            remoteImageWidth = file.width
            remoteImageHeight = file.height

            remoteVideoDuration = file.videoDuration.toLong()

            remoteAudioChannel = file.audioChannels
        }
        //Log.d(TAG, "mapRemoteFileInfo input: $file, output: $remoteFileData")
        fileDataList.add(remoteFileData)
    }
    return fileDataList
}


fun mapRemoteFileInfoErrorCode(errorCodeSdk: Int): Int {
    val result = when (errorCodeSdk) {
        ResultConstant.RESULT_CODE_SUCCESS -> IRemoteFileLoadCallback.ERROR_CODE_SUC
        ResultConstant.RESULT_CODE_FAIL_NORMAL -> IRemoteFileLoadCallback.ERROR_CODE_FAILED_NORMAL
        ResultConstant.RESULT_CODE_FAIL_A -> IRemoteFileLoadCallback.ERROR_CODE_FAILED_A
        ResultConstant.RESULT_CODE_CANCEL -> IRemoteFileLoadCallback.ERROR_CODE_CANCEL
        //这个地方sdk中这个校验密码失败的错误码
        ResultConstant.SERVER_REMOTE_PASSWORD_ERROR -> IRemoteFileLoadCallback.ERROR_CODE_PASSWORD_ERROR
        //当前PC设备已被其它设备连接
        ResultConstant.PC_CLIENT_OCCUPIED -> IRemoteFileLoadCallback.ERROR_CODE_CONNECTED_OTHER_PC
        //当前设备正在和其他PC连接
        ResultConstant.LOCAL_CONNECTING -> IRemoteFileLoadCallback.ERROR_LOCAL_CONNECTING
        //远程文件管理功能不可用
        ResultConstant.NOT_SUPPORT_FILE -> IRemoteFileLoadCallback.NOT_SUPPORT_FILE
        else -> IRemoteFileLoadCallback.ERROR_CODE_FAILED_NORMAL
    }
    return result
}
