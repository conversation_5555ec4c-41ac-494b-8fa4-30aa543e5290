/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForAddFileService.kt
 ** Description: add file service register
 ** Version: 1.0
 ** Date: 2025/2/14
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.addfilepanel.di

import androidx.annotation.Keep
import com.oplus.filemanager.addfilepanel.AddFileServiceApi
import com.oplus.filemanager.interfaze.addFile.IAddFileService
import org.koin.dsl.module

@Keep
object AutoDIForAddFileService {

    val fileServiceModule = module {
        single<IAddFileService>(createdAtStart = true) {
            AddFileServiceApi
        }
    }
}
