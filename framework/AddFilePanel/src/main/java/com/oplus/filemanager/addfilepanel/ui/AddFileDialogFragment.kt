/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddFileDialogFragment.kt
 * * Description : AddFileDialogFragment
 * * Version     : 1.0
 * * Date        : 2025/02/08
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.addfilepanel.ui

import android.content.DialogInterface
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.filemanager.common.utils.Log
import com.oplus.filemanager.addfilepanel.bean.AddFileBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.properties.Delegates

class AddFileDialogFragment : COUIBottomSheetDialogFragment(), AddFileDialogInterface, SelectedFilePanelInterface {

    companion object {
        private const val TAG = "ShortcutFolderAddFileDialogFragment"
        const val ADD_FILE__DIALOG_FRAGMENT_TAG = "ADD_FILE_DIALOG_FRAGMENT_TAG"
        private const val ADD_FILE_ACTIVITY = "com.oplus.filemanager.filechoose.ui.addFile.AddFileActivity"
    }

    private var addFilePanelFragment: AddFilePanelFragment? = null
    private var selectedFilePanelFragment: SelectedFilePanelFragment? = null

    private var mLifecycle: Lifecycle by Delegates.notNull()
    private var title: String = ""

    var mCurrentPath: String = ""
    var mAddFileClickCallback: (selectData: List<AddFileBean>) -> Unit = {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState != null) {
            dismiss()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        dismiss()
        onDestroyView()
        addFilePanelFragment = null
        selectedFilePanelFragment = null
    }

    override fun dismissAddFileDialog() {
        dialog?.dismiss()
    }

    /**
     * 收起已选面板
     */
    override fun dismissSelectFileDialog() {
        updateShowPanelFragment(true)
        addFilePanelFragment?.notifyUpdate()
    }

    /**
     * 弹出已选面板
     */
    override fun replaceSelectedPanelFragment(selectData: MutableList<AddFileBean>) {
        updateShowPanelFragment(false)
    }

    fun showAddFilPanelFragment(
        title: String,
        manager:
        FragmentManager,
        lifecycle: Lifecycle,
        limitCount: Int = 0,
        isExternalReference: Boolean = false
    ) {
        Log.d(TAG, "showAddFilPanelFragment title: $title")
        if (addFilePanelFragment == null) {
            addFilePanelFragment = AddFilePanelFragment()
            addFilePanelFragment?.limitCount = limitCount
            addFilePanelFragment?.isExternalReference = isExternalReference
        }
        addFilePanelFragment?.addFileClickCallback = { selectList ->
            mAddFileClickCallback.invoke(selectList)
        }
        addFilePanelFragment?.lifeCycle = lifecycle
        addFilePanelFragment?.title = title
        addFilePanelFragment?.setAddFileDialogInterface(this)
        addFilePanelFragment?.mCurrentPath = mCurrentPath
        setMainPanelFragment(addFilePanelFragment)
        if (manager.isStateSaved.not()) {
            show(manager, ADD_FILE__DIALOG_FRAGMENT_TAG)
        }
        mLifecycle = lifecycle
    }

    private fun updateShowPanelFragment(isHomePage: Boolean) {
        if (isHomePage) {
            backToFirstPanel()
        } else {
            if (selectedFilePanelFragment == null) {
                selectedFilePanelFragment = SelectedFilePanelFragment()
            }
            selectedFilePanelFragment?.addFileClickCallback = { selectList ->
                mAddFileClickCallback.invoke(selectList)
            }
            selectedFilePanelFragment?.setAddFileDialogInterface(this)
            selectedFilePanelFragment?.lifeCycle = mLifecycle
            selectedFilePanelFragment?.mCurrentPath = mCurrentPath
            selectedFilePanelFragment?.title = title
            replacePanelFragment(selectedFilePanelFragment)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        activity?.let { activity ->
            if (activity.componentName.className == ADD_FILE_ACTIVITY) {
                activity.lifecycleScope.launch(Dispatchers.Default) {
                    activity.finish()
                }
            }
        }
    }
}