/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AddFileServiceApi.kt
 ** Description: add file service api
 ** Version: 1.0
 ** Date: 2025/2/14
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.addfilepanel

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.oplus.filemanager.interfaze.addFile.IAddFileService

object AddFileServiceApi : IAddFileService {

    override fun addFile(title: String, fragmentManager: FragmentManager, lifecycle: Lifecycle, activity: AppCompatActivity, limitCount: Int) {
        val mAddFileController = AddFileController()
        mAddFileController.showAddFileDialog(title, fragmentManager, lifecycle, activity, limitCount)
    }
}