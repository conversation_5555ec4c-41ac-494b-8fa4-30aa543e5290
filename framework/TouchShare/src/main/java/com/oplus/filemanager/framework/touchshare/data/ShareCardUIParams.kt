/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareCardParams
 * * Description : 分享卡片显示参数
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.data

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.oplus.filemanager.framework.touchshare.thumbnail.ShareFileThumbnailUtils

data class ShareCardUIParams(
    val thumbnailPath: String, // 缩略图的path
    val fileName: String, // 文件名称
    val count: Int,  // 文集个数
    val size: Long,  // 文件大小
    val time: Long // 文件时间
) {

    companion object {
        fun create(files: List<BaseFileBean>): ShareCardUIParams {
            val fileBean = files.getOrNull(0) ?: throw IllegalArgumentException("files is empty")
            val fileName = fileBean.mDisplayName ?: ""
            val time = fileBean.mDateModified ?: 0L
            var totalSize = 0L
            files.forEach {
                totalSize += JavaFileHelper.fileTotalSize(it)
            }
            // 文件缩略图
            val thumbnailUri = ShareFileThumbnailUtils.getFileThumbnailPath(fileBean)
            return ShareCardUIParams(thumbnailUri, fileName, files.size, totalSize, time)
        }
    }
}