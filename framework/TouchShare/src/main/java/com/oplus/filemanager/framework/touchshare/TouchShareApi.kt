/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareApi
 * * Description : 碰一碰分享的Api
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare

import android.content.Context
import android.content.Intent
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.framework.touchshare.data.ShareCardUIParams
import com.oplus.filemanager.framework.touchshare.utils.ShareFileCheckUtils
import com.oplus.filemanager.interfaze.touchshare.ITouchShareApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.function.Consumer

class TouchShareApi : ITouchShareApi {

    companion object {
        private const val TAG = "TouchShareApi"
    }

    override fun create(context: Context) {
        TouchShareManager.create(context)
    }

    override fun isAvailable(): Boolean {
        return TouchShareManager.isAvailable()
    }

    override fun registerTapShareEvent(consumer: Consumer<Boolean>) {
        GlobalScope.launch(Dispatchers.IO) {
            TouchShareManager.registerTapShareEvent(consumer)
        }
    }

    override fun unregisterTapShareEvent() {
        GlobalScope.launch(Dispatchers.IO) {
            TouchShareManager.unregisterTapShareEvent()
        }
    }

    override fun share(from: Int, files: List<BaseFileBean>): Boolean {
        if (files.isEmpty()) {
            Log.e(TAG, "share files must not empty!!!")
            return false
        }
        if (!TouchShareManager.isTriggerTouch()) {
            Log.e(TAG, "share agent is not connect!!!")
            return false
        }
        if (!ShareFileCheckUtils.isSupport(from, files)) {
            Log.e(TAG, "share file not support!!!")
            rejectShare(ITouchShareApi.REJECT_REASON_CONDITION_LIMIT)
            return false
        }
        val uiParams = ShareCardUIParams.create(files)
        return TouchShareManager.share(from, files, uiParams = uiParams)
    }

    override fun rejectShare(code: Int): Boolean {
        if (!TouchShareManager.isTriggerTouch()) {
            Log.e(TAG, "rejectShare agent is not connect!!!")
            return false
        }
        Log.w(TAG, "rejectShare $code")
        val uiParams = ShareCardUIParams.create(emptyList())
        return TouchShareManager.share(code, emptyList(), uiParams)
//        return TouchShareManager.rejectShare(code)
    }

    override fun resumeShare(from: Int, files: List<BaseFileBean>): Boolean {
        if (files.isEmpty()) {
            Log.e(TAG, "resumeShare files must not empty!!!")
            return false
        }
        if (!TouchShareManager.isTriggerTouch()) {
            Log.e(TAG, "resumeShare agent is not connect!!!")
            return false
        }
        if (!ShareFileCheckUtils.isSupport(from, files)) {
            Log.e(TAG, "resumeShare file not support!!!")
            rejectShare(ITouchShareApi.REJECT_REASON_CONDITION_LIMIT)
            return false
        }
        return TouchShareManager.resumeShare(from, files)
    }

    override fun getShareData(intent: Intent): List<BaseFileBean> {
        return TouchShareManager.getShareData(intent)
    }

    override fun release() {
        TouchShareManager.release()
    }
}