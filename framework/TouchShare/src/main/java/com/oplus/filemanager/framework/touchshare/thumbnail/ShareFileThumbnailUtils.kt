/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareFileThumbnailUtils
 * * Description : 分享文件缩率图
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.thumbnail

import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thumbnail.ThumbnailFetcherFactory
import com.filemanager.common.wrapper.PathFileWrapper
import java.io.File

object ShareFileThumbnailUtils {

    /**
     * 获取文件的Uri
     * @param path 文件路径
     * @param mimeType 文件的mimeType
     */
    @JvmStatic
    fun getFileUri(path: String, mimeType: Int): String {
        return getFileUri(PathFileWrapper(path), mimeType)
    }

    /**
     * 获取文件的Uri
     * @param file 文件路径
     * @param mimeType 文件的mimeType
     */
    @JvmStatic
    fun getFileUri(file: BaseFileBean, mimeType: Int): String {
        return UriHelper.getFileUri(file, null, mimeType).toString()
    }


    /**
     * 获取文件缩略图的Uri
     * @param file 文件
     */
    @JvmStatic
    fun getFileThumbnailUri(file: BaseFileBean): String {
        val thumbnailPath = getFileThumbnailPath(file)
        val thumbnailUri = getFileUri(thumbnailPath, MimeTypeHelper.IMAGE_TYPE)
        return thumbnailUri
    }

    /**
     * 获取文件缩略图的地址
     */
    @JvmStatic
    fun getFileThumbnailPath(file: BaseFileBean): String {
        val isImage = MimeTypeHelper.isImageType(file.mLocalType)
        if (isImage) { // 当是图片时，用图片原图
            return file.mData ?: ""
        }
        val thumbnailPath = ThumbnailFetcherFactory.fetch(file)
        return thumbnailPath
    }

    /**
     * 获取文件的byte array
     * @param file 文件
     * @return byte array
     */
    @JvmStatic
    fun getFileBytes(file: File): ByteArray {
        return file.readBytes()
    }

    /**
     * 获取文管应用icon的uri
     */
    @JvmStatic
    fun getSelfAppIconUri(): String {
        // 获取缩略图文件地址
        val thumbnailPath = getSelfAppIconPath()
        // 将文件地址转成Uri
        val thumbnailUri = getFileUri(thumbnailPath, MimeTypeHelper.IMAGE_TYPE)
        return thumbnailUri
    }

    /**
     * 获取文管应用icon的 path
     */
    @JvmStatic
    fun getSelfAppIconPath(): String {
        val context = MyApplication.appContext
        val pkgName = context.packageName
        // 获取缩略图文件地址
        val thumbnailPath = ThumbnailFetcherFactory.fetch(context, pkgName)
        return thumbnailPath
    }

    /**
     * 获取文管应用icon的byte数组
     */
    @JvmStatic
    fun getSelfAppIcon(): ByteArray {
        // 获取缩略图文件地址
        val thumbnailPath = getSelfAppIconPath()
        val file = File(thumbnailPath)
        // 将文件内容转成ByteArray
        return getFileBytes(file)
    }
}