/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareCardOptionsFactory
 * * Description : 分享卡片的展示类型工厂
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.data

import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.framework.touchshare.thumbnail.ShareFileThumbnailUtils
import com.oplus.interconnect.share.sdk.data.Alignment
import com.oplus.interconnect.share.sdk.data.ButtonOptions
import com.oplus.interconnect.share.sdk.data.CardContent
import com.oplus.interconnect.share.sdk.data.CardOptions
import com.oplus.interconnect.share.sdk.data.CardType
import com.oplus.interconnect.share.sdk.data.LineType
import com.oplus.interconnect.share.sdk.data.RECEIVED_NEGATIVE
import com.oplus.interconnect.share.sdk.data.RECEIVED_POSITIVE
import com.oplus.interconnect.share.sdk.data.TextLine
import com.oplus.interconnect.share.sdk.data.Thumbnail
import com.oplus.interconnect.share.sdk.data.ThumbnailContent
import com.oplus.interconnect.share.sdk.data.ThumbnailTemplate
import java.io.File

object ShareCardOptionsFactory {

    private const val TAG = "ShareCardOptionsFactory"

    /**
     * 缩略图大小最大为1M
     */
    private const val THUMBNAIL_SIZE = 1024 * 1024L
    private val THUMBNAIL_READABLE_SIZE by lazy {
        Utils.byteCountToDisplaySize(THUMBNAIL_SIZE)
    }

    /**
     *
     */
    fun create(uiParams: ShareCardUIParams): CardOptions {
        // 底部两个按钮
        val buttonOptions = createButtonOptions()
        // 文件的描述信息 + 应用名称
        val desc = createFileDesc(uiParams.count, uiParams.size, uiParams.time)
        val cardContent = createCardContent(desc)
        // 文件缩略图
        val thumbContent = createThumbnailContent(uiParams.thumbnailPath)
        val options = CardOptions(CardType.DEFAULT, uiParams.fileName, Alignment.LEFT, desc, buttonOptions, cardContent, thumbContent, null)
        return options
    }


    /**
     * 创建文件描述
     * 单个文件： 文件大小 | 文件时间
     * 多个文件： 文件数量 | 文件时间
     */
    private fun createFileDesc(count: Int, size: Long, time: Long): String {
        val context = MyApplication.appContext
        // 时间
        val dateAndTime = Utils.getDateFormat(context, time)
        val detail = if (count == 1) { // 文件大小
            Utils.byteCountToDisplaySize(size)
        } else { // 文件数量
            context.resources.getQuantityString(R.plurals.text_x_items, count, count)
        }
        return Utils.formatDetailDrag(detail, dateAndTime)
    }


    /**
     * 创建内容
     * 文件的描述信息+ 应用名称
     */
    private fun createCardContent(desc: String): CardContent {
        // 描述信息，C2
        val descLine = TextLine(LineType.TEXT_LINE_TYPE, desc, null, Alignment.LEFT)
        // 应用icon+名称，C4 +D4
        val appName = MyApplication.appContext.getString(R.string.app_name)
        // icon缩略图
        val appIcon = ShareFileThumbnailUtils.getSelfAppIcon()
        val appIconThumbnail = Thumbnail(appIcon, null, ThumbnailTemplate.THUMBNAIL_FULL_IMG)
        val appLine = TextLine(LineType.BOTTOM_LINE_TYPE, appName, appIconThumbnail, Alignment.LEFT)
        return CardContent(listOf(descLine, appLine))
    }

    /**
     * 创建缩略图
     */
    private fun createThumbnailContent(path: String): ThumbnailContent {
        val file = File(path)
        val fileSize = file.length()
        Log.d(TAG, "createThumbnailContent $path size:${Utils.byteCountToDisplaySize(fileSize)} ,max thumbnail size:$THUMBNAIL_READABLE_SIZE")
        if (fileSize > THUMBNAIL_SIZE) {
            val thumbnailUri = ShareFileThumbnailUtils.getFileUri(path, MimeTypeHelper.IMAGE_TYPE)
            val thumbnail = Thumbnail(null, thumbnailUri, ThumbnailTemplate.THUMBNAIL_FULL_IMG)
            return ThumbnailContent(listOf(thumbnail))
        } else {
            val thumbnailBytes = ShareFileThumbnailUtils.getFileBytes(file)
            val thumbnail = Thumbnail(thumbnailBytes, null, ThumbnailTemplate.THUMBNAIL_FULL_IMG)
            return ThumbnailContent(listOf(thumbnail))
        }
    }


    /**
     * 创建底部按钮
     */
    private fun createButtonOptions(): ButtonOptions {
        val context = MyApplication.appContext
        val map = mutableMapOf<String, String>()
        map[RECEIVED_POSITIVE] = context.getString(R.string.receive)
        map[RECEIVED_NEGATIVE] = context.getString(R.string.disagree)
        return ButtonOptions(map)
    }
}