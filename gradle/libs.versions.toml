[versions]
######## Codes denpendency versions ########
# Build plugins:
androidGradlePlugin = "8.2.2"
oapm = "*******"
obuild = "1.7.0"
protobufPlugin = "0.9.4"
androidToolsR8 = "8.2.42"
# Kotlin libs:
kotlin = "1.9.22"
kotlinCoroutines = "1.7.3"
kotlinSerializationJson = "1.6.2"
koin = "3.4.3"
koinCoroutines = "3.4.1"
# Android libs:
chips = "1.0.0"
# Androidx libs:
coreKtx = "1.12.0"
appcompat = "1.6.1"
room = "2.4.3"
pagingKtx = "3.2.1"
fragmentKtx = "1.4.1"
lifecycle = "2.6.2"
recyclerview = "1.2.1"
constraintlayout = "2.1.3"
# Google libs:
material = "1.11.0"
protobuf = "3.23.0"
protobufProtoc = "3.10.0"
gson = "2.10.1"
# Oplus libs:
addon = "*********-SNAPSHOT"
apiAdapter = "13.3.5"
couiSupport = "16.0.4"
couiMaterialcolor = "1.0.1"
olint = "1.2.5"
trackinglib = "1.0.3"
feedbackCdp = "15.0.4"
feedbackEnvDomestic = "1.0.1"
synergysdk = "14.1.10"
castsdk = "4.1.4"
oplusSettings = "2.1.1"
oapsSdkDownload = "3.1.2"
sauaar = "3.0.3"
sysapi = "13.0.6"
#dmpSdk = "2.0.0-betafab8c2d-SNAPSHOT"
dmpSdk = "2.1.1"
dmpSdkExport = "2.3.0-20240903.080150-1"
aiunit = "1.1.0"
stdid = "1.0.8"
oworkSync = "1.0.2-beta"
appfeature = "1.0.2"
oplusTrack = "3.0.13"
oplusCloudconfig = "3.4.8"
oplusCloudconfigTest = "1.0.0"
heytapCloudconfigTest = "0.0.7"
pantanalCardSeedling = "3.0.1"
oplusBackup = "2.0.0"
onetSdk = "14.4.60"
dfsSdk = "1.0.1-alpha62bb292-SNAPSHOT"
ptcSdk = "1.0.1-alpha7066ba4-SNAPSHOT"
accessorySdk = "3.0.35"
oplusTBLPlayer = "1.6.44PRO"
remoteControlSdk = "15.0.0-alphacd8fa13-SNAPSHOT"
workmanagerversion = "2.7.0"
# HeyTap libs:
addonAdapter = "11.2.5"
cloudDrive = "1.1.8"
cloudBase = "1.1.3"
ucAccountSDK = "2.5.1"
heytapTrack = "*******"
heytapNearxUtils = "1.0.8.security"
heytapProtobuff = "*********"
heytapCloudconfig = "*******"
adOverseas = "2.24.0"
tapHttpVerSion  = "*******.1"
heytapCloudkitCommonVersion="2.2.10"
heytapCloudkitSyncVersion="2.2.10"
shareSdk = "1.0.1-SNAPSHOT"
# 3rd parts libs:
findbugs = "3.0.2"
okio = "3.7.0"
okhttp3 = "4.12.0"
glide = "4.16.0"
wechatSdk = "6.8.24"
retrofit2 = "2.9.0"
retrofit2Protobuf = "2.9.0"
commonsCompress = "1.21"
commonsLogging = "1.3.0"
commonsIo = "2.15.1"
zip4j = "2.11.5"
unrar = "1.0.0"
gnuCrypto = "2.0.1"
tika = "2.8.0"
cryptography = "2.1.1"
slf4j = "2.0.10"
rxjava = "3.1.8"
wpsSdkSnapshot = "********-SNAPSHOT"
maxVersion = "12.6.1"
googleAdapter = "********"
facebookAdapter = "********"
mytargetAdapter = "********"
bytedanceAdapter = "*******.0"

# FileManager inner libs:
fileManagerThumbnail = "16.0.0-alphac1e2c7c-SNAPSHOT"
fileManagerThumbnailWpsCompat = "16.0.0-alphac1e2c7c-SNAPSHOT"
fileManagerSimulateClick = "16.0.0-alpha7fea71f-SNAPSHOT"
fileManagerDragDrop = "16.0.0-alphac1e2c7c-SNAPSHOT"

######## Unit test denpendency versions ########
# Base test libs:
jacoco = "0.8.7"
junit = "4.13.2"
mockito = "3.11.2"
mockk = "1.12.1"
kotlinCoroutinesTest = "1.6.0"
robolectric = "4.10"
powermock = "2.0.9"
# Androidx test libs:
androidxCoreTest = "2.1.0"
# 3rd parts libs:
json = "20231013"

######## AndroidTest denpendency versions ########
# Androidx test libs:
espresso = "3.5.1"
testRunner = "1.4.0"
testRules = "1.4.0"
testTruth = "1.4.0"
androidxJunit = "1.1.5"
uiautomator = "2.2.0"
roomTesting = "2.2.5"
# Oplus test libs:
oTestLib = "1.4.1"
oTestCoverage = "2.0.6@aar"

[libraries]
######## Codes denpendencies ########
# Build plugins:
android-gradle-plugin = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
kotlin-serialization-plugin = { module = "org.jetbrains.kotlin:kotlin-serialization", version.ref = "kotlin" }
oplus-oapm-plugin = { module = "com.oppo.test:oapm-perf", version.ref = "oapm" }
oplus-build-plugin = { module = "com.inno.buildplugin:build-plugin", version.ref = "obuild" }
google-protobuf-plugin = { module = "com.google.protobuf:protobuf-gradle-plugin", version.ref = "protobufPlugin" }
android-tools-r8 = { module = "com.android.tools:r8", version.ref = "androidToolsR8" }
# Kotlin libs:
kotlin-stdlib-common = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinCoroutines" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinSerializationJson" }
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin-core-coroutines = { module = "io.insert-koin:koin-core-coroutines", version.ref = "koinCoroutines" }
koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
# Android libs:
android-ex-chips = { module = "com.android.ex.chips:chips", version.ref = "chips" }
# Androidx libs:
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-paging = { module = "androidx.room:room-paging", version.ref = "room" }
androidx-paging-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "pagingKtx" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtx" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-viewmodel-savedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "lifecycle" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "lifecycle" }
androidx-lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "lifecycle" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-workmanager-ktx = {module = "androidx.work:work-runtime-ktx", version.ref = "workmanagerversion"}
google-material = { module = "com.google.android.material:material", version.ref = "material" }
google-protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf" }
google-protobuf-protoc = { module = "com.google.protobuf:protoc", version.ref = "protobufProtoc" }
google-gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
# Oplus libs:
oplus-check-olint = { module = "com.sectools.check:olint", version.ref = "olint" }
# 原base包拆成22个子包
oplus-appcompat-core = { module = "com.oplus.appcompat:core", version.ref = "couiSupport" }
oplus-appcompat-recyclerview = { module = "com.oplus.appcompat:recyclerview", version.ref = "couiSupport" }
oplus-appcompat-input = { module = "com.oplus.appcompat:input", version.ref = "couiSupport" }
oplus-appcompat-reddot = { module = "com.oplus.appcompat:reddot", version.ref = "couiSupport" }
oplus-appcompat-button = { module = "com.oplus.appcompat:button", version.ref = "couiSupport" }
oplus-appcompat-dialog = { module = "com.oplus.appcompat:dialog", version.ref = "couiSupport" }
oplus-appcompat-grid = { module = "com.oplus.appcompat:grid", version.ref = "couiSupport" }
oplus-appcompat-listview = { module = "com.oplus.appcompat:listview", version.ref = "couiSupport" }
oplus-appcompat-poplist = { module = "com.oplus.appcompat:poplist", version.ref = "couiSupport" }
oplus-appcompat-progressbar = { module = "com.oplus.appcompat:progressbar", version.ref = "couiSupport" }
oplus-appcompat-rotateview = { module = "com.oplus.appcompat:rotateview", version.ref = "couiSupport" }
oplus-appcompat-scrollview = { module = "com.oplus.appcompat:scrollview", version.ref = "couiSupport" }
oplus-appcompat-scrollbar = { module = "com.oplus.appcompat:scrollbar", version.ref = "couiSupport" }
oplus-appcompat-scroll = { module = "com.oplus.appcompat:scroll", version.ref = "couiSupport" }
oplus-appcompat-springchain = { module = "com.oplus.appcompat:springchain", version.ref = "couiSupport" }
oplus-appcompat-toolbar = { module = "com.oplus.appcompat:toolbar", version.ref = "couiSupport" }
oplus-appcompat-viewpager = { module = "com.oplus.appcompat:viewpager", version.ref = "couiSupport" }
oplus-appcompat-responsiveui = { module = "com.oplus.appcompat:responsiveui", version.ref = "couiSupport" }
oplus-appcompat-statement = { module = "com.oplus.appcompat:statement", version.ref = "couiSupport" }
oplus-appcompat-clickablespan = { module = "com.oplus.appcompat:clickablespan", version.ref = "couiSupport" }
oplus-appcompat-stepper = { module = "com.oplus.appcompat:stepper", version.ref = "couiSupport" }
oplus-appcompat-textview = { module = "com.oplus.appcompat:textview", version.ref = "couiSupport" }
# 原controls包拆分成10个子包
oplus-appcompat-calendar = { module = "com.oplus.appcompat:calendar", version.ref = "couiSupport" }
oplus-appcompat-cardview = { module = "com.oplus.appcompat:cardview", version.ref = "couiSupport" }
oplus-appcompat-chip = { module = "com.oplus.appcompat:chip", version.ref = "couiSupport" }
oplus-appcompat-floatingactionbutton = { module = "com.oplus.appcompat:floatingactionbutton", version.ref = "couiSupport" }
oplus-appcompat-indicator = { module = "com.oplus.appcompat:indicator", version.ref = "couiSupport" }
oplus-appcompat-lockview = { module = "com.oplus.appcompat:lockview", version.ref = "couiSupport" }
oplus-appcompat-picker = { module = "com.oplus.appcompat:picker", version.ref = "couiSupport" }
oplus-appcompat-seekbar = { module = "com.oplus.appcompat:seekbar", version.ref = "couiSupport" }
oplus-appcompat-snackbar = { module = "com.oplus.appcompat:snackbar", version.ref = "couiSupport" }
oplus-appcompat-tips = { module = "com.oplus.appcompat:tips", version.ref = "couiSupport" }
# 原bars包拆分成3个子包
oplus-appcompat-bottomnavigation = { module = "com.oplus.appcompat:bottomnavigation", version.ref = "couiSupport" }
oplus-appcompat-sidenavigationbar = { module = "com.oplus.appcompat:sidenavigationbar", version.ref = "couiSupport" }
oplus-appcompat-tablayout = { module = "com.oplus.appcompat:tablayout", version.ref = "couiSupport" }
# 原lists包拆分成4个子包
oplus-appcompat-preference = { module = "com.oplus.appcompat:preference", version.ref = "couiSupport" }
oplus-appcompat-expandable = { module = "com.oplus.appcompat:expandable", version.ref = "couiSupport" }
oplus-appcompat-touchsearchview = { module = "com.oplus.appcompat:touchsearchview", version.ref = "couiSupport" }
oplus-appcompat-slideview = { module = "com.oplus.appcompat:slideview", version.ref = "couiSupport" }
# 原panel不变
oplus-appcompat-panel = { module = "com.oplus.appcompat:panel", version.ref = "couiSupport" }
# 原component拆分成7个包
oplus-appcompat-component = { module = "com.oplus.appcompat:component", version.ref = "couiSupport" }
oplus-appcompat-autocomplete = { module = "com.oplus.appcompat:autocomplete", version.ref = "couiSupport" }
oplus-appcompat-card = { module = "com.oplus.appcompat:card", version.ref = "couiSupport" }
oplus-appcompat-emptyview = { module = "com.oplus.appcompat:emptyview", version.ref = "couiSupport" }
oplus-appcompat-privacypolicy = { module = "com.oplus.appcompat:privacypolicy", version.ref = "couiSupport" }
oplus-appcompat-scanview = { module = "com.oplus.appcompat:scanview", version.ref = "couiSupport" }
oplus-appcompat-searchhistory = { module = "com.oplus.appcompat:searchhistory", version.ref = "couiSupport" }

oplus-material-color = { module = "com.oplus.materialcolor:coui-material-color", version.ref = "couiMaterialcolor" }
oplus-tool-trackinglib = { module = "com.oplus.tool.trackinglib:trackinglib", version.ref = "trackinglib" }
oplus-feedback-cdp = { module = "com.customer.feedback.sdk:feedback-cdp", version.ref = "feedbackCdp" }
oplus-feedback-env-domestic = { module = "com.customer.feedback.sdk:feedback-env-domestic", version.ref = "feedbackEnvDomestic" }
oplus-synergy-compat = { module = "com.oplus.synergysdk:synergysdkcompat", version.ref = "synergysdk" }
oplus-cast-sdk = { module = "com.oplus.cast.api:castsdk", version.ref = "castsdk" }
oplus-appprovider-settings = { module = "com.oplus.appprovider:settings-oplus", version.ref = "oplusSettings" }
oplus-sau-coui = { module = "com.oplus.sauaar:coui-sauaar", version.ref = "sauaar" }
oplus-aiunit-core = { module = "com.oplus.aiunit.open:core", version.ref = "aiunit" }
oplus-aiunit-nlp = { module = "com.oplus.aiunit.open:nlp", version.ref = "aiunit" }
oplus-stdid-sdk = { module = "com.oplus.stdid.sdk:sdk", version.ref = "stdid" }
oplus-owork-sync = { module = "com.oplus.owork:sync-sdk", version.ref = "oworkSync" }
oplus-coreapp-appfeature = { module = "com.oplus.coreapp.appfeature:AppFeatureHelper", version.ref = "appfeature" }
oplus-statistics-track = { module = "com.oplus.statistics:track", version.ref = "oplusTrack" }
oplus-cloudconfig-base = { module = "com.oplus.nearx:cloudconfig", version.ref = "oplusCloudconfig" }
oplus-cloudconfig-env = { module = "com.oplus.nearx:cloudconfig-env", version.ref = "oplusCloudconfig" }
oplus-cloudconfig-testenv = { module = "com.nearx.test:env", version.ref = "oplusCloudconfigTest" }
heytap-cloudconfig-testenv = { module = "com.heytap.test:env", version.ref = "heytapCloudconfigTest"}
oplus-pantanal-card-seedling = { module = "com.oplus.pantanal.card:seedling-support-internal", version.ref = "pantanalCardSeedling" }
oplus-backup-sdk = { module = "com.oplus.backup:backup-sdk", version.ref = "oplusBackup" }
oplus-onet-sdk = { module = "com.oplus.onet:sdk", version.ref = "onetSdk" }
oplus-dfs-sdk = { module = "com.oplus.dfs:dfssdk", version.ref = "dfsSdk" }
oplus-ptc-sdk-core = { module = "com.oplus.pantaconnect.sdk:core", version.ref = "ptcSdk" }
oplus-ptc-sdk-connect = { module = "com.oplus.pantaconnect.sdk:connectionservice", version.ref = "ptcSdk" }
oplus-ptc-sdk-ext = { module = "com.oplus.pantaconnect.sdk:extensions", version.ref = "ptcSdk" }
oplus-ptc-sdk-protocol = { module = "com.oplus.pantaconnect.sdk:protocol-plugin", version.ref = "ptcSdk" }
oplus-accessory-sdk = { module = "com.heytap.accessory:sdk", version.ref = "accessorySdk" }
oplus-addon-sdk = { module = "com.oplus.sdk:addon", version.ref = "addon" }
oplus-api-adapter-compat = { module = "com.oplus.support:api-adapter-compat", version.ref = "apiAdapter" }
oplus-api-adapter-oplus = { module = "com.oplus.support:api-adapter-oplus", version.ref = "apiAdapter" }
oplus-sysapi = { module = "com.oplus.appplatform:sysapi", version.ref = "sysapi" }
oplus-dmp-sdk = { module = "com.oplus.dmp.sdk:connect", version.ref = "dmpSdk" }
oplus-dmp-sdk-export = { module = "com.oplus.dmp.sdk:connect-export", version.ref = "dmpSdkExport" }
oplus-tbl-player = { module = "com.oplus.TBLPlayer:CommonPlayer", version.ref = "oplusTBLPlayer" }
oplus-remotecontrol-sdk = { module = "com.oplus.remotecontrol:remotecontrolsdk", version.ref = "remoteControlSdk" }
# HeyTap libs:
heytap-addon-adapter = { module = "com.heytap.support:addon-adapter", version.ref = "addonAdapter" }
heytap-cloud-drive = { module = "com.heytap.cloud:clouddisksdk", version.ref = "cloudDrive" }
heytap-cloud-base = { module = "com.heytap.cloud:base", version.ref = "cloudBase" }
heytap-account-uc = { module = "com.heytap.accountsdk:UCAccountSDK_Base_heytap", version.ref = "ucAccountSDK" }
heytap-nearx-track = { module = "com.heytap.nearx:track", version.ref = "heytapTrack" }
heytap-nearx-utils = { module = "com.heytap.nearx:utils", version.ref = "heytapNearxUtils" }
heytap-nearx-protobuff = { module = "com.heytap.nearx.protobuff:wire-runtime-isolate", version.ref = "heytapProtobuff" }
heytap-cloudconfig-base = { module = "com.heytap.nearx:cloudconfig", version.ref = "heytapCloudconfig" }
heytap-cloudconfig-env = { module = "com.heytap.nearx:cloudconfig-env-oversea", version.ref = "heytapCloudconfig" }
heytap-cloudconfig-area = { module = "com.heytap.nearx:cloudconfig-area", version.ref = "heytapCloudconfig" }
opos-ad-overseas = { module = "com.opos.ad:overseas-ad-global-pub", version.ref = "adOverseas" }
#opos-ad-overseas = { module = "com.opos.ad:overseas-ad-global-dev", version.ref = "adOverseas" }
taphttp = { module = "com.heytap.nearx:taphttp", version.ref = "tapHttpVerSion" }
taphttp-domestic = { module = "com.heytap.nearx:taphttp-env", version.ref = "tapHttpVerSion" }
taphttp-env = { module = "com.heytap.nearx:taphttp-env-oversea", version.ref = "tapHttpVerSion" }#HttpDns需要
oppo-marker-oaps-download = { module = "com.oppo.market:oaps_sdk_download", version.ref = "oapsSdkDownload" }
heytap-cloudkit-common = { module = "com.heytap.cloudkit.libcommon:libcommon", version.ref = "heytapCloudkitCommonVersion"}
heytap-cloudkit-sync = { module = "com.heytap.cloudkit.libsync:libsync", version.ref = "heytapCloudkitSyncVersion"}
heytap-accessory-share = { module = "com.heytap.accessory:share", version.ref = "shareSdk"}
# 3rd parts libs:
findbugs = { module = "com.google.code.findbugs:jsr305", version.ref = "findbugs" }
okio = { module = "com.squareup.okio:okio", version.ref = "okio" }
squareup-okhttp3-okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp3" }
squareup-okhttp3-logging = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp3" }
squareup-retrofit2-retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit2" }
squareup-retrofit2-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit2" }
squareup-retrofit2-adapter = { module = "com.squareup.retrofit2:adapter-rxjava3", version.ref = "retrofit2" }
squareup-retrofit2-protobuf = { module = "com.squareup.retrofit2:converter-protobuf", version.ref = "retrofit2Protobuf" }
bumptech-glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
bumptech-glide-base = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
tencent-sdk-wechat = { module = "com.tencent.mm.opensdk:wechat-sdk-android", version.ref = "wechatSdk" }
apache-commons-compress = { module = "org.apache.commons:commons-compress", version.ref = "commonsCompress" }
apache-commons-logging = { module = "commons-logging:commons-logging", version.ref = "commonsLogging" }
apache-commons-io = { module = "commons-io:commons-io", version.ref = "commonsIo" }
apache-tika-core = { module = "org.apache.tika:tika-core", version.ref = "tika" }
lingala-zip4j = { module = "net.lingala.zip4j:zip4j", version.ref = "zip4j" }
innosystec-unrar-java = { module = "de.innosystec.unrar:java-unrar", version.ref = "unrar" }
gnu-crypto = { module = "gnu.crypto:crypto", version.ref = "gnuCrypto" }
allawn-crypto-android = { module = "com.allawn.cryptography:crypto-android-sdk", version.ref = "cryptography" }
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
rxjava = { module = "io.reactivex.rxjava3:rxjava", version.ref = "rxjava" }
wps-sdk-snapshot = { module = "com.wps.sdk:snapshot", version.ref = "wpsSdkSnapshot" }
# FileManager Inner libs:
oplus-filemanager-thumbnail = { module = "com.oplus.filemanager:thumbnail", version.ref = "fileManagerThumbnail" }
oplus-filemanager-thumbnailWpsCompat = { module = "com.oplus.filemanager:thumbnail_wps_compat", version.ref = "fileManagerThumbnailWpsCompat" }
oplus-filemanager-simulateClickEngine = { module = "com.oplus.filemanager:simulate_click_engine", version.ref = "fileManagerSimulateClick" }
oplus-filemanager-dragDrop = { module = "com.oplus.filemanager:dragdrop", version.ref = "fileManagerDragDrop" }

max-applovin-sdk = { module = "com.applovin:applovin-sdk", version.ref = "maxVersion" }
mediation-google-adapter = { module = "com.applovin.mediation:google-adapter", version.ref = "googleAdapter" }
mediation-facebook-adapter = { module = "com.applovin.mediation:facebook-adapter", version.ref = "facebookAdapter" }
mediation-mytarget-adapter = { module = "com.applovin.mediation:mytarget-adapter", version.ref = "mytargetAdapter" }
mediation-bytedance-adapter = { module = "com.applovin.mediation:bytedance-adapter", version.ref = "bytedanceAdapter" }
######## Unit test denpendencies ########
junit-base = { module = "junit:junit", version.ref = "junit" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito" }
mockito-inline = { module = "org.mockito:mockito-inline", version.ref = "mockito" }
mockk-base = { module = "io.mockk:mockk", version.ref = "mockk" }
mockk-android = { module = "io.mockk:mockk-android", version.ref = "mockk" }
mockk-agnet = { module = "io.mockk:mockk-agent-jvm", version.ref = "mockk" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinCoroutinesTest" }
robolectric-base = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
powermock-module-junit4-base = { module = "org.powermock:powermock-module-junit4", version.ref = "powermock" }
powermock-module-junit4-rule = { module = "org.powermock:powermock-module-junit4-rule", version.ref = "powermock" }
powermock-api-mockito2 = { module = "org.powermock:powermock-api-mockito2", version.ref = "powermock" }
powermock-classloading-xstream = { module = "org.powermock:powermock-classloading-xstream", version.ref = "powermock" }
androidx-arch-core-testing = { module = "androidx.arch.core:core-testing", version.ref = "androidxCoreTest" }
json-base = { module = "org.json:json", version.ref = "json" }

######## AndroidTest denpendencies ########
androidx-test-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso" }
androidx-test-espresso-intents = { module = "androidx.test.espresso:espresso-intents", version.ref = "espresso" }
androidx-test-espresso-web = { module = "androidx.test.espresso:espresso-web", version.ref = "espresso" }
androidx-test-espresso-idling-concurrent = { module = "androidx.test.espresso.idling:idling-concurrent", version.ref = "espresso" }
androidx-test-espresso-idling-resource = { module = "androidx.test.espresso:espresso-idling-resource", version.ref = "espresso" }
androidx-test-runner = { module = "androidx.test:runner", version.ref = "testRunner" }
androidx-test-rules = { module = "androidx.test:rules", version.ref = "testRules" }
androidx-test-junit = { module = "androidx.test.ext:junit", version.ref = "androidxJunit" }
androidx-test-truth = { module = "androidx.test.ext:truth", version.ref = "testTruth" }
androidx-test-uiautomator = { module = "androidx.test.uiautomator:uiautomator", version.ref = "uiautomator" }
androidx-room-testing = { module = "androidx.room:room-testing", version.ref = "roomTesting" }
mockito-android = { module = "org.mockito:mockito-android", version.ref = "mockito" }
otest-testLib = { module = "otestPlatform:testLib", version.ref = "oTestLib" }
otest-coverageLib = { module = "otestPlatform:coverageLibTest", version.ref = "oTestCoverage" }
koin-test = { module = "io.insert-koin:koin-test", version.ref = "koin" }
koin-test-junit4 = { module = "io.insert-koin:koin-test-junit4", version.ref = "koin" }
seedling-sdk = { module = "com.oplus.pantanal.card:seedling-support-internal", version.ref = "koin" }

[bundles]
######## Codes denpendencies ########
kotlin = ["kotlin-stdlib-common", "kotlin-stdlib-jdk8", "kotlinx-coroutines-android"]
oplus-cloudconfig = ["oplus-cloudconfig-base", "oplus-cloudconfig-env"]
oplus-feedback-domestic = ["oplus-feedback-cdp", "oplus-feedback-env-domestic"]
heytap-cloudconfig = ["heytap-cloudconfig-base", "heytap-cloudconfig-env", "heytap-cloudconfig-area"]
heytap-clouddrive = ["heytap-cloud-drive", "heytap-cloud-base"]
squareup-okhttp3 = ["squareup-okhttp3-okhttp", "squareup-okhttp3-logging"]
squareup-retrofit2 = [
    "squareup-retrofit2-retrofit",
    "squareup-retrofit2-gson",
    "squareup-retrofit2-adapter",
    "rxjava"
]
######## Unit test denpendencies ########
test-mockito = ["mockito-core", "mockito-inline"]
test-mockk = ["mockk-base", "mockk-android", "mockk-agnet"]
test-powermock = [
    "powermock-module-junit4-base",
    "powermock-module-junit4-rule",
    "powermock-api-mockito2",
    "powermock-classloading-xstream"
]
######## AndroidTest denpendencies ########
android-test-suit = [
    "androidx-test-runner",
    "androidx-test-rules",
    "androidx-test-junit",
    "androidx-test-truth"
]
android-test-espresso = [
    "androidx-test-espresso-core",
    "androidx-test-espresso-intents",
    "androidx-test-espresso-web",
    "androidx-test-espresso-idling-concurrent",
    "androidx-test-espresso-idling-resource"
]