/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : StorageSafetyHelper
 * * Description : 存储空间安全检查工具类，用于处理存储空间不足时的各种异常
 * * Version     : 1.0
 * * Date        : 2025/2/27
 * * Author      : AI Assistant
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.disconect

import android.content.Context
import android.content.SharedPreferences
import android.database.sqlite.SQLiteException
import android.util.Log
import androidx.work.WorkManager
import java.io.File
import java.io.IOException

/**
 * 存储空间安全检查工具类
 * 用于在存储空间不足时提供安全的初始化和操作
 */
object StorageSafetyHelper {
    
    private const val TAG = "StorageSafetyHelper"
    
    // 最小可用空间要求 (50MB)
    private const val MIN_AVAILABLE_SPACE = 50 * 1024 * 1024L
    
    // 关键操作最小空间要求 (10MB)
    private const val CRITICAL_MIN_SPACE = 10 * 1024 * 1024L
    
    /**
     * 检查存储空间是否充足
     */
    @JvmStatic
    fun checkStorageSpace(context: Context): StorageStatus {
        return runCatching {
            val filesDir = context.filesDir
            val cacheDir = context.cacheDir
            val externalFilesDir = context.getExternalFilesDir(null)
            
            val internalSpace = filesDir?.usableSpace ?: 0L
            val cacheSpace = cacheDir?.usableSpace ?: 0L
            val externalSpace = externalFilesDir?.usableSpace ?: 0L
            
            Log.d(TAG, "Storage space - Internal: ${internalSpace / 1024 / 1024}MB, " +
                    "Cache: ${cacheSpace / 1024 / 1024}MB, " +
                    "External: ${externalSpace / 1024 / 1024}MB")
            
            when {
                internalSpace < CRITICAL_MIN_SPACE -> StorageStatus.CRITICAL_LOW
                internalSpace < MIN_AVAILABLE_SPACE -> StorageStatus.LOW
                else -> StorageStatus.SUFFICIENT
            }
        }.getOrElse { exception ->
            Log.e(TAG, "Failed to check storage space: ${exception.message}", exception)
            StorageStatus.UNKNOWN
        }
    }
    
    /**
     * 安全地获取 WorkManager 实例
     */
    @JvmStatic
    fun safeGetWorkManager(context: Context): WorkManager? {
        val storageStatus = checkStorageSpace(context)
        if (storageStatus == StorageStatus.CRITICAL_LOW) {
            Log.w(TAG, "Storage critically low, skipping WorkManager initialization")
            return null
        }
        
        return runCatching {
            WorkManager.getInstance(context)
        }.onFailure { exception ->
            Log.e(TAG, "Failed to get WorkManager: ${exception.message}", exception)
            when (exception) {
                is SQLiteException -> Log.e(TAG, "SQLite error - likely storage issue")
                is IOException -> Log.e(TAG, "IO error - likely storage issue")
                else -> Log.e(TAG, "Unknown error in WorkManager initialization")
            }
        }.getOrNull()
    }
    
    /**
     * 安全地获取 SharedPreferences
     */
    @JvmStatic
    fun safeGetSharedPreferences(context: Context, name: String): SharedPreferences? {
        val storageStatus = checkStorageSpace(context)
        if (storageStatus == StorageStatus.CRITICAL_LOW) {
            Log.w(TAG, "Storage critically low, using memory-only preferences")
            return MemorySharedPreferences()
        }
        
        return runCatching {
            context.getSharedPreferences(name, Context.MODE_PRIVATE)
        }.onFailure { exception ->
            Log.e(TAG, "Failed to get SharedPreferences '$name': ${exception.message}", exception)
        }.getOrElse {
            Log.w(TAG, "Falling back to memory-only preferences")
            MemorySharedPreferences()
        }
    }
    
    /**
     * 安全地执行数据库操作
     */
    @JvmStatic
    fun <T> safeExecuteDbOperation(
        context: Context,
        operation: () -> T,
        fallback: (() -> T)? = null
    ): T? {
        val storageStatus = checkStorageSpace(context)
        if (storageStatus == StorageStatus.CRITICAL_LOW) {
            Log.w(TAG, "Storage critically low, skipping database operation")
            return fallback?.invoke()
        }
        
        return runCatching {
            operation()
        }.onFailure { exception ->
            Log.e(TAG, "Database operation failed: ${exception.message}", exception)
            when (exception) {
                is SQLiteException -> {
                    Log.e(TAG, "SQLite error - checking if storage related")
                    if (exception.message?.contains("disk", ignoreCase = true) == true ||
                        exception.message?.contains("space", ignoreCase = true) == true) {
                        Log.e(TAG, "Storage-related SQLite error detected")
                    }
                }
            }
        }.getOrElse {
            fallback?.invoke()
        }
    }
    
    /**
     * 清理临时文件和缓存
     */
    @JvmStatic
    fun cleanupTemporaryFiles(context: Context) {
        runCatching {
            // 清理缓存目录
            context.cacheDir?.let { cacheDir ->
                cleanDirectory(cacheDir)
            }
            
            // 清理外部缓存目录
            context.externalCacheDir?.let { externalCacheDir ->
                cleanDirectory(externalCacheDir)
            }
            
            Log.i(TAG, "Temporary files cleanup completed")
        }.onFailure { exception ->
            Log.e(TAG, "Failed to cleanup temporary files: ${exception.message}", exception)
        }
    }
    
    private fun cleanDirectory(directory: File) {
        if (!directory.exists() || !directory.isDirectory) return
        
        directory.listFiles()?.forEach { file ->
            runCatching {
                if (file.isDirectory) {
                    cleanDirectory(file)
                }
                file.delete()
            }.onFailure { exception ->
                Log.w(TAG, "Failed to delete file: ${file.absolutePath}, ${exception.message}")
            }
        }
    }
    
    /**
     * 存储状态枚举
     */
    enum class StorageStatus {
        SUFFICIENT,     // 充足
        LOW,           // 不足
        CRITICAL_LOW,  // 严重不足
        UNKNOWN        // 未知
    }
}

/**
 * 内存版本的 SharedPreferences，用于存储空间不足时的降级方案
 */
private class MemorySharedPreferences : SharedPreferences {
    private val data = mutableMapOf<String, Any?>()
    
    override fun getAll(): MutableMap<String, *> = data.toMutableMap()
    override fun getString(key: String?, defValue: String?): String? = data[key] as? String ?: defValue
    override fun getStringSet(key: String?, defValues: MutableSet<String>?): MutableSet<String>? = 
        data[key] as? MutableSet<String> ?: defValues
    override fun getInt(key: String?, defValue: Int): Int = data[key] as? Int ?: defValue
    override fun getLong(key: String?, defValue: Long): Long = data[key] as? Long ?: defValue
    override fun getFloat(key: String?, defValue: Float): Float = data[key] as? Float ?: defValue
    override fun getBoolean(key: String?, defValue: Boolean): Boolean = data[key] as? Boolean ?: defValue
    override fun contains(key: String?): Boolean = data.containsKey(key)
    
    override fun edit(): SharedPreferences.Editor = MemoryEditor()
    override fun registerOnSharedPreferenceChangeListener(listener: SharedPreferences.OnSharedPreferenceChangeListener?) {}
    override fun unregisterOnSharedPreferenceChangeListener(listener: SharedPreferences.OnSharedPreferenceChangeListener?) {}
    
    private inner class MemoryEditor : SharedPreferences.Editor {
        private val edits = mutableMapOf<String, Any?>()
        
        override fun putString(key: String?, value: String?): SharedPreferences.Editor = apply { edits[key!!] = value }
        override fun putStringSet(key: String?, values: MutableSet<String>?): SharedPreferences.Editor = apply { edits[key!!] = values }
        override fun putInt(key: String?, value: Int): SharedPreferences.Editor = apply { edits[key!!] = value }
        override fun putLong(key: String?, value: Long): SharedPreferences.Editor = apply { edits[key!!] = value }
        override fun putFloat(key: String?, value: Float): SharedPreferences.Editor = apply { edits[key!!] = value }
        override fun putBoolean(key: String?, value: Boolean): SharedPreferences.Editor = apply { edits[key!!] = value }
        override fun remove(key: String?): SharedPreferences.Editor = apply { edits[key!!] = null }
        override fun clear(): SharedPreferences.Editor = apply { edits.clear(); data.clear() }
        
        override fun commit(): Boolean {
            data.putAll(edits.filterValues { it != null })
            edits.filterValues { it == null }.keys.forEach { data.remove(it) }
            return true
        }
        
        override fun apply() { commit() }
    }
}
