<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.share.ShareActivity"
            android:label="@string/save_file_to_file_manager"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboardHidden|layoutDirection"
            android:screenOrientation="fullUser"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="application/vnd.apple.keynote" />
                <data android:mimeType="application/vnd.apple.numbers" />
                <data android:mimeType="application/vnd.apple.pages" />
                <data android:mimeType="text/markdown" />
                <data android:mimeType="application/acad" />
                <data android:mimeType="image/vnd.dxf" />
                <data android:mimeType="application/x-autocad" />
                <data android:mimeType="application/xmind" />
                <data android:mimeType="application/photoshop" />
                <data android:mimeType="application/postscript" />
                <data android:mimeType="application/vnd.visio" />
                <data android:mimeType="*/*" />
            </intent-filter>
        </activity>

        <activity android:name=".ui.share.ShareReceiverActivity"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout|keyboardHidden|layoutDirection"
            android:screenOrientation="fullUser"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:exported="true"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="com.oplus.interconnect.action.SHARED_DATA" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>
</manifest>