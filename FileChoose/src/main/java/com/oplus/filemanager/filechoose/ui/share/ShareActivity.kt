/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filechoose.ui.share.ShareActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <keweiwei>                <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose.ui.share

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.os.Environment
import android.view.KeyEvent
import android.view.ViewTreeObserver
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ForceDialogDimAmount
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.filechoose.R

class ShareActivity : BaseVMActivity(), TransformNextFragmentListener,
    BaseVMActivity.PermissonCallBack, ForceDialogDimAmount {
    companion object {
        const val TAG = "ShareActivity"
        const val NEED_FINISH_IF_RECREATE = "needFinishIfReCreate"

        private val DOWNLOAD_PATH by lazy {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
        }
    }

    private var needFinishIfReCreate: Boolean = false
    private val selectPathAndSaveFile by lazy { SelectPathAndSaveFile(this) }

    override fun getLayoutResId(): Int {
        return R.layout.share_activity
    }

    /**
    #7838609 体验单，保存界面不需要更对系统主题色，因为这个界面有自己的主题色控制逻辑
     */
    override fun needApplyThemeOverlays(): Boolean {
        return false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (savedInstanceState?.getBoolean(NEED_FINISH_IF_RECREATE) == true) {
            finish()
        }
        selectPathAndSaveFile.initData(savedInstanceState)
        selectPathAndSaveFile.updateColorTheme()
        if (SdkUtils.isAtLeastR()) {
            setTranslucent(true)
        }
        super.onCreate(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putBoolean(NEED_FINISH_IF_RECREATE, needFinishIfReCreate)
        super.onSaveInstanceState(outState)
        selectPathAndSaveFile.onSaveInstanceState(outState)
    }

    override fun initView() {
        openFragmentWaitLayout()
    }

    private fun openFragmentWaitLayout() {
        if (PrivacyPolicyController.hasAgreePrivacy()) {
            this.window.decorView.viewTreeObserver.addOnGlobalLayoutListener(
                object : ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        <EMAIL>(this)
                        //在侧边栏弹出的浮窗中，打开Activity直接ShowFragment会页面显示不全，底下一部分不显示
                        selectPathAndSaveFile.openFragmentByAction()
                    }
                }
            )
        }
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        selectPathAndSaveFile.onSelect(code, paths?.getOrNull(0))
        if (code == MessageConstant.MSG_SAVE) {
            needFinishIfReCreate = true
        }
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
    }

    override fun initData() {
        Log.d(TAG, "initData")
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage")
    }

    override fun transformToNextFragment(path: String?) {
        selectPathAndSaveFile.showSelectPathFragmentDialog(path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        selectPathAndSaveFile.showSelectPathFragmentDialog(code, DOWNLOAD_PATH)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        Log.d(TAG, "showEditLabelFragmentDialog")
    }

    override fun onUpdatedLabel() {
        Log.d(TAG, "onUpdatedLabel")
    }

    override fun hasShowPanel(): Boolean {
        return selectPathAndSaveFile.hasShowPanel()
    }

    override fun getScreenOrientation(): Int? {
        if (selectPathAndSaveFile.fromOplusDocument) {
            return ActivityInfo.SCREEN_ORIENTATION_USER
        }
        return null
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        selectPathAndSaveFile.onUIConfigChanged()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        Log.d(TAG, "onKeyDown keyCode = $keyCode")
        return super.onKeyDown(keyCode, event)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        selectPathAndSaveFile.onDestroy()
        super.onDestroy()
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun handleNoStoragePermission() {
        selectPathAndSaveFile.showSaveFileDialogIfNeed()
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        selectPathAndSaveFile.showSaveFileDialogIfNeed()
    }

    override fun doNothing() {
        Log.d(TAG, "doNothing")
    }
}