/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: FolderPickerFragmentViewModel
 * * Description: the fragment for FolderPickerFragmentViewModel
 * * Version: 1.0
 * * Date : 2020/6/29
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/6/29       1.0         the fragment for FolderPickerFragmentViewModel
 ****************************************************************/
package com.oplus.filemanager.filechoose.ui.folderpicker


import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.oplus.filemanager.filechoose.factor.LoaderFactor
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.base.PathLoadResult
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.createdir.FileActionCreateDir
import com.filemanager.fileoperate.createdir.FileCreateDirObserver
import com.oplus.filemanager.filechoose.R
import com.oplus.filemanager.filechoose.ui.folderpicker.FolderPickerFragment.Companion.SAVE_FILE_PATH_EXTRA
import com.oplus.filemanager.filechoose.utils.FolderPickerUtil
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import java.io.File

class FolderPickerFragmentViewModel : BaseViewModel() {

    companion object {
        private const val TAG = "FolderPickerFragmentViewModel"
        private const val FOLDER_LOADER_ID = 1
        private const val EMULATED_PATH = "/storage/emulated"
    }

    val mUiState = MutableLiveData<FolderUiModel>()
    val mPositionModel = MutableLiveData<SelectFileBrowserViewModel.PositionModel>()
    // Data is one of STATE_START/STATE_DONE/STATE_CANCEL in OnLoaderListener.
    val mDataLoadState = MutableLiveData<Int>()
    var mPathHelp: FileBrowPathHelper? = null
    val mLoaderCallback = FolderPickerCallBack(this)

    fun initLoader(mLoaderController: LoaderController?, path: String) {
        if (mLoaderCallback.getLoader() == null) {
            mPositionModel.value = SelectFileBrowserViewModel.PositionModel(path, 0, 0)
            mPathHelp?.pushTo(path)
            mLoaderController?.initLoader(FOLDER_LOADER_ID, mLoaderCallback)
        } else {
            mLoaderCallback.loadData()
        }
    }

    fun initPathHelper(currentPath: String) {
        if (mPathHelp == null) {
            mPathHelp = FileBrowPathHelper(currentPath)
        }
    }

    class FolderPickerCallBack :
        LoadingLoaderListener<FolderPickerFragmentViewModel, FolderLoader, PathLoadResult<Int, BaseFileBean>> {
        private var mLoadNewPath = true

        constructor(viewModel: FolderPickerFragmentViewModel): super(viewModel, viewModel.mDataLoadState)

        override fun onCreateLoader(viewModel: FolderPickerFragmentViewModel?): FolderLoader? {
            return LoaderFactor.getFolderLoader(viewModel?.mPositionModel?.value?.mCurrentPath ?: "")
        }

        internal fun loadData(path: String? = null, loadNewPath: Boolean = false) {
            mLoadNewPath = loadNewPath
            getLoader()?.apply {
                if (path.isNullOrEmpty().not()) {
                    setPath(path!!)
                }
                forceLoad()
            }
        }

        internal fun isLoadNewPath() = mLoadNewPath

        override fun onLoadComplete(viewModel: FolderPickerFragmentViewModel?, data: PathLoadResult<Int, BaseFileBean>?) {
            Log.d(TAG, "onLoadFinished size" + data?.mResultList?.size)
            viewModel?.apply {
                data?.let {
                    mUiState.postValue(FolderUiModel(data.mResultList as MutableList<BaseFileBean>, true))
                }
                mPositionModel.value = mPositionModel.value
                mPositionModel.value?.mPosition = 0
                mPositionModel.value?.mOffset = 0
            }
        }
    }

    fun pressBack(info: FilePathHelper.PathInfo): Boolean {
        info.path.let {
            mPositionModel.value = SelectFileBrowserViewModel.PositionModel(it, info.position, info.y)
            mLoaderCallback.loadData(it, true)
            return true
        }
    }

    fun onItemClick(activity: BaseVMActivity?, position: Int, firstVisibleItemPosition: Int, offset: Int) {
        if (position >= (mUiState.value?.mFileList?.size ?: 0)) {
            Log.d(TAG, "onItemClick: position > mFileList.size")
            return
        }
        val baseFile = mUiState.value?.mFileList?.get(position)
        Log.d(TAG, "onItemClick baseFile=${baseFile}")
        if ((baseFile == null) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return
        }
        launch {
            val isExits = baseFile.checkExist()
            if (!isExits) {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                return@launch
            } else {
                if (activity != null && baseFile != null) {
                    baseFile.mData?.let {
                        var loadPath = it
                        if (it == EMULATED_PATH) {
                            val currentPath = mPathHelp!!.getInternalPath() ?: it
                            mPathHelp?.push(FilePathHelper.PathInfo(currentPath, firstVisibleItemPosition, offset))
                            mPositionModel.value = SelectFileBrowserViewModel.PositionModel(currentPath, firstVisibleItemPosition, offset)
                            loadPath = currentPath
                        } else {
                            mPathHelp?.push(FilePathHelper.PathInfo(it, firstVisibleItemPosition, offset))
                            mPositionModel.value = SelectFileBrowserViewModel.PositionModel(it, firstVisibleItemPosition, offset)
                        }
                        mLoaderCallback.loadData(loadPath, true)
                    }
                }
            }
        }
    }

    fun onButtonClick(activity: BaseVMActivity?, fileName: String) {
        activity?.let {
            val intent = Intent()
            if (fileName.isEmpty()) {
                intent.putExtra("SAVE_PATH", "file://${mPositionModel.value?.mCurrentPath}")
            } else {
                val filePath = mPositionModel.value?.mCurrentPath + File.separator + fileName
                FolderPickerUtil.grantUriPermissionToData(intent, filePath, SAVE_FILE_PATH_EXTRA)
            }
            activity.setResult(Activity.RESULT_OK, intent)
            activity.finish()
        }
    }

    fun createNewFolder(activity: BaseVMActivity) {
        mPositionModel.value?.mCurrentPath?.let {
            FileActionCreateDir(activity, PathFileWrapper(it)).execute(object : FileCreateDirObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    super.onActionDone(result, data)
                    if (result) {
                        mLoaderCallback.loadData()
                    }
                }
            })
        }
    }

    open class FolderUiModel(
            val mFileList: MutableList<BaseFileBean>,
            var mInitState: Boolean
    )
}