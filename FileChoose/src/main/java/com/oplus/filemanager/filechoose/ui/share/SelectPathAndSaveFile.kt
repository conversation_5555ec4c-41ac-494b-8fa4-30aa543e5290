/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectPathAndSaveFile
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose.ui.share

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.FileOperateApi
import com.filemanager.fileoperate.FileOperateUtil
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.copy.FileCopyObserver
import com.filemanager.fileoperate.save.FileActionSave
import com.oplus.filemanager.filechoose.R
import com.oplus.filemanager.filechoose.ui.folderpicker.FolderPickerActivity.Companion.FOLDER_CURRENT_PATH
import com.oplus.filemanager.filechoose.utils.FolderPickerUtil
import com.oplus.selectdir.SelectDirPathRenamePanelFragment
import com.oplus.selectdir.SelectPathController
import com.oplus.selectdir.SelectPathController.Companion.SELECT_FILE_TYPE_FILL_NAME_LIST
import com.oplus.selectdir.utils.SelectPanelThemeMode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.max

class SelectPathAndSaveFile(private val shareActivity: ShareActivity) {

    companion object {
        private const val TAG = "SelectPathAndSaveFile"
        private val DOWNLOAD_PATH by lazy {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
        }
        private val WECHAT_PATH by lazy {
            "${Environment.getExternalStorageDirectory().absolutePath}/Download/WeiXin"
        }
        private val QQ_PATH by lazy {
            "${Environment.getExternalStorageDirectory().absolutePath}/Tencent/QQfile_recv"
        }

        private const val DELAY_TIME = 500L
        private const val DURATION = 2000L

        @VisibleForTesting
        const val OPLUS_INNER_SEND_ACTION = "oplus.intent.action.filemanager.SEND"
        const val OPLUS_FOLDER_PICKER_ACTION = "oplus.intent.action.folder.picker"
        private const val FROM_SAVE_INSTANCE = "fromSaveInstance"
        private const val FILE_PATH_NAME = "filePath"
        private const val FROM_OPLUS_DOCUMENT = "fromOplusDocument"

        @VisibleForTesting
        const val HOST_WECHAT = "com.tencent.mm.external.fileprovider"

        @VisibleForTesting
        const val HOST_QQ = "com.tencent.mobileqq.fileprovider"

        //定制保存文件对话框样式参数
        private const val PARAMETER_FILE_EXT_LIST = "fileExtList"
        private const val PARAMETER_RETURN_PATH = "returnPath"
        private const val PARAMETER_COLOR_THEME_MODE = "color_theme_mode"
        private const val PARAMETER_DESTINATION_DIR_PATH = "destinationDirPath"
        private const val PARAMETER_IS_OVERRIDE = "is_override"
        private const val PARAMETER_PANEL_TITLE = "title"
        private const val PARAMETER_BTN_CONFIRM = "btn_confirm"
    }

    private val intent = shareActivity.intent
    private val supportFragmentManager = shareActivity.supportFragmentManager

    @VisibleForTesting
    var hasShowSaveFileDialog: Boolean = false

    @VisibleForTesting
    var fromSaveInstance: Boolean = false
    private val selectPathController by lazy { SelectPathController(shareActivity.lifecycle) }
    private val handler by lazy { Handler(Looper.getMainLooper()) }

    @VisibleForTesting
    var filePath: String? = null

    // -1无效的颜色
    private var themeMode: Int = -1

    @VisibleForTesting
    var destinationDirPath: String? = null
    private var isOverrideStyle: Boolean = false
    private var fileExtList: ArrayList<String> = ArrayList()
    private var fileTypeFillNameList: ArrayList<String> = ArrayList()
    private var title: String? = null
    private var btnTitle: String? = null

    var fromOplusDocument: Boolean = false // 是否从文件随心开跳转，只有文件随心开支持弹框横屏

    private val finishRunnable = Runnable {
        shareActivity.finish()
    }

    fun initData(savedInstanceState: Bundle?) {
        if (savedInstanceState?.getBoolean(FROM_SAVE_INSTANCE) == true) {
            filePath = savedInstanceState.getString(FILE_PATH_NAME)
            fromOplusDocument = savedInstanceState.getBoolean(FROM_OPLUS_DOCUMENT, false)
            themeMode = savedInstanceState.getInt(PARAMETER_COLOR_THEME_MODE)
            destinationDirPath = savedInstanceState.getString(PARAMETER_DESTINATION_DIR_PATH)
            isOverrideStyle = savedInstanceState.getBoolean(PARAMETER_IS_OVERRIDE)
            fileExtList = savedInstanceState.getStringArrayList(PARAMETER_FILE_EXT_LIST) ?: ArrayList()
            fileTypeFillNameList = savedInstanceState.getStringArrayList(SELECT_FILE_TYPE_FILL_NAME_LIST) ?: ArrayList()
            title = savedInstanceState.getString(PARAMETER_PANEL_TITLE)
            btnTitle = savedInstanceState.getString(PARAMETER_BTN_CONFIRM)
            fromSaveInstance = true
        } else {
            filePath = (IntentUtils.getParcelable(intent, Intent.EXTRA_STREAM) as? Uri)?.path
            fromOplusDocument = IntentUtils.getBoolean(intent, FROM_OPLUS_DOCUMENT, false)
            themeMode = IntentUtils.getInt(intent, PARAMETER_COLOR_THEME_MODE, -1)
            destinationDirPath = IntentUtils.getString(intent, PARAMETER_DESTINATION_DIR_PATH)
            isOverrideStyle = IntentUtils.getBoolean(intent, PARAMETER_IS_OVERRIDE, false)
            fileExtList = IntentUtils.getStringArrayList(intent, PARAMETER_FILE_EXT_LIST) ?: ArrayList()
            fileTypeFillNameList = IntentUtils.getStringArrayList(intent, SELECT_FILE_TYPE_FILL_NAME_LIST) ?: ArrayList()
            title = IntentUtils.getString(intent, PARAMETER_PANEL_TITLE)
            btnTitle = IntentUtils.getString(intent, PARAMETER_BTN_CONFIRM)
            fromSaveInstance = false
        }
        Log.d(
            TAG, "filePath = $filePath" +
                    ", fromOplusDocument = $fromOplusDocument" +
                    ", colorStyleMode = $themeMode" +
                    ", destinationDirPath = $destinationDirPath" +
                    ", warningDuplicateFile = $isOverrideStyle" +
                    ", fileExtList = $fileExtList" +
                    ", fileTypeFillNameList = $fileTypeFillNameList" +
                    ", fromSaveInstance = $fromSaveInstance" +
                    ", title = $title" +
                    ", btnTitle = $btnTitle"
        )
    }

    fun openFragmentByAction() {
        val action = intent?.action ?: kotlin.run {
            Log.e(TAG, "openFragmentByAction action is null, return")
            delayFinish()
            return
        }
        if (fromSaveInstance.not()) {
            when (action) {
                OPLUS_INNER_SEND_ACTION -> handleOplusAction()
                OPLUS_FOLDER_PICKER_ACTION -> handleFolderPickerAction()
                else -> handleOtherAction()
            }
        }
        hasShowSaveFileDialog = true
        fromSaveInstance = false
    }

    @VisibleForTesting
    fun delayFinish(delay: Long = 0L) {
        if (delay == 0L) {
            handler.post(finishRunnable)
        } else {
            handler.postDelayed(finishRunnable, delay)
        }
    }

    @VisibleForTesting
    fun handleOplusAction() {
        if (filePath.isNullOrEmpty()) {
            Log.e(TAG, "handleOplusAction filePath is null, return")
            delayFinish()
            return
        }
        val desDir = destinationDirPath ?: DOWNLOAD_PATH
        Log.i(TAG, "handleOplusAction destinationDirPath $destinationDirPath, desDir = $desDir")
        val hasStoragePermission = PermissionUtils.hasStoragePermission(shareActivity)
        val file = File(desDir)
        if (!file.exists()) {
            Log.e(TAG, "destination dir don't exits,create it")
            file.mkdir()
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.FILE_NAME, FilenameUtils.getBaseName(filePath))
        bundle.putStringArrayList(KtConstants.FILE_EXT, fileExtList)
        bundle.putStringArrayList(
            SELECT_FILE_TYPE_FILL_NAME_LIST,
            fileTypeFillNameList
        )
        bundle.putBoolean(
            SelectDirPathRenamePanelFragment.PARAMETER_IS_OVERRIDE,
            isOverrideStyle
        )
        bundle.putString(
            SelectDirPathRenamePanelFragment.PARAMETER_PANEL_TITLE,
            title
        )
        bundle.putString(
            SelectDirPathRenamePanelFragment.PARAMETER_BTN_CONFIRM,
            btnTitle
        )
        showSelectPathFragmentDialog(
            MessageConstant.MSG_SAVE_AND_RENAME,
            desDir,
            bundle,
            hasStoragePermission
        )
    }

    private fun handleFolderPickerAction() {
        val hasStoragePermission = PermissionUtils.hasStoragePermission(shareActivity)
        showSelectPathFragmentDialog(
            MessageConstant.MSG_FOLDER_PICKER,
            "",
            hasStoragePermission = hasStoragePermission
        )
    }

    @VisibleForTesting
    fun handleOtherAction() {
        val hasStoragePermission = PermissionUtils.hasStoragePermission(shareActivity)
        val shareHost = intent?.data?.host ?: ""
        Log.d(TAG, "handleOtherAction shareHost = $shareHost")
        val savePath = when (shareHost) {
            HOST_WECHAT -> WECHAT_PATH
            HOST_QQ -> QQ_PATH
            else -> DOWNLOAD_PATH
        }
        Log.d(TAG, "openFragmentByAction savePath:$savePath")
        showSelectPathFragmentDialog(
            MessageConstant.MSG_SAVE,
            savePath,
            hasStoragePermission = hasStoragePermission
        )
    }

    fun updateColorTheme() {
        val themeRes = SelectPanelThemeMode.getTheme(themeMode)
        shareActivity.setTheme(themeRes)
        shareActivity.theme.applyStyle(R.style.AppNoTitleThemeTranslucentInShareActivity, true)
    }

    fun onSaveInstanceState(outState: Bundle) {
        outState.putBoolean(FROM_SAVE_INSTANCE, true)
        outState.putString(FILE_PATH_NAME, filePath)
        outState.putInt(PARAMETER_COLOR_THEME_MODE, themeMode)
        outState.putString(PARAMETER_DESTINATION_DIR_PATH, destinationDirPath)
        outState.putBoolean(PARAMETER_IS_OVERRIDE, isOverrideStyle)
        outState.putStringArrayList(PARAMETER_FILE_EXT_LIST, fileExtList)
        outState.putStringArrayList(SELECT_FILE_TYPE_FILL_NAME_LIST, fileTypeFillNameList)
        outState.putString(PARAMETER_PANEL_TITLE, title)
        outState.putString(PARAMETER_BTN_CONFIRM, btnTitle)
    }

    fun showSelectPathFragmentDialog(
        code: Int,
        path: String,
        bundle: Bundle? = null,
        hasStoragePermission: Boolean = true
    ) {
        selectPathController.showSelectPathFragmentDialog(
            supportFragmentManager,
            code,
            path,
            bundle,
            hasStoragePermission
        )
    }

    fun showSelectPathFragmentDialog(path: String?) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    @VisibleForTesting
    fun destroySelectPathController() {
        selectPathController.onDestroy()
    }

    fun hasShowPanel(): Boolean {
        return selectPathController.hasShowPanel()
    }

    fun onSelect(code: Int, path: String?) {
        destroySelectPathController()
        val filePath = path ?: kotlin.run {
            Log.e(TAG, "path is null, return")
            return
        }
        val action = intent?.action ?: kotlin.run {
            Log.e(TAG, "onSelect action is null, return")
            return
        }
        Log.d(TAG, "action=$action, intent.type=${intent.type}")
        val state = when (action) {
            Intent.ACTION_SEND -> handleActionSend(filePath)
            Intent.ACTION_SEND_MULTIPLE -> handleActionSendMultiple()
            OPLUS_INNER_SEND_ACTION -> handleOplusInnerSendAction(filePath)
            OPLUS_FOLDER_PICKER_ACTION -> handleOplusFolderPicker(filePath)
            Intent.ACTION_VIEW -> handlerActionView()
            else -> {
                Log.e(TAG, "do nothing")
                Pair(false, arrayListOf())
            }
        }
        val handlerSuccess = state.first
        if (handlerSuccess.not()) {
            Log.e(TAG, "now need save file, return")
            return
        }
        val listUris = state.second
        if (listUris.isNotEmpty()) {
            saveFile(filePath, listUris)
        } else {
            waringUser()
        }
    }

    @VisibleForTesting
    fun handleActionSend(filePath: String): Pair<Boolean, ArrayList<Uri>> {
        return if ((intent.type?.let { MimeTypeHelper.getTypeByMimeType(it) } == MimeTypeHelper.TXT_TYPE)
            && !IntentUtils.getString(intent, Intent.EXTRA_TEXT).isNullOrEmpty()) {
            handleSendTextFile(intent, filePath)
            Pair(false, arrayListOf())
        } else {
            val listUris = ArrayList<Uri>()
            val uri = IntentUtils.getParcelable(intent, Intent.EXTRA_STREAM) as? Uri
            if (uri != null) {
                listUris.add(uri)
            }
            Pair(true, listUris)
        }
    }

    @VisibleForTesting
    fun handleActionSendMultiple(): Pair<Boolean, ArrayList<Uri>> {
        val listUris = ArrayList<Uri>()
        val tempUris = IntentUtils.getParcelableArrayList(intent, Intent.EXTRA_STREAM)
        if (tempUris != null && tempUris.size > 0) {
            for (uri in tempUris) {
                listUris.add(uri as Uri)
            }
        }
        return Pair(true, listUris)
    }

    @VisibleForTesting
    fun handleOplusInnerSendAction(filePath: String): Pair<Boolean, ArrayList<Uri>> {
        Log.d(TAG, "handleOplusInnerSendAction : file path $filePath")
        val data = Intent()
        FolderPickerUtil.grantUriPermissionToData(
            data, filePath,
            PARAMETER_RETURN_PATH
        )
        shareActivity.setResult(AppCompatActivity.RESULT_OK, data)
        delayFinish(DELAY_TIME)
        return Pair(false, arrayListOf())
    }

    @VisibleForTesting
    fun handleOplusFolderPicker(filePath: String): Pair<Boolean, ArrayList<Uri>> {
        Log.d(TAG, "handleOplusFolderPicker : file path $filePath")
        val data = Intent()
        data.putExtra(FOLDER_CURRENT_PATH, filePath)
        shareActivity.setResult(AppCompatActivity.RESULT_OK, data)
        delayFinish(DELAY_TIME)
        return Pair(false, arrayListOf())
    }

    @VisibleForTesting
    fun handlerActionView(): Pair<Boolean, ArrayList<Uri>> {
        val listUris = ArrayList<Uri>()
        intent?.data?.let {
            listUris.add(it)
        }
        return Pair(true, listUris)
    }

    @VisibleForTesting
    fun saveFile(filePath: String, listUris: ArrayList<Uri>) {
        val listFiles = ArrayList<BaseFileBean>()
        shareActivity.lifecycleScope.launch(Dispatchers.IO) {
            for (uri in listUris) {
                Log.d(TAG, "saveFile authority=${uri.authority}")
                uri.authority?.apply {
                    if (startsWith("com.oplus.filemanager")
                        || startsWith("com.coloros.filemanager")
                        || startsWith("com.oneplus.filemanager")
                    ) {
                        delayFinish(DELAY_TIME)
                        return@launch
                    }
                }
                val propertyBean = FileMediaHelper.getOpenableProperty(shareActivity, uri)
                Log.d(TAG, "onSelect name = ${propertyBean.mDisplayName} size = ${propertyBean.mSize} uri = $uri")
                val bean = BaseFileBean()
                val hashCode = uri.hashCode()
                bean.mData = shareActivity.cacheDir.absolutePath + File.separator + hashCode + File.separator + propertyBean.mDisplayName
                bean.mLocalFileUri = uri
                bean.mSize = propertyBean.mSize
                bean.mDisplayName = propertyBean.mDisplayName
                bean.virtualFileHashCode = hashCode.toString()
                listFiles.add(bean)
                bean.mDisplayName?.apply {
                    if (KtUtils.isIllegalPathString(this)) {
                        Log.e(TAG, "saveFile isIllegalPath")
                        delayFinish(DELAY_TIME)
                        return@launch
                    }
                }
            }
            Log.d(TAG, " target file :$filePath")
            withContext(Dispatchers.Main) {
                FileActionSave(shareActivity, listFiles, PathFileWrapper(filePath)).execute(
                    object : FileCopyObserver(shareActivity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            val delayedTime = if (result) DURATION else DELAY_TIME
                            delayFinish(delayedTime)
                        }

                        override fun onActionCancelled() {
                            delayFinish(DELAY_TIME)
                        }

                        override fun onActionReloadData() {
                            Log.d(TAG, "onActionReloadData")
                        }

                        override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                            return when (result.first) {
                                ACTION_DONE -> {
                                    FileOperateUtil.savedFileNotice(
                                        shareActivity,
                                        result.second as? String
                                    )
                                    false
                                }

                                ACTION_FAILED -> {
                                    FileOperateUtil.saveFileFailed()
                                    false
                                }

                                else -> super.onChanged(context, result)
                            }
                        }
                    })
            }
        }
    }

    @VisibleForTesting
    fun waringUser() {
        Toast.makeText(shareActivity, com.filemanager.common.R.string.drag_saved_fail, Toast.LENGTH_SHORT).show()
        shareActivity.finish()
    }

    @VisibleForTesting
    fun handleSendTextFile(intent: Intent, path: String) {
        val text = IntentUtils.getString(intent, Intent.EXTRA_TEXT) ?: return
        val date = Date()
        val f = SimpleDateFormat(FileOperateApi.TIME_PATTERN, Locale.CHINA)
        val fileName = FileOperateApi.FILE_NAME_PREFIX + f.format(date) + FileOperateApi.TEXT_EXT
        val file = File(path, fileName)
        var saveSuccess = true
        shareActivity.lifecycleScope.launch(Dispatchers.IO) {
            var fileSize = text.toByteArray().size.toLong() + FileOperateUtil.FILE_SIZE_GAP
            fileSize = max(FileOperateUtil.MIN_FILE_SIZE, fileSize)
            if (!FileOperateUtil.checkDestPathIsEnoughSpace(path, fileSize)) {
                withContext(Dispatchers.Main) {
                    FileOperateUtil.showSpaceNotEnoughTips(activity = shareActivity)
                }
                return@launch
            }
            var outputStream: OutputStream? = null
            try {
                outputStream = FileOutputStream(file)
                outputStream.write(text.toByteArray())
                MediaScannerCompat.sendMediaScanner(path, Utils.MEDIA_SCAN_SAVE)
            } catch (e: IOException) {
                saveSuccess = false
                Log.d(TAG, "handleSendTextFile save fail e = $e")
            } finally {
                try {
                    outputStream?.close()
                } catch (e: IOException) {
                    Log.d(TAG, "handleSendTextFile close fail e = $e")
                }
            }
            withContext(Dispatchers.Main) {
                if (saveSuccess) {
                    FileOperateUtil.savedFileNotice(shareActivity, path)
                    delayFinish(DURATION)
                } else {
                    FileOperateUtil.saveFileFailed()
                    delayFinish(DELAY_TIME)
                }
            }
        }
    }


    fun onUIConfigChanged() {
        selectPathController.updateDialogHeightIfNeed(supportFragmentManager)
        if (fromOplusDocument) {
            selectPathController.onUIConfigChanged()
        }
    }

    fun showSaveFileDialogIfNeed() {
        val hasStoragePermission = PermissionUtils.hasStoragePermission(shareActivity)
        Log.d(
            TAG, "showSaveFileDialogIfNeed hasStoragePermission $hasStoragePermission" +
                    ", hasShowSaveFileDialog = $hasShowSaveFileDialog"
        )
        if (hasShowSaveFileDialog.not()) {
            if (PrivacyPolicyController.hasAgreePrivacy()) {
                openFragmentByAction()
            }
        } else {
            selectPathController.onStoragePermissionChange(
                hasStoragePermission,
                supportFragmentManager
            )
        }
    }

    fun onDestroy() {
        Log.d(TAG, "onDestroy")
        handler.removeCallbacks(finishRunnable)
        selectPathController.hideSelectPathFragmentDialog(supportFragmentManager)
    }
}