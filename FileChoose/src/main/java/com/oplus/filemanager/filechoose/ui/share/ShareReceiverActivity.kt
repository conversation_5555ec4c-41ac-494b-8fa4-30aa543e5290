/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareReceiverActivity
 * * Description : 接收来自一碰分享的数据的Activity
 * * Version     : 1.0
 * * Date        : 2025/05/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose.ui.share

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.ArrayUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.touchshare.ITouchShareApi
import java.io.File

class ShareReceiverActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ShareReceiverActivityTAG"
    }

    private val shareApi by lazy {
        Injector.injectFactory<ITouchShareApi>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.e(TAG, "onCreate")
        dispatchJumpActivity(intent)
    }

    private fun dispatchJumpActivity(intent: Intent) {
        val files = shareApi?.getShareData(intent)
        Log.d(TAG, "dispatchJumpActivity receive share files:$files")
        if (files.isNullOrEmpty()) {
            Log.e(TAG, "dispatchJumpActivity receive share files is empty !!!")
            finish()
            return
        }
        if (files.size == 1) {
            openSingleFile(files.get(0))
        } else {
            openMultiFiles(files)
        }
    }

    private fun openSingleFile(file: BaseFileBean) {
        Log.d(TAG, "openSingleFile $file")
        FileActionOpen.Companion.Builder(this, file)
            .setIsFromRecentCardWidget(true)
            .build()
            .execute(object : FileOpenObserver(this@ShareReceiverActivity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    val isShowDialog = isShowDialog()
                    Log.d(TAG, "openSingleFile -> onActionDone result $result, showDialog:$isShowDialog")
                    if (!isShowDialog) {
                        finish()
                    }
                }

                override fun onActionCancelled() {
                    super.onActionCancelled()
                    Log.d(TAG, "openSingleFile -> onActionCancelled")
                    finish()
                }
            })
    }

    /**
     * 多个文件打开情况
     */
    private fun openMultiFiles(files: List<BaseFileBean>) {
        val filePath = files.get(0).mData ?: return
        Log.d(TAG, "openMultiFiles $filePath")
        if (FeatureCompat.isSmallScreenPhone) {
            jumpShareSuperActivity(filePath)
        } else {
            jumpMainActivity(filePath)
        }
        finish()
    }


    /**
     * 跳转到FileBrowserActivity
     */
    private fun jumpFileBrowserActivity(filePath: String) {
        Log.d(TAG, "jumpFileBrowserActivity filePath:$filePath")
        val api = Injector.injectFactory<IFileBrowser>()
        api?.toFileBrowserActivity(this, filePath)
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_ACCESSORY, "", Constants.PAGE_FILE_BROWSER)
    }

    /**
     * 跳转到来源的互传界面
     */
    private fun jumpShareSuperActivity(filePath: String) {
        Log.d(TAG, "jumpShareSuperActivity filePath:$filePath")
        val relativePath = File(filePath).parent
        val superApp = Injector.injectFactory<ISuperApp>() ?: return
        val data = superApp.getCategoryItems(this).find { it.itemType == CategoryHelper.CATEGORY_SHARE } ?: return
        val defaultList = data.fileList
        data.externalPath = filePath
        data.fileList = ArrayUtils.add(defaultList, relativePath)
        superApp.startSuperApp(this, data)
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_ACCESSORY, "", Constants.PAGE_SUPER)
    }

    /**
     * 跳转到文管首页
     */
    private fun jumpMainActivity(path: String) {
        Log.d(TAG, "jumpMainActivity filePath:$path")
        val intent = Intent()
        intent.setAction(Constants.ACTION_MAIN_CATEGORY_SUPER)
        intent.putExtra(KtConstants.P_CATEGORY_TYPE, CategoryHelper.CATEGORY_SHARE)
        intent.putExtra(KtConstants.FILE_PATH, path)
        intent.setPackage(this.packageName)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivity(intent)
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_ACCESSORY, "", Constants.PAGE_MAIN)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        Log.e(TAG, "onBackPressed")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e(TAG, "onDestroy")
    }
}