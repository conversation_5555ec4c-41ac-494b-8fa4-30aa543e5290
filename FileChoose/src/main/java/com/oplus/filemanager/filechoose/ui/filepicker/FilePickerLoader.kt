/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.fileselection
 * * Version     : 1.0
 * * Date        : 2020/6/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose.ui.filepicker

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.loader.BaseUriLoader
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.main.IMain

class FilePickerLoader(context: Context, currentFileType: Int) : BaseUriLoader<Int, MediaFileWrapper>(context) {

    companion object {
        private const val TAG = "FileSelectionLoader"
        private const val CATEGORY_AUDIO = 0
        private const val DEFAULT_SORT = -1
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private val MEDIA_PROJECT = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE
        )

        @JvmField
        val DEF_ALL_EXT_ARRAY = java.util.ArrayList<String?>().apply {
            add(".txt")
            add(".doc")
            add(".docx")
            add(".xls")
            add(".xlsx")
            add(".ppt")
            add(".pptx")
            add(".pdf")
            add(".ofd")
        }
    }

    private var mCurrentFileType = currentFileType
    private val mInternalPath: String? = VolumeEnvironment.getInternalSdPath(appContext)
    private var mFilter: com.oplus.filemanager.category.document.ui.DocumentFilter? = null

    init {
        super.initData()
        if (mCurrentFileType == CategoryHelper.CATEGORY_DOC) {
            mFilter = com.oplus.filemanager.category.document.ui.DocumentFilter()
        }
    }

    override fun getUri(): Uri? {
        val uri = when (mCurrentFileType) {
            CATEGORY_AUDIO -> MediaHelper.AUDIO_MEDIA_URI
            else -> MediaStore.Files.getContentUri("external")
        }
        Log.d(TAG, "getUri = $uri")
        return uri
    }

    override fun getSelection(): String? {
        val selection = when (mCurrentFileType) {
            CATEGORY_AUDIO -> {
                MediaStore.Audio.Media.DATA + " LIKE '%" + mInternalPath + "%'"
            }
            CategoryHelper.CATEGORY_DOC -> getDocSelection()
            else -> null
        }
        Log.d(TAG, "getSelection = selection")
        return selection
    }

    override fun getSelectionArgs(): Array<String>? {
        Log.d(TAG, "getSelectionArgs(): Not yet implemented")
        return null
    }

    override fun getObserverUri(): Array<Uri>? {
        Log.d(TAG, "getObserverUri(): Not yet implemented")
        return null
    }

    override fun getProjection(): Array<String> {
        return MEDIA_PROJECT
    }

    override fun preHandleBeforeBackground() {
        super.preHandleBeforeBackground()
        mFilter?.updateFilterOptions(DEFAULT_SORT)
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(list)
        return list
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val id = cursor.getInt(INDEX_ID)
        val data = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * SECONDS_TO_MILLISECONDS
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        // 从媒体库可能 mDateModified 读取出来为0，这里为0时，从新从文件中读取一下
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(data) ?: 0
        }
        val mediaFileWrapper = MediaFileWrapper(id, data, displayName, mimeType, size, dateModified, MediaHelper.FILE_URI)
        if (!JavaFileHelper.exists(mediaFileWrapper)) {
            Log.d(TAG, "createFromCursor: mediaFileWrapper not exists ")
            return null
        }
        if (mFilter?.checkIsFilterItem(mediaFileWrapper) == true) {
            Log.d(TAG, "createFromCursor filter file")
            return null
        }
        when (mCurrentFileType) {
            CATEGORY_AUDIO -> {
                if (mediaFileWrapper.mLocalType != MimeTypeHelper.DRM_TYPE) {
                    mediaFileWrapper.mLocalType = MimeTypeHelper.AUDIO_TYPE
                }
            }
        }
        return mediaFileWrapper
    }

    private fun getDocSelection(): String {
        val sql = StringBuilder()
        val selectionArgs = ArrayList<String?>()
        selectionArgs.addAll(DEF_ALL_EXT_ARRAY)
        sql.append(MediaStoreCompat.getMediaStoreSqlQuery(CategoryHelper.CATEGORY_DOC, selectionArgs))
        if (!TextUtils.isEmpty(sql)) {
            sql.append(" AND ")
        }
        if (!SdkUtils.isAtLeastR()) {
            /**Confirmed with Media colleagues (XiongBobo W9002523)
             * that both VOLUME_NAME [MediaStore.VOLUME_EXTERNAL_PRIMARY] [MediaStore.VOLUME_EXTERNAL] are stored on the phone,
             * so they are added.*/
            sql.append(" ( ")
            sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL_PRIMARY + "'")
            sql.append(" or ")
            sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL + "'")
            sql.append(" ) ")
        } else {
            sql.append(MediaStore.Files.FileColumns.DATA + " LIKE '%" + mInternalPath + "%'")
        }
        Log.d(TAG, "getDocSelection: sql = $sql")
        return sql.toString()
    }

    override fun getItemKey(item: MediaFileWrapper): Int? {
        return item.id
    }
}