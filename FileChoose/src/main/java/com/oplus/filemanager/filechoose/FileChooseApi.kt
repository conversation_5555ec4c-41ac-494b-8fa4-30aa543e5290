/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileChooseApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose

import android.content.Context
import com.oplus.filemanager.filechoose.ui.singlepicker.SinglePickerActivity
import com.oplus.filemanager.interfaze.filechoose.IFileChoose

object FileChooseApi : IFileChoose {

    override fun isSinglePickerActivity(context: Context?): Boolean {
        return context is SinglePickerActivity
    }

    override fun singlePickerOnBackPressed(context: Context?) {
        (context as? SinglePickerActivity)?.onBackPressed()
    }

    override fun singlePickerTransformToPickerFragment(context: Context?, path: String) {
        (context as? SinglePickerActivity)?.transformToPickerFragment(path)
    }
}