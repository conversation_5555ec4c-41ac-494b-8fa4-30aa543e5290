<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppNoTitleThemeTranslucent" parent="AppNoTitleTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="windowPreviewType">0</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>


    <style name="AppNoTitleThemeTranslucentInShareActivity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="windowPreviewType">0</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.COUITheme.WithToolBar</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:textDirection">locale</item>
        <item name="viewInflaterClass">com.coui.appcompat.theme.COUIComponentsViewInflater</item>
        <item name="android:forceDarkAllowed">true</item>
        <item name="android:isLightTheme">true</item>
        <item name="enableFollowSystemForceDarkRank">true</item>
        <item name="android:fastScrollThumbDrawable">@drawable/icon_fast_scroll</item>
        <item name="android:fastScrollTrackDrawable">@null</item>
    </style>

</resources>