/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: CollectPrivacyDao
 * * Description: 收集隐私的数据库DAO
 * * Version: 1.0
 * * Date : 2024/08/26
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445        2024/08/26      1.0            create
 ****************************************************************/
package com.oplus.filemanager.room.dao.privacy

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy.REPLACE
import androidx.room.Query
import androidx.room.Update
import com.oplus.filemanager.room.model.CollectPrivacyEntity

@Dao
interface CollectPrivacyDao {

    @Insert(onConflict = REPLACE)
    fun insertItem(item: CollectPrivacyEntity): Long


    @Insert(onConflict = REPLACE)
    fun insertItems(items: List<CollectPrivacyEntity>?): List<Long>

    @Delete
    fun deleteItem(item: CollectPrivacyEntity): Int


    @Delete
    fun deleteItems(item: List<CollectPrivacyEntity>?): Int


    @Update(onConflict = REPLACE)
    fun updateItem(item: CollectPrivacyEntity): Int

    /**
     * Update multiple items, by match PrimaryKey
     */
    @Update(onConflict = REPLACE)
    fun updateItems(item: List<CollectPrivacyEntity>?): Int

    /**
     * 查询数据
     * @param category 分类
     * @param earliestTime 记录的最早时间
     * @param latestTime 记录的最晚时间
     */
    @Query("SELECT * FROM collect_privacy WHERE category =:category and time >= :earliestTime and time <= :latestTime")
    fun queryAll(category: String, earliestTime: Long, latestTime: Long): List<CollectPrivacyEntity>?

    /**
     * 查询数据
     * @param category 分类
     */
    @Query("SELECT * FROM collect_privacy WHERE category =:category")
    fun queryAll(category: String): List<CollectPrivacyEntity>?


    @Query("DELETE FROM collect_privacy")
    fun clear(): Int
}
