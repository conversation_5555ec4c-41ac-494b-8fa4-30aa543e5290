/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchHistoryDao
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/20      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.room.dao.searchHistory

import androidx.room.Dao
import androidx.room.Query
import com.oplus.filemanager.room.dao.RoomBaseDao
import com.oplus.filemanager.room.model.SearchHistoryEntity

@Dao
interface SearchHistoryDao : RoomBaseDao<SearchHistoryEntity> {

    @Query("SELECT * FROM search_history ORDER BY search_time DESC, _id DESC")
    fun getAllSearchHistory(): List<SearchHistoryEntity>?

    @Query("DELETE FROM search_history WHERE _id IN (:ids)")
    fun deleteSearchHistory(ids: List<Long>): Int

    @Query("UPDATE search_history SET search_time = :time WHERE _id = :id")
    fun updateSearchHistory(id: Long, time: Long): Int

    @Query("DELETE FROM search_history")
    fun clearHistory(): Int
}