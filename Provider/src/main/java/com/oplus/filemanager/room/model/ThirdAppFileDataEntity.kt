/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:ThirdAppFileDataEntity.kt
 * * Description:Table search_filter entity
 * * Version:1.0
 * * Date :2024/4/11
 * * Author:huangyuanwang
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/28,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.room.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey

@Entity(
    tableName = "third_app_file_data",
    indices = [Index(value = ["name", "package", "source_name"], unique = true)]
)
data class ThirdAppFileDataEntity(
    //行号
    @ColumnInfo(name = "_id")
    @PrimaryKey(autoGenerate = true)
    var id: Int? = null,
    //接收发送的文件名称(ps: 文档.doc)
    @ColumnInfo(name = "name") var mFileName: String,
    //接收发送的文件大小，单位B (ps: )
    @ColumnInfo(name = "size") var mFileSize: Long? = 0L,
    //文件所在应用唯一id，可空
    @ColumnInfo(name = "uid") var mUid: String? = null,
    //监听的apk的包名（ps: com.tentent.mm）
    @ColumnInfo(name = "package") var mDectPackage: String,
    //监听的apk的版本号(ps: 微信版本号)
    @ColumnInfo(name = "pkg_version") var mDectPackageVersion: String?,
    //来源类型，0单聊，1群聊，2收藏，3云文档等
    @ColumnInfo(name = "source_type") var mSourceType: Int,
    //来源名称，聊天会话的昵称或群名
    @ColumnInfo(name = "source_name") var mSourceName: String,
    //文件来源的界面名称,主要是数据采集的页面(ps. com.tencent.mm.ui.LauncherUI)
    @ColumnInfo(name = "source_activity") var mSourceActivity: String,
    //文件预览页面名称，如(ps. com.tencent.wework.common.controller.TbsFileActivity)
    @ColumnInfo(name = "preview_activity") var mPreviewActivity: String,
    //图文提取框架中提取到的文件发送时间(微信，qq中文件的发送时间)
    @ColumnInfo(name = "file_time") var mFileSendTime: Long? = null,
    //AppSwitch监听到页面时，存储的检测的时间戳
    @ColumnInfo(name = "detect_time") var mDetectTime: Long,
    //图文提取框架中解析的所有页面数据，放在meta-data中
    @ColumnInfo(name = "meta_data") var mMetaData: String,
    //在搜索中台中索引记录版本号
    @ColumnInfo(name = "index_checksum") var mIndexCheckSum: Long,
    //在搜索中台中索引id
    @ColumnInfo(name = "identification") var mIndentification: String? = null,
    //解析这个meta时，当前云控配置的版本号
    @ColumnInfo(name = "parse_version") var mParseVersion: Int
)