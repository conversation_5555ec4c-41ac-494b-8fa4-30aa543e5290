/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:AppDatabase.kt
 * * Description:Database class, used to obtain the database and table operation Dao implementation class
 * * Version:1.0
 * * Date :2020/8/28
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/28,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.room

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase.CONFLICT_REPLACE
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.provider.store.CloudFileDriveStore
import com.oplus.filemanager.provider.store.GlobalSearchStore
import com.oplus.filemanager.provider.store.LabelStore
import com.oplus.filemanager.provider.store.PreviewDataStore
import com.oplus.filemanager.provider.store.RecentFileStore
import com.oplus.filemanager.provider.store.ShortcutFolderStore
import com.oplus.filemanager.provider.store.ThirdAppFileStore
import com.oplus.filemanager.room.dao.document.FileTimeDataDao
import com.oplus.filemanager.room.dao.filedrive.DriveFileDao
import com.oplus.filemanager.room.dao.label.FileLabelDao
import com.oplus.filemanager.room.dao.label.FileLabelMappingDao
import com.oplus.filemanager.room.dao.label.FileLabelMappingRecycleDao
import com.oplus.filemanager.room.dao.preview.PreviewDataDao
import com.oplus.filemanager.room.dao.recent.RecentDataDao
import com.oplus.filemanager.room.dao.searchHistory.SearchHistoryDao
import com.oplus.filemanager.room.dao.shortcutFolder.ShortcutFolderDao
import com.oplus.filemanager.room.dao.shortcutFolder.ShortcutFolderRecycleDao
import com.oplus.filemanager.room.dao.thirdappfile.ThirdAppFileDataDao
import com.oplus.filemanager.room.model.DriveFileEntity
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import com.oplus.filemanager.room.model.FileLabelMappingRecycleEntity
import com.oplus.filemanager.room.model.FileTimeDataEntity
import com.oplus.filemanager.room.model.PreviewDataEntity
import com.oplus.filemanager.room.model.RecentFilesEntity
import com.oplus.filemanager.room.model.SearchFilterEntity
import com.oplus.filemanager.room.model.SearchHistoryEntity
import com.oplus.filemanager.room.model.ShortcutFolderEntity
import com.oplus.filemanager.room.model.ShortcutFolderRecycleEntity
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity
import com.oplus.filemanager.room.utils.OplusFavoriteHelper

private const val DATABASE_NAME = "internal_filemanager.db"

/**
 * 2 > 3 ADD file_label table and file_label_mapping table
 * 3 > 4 add preview_data table
 *
 * 5 >> 6 add cloud docs table
 * 7 >> 8 add quick_folder relate tables
 * **/
private const val DATABASE_VERSION_5 = 5
private const val DATABASE_VERSION_6 = 6
private const val DATABASE_VERSION_7 = 7
private const val DATABASE_VERSION_8 = 8

private const val DATABASE_VERSION = DATABASE_VERSION_8

@Database(
    entities = [SearchHistoryEntity::class,
        SearchFilterEntity::class,
        RecentFilesEntity::class,
        FileLabelEntity::class,
        FileLabelMappingEntity::class,
        FileLabelMappingRecycleEntity::class,
        PreviewDataEntity::class,
        FileTimeDataEntity::class,
        DriveFileEntity::class,
        ThirdAppFileDataEntity::class,
        ShortcutFolderEntity::class,
        ShortcutFolderRecycleEntity::class],
    version = DATABASE_VERSION
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun recentDao(): RecentDataDao?

    /** search history dao */
    abstract fun searchHistoryDao(): SearchHistoryDao?

    /** file label dao */
    abstract fun fileLabelDao(): FileLabelDao?

    /** file label mapping dao */
    abstract fun fileLabelMappingDao(): FileLabelMappingDao?

    /** file label mapping for recycle dao */
    abstract fun fileLabelMappingRecycleDao(): FileLabelMappingRecycleDao?

    /**
     * preview dat dao
     */
    abstract fun previewDataDao(): PreviewDataDao?

    /**
     * document Time Data  Dao
     */
    abstract fun fileTimeDataDao(): FileTimeDataDao?

    /**
     * Drive File Dao
     */
    abstract fun driveFileDao(): DriveFileDao

    /**
     * ThirdAppFileData Dao
     */
    abstract fun thirdAppFileDataDao(): ThirdAppFileDataDao

    /**
     * shortcut folder dao
     */
    abstract fun shortcutFolderDao(): ShortcutFolderDao

    /**
     * the dao of shortcut folder relate real file path mapping when recycle
     */
    abstract fun shortcutFolderRecycleDao(): ShortcutFolderRecycleDao

    companion object {

        const val TAG = "AppDatabase"

        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase = INSTANCE ?: synchronized(this) {
            INSTANCE ?: buildDatabase(context.applicationContext).also { INSTANCE = it }
        }

        private fun buildDatabase(context: Context) = Room.databaseBuilder(
            context,
            AppDatabase::class.java, DATABASE_NAME
        )
            .addCallback(object : Callback() {
                override fun onCreate(db: SupportSQLiteDatabase) {
                    super.onCreate(db)
                    insertDefaultLabels(db)
                }
            })
            .addMigrations(MIGRATION_1_2)
            .addMigrations(MIGRATION_2_3)
            .addMigrations(MIGRATION_3_4)
            .addMigrations(MIGRATION_4_5)
            .addMigrations(MIGRATION_5_6)
            .addMigrations(MIGRATION_6_7)
            .addMigrations(MIGRATION_7_8)
            .build()

        private fun insertDefaultLabels(db: SupportSQLiteDatabase) {
            val labelNames = listOf(
                appContext.resources.getString(com.filemanager.common.R.string.default_label_important),
                appContext.resources.getString(com.filemanager.common.R.string.default_label_work),
                appContext.resources.getString(com.filemanager.common.R.string.default_label_study),
                appContext.resources.getString(com.filemanager.common.R.string.default_label_life)
            )
            var importantLabelId = 0L
            labelNames.forEachIndexed { index, name ->
                val contentValues = ContentValues()
                contentValues.put(LabelStore.FileLabelColumns.NAME, name)
                contentValues.put(LabelStore.FileLabelColumns.VIEW_COUNT, 0)
                contentValues.put(LabelStore.FileLabelColumns.USE_COUNT, 0)
                contentValues.put(
                    LabelStore.FileLabelColumns.PIN_TIMESTAMP,
                    "${if (index == 0) "${System.currentTimeMillis()}" else 0}"
                )
                contentValues.put(LabelStore.FileLabelColumns.LAST_USED_TIME, 0)
                val result = db.insert(LabelStore.FILE_LABEL, CONFLICT_REPLACE, contentValues)
                if (index == 0 && result != -1L) {
                    importantLabelId = result
                }
            }
            migrateFavoriteDataToImportantLabel(db, importantLabelId)
        }

        private fun migrateFavoriteDataToImportantLabel(
            db: SupportSQLiteDatabase,
            importantLabelId: Long
        ) {
            val favoriteHelper = OplusFavoriteHelper()
            val list: List<String?>? = favoriteHelper.getFavoriteFileList()
            list ?: return
            list.forEach { path ->
                val contentValues = ContentValues()
                val localType = path?.let {
                    val file = PathFileWrapper(it)
                    file.mLocalType
                }
                val mimeType = MimeTypeHelper.getMimeTypeFromPath(path)
                contentValues.put(LabelStore.FileLabelMappingColumns.LABEL_ID, importantLabelId)
                contentValues.put(LabelStore.FileLabelMappingColumns.FILE_PATH, path ?: "")
                contentValues.put(LabelStore.FileLabelMappingColumns.LOCAL_TYPE, localType)
                contentValues.put(LabelStore.FileLabelMappingColumns.MIME_TYPE, mimeType)
                contentValues.put(LabelStore.FileLabelMappingColumns.MEDIA_DURATION,
                    GetMediaDurationUtil.getDuration(
                        BaseFileBean().apply {
                            mLocalType = localType ?: MimeTypeHelper.UNKNOWN_TYPE
                            mData = path
                        }
                    )
                )
                contentValues.put(
                    LabelStore.FileLabelMappingColumns.TIMESTAMP,
                    System.currentTimeMillis()
                )
                contentValues.put(LabelStore.FileLabelMappingColumns.VISIBLE, 0)
                db.insert(LabelStore.FILE_LABEL_MAPPING, CONFLICT_REPLACE, contentValues)
            }
            favoriteHelper.clearData()
        }

        @Suppress("MagicNumber")
        private val MIGRATION_4_5: Migration = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                val fileOpenTimeSql = Injector.injectFactory<IFileOpenTime>()?.getFileOpenTimeSql()
                fileOpenTimeSql?.let {
                    database.execSQL(it)
                }
            }
        }

        private val MIGRATION_5_6: Migration =
            object : Migration(DATABASE_VERSION_5, DATABASE_VERSION_6) {
                override fun migrate(database: SupportSQLiteDatabase) {
                    database.execSQL(
                        "CREATE TABLE IF NOT EXISTS ${CloudFileDriveStore.DRIVE_FILE_LABEL} " +
                                "(${CloudFileDriveStore.Columns.ID} INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.NAME} TEXT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.SIZE} INTEGER NOT NULL," +
                                "${CloudFileDriveStore.Columns.URI} TEXT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.TYPE} TEXT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.MIME_TYPE} TEXT, " +
                                "${CloudFileDriveStore.Columns.CREATE_TIME} INTEGER NOT NULL, " +
                                "${CloudFileDriveStore.Columns.LAST_MODIFY_TIME} INTEGER NOT NULL, " +
                                "${CloudFileDriveStore.Columns.LAST_BROWSER_TIME} INTEGER NOT NULL, " +
                                "${CloudFileDriveStore.Columns.FILE_ID} TEXT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.PARENT_ID} TEXT NOT NULL, " +
                                "${CloudFileDriveStore.Columns.OPEN_TYPE} INTEGER NOT NULL, " +
                                "${CloudFileDriveStore.Columns.SOURCE} TEXT NOT NULL);"
                    )
                }
            }

        /**
         * 新增ShortFolder相关的一系列表
         */
        @Suppress("MagicNumber")
        private val MIGRATION_7_8: Migration = object : Migration(DATABASE_VERSION_7, DATABASE_VERSION_8) {
            override fun migrate(database: SupportSQLiteDatabase) {
                val shortcutFolderSql = ShortcutFolderStore.ShortcutFolderColumns.CREATE_SQL
                val shortcutFolderRecycleSql = ShortcutFolderStore.ShortcutFolderMappingRecycleColumns.CREATE_SQL
                database.execSQL(shortcutFolderSql)
                database.execSQL(shortcutFolderRecycleSql)
                Log.d(TAG, "migration 7_8 ${ShortcutFolderStore.SHORTCUT_FOLDER} table: $shortcutFolderSql \n" +
                    "${ShortcutFolderStore.SHORTCUT_FOLDER_MAPPING_RECYCLE} table: $shortcutFolderRecycleSql")
            }
        }

        /**
         * 新增ThirdAppFileData表格
         */
        @Suppress("MagicNumber")
        private val MIGRATION_6_7: Migration =
            object : Migration(DATABASE_VERSION_6, DATABASE_VERSION_7) {
                override fun migrate(database: SupportSQLiteDatabase) {
                    val createTableSqlString =
                        "CREATE TABLE IF NOT EXISTS ${ThirdAppFileStore.THIRD_APP_FILE_DATA_TABEL} " +
                                "(${ThirdAppFileStore.Columns.ID} INTEGER PRIMARY KEY AUTOINCREMENT," +
                                "${ThirdAppFileStore.Columns.NAME} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.SIZE} INTEGER, " +
                                "${ThirdAppFileStore.Columns.UID} TEXT, " +
                                "${ThirdAppFileStore.Columns.PACKAGE} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.PACKAGE_VERSION} TEXT, " +
                                "${ThirdAppFileStore.Columns.SOURCE_TYPE} INTEGER NOT NULL, " +
                                "${ThirdAppFileStore.Columns.SOURCE_NAME} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.SOURCE_ACTIVITY} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.PREVIEW_ACTIVITY} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.FILE_TIME} INTEGER, " +
                                "${ThirdAppFileStore.Columns.DETECT_TIME} INTEGER NOT NULL, " +
                                "${ThirdAppFileStore.Columns.META_DATA} TEXT NOT NULL, " +
                                "${ThirdAppFileStore.Columns.INDEX_CHECKSUM} INTEGER NOT NULL, " +
                                "${ThirdAppFileStore.Columns.IDENTIFICATION} TEXT, " +
                                "${ThirdAppFileStore.Columns.PARSE_VERSION} INTEGER NOT NULL" +
                                ");"
                    val createIndexSqlString =
                        "CREATE UNIQUE INDEX ${ThirdAppFileStore.INDEX_NAME} ON ${ThirdAppFileStore.THIRD_APP_FILE_DATA_TABEL} " +
                                "(${ThirdAppFileStore.Columns.NAME}, " +
                                "${ThirdAppFileStore.Columns.PACKAGE}, " +
                                "${ThirdAppFileStore.Columns.SOURCE_NAME})"
                    Log.i(
                        TAG,
                        "AppDatabase migrate 6_7, createTableSql string $createTableSqlString, createIndexString $createIndexSqlString"
                    )
                    database.execSQL(createTableSqlString)
                    database.execSQL(createIndexSqlString)
                }
            }

        @Suppress("MagicNumber")
        private val MIGRATION_3_4: Migration = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS ${PreviewDataStore.TABLE_NAME} " +
                            "(${PreviewDataStore.Columns.ID} INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                            "${PreviewDataStore.Columns.FILE_PATH} TEXT NOT NULL, " +
                            "${PreviewDataStore.Columns.TASK_ID} TEXT, " +
                            "${PreviewDataStore.Columns.STATUS} INTEGER NOT NULL, " +
                            "${PreviewDataStore.Columns.ERROR_CODE} INTEGER NOT NULL, " +
                            "${PreviewDataStore.Columns.UPLOAD_PROGRESS} INTEGER NOT NULL, " +
                            "${PreviewDataStore.Columns.CONVERT_PROGRESS} INTEGER NOT NULL, " +
                            "${PreviewDataStore.Columns.CONVERT_URL} TEXT, " +
                            "${PreviewDataStore.Columns.CONVERT_MAP} TEXT, " +
                            "${PreviewDataStore.Columns.CONVERTED_TIME} INTEGER, " +
                            "${PreviewDataStore.Columns.DOWNLOAD_PROGRESS} INTEGER NOT NULL, " +
                            "${PreviewDataStore.Columns.SAVE_PATH} TEXT, " +
                            "${PreviewDataStore.Columns.TEMP1} TEXT, " +
                            "${PreviewDataStore.Columns.TEMP2} TEXT);"
                )
            }
        }

        @Suppress("MagicNumber")
        private val MIGRATION_2_3: Migration = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS ${LabelStore.FILE_LABEL} " +
                            "(${LabelStore.FileLabelColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                            "${LabelStore.FileLabelColumns.NAME} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelColumns.VIEW_COUNT} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelColumns.USE_COUNT} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelColumns.PIN_TIMESTAMP} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelColumns.TEMP1} TEXT, " +
                            "${LabelStore.FileLabelColumns.TEMP2} TEXT, " +
                            "${LabelStore.FileLabelColumns.LAST_USED_TIME} INTEGER NOT NULL);"
                )
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS ${LabelStore.FILE_LABEL_MAPPING} " +
                            "(${LabelStore.FileLabelMappingColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.LABEL_ID} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.FILE_PATH} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.LOCAL_TYPE} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.MIME_TYPE} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.MEDIA_DURATION} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.TIMESTAMP} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.VISIBLE} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingColumns.TEMP1} TEXT, " +
                            "${LabelStore.FileLabelMappingColumns.TEMP2} TEXT, " +
                            "FOREIGN KEY(${LabelStore.FileLabelMappingColumns.LABEL_ID}) " +
                            "REFERENCES ${LabelStore.FILE_LABEL}(${LabelStore.FileLabelColumns.ID}) " +
                            "ON UPDATE NO ACTION ON DELETE CASCADE);"
                )
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS ${LabelStore.FILE_LABEL_MAPPING_RECYCLE} " +
                            "(${LabelStore.FileLabelMappingRecycleColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.LABEL_ID} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.FILE_PATH} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.LOCAL_TYPE} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.MIME_TYPE} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.MEDIA_DURATION} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.TIMESTAMP} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.DELETE_FILE_PATH} TEXT NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.VISIBLE} INTEGER NOT NULL, " +
                            "${LabelStore.FileLabelMappingRecycleColumns.TEMP1} TEXT , " +
                            "${LabelStore.FileLabelMappingRecycleColumns.TEMP2} TEXT, " +
                            "FOREIGN KEY(${LabelStore.FileLabelMappingRecycleColumns.LABEL_ID}) " +
                            "REFERENCES ${LabelStore.FILE_LABEL}(${LabelStore.FileLabelColumns.ID}) " +
                            "ON UPDATE NO ACTION ON DELETE CASCADE);"
                )
                insertDefaultLabels(database)
            }
        }

        private val MIGRATION_1_2: Migration = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS  ${RecentFileStore.TABLE_NAME_RECENT_FILES} (" +
                            "${RecentFileStore.FileColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT," +
                            "${RecentFileStore.FileColumns.ABSOLUTE_PATH} TEXT," +
                            "${RecentFileStore.FileColumns.RELATIVE_PATH} TEXT," +
                            "${RecentFileStore.FileColumns.ANOTHER_NAME} TEXT," +
                            "${RecentFileStore.FileColumns.DISPLAY_NAME} TEXT," +
                            "${RecentFileStore.FileColumns.LAST_MODIFIED} TEXT," +
                            "${RecentFileStore.FileColumns.PARENT_DATE} TEXT," +
                            "${RecentFileStore.FileColumns.SIZE} TEXT," +
                            "${RecentFileStore.FileColumns.TYPE} INTEGER," +
                            "${RecentFileStore.FileColumns.VOLUME_NAME} TEXT)"
                )
            }
        }

        private val MIGRATION_0_1: Migration = object : Migration(0, 1) {
            override fun migrate(database: SupportSQLiteDatabase) {
                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS  ${GlobalSearchStore.TABLE_NAME_SEARCH_HISTORY} (" +
                            "${GlobalSearchStore.SearchHistory.FileColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT," +
                            "${GlobalSearchStore.SearchHistory.FileColumns.KEY_WORD} TEXT UNIQUE," +
                            "${GlobalSearchStore.SearchHistory.FileColumns.UPDATE_TIME} INTEGER," +
                            "${GlobalSearchStore.SearchHistory.FileColumns.EXTEND} TEXT)"
                )

                database.execSQL(
                    "CREATE TABLE IF NOT EXISTS  ${GlobalSearchStore.TABLE_NAME_SEARCH_FILTER} (" +
                            "${GlobalSearchStore.SearchFilter.FileColumns.ID} INTEGER PRIMARY KEY AUTOINCREMENT," +
                            "${GlobalSearchStore.SearchFilter.FileColumns.FILTER_ID} INTEGER UNIQUE," +
                            "${GlobalSearchStore.SearchFilter.FileColumns.FILTER_VALUE} INTEGER," +
                            "${GlobalSearchStore.SearchFilter.FileColumns.FILTER_DESC} VARCHAR," +
                            "${GlobalSearchStore.SearchFilter.FileColumns.EXTEND} TEXT)"
                )
            }
        }
    }
}