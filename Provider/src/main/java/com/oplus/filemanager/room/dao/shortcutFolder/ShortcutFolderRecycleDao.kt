/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ShortFolderMappingRecycleDao
 * * Description: 快捷文件夹和真实文件路径移动到最近删除时的映射实体类 Dao
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.room.dao.shortcutFolder

import androidx.room.Dao
import androidx.room.Query
import com.oplus.filemanager.room.dao.RoomBaseDao
import com.oplus.filemanager.room.model.ShortcutFolderRecycleEntity

@Dao
interface ShortcutFolderRecycleDao : RoomBaseDao<ShortcutFolderRecycleEntity> {

    @Query("SELECT * FROM shortcut_folder_mapping_recycle WHERE _id = :id")
    fun queryById(id: Long): ShortcutFolderRecycleEntity?

    @Query("SELECT * FROM shortcut_folder_mapping_recycle WHERE file_path = :path")
    fun queryByPath(path: String): ShortcutFolderRecycleEntity?

    @Query("SELECT * FROM shortcut_folder_mapping_recycle WHERE file_path IN (:paths)")
    fun queryByPaths(paths: List<String>): List<ShortcutFolderRecycleEntity>?

    @Query("SELECT * FROM shortcut_folder_mapping_recycle WHERE delete_file_path = :deletePath")
    fun queryByDeleteFilePath(deletePath: String): List<ShortcutFolderRecycleEntity>?

    @Query("SELECT * FROM shortcut_folder_mapping_recycle WHERE file_path LIKE :path || '%'")
    fun queryByPathFuzzy(path: String): List<ShortcutFolderRecycleEntity>?

    @Query("SELECT * FROM shortcut_folder_mapping_recycle")
    fun queryAll(): List<ShortcutFolderRecycleEntity>?

    @Query("DELETE FROM shortcut_folder_mapping_recycle WHERE delete_file_path = :deletePath")
    fun deleteByDeletePath(deletePath: String): Int

    @Query("DELETE FROM shortcut_folder_mapping_recycle WHERE file_path = :filePath")
    fun deleteByPath(filePath: String): Int
}