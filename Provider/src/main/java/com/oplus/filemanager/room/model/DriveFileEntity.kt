/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TencentCloudFileEntity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/28 12:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/28       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.room.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "drive_file")
data class DriveFileEntity(
    @PrimaryKey(autoGenerate = true) @ColumnInfo(name = "_id") var id: Long,
    @ColumnInfo(name = "name") var name: String = "",
    @ColumnInfo(name = "size") var size: Int = 0,
    @ColumnInfo(name = "uri") var uri: String = "",
    @ColumnInfo(name = "type") var type: String = "file",
    @ColumnInfo(name = "mimeType") var mimeType: String? = null,
    @ColumnInfo(name = "createTime") var createTime: Int = 0,
    @ColumnInfo(name = "lastModifyTime") var lastModifyTime: Int = 0,
    @ColumnInfo(name = "lastBrowserTime") var lastBrowserTime: Int = 0,
    @ColumnInfo(name = "fileId") var fileId: String = "",
    @ColumnInfo(name = "parentId") var parentId: String = "",
    @ColumnInfo(name = "openType") var openType: Int = 0,
    @ColumnInfo(name = "source") var source: String = ""
) {
    companion object {
        /**
         * 文件类型：文件
         */
        const val TYPE_FILE = "file"

        /**
         * 文件类型：文件夹
         */
        const val TYPE_FOLDER = "folder"
        /**
         * 文件来源：腾讯文档
         */
        const val SOURCE_TYPE_TENCENT = "tencent"

        /**
         * 文件来源：金山文档
         */
        const val SOURCE_TYPE_KINGSSOFT = "kingssoft"
    }

    /**
     * 判断是否是文件夹
     */
    fun isDirectory(): Boolean = type == TYPE_FOLDER
}