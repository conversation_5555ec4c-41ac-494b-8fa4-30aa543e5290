/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ShortFolderEntity
 * * Description: 快捷文件夹的实体类
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.room.model

import androidx.annotation.Keep
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.oplus.filemanager.provider.store.ShortcutFolderStore

@Entity(tableName = ShortcutFolderStore.SHORTCUT_FOLDER)
@Keep
data class ShortcutFolderEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    var id: Long = 0L,

    @ColumnInfo(name = "name")
    var name: String,

    @ColumnInfo(name = "path")
    var path: String
) {
    @ColumnInfo(name = "open_count")
    var openCount: Int = 0

    @ColumnInfo(name = "open_time")
    var openTime: Long = 0L

    @ColumnInfo(name = "modify_time")
    var modifyTime: Long = 0L

    @ColumnInfo(name = "temp1")
    var temp1: String? = null

    @ColumnInfo(name = "temp2")
    var temp2: String? = null
}
