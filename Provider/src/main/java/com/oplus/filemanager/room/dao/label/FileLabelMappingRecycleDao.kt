/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileLabelMappingDao.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  v-yanshengneng      2022/8/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.room.dao.label

import androidx.room.Dao
import androidx.room.Query
import com.oplus.filemanager.room.dao.RoomBaseDao
import com.oplus.filemanager.room.model.FileLabelMappingRecycleEntity

@Dao
interface FileLabelMappingRecycleDao : RoomBaseDao<FileLabelMappingRecycleEntity> {

    @Query("SELECT * FROM file_label_mapping_recycle WHERE delete_file_path = :deletePath")
    fun getFileListByDeleteFilePath(deletePath: String): List<FileLabelMappingRecycleEntity>?

    @Query("SELECT * FROM file_label_mapping_recycle WHERE file_path = :path")
    fun getFileListByPath(path: String): List<FileLabelMappingRecycleEntity>?

    @Query("SELECT * FROM file_label_mapping_recycle WHERE file_path IN (:paths)")
    fun getFileListByPathList(paths: List<String>): List<FileLabelMappingRecycleEntity>?
}