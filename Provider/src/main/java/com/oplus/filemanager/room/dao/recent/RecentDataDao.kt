/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:RecentDataDao.kt
 * * Description: Database table (recent_files) operation interface class
 * * Version:1.0
 * * Date :2020/8/28
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/28,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.room.dao.recent

import androidx.room.Dao
import androidx.room.Query
import com.oplus.filemanager.room.dao.RoomBaseDao
import com.oplus.filemanager.room.model.RecentFilesEntity


@Dao
interface RecentDataDao : RoomBaseDao<RecentFilesEntity> {

    /**
     * Query start and end time recent files
     */
    @Query(
        "SELECT * FROM recent_files WHERE last_modified between :startDate and :endDate GROUP BY absolute_path ORDER BY last_modified DESC, " +
                "display_name DESC"
    )
    fun getRecentFiles(startDate: Long, endDate: Long): List<RecentFilesEntity>

    /**
     * Update the data through the absolute path,
     * if the absolute path does not exist, a new data will be inserted
     */
    @Query("UPDATE OR REPLACE recent_files SET absolute_path = :absolutePath, relative_path = :relativePath, another_name = :anotherName, display_name = :displayName, last_modified = :lastModified, parent_date = :parentDate, size = :size, type = :type, volume_name = :volumeName WHERE absolute_path = :absolutePath")
    fun updateRecentFilesByPath(absolutePath: String, relativePath: String, anotherName: String, displayName: String,
                                lastModified: Long, parentDate: String, size: Long, type: Int, volumeName: String): Int

    /**
     * Delete by absolute path matching
     */
    @Query("DELETE FROM recent_files WHERE absolute_path IN (:filePaths)")
    fun deleteRecentFilesByPaths(filePaths: Array<String>): Int
}