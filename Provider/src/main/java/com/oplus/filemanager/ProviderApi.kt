/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.category.globalsearch.GlobalSearchApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager

import android.content.Context
import android.net.Uri
import com.oplus.filemanager.interfaze.provider.IProvider
import com.oplus.filemanager.provider.MyFileProvider
import java.io.File

object ProviderApi : IProvider {

    override fun getUriForFile(context: Context, authority: String, file: File): Uri {
        return MyFileProvider.getUriForFile(context, authority, file)
    }
}