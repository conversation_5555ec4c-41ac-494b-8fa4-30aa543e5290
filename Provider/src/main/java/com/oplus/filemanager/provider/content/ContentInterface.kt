/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.oplus.filemanager.provider
 * * Version     : 1.0
 * * Date        : 2020/7/27
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.provider.content

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteQueryBuilder
import android.net.Uri
import androidx.sqlite.db.SimpleSQLiteQuery
import androidx.sqlite.db.SupportSQLiteOpenHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.provider.store.FileManagerStore

private const val TAG = "ContentInterface"

abstract class BaseProviderContent(val mHelper: SupportSQLiteOpenHelper, val mContext: Context?) : ContentInterface {

    abstract fun getTableName(): String

    /**
     * the default function to insert by uri; you should verify {@param initialValues} first
     *
     * @param uri: The content:// URI of the insertion request. This must not be {@code null}.
     * @param initialValues: A set of column_name/value pairs to add to the database.
     */
    override fun insert(uri: Uri, initialValues: ContentValues?): Uri? {
        try {
            mHelper.writableDatabase?.use { db ->
                val rowId = db.insert(getTableName(), SQLiteDatabase.CONFLICT_NONE, initialValues)
                val newUri: Uri?
                if (rowId > 0) {
                    newUri = ContentUris.withAppendedId(uri, rowId)
                    mContext?.contentResolver?.notifyChange(uri, null, 0)
                } else {
                    Log.w(TAG, "insert insert failed")
                    newUri = null
                }
                return newUri
            }
        } catch (e: Exception) {
            Log.e(TAG, "insert:failed ${e.message}")
        }
        return null
    }

    /**
     * the default function to bulkInsert by uri, the most common case ，we don`t change this
     */
    override fun bulkInsert(uri: Uri, contentValues: Array<ContentValues>): Int {
        val db = mHelper.writableDatabase
        db.beginTransaction()
        try {
            contentValues.forEach { value ->
                insert(uri, value)
            }
            db.setTransactionSuccessful()
            return contentValues.size
        } finally {
            db.endTransaction()
        }
    }

    /**
     *  the default function to  insert or update by uri; you should verify [initialValues] first
     * this function is similar to [insert];
     * but if your table have UNIQUE field;this function will insert or update database;
     *
     * @return if has some exception will return -1
     */
    open fun replace(uri: Uri, initialValues: ContentValues?): Uri? {
        try {
            mHelper.writableDatabase?.use { db ->
                val rowId = db.insert(getTableName(), SQLiteDatabase.CONFLICT_REPLACE, initialValues)
                val newUri: Uri?
                if (rowId > 0) {
                    newUri = ContentUris.withAppendedId(uri, rowId)
                    mContext?.contentResolver?.notifyChange(uri, null, 0)
                } else {
                    Log.w(TAG, "insert insert failed")
                    newUri = null
                }
                return newUri
            }
        } catch (e: Exception) {
            Log.e(TAG, "replace:failed ${e.message}")
        }
        return null
    }

    /**
     * the default function to delete by uri; you can verify param if necessary
     *
     * if uri has flags  "cleardata" if true ; this function will clear data regardless of your other param;
     *
     * if uri flags "cleardata" is not true; the {@param userWhere}  and  {@param userWhereArgs} is not be null or empty ;
     *
     * @return The number of rows affected; if has some exception will return -1
     *
     */
    override fun delete(uri: Uri, userWhere: String?, userWhereArgs: Array<String>?): Int {
        val clearFlags = uri.getQueryParameter(FileManagerStore.PARAM_CLEAR_DATA)
        val volumeName = FileManagerStore.getVolumeName(uri)

        if (clearFlags == FileManagerStore.CLEAR_DATA_FLAGS) {
            Log.d(TAG, "delete: clear table")
            try {
                mHelper.writableDatabase?.use { db ->
                    val count = db.delete(getTableName(), "", emptyArray())
                    val notifyUri = Uri.parse("content://${FileManagerStore.FILEMANAGER_AUTHORITY}/"
                            + "${volumeName}/${getTableName()}/${FileManagerStore.PARAM_CLEAR_DATA}/$count")
                    mContext?.contentResolver?.notifyChange(notifyUri, null)
                    return count
                }
            } catch (e: Exception) {
                Log.e(TAG, "delete:clear all if Fail ${e.message}")
            }
        } else {
            if (userWhere.isNullOrEmpty() || userWhereArgs.isNullOrEmpty()) {
                Log.e(
                    TAG, "delete:clear table should use param cleardata true in uri;"
                        + " userWhere isnull=${userWhere.isNullOrEmpty()};"
                        + "userWhereArgs isNull=${userWhereArgs.isNullOrEmpty()}")
                return -1
            }

            try {
                mHelper.writableDatabase?.use { db ->
                    val count = db.delete(getTableName(), userWhere, userWhereArgs)
                    val notifyUri = Uri.parse("content://${FileManagerStore.FILEMANAGER_AUTHORITY}/"
                            + "${volumeName}/${getTableName()}/$count")
                    mContext?.contentResolver?.notifyChange(notifyUri, null)
                    return count
                }
            } catch (e: Exception) {
                Log.e(TAG, "delete:clear all if Fail")
            }
        }
        return -1
    }

    /**
     * only verify initialValues is null or empty;
     *  you can verify other value if necessary;
     *
     */
    override fun update(uri: Uri, initialValues: ContentValues?, userWhere: String?, whereArgs: Array<String>?): Int {
        if ((initialValues == null) || (initialValues.size() == 0)) {
            Log.d(TAG, "update no thing")
            return -1
        }

        try {
            mHelper.writableDatabase?.use { db ->
                val count = db.update(getTableName(), SQLiteDatabase.CONFLICT_NONE, initialValues, userWhere, whereArgs)
                if (count > 0 && !db.inTransaction()) {
                    mContext?.contentResolver?.notifyChange(uri, null)
                }
                return count
            }
        } catch (e: Exception) {
            Log.e(TAG, "updateHistory:update Fail ${e.message}")
        }

        return -1
    }

    /**
     * this function only simply query
     *
     */
    override fun query(uri: Uri,
                       projection: Array<String>?,
                       selection: String?,
                       selectionArgs: Array<String>?,
                       sortOrder: String?): Cursor? {
        val db = mHelper.readableDatabase
        val sql = SQLiteQueryBuilder.buildQueryString(
                false, getTableName(), projection, selection, null, null, sortOrder, null)

        Log.d(TAG, "sql -> $sql")
        val query = SimpleSQLiteQuery(sql, selectionArgs)
        val cursor = db?.query(query)
        cursor?.setNotificationUri(mContext?.contentResolver, uri)
        return cursor
    }

    /**
     *  Default return null
     */
    override fun getType(uri: Uri): String? {
        return null
    }
}

interface ContentInterface {
    fun insert(uri: Uri, initialValues: ContentValues?): Uri?

    fun bulkInsert(uri: Uri, contentValues: Array<ContentValues>): Int

    fun delete(uri: Uri, userWhere: String?, userWhereArgs: Array<String>?): Int

    fun update(uri: Uri, initialValues: ContentValues?, userWhere: String?, whereArgs: Array<String>?): Int

    fun query(uri: Uri,
              projection: Array<String>?,
              selection: String?,
              selectionArgs: Array<String>?,
              sortOrder: String?): Cursor?

    fun getType(uri: Uri): String?
}