/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.oplus.filemanager.provider
 * * Version     : 1.0
 * * Date        : 2020/7/27
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.provider.content

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import androidx.sqlite.db.SupportSQLiteOpenHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.provider.store.GlobalSearchStore

class GlobalSearchHistoryContent(mHelper: SupportSQLiteOpenHelper, mContext: Context?) : BaseProviderContent(mHelper, mContext) {
    companion object {
        private const val TAG = "GlobalSearchHistoryContent"
    }

    override fun getTableName(): String = GlobalSearchStore.TABLE_NAME_SEARCH_HISTORY

    override fun insert(uri: Uri, initialValues: ContentValues?): Uri? {
        if (initialValues == null) {
            return null
        } else if (!initialValues.containsKey(GlobalSearchStore.SearchHistory.FileColumns.KEY_WORD)) {
            Log.e(TAG, "insert: FileColumns search_content missed")
            return null
        } else if (!initialValues.containsKey(GlobalSearchStore.SearchHistory.FileColumns.UPDATE_TIME)) {
            Log.e(TAG, "insert: FileColumns search_time missed")
            return null
        }

        val keyWord = initialValues.getAsString(GlobalSearchStore.SearchHistory.FileColumns.KEY_WORD)
        if (keyWord.isNullOrEmpty()) {
            Log.e(TAG, "insert: search_content empty")
            return null
        }

        return super.replace(uri, initialValues)
    }
}