/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileLabelContent
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.provider.content

import android.content.Context
import androidx.sqlite.db.SupportSQLiteOpenHelper
import com.oplus.filemanager.provider.store.LabelStore

class FileLabelContent(helper: SupportSQLiteOpenHelper, context: Context?) : BaseProviderContent(helper, context) {

    override fun getTableName(): String {
        return LabelStore.FILE_LABEL
    }
}