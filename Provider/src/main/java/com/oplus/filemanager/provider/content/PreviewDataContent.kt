/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PreviewDataContent
 ** Description : PreviewDataContent
 ** Version     : 1.0
 ** Date        : 2022/12/26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/12/26      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.provider.content

import android.content.Context
import androidx.sqlite.db.SupportSQLiteOpenHelper
import com.oplus.filemanager.provider.store.PreviewDataStore

class PreviewDataContent(helper: SupportSQLiteOpenHelper, context: Context?) : BaseProviderContent(helper, context) {

    override fun getTableName(): String {
        return PreviewDataStore.TABLE_NAME
    }
}