/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.oplus.filemanager.provider
 * * Version     : 1.0
 * * Date        : 2020/7/23
 * * Author      : w9007122
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.provider.store

import com.oplus.filemanager.provider.FileManagerProvider
import com.oplus.filemanager.provider.content.GlobalSearchFilterContent
import com.oplus.filemanager.provider.content.GlobalSearchHistoryContent
import com.oplus.filemanager.provider.store.FileManagerStore.getContentUri

object GlobalSearchStore {
    const val TABLE_NAME_SEARCH_HISTORY = "search_history"
    const val TABLE_NAME_SEARCH_FILTER = "search_filter"

    const val PATH_SEARCH_HISTORY = "history"
    const val PATH_SEARCH_FILTER = "filter"

    private const val SEARCH_HISTORY = 1
    private const val SEARCH_FILTER = 2

    internal fun init() {
        FileManagerProvider.addURI(
            FileManagerStore.FILEMANAGER_AUTHORITY,
            "*/$PATH_SEARCH_HISTORY",
            SEARCH_HISTORY
        )
        FileManagerProvider.addURI(
            FileManagerStore.FILEMANAGER_AUTHORITY,
            "*/$PATH_SEARCH_FILTER",
            SEARCH_FILTER
        )
        FileManagerProvider.addContentInterface(SEARCH_HISTORY, GlobalSearchHistoryContent::class.java)
        FileManagerProvider.addContentInterface(SEARCH_FILTER, GlobalSearchFilterContent::class.java)
    }

    object SearchHistory {
        interface FileColumns {
            companion object {
                const val ID = "_id"
                const val KEY_WORD = "search_content"
                const val UPDATE_TIME = "search_time"
                const val EXTEND = "extend"
            }
        }

        val HISTORY_URI = getContentUri(PATH_SEARCH_HISTORY)
        val HISTORY_CLEAR_URI = getContentUri(PATH_SEARCH_HISTORY, FileManagerStore.CLEAR_DATA_FLAGS)
    }

    object SearchFilter {
        interface FileColumns {
            companion object {
                const val ID = "_id"
                const val FILTER_ID = "filter_id"
                const val FILTER_VALUE = "filter_value"
                const val FILTER_DESC = "filter_desc"
                const val EXTEND = "extend"
            }
        }

        val FILTER_URI = getContentUri(PATH_SEARCH_FILTER)
        val FILTER_CLEAR_URI = getContentUri(PATH_SEARCH_FILTER, FileManagerStore.CLEAR_DATA_FLAGS)
    }
}