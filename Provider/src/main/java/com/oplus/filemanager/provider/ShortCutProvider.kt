/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  ShortCutProvider.kt
 * * Description : ContentProvider for update shortcut
 * * Version     : 1.0
 * * Date        : 2024/10/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.oplus.filemanager.provider

import android.content.ContentProvider
import android.content.ContentUris
import android.content.ContentValues
import android.content.pm.ShortcutInfo
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.utils.StatisticsUtils

const val METHOD_NAME = "update_short_cut"
const val KEY_URI_PAIR_LIST = "key_uri_pair_list"

class ShortCutProvider : ContentProvider() {

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        if (method == METHOD_NAME) {
            processData(extras)
        }
        return super.call(method, arg, extras)
    }

    private fun processData(extras: Bundle?) {
        val data = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            extras?.getSerializable(KEY_URI_PAIR_LIST, HashMap::class.java)
        } else {
            extras?.getSerializable(KEY_URI_PAIR_LIST) as? HashMap<*, *>
        }
        val needUpdateList: MutableList<ShortcutInfo> = mutableListOf()
        val fileTypeList = mutableListOf<String>()
        data?.forEach { item ->
            val oldMediaId = item.key
            val newMediaId = item.value
            if (oldMediaId is Long && newMediaId is Long) {
                val newUri = ContentUris.withAppendedId(
                    MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL),
                    newMediaId
                )
                val baseFileBean = UriHelper.getBaseFileBeanFromUri(context, newUri)
                baseFileBean?.let {
                    val shortcutItemId = ShortCutUtils.getShortCutIdFromMediaId(context, oldMediaId)
                    if (context != null && !shortcutItemId.isNullOrEmpty()) {
                        val shortCutInfo = ShortCutUtils.getShortCutInfoById(context!!, it, shortcutItemId)
                        needUpdateList.add(shortCutInfo)
                        fileTypeList.add(it.mLocalType.toString())
                    }
                }
            }
        }
        if (needUpdateList.isNotEmpty()) {
            context?.let { ShortCutUtils.updateShortCuts(it, needUpdateList) }
            OptimizeStatisticsUtil.shortcutEvent(StatisticsUtils.SHORTCUT_OPE_UPDATE, fileTypeList.joinToString(";"), null)
        }
    }

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }
}