/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchHistoryDBHelper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/20      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.provider

import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.oplus.filemanager.room.AppDatabase
import com.oplus.filemanager.room.model.SearchHistoryEntity

object SearchHistoryDBHelper {

    private fun getAppDatabase(): AppDatabase {
        return AppDatabase.getInstance(MyApplication.sAppContext)
    }

    @WorkerThread
    fun getAllSearchHistory(): List<SearchHistoryEntity>? {
        return getAppDatabase().searchHistoryDao()?.getAllSearchHistory()
    }

    @WorkerThread
    fun addSearchHistory(keyWords: String): Long? {
        return getAppDatabase().searchHistoryDao()?.insertItem(SearchHistoryEntity().apply {
            mId = null
            mSearchContent = keyWords
            mSearchTime = System.currentTimeMillis()
        })
    }

    @WorkerThread
    fun deleteSearchHistory(ids: List<Long>): Int {
        return getAppDatabase().searchHistoryDao()?.deleteSearchHistory(ids) ?: -1
    }

    @WorkerThread
    fun updateSearchHistory(id: Long, time: Long?): Int {
        return getAppDatabase().searchHistoryDao()?.updateSearchHistory(id, time ?: System.currentTimeMillis()) ?: -1
    }

    @WorkerThread
    fun clearHistory(): Int {
        return getAppDatabase().searchHistoryDao()?.clearHistory() ?: -1
    }
}