/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ShortFolderDBHelper
 * * Description: ShortFolderDBHelper
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.provider

import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.oplus.filemanager.room.AppDatabase
import com.oplus.filemanager.room.dao.shortcutFolder.ShortcutFolderDao
import com.oplus.filemanager.room.model.ShortcutFolderEntity
import java.io.File

object ShortcutFolderDBHelper {

    private const val TAG = "ShortFolderDBHelper"

    private val _dao by lazy { AppDatabase.getInstance(MyApplication.appContext).shortcutFolderDao() }

    private val dao: ShortcutFolderDao
        get() = _dao


    @WorkerThread
    fun insert(shortcutFolderEntity: ShortcutFolderEntity): Long {
        kotlin.runCatching {
            return dao.insertItem(shortcutFolderEntity)
        }.onFailure {
            Log.e(TAG, "insert error", it)
        }
        return -1
    }

    @WorkerThread
    fun insert(items: List<ShortcutFolderEntity>): List<Long> {
        kotlin.runCatching {
            return dao.insertItems(items)
        }.onFailure {
            Log.e(TAG, "insert items error", it)
        }
        return mutableListOf(-1)
    }

    @WorkerThread
    fun delete(shortcutFolderEntity: ShortcutFolderEntity) {
        kotlin.runCatching {
            dao.deleteItem(shortcutFolderEntity)
        }.onFailure {
            Log.e(TAG, "deleteItem error", it)
        }
    }

    @WorkerThread
    fun delete(id: Long): Int {
        kotlin.runCatching {
            return dao.delete(id)
        }.onFailure {
            Log.e(TAG, "deleteItem error", it)
        }
        return 0
    }

    @WorkerThread
    fun update(shortcutFolderEntity: ShortcutFolderEntity): Int {
        kotlin.runCatching {
            return dao.updateItem(shortcutFolderEntity)
        }.onFailure {
            Log.e(TAG, "update error", it)
        }
        return 0
    }

    @WorkerThread
    fun queryAll(): List<ShortcutFolderEntity> {
        kotlin.runCatching {
            return dao.queryAll() ?: emptyList()
        }.onFailure {
            Log.e(TAG, "queryAll error", it)
        }
        return emptyList()
    }

    @WorkerThread
    fun queryShortFolderByName(name: String): ShortcutFolderEntity? {
        return dao.query(name)
    }

    @WorkerThread
    fun queryShortFolderByPath(path: String): ShortcutFolderEntity? {
        kotlin.runCatching {
            return dao.queryByPath(path)
        }.onFailure {
            Log.e(TAG, "queryShortFolderByPath error", it)
        }
        return null
    }

    @WorkerThread
    fun queryShortFolderByPaths(path: List<String>): List<ShortcutFolderEntity>? {
        kotlin.runCatching {
            return dao.queryByPaths(path)
        }.onFailure {
            Log.e(TAG, "queryShortFolderByPaths error", it)
        }
        return null
    }

    @WorkerThread
    fun queryShortFolderByPathFuzzy(path: String): List<ShortcutFolderEntity>? {
        return dao.queryByPathFuzzy(path)
    }

    @WorkerThread
    fun queryShortFolderById(id: Long): ShortcutFolderEntity? {
        kotlin.runCatching {
            return dao.query(id)
        }.onFailure {
            Log.e(TAG, "queryShortFolderById error", it)
        }
        return null
    }

    @WorkerThread
    fun updateModifyTime(id: Long, modifyTime: Long) {
        kotlin.runCatching {
            dao.updateModifyTime(id, modifyTime)
        }.onFailure {
            Log.e(TAG, "updateModifyTime error", it)
        }
    }

    @WorkerThread
    fun updatePath(oldPath: String, newPath: String) {
        kotlin.runCatching {
            val mappingResultList = dao.queryByPathFuzzy(oldPath)
            mappingResultList?.forEach {
                it.path = it.path.replace(oldPath, newPath)
                it.name = File(it.path).name
            }
            dao.updateItems(mappingResultList)
        }.onFailure {
            Log.e(TAG, "updatePath error", it)
        }
    }

    @WorkerThread
    fun removeByPath(path: String): List<ShortcutFolderEntity>? {
        kotlin.runCatching {
            val entityList = dao.queryByPathFuzzy(path) ?: return null
            entityList.forEach { entity ->
                val result = dao.deleteItem(entity)
                Log.d(TAG, "removeByPath:$path  result:$result")
            }
            return entityList
        }.onFailure {
            Log.e(TAG, "removeByPath error", it)
        }
        return null
    }
}