/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.room.utils.OplusFavoriteHelperTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.room.utils

import android.content.Context
import android.content.SharedPreferences
import com.filemanager.common.MyApplication
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class OplusFavoriteHelperTest {

    @MockK
    lateinit var mFavoriteHelper: OplusFavoriteHelper


    @MockK
    lateinit var mContext: Context

    @MockK
    lateinit var mSp: SharedPreferences

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mContext = mockk {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(mContext)
        every { mFavoriteHelper.getSharedPreferences() } returns mSp
    }

    @Test
    fun `should clear when clear data`() {
        val editorMock = mockk<SharedPreferences.Editor> {
            every { clear() } returns this
            justRun { apply() }
        }
        every { mSp.edit() } returns editorMock

        every { mFavoriteHelper.clearData() } answers { callOriginal() }

        mFavoriteHelper.clearData()

        verify { editorMock.clear() }
        verify { editorMock.apply() }
    }

    @Test
    fun `should return null when text is null`() {
        every { mSp.getString("favorite_files", "") } returns ""
        every { mFavoriteHelper.getFavoriteFileList() } answers { callOriginal() }
        assertEquals(null, mFavoriteHelper.getFavoriteFileList())
    }

    @Test
    fun `should return list size when text is not null`() {
        val saveText = "rO0ABXNyABNqYXZhLnV0aWwuQXJyYXlMaXN0eIHSHZnHYZ0DAAFJAARzaXpleHAAAAACdwQAAAAC\n" +
                "    dABRL3N0b3JhZ2UvZW11bGF0ZWQvMC9hd2VxMTEyMi8wMTAgLSDlia/mnKwgLSDlia/mnKwgLSDl\n" +
                "    ia/mnKwgLSDlia/mnKwgLSDlia/mnKwuYm1wdABML3N0b3JhZ2UvZW11bGF0ZWQvMC9hd2VxMTEy\n" +
                "    Mi8wMTAgLSDlia/mnKwgLSDlia/mnKwgLSDlia/mnKwgLSDlia/mnKwgKDIpLmJtcHg=\n"
        every { mSp.getString("favorite_files", "") } returns saveText
        val list = listOf(
            "storage/emulated/0/aweq1122/010 - 副本 - 副本 - 副本 - 副本 - 副本.bmp",
            "/storage/emulated/0/aweq1122/010 - 副本 - 副本 - 副本 - 副本 (2).bmp"
        )
        every { mFavoriteHelper.string2List(saveText) } returns list
        every { mFavoriteHelper.getFavoriteFileList() } answers { callOriginal() }
        assertEquals(list.size, mFavoriteHelper.getFavoriteFileList()?.size)
    }
}