/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.provider
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.provider

import android.content.Context
import android.database.sqlite.SQLiteException
import com.filemanager.common.MyApplication
import com.oplus.filemanager.room.AppDatabase
import com.oplus.filemanager.room.dao.label.FileLabelDao
import com.oplus.filemanager.room.model.FileLabelEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FileLabelDBHelperTest {

    @MockK
    lateinit var context: Context

    @MockK
    lateinit var appDatabase: AppDatabase

    @MockK
    lateinit var fileLabelDao: FileLabelDao

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)
        mockkObject(FileLabelDBHelper)
        mockkObject(AppDatabase.Companion)
        every { AppDatabase.getInstance(context) } returns appDatabase
        every { appDatabase.fileLabelDao() } returns fileLabelDao
    }

    @After
    fun tearDown() {
        unmockkObject(FileLabelDBHelper)
        unmockkObject(AppDatabase.Companion)
    }

    @Test
    fun `should return 1 when insertFileLabel if insert entity`() {
        every { fileLabelDao.insertItem(any()) } returns 1
        val entity = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        assertEquals(1L, FileLabelDBHelper.insertFileLabel(entity))
    }

    @Test
    fun `should return null when insertFileLabel if throw exception`() {
        every { fileLabelDao.insertItem(any()) } throws SQLiteException("sql")
        val entity = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        assertNull(FileLabelDBHelper.insertFileLabel(entity))
    }

    @Test
    fun `should return 2 when getAllLabels if had pin and not pin`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getAllItems() } returns allItems
        justRun { FileLabelDBHelper.updateFileLabelList(any()) }
        assertEquals(2, FileLabelDBHelper.getAllLabels().size)
    }

    @Test
    fun `should return 4 when getAllLabels if had pin and not pin`() {
        val entity1 = FileLabelEntity(1, "1", 1, 1, 0L, 5L)
        val entity2 = FileLabelEntity(2, "2", 1, 1, 0L, 100L)
        val entity3 = FileLabelEntity(3, "3", 1, 1, 100L, 100L)
        val entity4 = FileLabelEntity(4, "4", 1, 1, 300L, 0L)
        val allItems = arrayListOf(entity1, entity2, entity3, entity4)

        every { fileLabelDao.getAllItems() } returns allItems
        justRun { FileLabelDBHelper.updateFileLabelList(any()) }
        assertEquals(entity4.id, FileLabelDBHelper.getAllLabels()[0].id)
        assertEquals(entity3.id, FileLabelDBHelper.getAllLabels()[1].id)
        assertEquals(entity2.id, FileLabelDBHelper.getAllLabels()[2].id)
        assertEquals(entity1.id, FileLabelDBHelper.getAllLabels()[3].id)
    }

    @Test
    fun `should return 0 when getAllLabels if throw exception`() {
        every { fileLabelDao.getAllItems() } throws SQLiteException("sql")
        assertEquals(0, FileLabelDBHelper.getAllLabels().size)
    }

    @Test
    fun `should return null when getFileLabelByName if throw exception`() {
        every { fileLabelDao.getItemByName(any()) } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getFileLabelByName("name"))
    }

    @Test
    fun `should return name when getFileLabelByName if get name`() {
        val entity = FileLabelEntity(1, "name", 1, 1, 0L, 5L)
        every { fileLabelDao.getItemByName(any()) } returns entity
        assertEquals("name", FileLabelDBHelper.getFileLabelByName("name")?.name)
    }

    @Test
    fun `should return 2 when getFileLabelByNames`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getItemByNames(any()) } returns allItems
        assertEquals(2, FileLabelDBHelper.getFileLabelByNames(arrayListOf())?.size)
    }

    @Test
    fun `should return 0 when getFileLabelByNames if throw exception`() {
        every { fileLabelDao.getItemByNames(any()) } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getFileLabelByNames(arrayListOf())?.size)
    }

    @Test
    fun `should return 2 when getFileLabelByNameFuzzy`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getItemByNameFuzzy(any()) } returns allItems
        assertEquals(2, FileLabelDBHelper.getFileLabelByNameFuzzy("")?.size)
    }

    @Test
    fun `should return 0 when getFileLabelByNameFuzzy if throw exception`() {
        every { fileLabelDao.getItemByNameFuzzy(any()) } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getFileLabelByNameFuzzy("name")?.size)
    }

    @Test
    fun `should return 2 when getTopUsedLabels if getLabelsOrderByUsedCount`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByUsedCount() } returns allItems
        assertEquals(2, FileLabelDBHelper.getTopUsedLabels(2)?.size)
    }

    @Test
    fun `should return 1 when getTopUsedLabels if getLabelsOrderByUsedCount and top is 1`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByUsedCount() } returns allItems
        assertEquals(1, FileLabelDBHelper.getTopUsedLabels(1)?.size)
    }

    @Test
    fun `should return null when getTopUsedLabels if getLabelsOrderByUsedCount`() {
        val allItems = arrayListOf<FileLabelEntity>()
        every { fileLabelDao.getLabelsOrderByUsedCount() } returns allItems
        assertNull(FileLabelDBHelper.getTopUsedLabels(1))
    }

    @Test
    fun `should return null when getTopUsedLabels if getLabelsOrderByUsedCount throws exception`() {
        every { fileLabelDao.getLabelsOrderByUsedCount() } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getTopUsedLabels(1))
    }

    @Test
    fun `should return 2 when getTopViewedLabels if getLabelsOrderByViewedCount`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByViewedCount() } returns allItems
        assertEquals(2, FileLabelDBHelper.getTopViewedLabels(2)?.size)
    }

    @Test
    fun `should return 1 when getTopViewedLabels if getLabelsOrderByViewedCount and top is 1`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByViewedCount() } returns allItems
        assertEquals(1, FileLabelDBHelper.getTopViewedLabels(1)?.size)
    }

    @Test
    fun `should return null when getTopViewedLabels if getLabelsOrderByViewedCount`() {
        val allItems = arrayListOf<FileLabelEntity>()
        every { fileLabelDao.getLabelsOrderByViewedCount() } returns allItems
        assertNull(FileLabelDBHelper.getTopViewedLabels(1))
    }

    @Test
    fun `should return null when getTopViewedLabels if getLabelsOrderByViewedCount throws exception`() {
        every { fileLabelDao.getLabelsOrderByViewedCount() } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getTopViewedLabels(1))
    }

    @Test
    fun `should return 2 when getTopRecentPinnedLabels if getLabelsOrderByViewedCount`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByPinned() } returns allItems
        assertEquals(2, FileLabelDBHelper.getTopRecentPinnedLabels(2)?.size)
    }

    @Test
    fun `should return 1 when getTopRecentPinnedLabels if getLabelsOrderByViewedCount and top is 1`() {
        val entity1 = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        val entity2 = FileLabelEntity(2, "", 1, 1, 100L, 0L)
        val allItems = arrayListOf(entity1, entity2)
        every { fileLabelDao.getLabelsOrderByPinned() } returns allItems
        assertEquals(1, FileLabelDBHelper.getTopRecentPinnedLabels(1)?.size)
    }

    @Test
    fun `should return null when getTopRecentPinnedLabels if getLabelsOrderByViewedCount`() {
        val allItems = arrayListOf<FileLabelEntity>()
        every { fileLabelDao.getLabelsOrderByPinned() } returns allItems
        assertNull(FileLabelDBHelper.getTopRecentPinnedLabels(1))
    }

    @Test
    fun `should return null when getTopRecentPinnedLabels if getLabelsOrderByViewedCount throws exception`() {
        every { fileLabelDao.getLabelsOrderByPinned() } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getTopRecentPinnedLabels(1))
    }

    @Test
    fun `should return 1 when getFileLabelById`() {
        val entity = FileLabelEntity(1, "", 1, 1, 0L, 0L)
        every { fileLabelDao.getItemById(1) } returns entity
        assertEquals(1L, FileLabelDBHelper.getFileLabelById(1)?.id)
    }

    @Test
    fun `should return null when getFileLabelById if throw exception`() {
        every { fileLabelDao.getItemById(any()) } throws SQLiteException("sql")
        assertNull(FileLabelDBHelper.getFileLabelById(1))
    }
}