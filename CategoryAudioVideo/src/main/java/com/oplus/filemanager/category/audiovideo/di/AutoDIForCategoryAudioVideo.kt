/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryAudioVideo.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.category.audiovideo.di

import com.oplus.filemanager.category.audiovideo.CategoryAudioVideoApi
import com.oplus.filemanager.interfaze.categoryaudiovideo.ICategoryAudioVideoApi
import org.koin.dsl.module

object AutoDIForCategoryAudioVideo {

    val categoryAudioVideoApi = module {
        single<ICategoryAudioVideoApi>(createdAtStart = true) {
            CategoryAudioVideoApi
        }
    }
}