/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.categoryaudio
 * * Version     : 1.0
 * * Date        : 2020/2/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.audiovideo.ui

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DefaultDragListener
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnRefreshDataCallback
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.SortAnimationHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.wrapper.AudioFileWrapper
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.audiovideo.R
import com.oplus.filemanager.category.audiovideo.adapter.CategoryAudioAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class CategoryAudioFragment : RecyclerSelectionVMFragment<CategoryAudioFragmentViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener, IPreviewListFragment {

    companion object {
        const val TAG = "CategoryAudioFragment"
    }

    private var mUri: Uri? = null
    private var mSql: String? = null
    private var mCategoryType = -1
    private var mTempSortType = -1
    private var tempSortDesc = -1
    private var mIsNeedFilter = false
    private var mTitle: String? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var sortAnimationHelper: SortAnimationHelper? = null
    private var mFolderTransformAnimator: FolderTransformAnimator? = null
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mAdapter: CategoryAudioAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mAdEntryName: String? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO, mCategoryType)
    }

    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var bySideRefreshScanMode = false
    private var mFileCount = 0L
    private var hasShowEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    private var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    private var previewOperate: IPreviewOperate? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.category_audio_fragment
    }

    fun setTitle(title: String?) {
        mTitle = title
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.category_audio_menu, menu)
        mToolbar?.apply {
            mNeedSkipAnimation = true
            refreshScanModeItemIcon(this)
            setToolbarMenuVisible(this, !isChildDisplay)
            val edit = menu.findItem(R.id.actionbar_edit)
            edit?.isVisible = mFileCount > 0

        }
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }

        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, mCategoryType)
                val page = OptimizeStatisticsUtil.signalToPage(mTitle ?: "")
                OptimizeStatisticsUtil.pageSearch(page)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, page)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.signalToPage(mTitle ?: ""))
                true
            }
            R.id.navigation_sort -> {
                baseVMActivity?.let {
                    StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                    OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.signalToPage(mTitle ?: ""))
                    val bundle = Bundle()
                    bundle.putInt(Constants.TEMP_SORT_TYPE, mTempSortType)
                    bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
                    bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, SortRecordModeFactory.getCategoryKey(mCategoryType))
                    val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                    mSortPopupController.showSortPopUp(
                        it,
                        anchorView,
                        bundle,
                        object : SelectItemListener {

                            override fun onDismiss() {
                                sortEntryView?.rotateArrow()
                            }

                            override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                if (flag) {
                                    sortEntryView?.setSortOrder(sortMode, isDesc)
                                    fragmentViewModel?.sortReload()
                                }
                            }
                        })
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                val page = OptimizeStatisticsUtil.signalToPage(mTitle ?: "")
                OptimizeStatisticsUtil.pageSetting(page)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, page)
                true
            }
            com.filemanager.common.R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.let { controller ->
                controller.operationPage = OptimizeStatisticsUtil.signalToPage(mTitle ?: "")
                controller.onNavigationItemSelected(it, item)
            }
        } ?: false
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        baseVMActivity = activity as BaseVMActivity
        baseVMActivity?.let {
            val bundle = arguments ?: return
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            mFileCount = bundle.getLong(KtConstants.P_CATEGORY_COUNT, 0L)
            val resId = bundle.getInt(Constants.TITLE_RES_ID, -1)
            if (resId != -1) {
                mTitle = getString(resId)
            }
            val uri = bundle.getString(KtConstants.P_URI)
            mSql = bundle.getString(KtConstants.P_SQL)
            mCategoryType = bundle.getInt(KtConstants.P_CATEGORY_TYPE, -1)
            mTempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            mIsNeedFilter = bundle.getBoolean(KtConstants.P_IS_NEED_FILTER)
            mUri = Uri.parse(uri)
            mAdapter = CategoryAudioAdapter(it, mCategoryType, <EMAIL>)
            mAdapter!!.setHasStableIds(true)
            tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
        }

    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById<FileManagerRecyclerView>(R.id.recycler_view).also {
            it.setLoadStateForScroll(this)
        }
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        /**
         * 视频音频共用一个页面，视频itemView动画和图片一样，音频和全部文件动画一样
         * 需要通过categoryType区分是哪个页面
         */
        if (mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
            sortAnimationHelper = SortAnimationHelper(SortRecordModeFactory.getCategoryKey(mCategoryType))
        } else {
            mFolderTransformAnimator = FolderTransformAnimator()
        }
        initToolbar()
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        if (mTempSortType != -1) {
            sortEntryView?.setSortOrder(mTempSortType, tempSortDesc == 0)
        } else {
            sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(mCategoryType))
        }
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onMenuItemSelected(menu)
        }
        scrollHelper = DragScrollHelper(getRecyclerView())
    }

    override fun createViewModel(): CategoryAudioFragmentViewModel {
        val vm = ViewModelProvider(this)[CategoryAudioFragmentViewModel::class.java]
        mFileOperateController = NormalFileOperateController(lifecycle, mCategoryType, vm).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentViewModel?.mCurrentFileType = mCategoryType
        fragmentRecyclerView?.let { recyclerView ->
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            val tmpScanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            val spanSize = ItemDecorationFactory.getGridItemCount(activity, tmpScanMode,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO, mCategoryType)
            mLayoutManager = FileGridLayoutManager(context, spanSize).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        if (AdvertManager.isAdEnabled()) {
                            return if (viewType == (BaseFileBean.TYPE_FILE_LIST_FOOTER + CategoryHelper.CATEGORY_VIDEO)
                                || (viewType == BaseFileBean.TYPE_FILE_AD)) spanCount else 1
                        }
                        return if (viewType == (BaseFileBean.TYPE_FILE_LIST_FOOTER + CategoryHelper.CATEGORY_VIDEO)) spanCount else 1
                    }
                }
            }
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            if (mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
                recyclerView.itemAnimator = sortAnimationHelper?.sortAnimator
            } else {
                recyclerView.itemAnimator = mFolderTransformAnimator
                recyclerView.itemAnimator?.apply {
                    changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                    addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                    removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                    moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                }
            }

            val animator = recyclerView.itemAnimator
            if (animator is SimpleItemAnimator) {
                animator.supportsChangeAnimations = false
            }
            mAdapter?.let {
                recyclerView.adapter = it
            }
            mToolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(recyclerView.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appBarLayout, 0),
                        recyclerView.paddingRight, paddingBottom)
                    fragmentViewModel?.mBrowseModeState?.value = fragmentViewModel?.mBrowseModeState?.value
                }
            }
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.category_audio_menu)
            setToolbarEditIcon(this, isChildDisplay)
            updateToolbarHeight(this)
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(!isChildDisplay)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        val edit = mToolbar?.menu?.findItem(R.id.actionbar_edit)
        edit?.isVisible = mFileCount > 0
    }

    override fun startObserve() {
        if (baseVMActivity == null) {
            return
        }
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModelChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { categoryAudioUiModel ->
                onCategoryAudioModel(categoryAudioUiModel, viewModule)
            }
            startScrollToPositionObserver()
            startScanModeObserver()
            startObserveLoadState()
            startSideNavigationStatusObserver()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun onListModelChanged(
        viewModule: CategoryAudioFragmentViewModel,
        listModel: Int
    ) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "CategoryAudioFragment mListModel=$listModel")
        if (listModel == LIST_SELECTED_MODE) {
            (baseVMActivity as? NavigationInterface)?.apply {
                showNavigation()
                viewModule.setNavigateItemAble(this@apply)
            }
            mAdapter?.setSelectEnabled(true)
            previewEditedFiles(fragmentViewModel?.getSelectItems())
            fragmentRecyclerView?.let {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                val paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.trackMarginBottom = paddingBottom
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
        } else {
            previewEditedFiles(null)
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                    refreshScanModeItemIcon(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            (baseVMActivity as? NavigationInterfaceForMain)?.apply {
                hideNavigation { setRecyclerViewNormalState() }
            } ?: (baseVMActivity as? NavigationInterface)?.apply {
                setRecyclerViewNormalState()
                hideNavigation()
            }
        }
    }

    private fun onCategoryAudioModel(
        categoryAudioUiModel: BaseUiModel<AudioFileWrapper>,
        viewModule: CategoryAudioFragmentViewModel
    ) {
        Log.d(
            TAG, "CategoryAudioFragment mUiState =" + categoryAudioUiModel.fileList.size
                    + "," + categoryAudioUiModel.selectedList.size + ","
                    + categoryAudioUiModel.keyWord + "," + categoryAudioUiModel.stateModel.toString()
        )
        sortEntryView?.setFileCount(viewModule.getRealFileSize())
        if (categoryAudioUiModel.fileList.isEmpty()) {
            showEmptyView()
            sortEntryView?.visibility = View.GONE
        } else {
            hasShowEmpty = false
            mFileEmptyController.hideFileEmptyView()
            sortEntryView?.visibility = View.VISIBLE
        }
        if (categoryAudioUiModel.stateModel.listModel.value == LIST_SELECTED_MODE) {
            mToolbar?.let {
                refreshSelectToolbar(it)
            }
            (categoryAudioUiModel.fileList as? ArrayList<AudioFileWrapper>)?.let { fileList ->
                if (viewModule.mBrowseModeState.value == KtConstants.SCAN_MODE_LIST && mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
                    listDataRefresh(categoryAudioUiModel, false)
                } else {
                    mAdapter?.let {
                        it.setData(
                            fileList as ArrayList<MediaFileWrapper>,
                            categoryAudioUiModel.selectedList
                        )
                    }
                }
                (baseVMActivity as? CategoryAudioActivity)?.mAdManager?.refreshIfListChanged()
                previewEditedFiles(fragmentViewModel?.getSelectItems())
            }
        } else {
            previewClickedFile(fragmentViewModel?.previewClickedFileLiveData?.value, fragmentViewModel?.previewClickedFileLiveData)
            mToolbar?.let {
                refreshScanModeItemIcon(it)
                updateEditAndSortMenu(it)
                setToolbarEditIcon(it, isChildDisplay)
            }
            baseVMActivity?.supportActionBar?.setDisplayHomeAsUpEnabled(!isChildDisplay)
            (categoryAudioUiModel.fileList as? ArrayList<AudioFileWrapper>)?.let { fileList ->
                if (viewModule.mBrowseModeState.value == KtConstants.SCAN_MODE_LIST && mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
                    listDataRefresh(categoryAudioUiModel, true)
                } else {
                    mAdapter?.let {
                        it.setData(
                            fileList as ArrayList<MediaFileWrapper>,
                            categoryAudioUiModel.selectedList
                        )
                        startRequestAd(categoryAudioUiModel.fileList as ArrayList<AudioFileWrapper>)
                    }
                }
            }
        }
    }

    private fun listDataRefresh(categoryAudioUiModel: BaseUiModel<AudioFileWrapper>, requestAd: Boolean) {
        val mediaFileWrappers = categoryAudioUiModel.fileList as ArrayList<MediaFileWrapper>
        val needDoListAnimate = sortAnimationHelper?.needDoListAnimate() ?: false
        Log.d(TAG, "list setData animate $needDoListAnimate")
        if (mGridSpanAnimationHelper != null && needDoListAnimate) {
            fragmentRecyclerView?.let { recyclerView ->
                recyclerView.mTouchable = false
                recyclerView.stopScroll()
            }
            mGridSpanAnimationHelper?.startListSortAnimation(object : OnRefreshDataCallback {
                override fun onRefreshDataCallback() {
                    mAdapter?.setData(mediaFileWrappers, categoryAudioUiModel.selectedList)
                    if (requestAd) {
                        startRequestAd(categoryAudioUiModel.fileList as ArrayList<AudioFileWrapper>)
                    }
                }
            }, object : OnAnimatorEndListener {
                override fun onAnimatorEnd() {
                    fragmentRecyclerView?.mTouchable = true
                }
            })
        } else {
            mAdapter?.setData(mediaFileWrappers, categoryAudioUiModel.selectedList)
            if (requestAd) {
                startRequestAd(categoryAudioUiModel.fileList as ArrayList<AudioFileWrapper>)
            }
        }
    }

    private fun setRecyclerViewNormalState() {
        mAdapter?.setSelectEnabled(false)
        fragmentRecyclerView?.let {
            val paddingBottom = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
            fragmentFastScroller?.apply {
                trackMarginBottom = paddingBottom
            }
        }
    }


    private fun startScanModeObserver() {
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            sortAnimationHelper?.setAnimatorDurationWithScanModeChange(fragmentRecyclerView, scanMode)
            updateScanModeView(scanMode)
        }
    }

    private fun updateScanModeView(scanMode: Int) {
        mToolbar?.let {
            val needSkipAnimation = mNeedSkipAnimation
            if (needSkipAnimation) {
                refreshScanModeAdapter(scanMode, true)
            } else {
                fragmentRecyclerView?.let { recyclerView ->
                    recyclerView.mTouchable = false
                    recyclerView.stopScroll()
                }
                mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                    override fun onSpanChangeCallback() {
                        mLayoutManager?.scrollToPosition(0)
                        refreshScanModeAdapter(scanMode, true)
                    }
                }, object : OnAnimatorEndListener {
                    override fun onAnimatorEnd() {
                        fragmentRecyclerView?.mTouchable = true
                    }
                })
            }
            delay { refreshScanModeItemIcon(it) }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode, false)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
                if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
                    showEmptyView()
                }
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = fragmentViewModel?.uiState?.value?.fileList
        val size = fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int, fromUpdateScanMode: Boolean) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO, mCategoryType)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            if (fromUpdateScanMode && scanMode == KtConstants.SCAN_MODE_LIST) {
                fragmentRecyclerView?.adapter = this
            }
            mScanViewModel = scanMode
            mFolderTransformAnimator?.mSkipAddRemoveAnimation = true
            notifyDataSetChanged()
        }
        if ((AdvertManager.isAdEnabled())) {
            refreshADView(scanMode)
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, needSkipAnimation, resId)
        }
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator?.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon(it)
                if (bySideRefreshScanMode) {
                    setToolbarEditIcon(it, isChildDisplay)
                }
                bySideRefreshScanMode = true
            }
        }
    }

    private fun startScrollToPositionObserver() {
        fragmentViewModel?.mPositionModel?.observe(this) { value -> mLayoutManager?.scrollToPosition(value) }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    private fun refreshADView(scanMode: Int) {
        baseVMActivity?.let { activity ->
            if (activity is CategoryAudioActivity) {
                activity.mAdManager?.let {
                    if (mAdEntryName.isNullOrEmpty()) {
                        return
                    }
                    val needHideAD = ((scanMode == KtConstants.SCAN_MODE_GRID) && (mCategoryType == CategoryHelper.CATEGORY_VIDEO))
                    it.scanModeChange(mAdEntryName!!, needHideAD)
                    it.refreshByScanModeChanged()
                }
            }
        }
    }


    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(!isChildDisplay)
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(R.menu.category_audio_menu)

        updateEditAndSortMenu(toolbar)
        setToolbarEditIcon(toolbar, isChildDisplay)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            val desc = getString(com.filemanager.common.R.string.menu_recent_file_edit)
            it.contentDescription = desc
            if (baseVMActivity?.sideNavigationStatus?.value == LIST_NORMAL_MODE
                && fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true && isChildDisplay) {
                it.setTitle(null)
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setTitle(desc)
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        val isNotEmpty = fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() ?: false
        val isEnable = isNotEmpty && !DragUtils.isDragging
        (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(
            isEnable, hasDrmFile(
                fragmentViewModel?.getSelectItems())
        )
    }

    override fun pressBack(): Boolean {
        val result = fragmentViewModel?.pressBack() ?: false
        if (result) {
            previewEditedFiles(null)
        }
        return result
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            listEmptyFile()
        }
        Log.d(TAG, "showEmptyView")
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty()) {
            sortEntryView?.setFileCount(0)
            return
        }
        fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), mUri, mSql, mCategoryType, mIsNeedFilter)
        if (mTempSortType != -1) {
            mViewModel?.setSort(mTempSortType, tempSortDesc)
        }
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, paths) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(LIST_NORMAL_MODE)
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            StatisticsUtils.nearMeStatisticsSuccessSearch(baseVMActivity, mCategoryType)
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                if (!previewResult) {
                    mFileOperateController?.operationPage = OptimizeStatisticsUtil.signalToPage(mTitle ?: "")
                    mFileOperateController?.onFileClick(it, baseFile, e)
                }
            }
        }
        return true
    }

    private fun startRequestAd(data: java.util.ArrayList<AudioFileWrapper>) {
        baseVMActivity?.let { activity ->
            if (activity is CategoryAudioActivity) {
                activity.mAdManager?.let {
                    if (mAdEntryName.isNullOrEmpty()) {
                        mAdEntryName = it.makeName(TAG)
                        mAdapter?.mEntryName = mAdEntryName
                    }
                    val needHideAD = (((fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST) == KtConstants.SCAN_MODE_GRID)
                            && (mCategoryType == CategoryHelper.CATEGORY_VIDEO))
                    it.requestSubAd(activity, mAdEntryName!!, mAdapter!!, data as ArrayList<MediaFileWrapper>, needHideAD)
                    it.requestCommonAd(activity, mAdapter!!, data)
                }
            }
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
            refreshScanModeItemIcon(it)
        }
        baseVMActivity?.supportActionBar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            }
        }
    }

    override fun checkPermission() {
        baseVMActivity?.let {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}
    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart dragging:${DragUtils.isDragging}")
        if (DragUtils.isDragging) return false
        fragmentDragScanner?.cancel(true)
        val view = fragmentRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        var result = true
        fragmentRecyclerView?.post {
            val position = fragmentRecyclerView?.getChildAdapterPosition(view) ?: 0
            if (position == -1) {
                Log.e(TAG, "onDragStart position is -1")
                result = false
            }
            val dragHoldDownFile = fragmentViewModel?.uiState?.value?.fileList?.get(position) ?: run {
                result = false
                return@post
            }
            val activityContext = this.activity ?: run {
                result = false
                return@post
            }
            val selectList = fragmentViewModel?.getSelectItems() ?: run {
                result = false
                return@post
            }

            val viewMode = fragmentViewModel?.getRecyclerViewScanMode()
            val isGridImg = viewMode == SelectionTracker.LAYOUT_TYPE.GRID && dragHoldDownFile.mLocalType == MimeTypeHelper.VIDEO_TYPE

            val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_list_item_icon).drawable
            } else {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_grid_item_icon).drawable
            }

            val itemViewList = ArrayList<View>()
            selectList.forEach { baseFileBean ->
                val fileList = fragmentViewModel?.uiState?.value?.fileList
                val indexOf = fileList?.indexOf(baseFileBean)
                if (indexOf != null && indexOf >= 0 && indexOf < fileList.size) {
                    val viewHolder = fragmentRecyclerView?.findViewHolderForAdapterPosition(indexOf)
                    if (viewHolder != null) {
                        itemViewList.add(viewHolder.itemView)
                    }
                }
            }
            DragUtils.createSelectedFileList(selectList)
            (baseVMActivity as? NavigationInterface)?.let { fragmentViewModel?.setNavigateItemAble(it) }
            fragmentDragScanner = FileDragDropScanner(
                activityContext,
                DefaultDragListener(
                    activityContext,
                    view,
                    dragHoldDownFile,
                    dragHoldDrawable,
                    getFragmentCategoryType(),
                    e,
                    viewMode
                ).addSelectedView(itemViewList),
                viewMode,
                isGridImg
            )
            fragmentDragScanner?.let {
                if (it.addData(selectList)) {
                    it.execute()
                }
            }
            result = true
            Log.d(TAG, "onDragStart end")
        }
        return result
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        val gridWidth = if (isOpen) {
            windowWidth - sideNavigationWidth
        } else {
            windowWidth
        }
        val gridWidthDp = ViewHelper.px2dip(appContext, gridWidth)
        val spanCount = ItemDecorationFactory.getAlumOrVideoColumnByScreenWidth(gridWidthDp)
        val itemSpace = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.weixin_grid_vertical_spacing)
        val itemWidth = KtViewUtils.getGridItemWidth(activity, itemSpace, spanCount, 0, gridWidth)
        mAdapter?.setItemWith(itemWidth)

        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
            mCategoryType
        )
        return true
    }

    override fun getFragmentCategoryType(): Int {
        if (mCategoryType != -1) {
            return mCategoryType
        }
        return CategoryHelper.CATEGORY_AUDIO
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator?.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }
}