/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/3/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.oplus.filemanager.category.audiovideo.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FootViewManager
import com.filemanager.common.view.FootViewOperation
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.NormalFooterVH
import com.filemanager.common.viewholder.NormalGridVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdViewHolder
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.audiovideo.R
import com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity
import java.util.*

class CategoryAudioAdapter : BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>, LifecycleObserver,
    FootViewOperation {
    companion object {
        private const val TAG = "CategoryAudioAdapter"
    }

    var mEntryName: String? = null
    var mScanViewModel = KtConstants.SCAN_MODE_GRID
        set(value) {
            field = value
            if (value == KtConstants.SCAN_MODE_GRID) {
                (mContext as? Activity)?.let {
                    mImgItemWith = ItemDecorationFactory.getGridItemWidth(it, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM)
                }
            }
        }
    private var mIsRtl = false
    private var mCategoryType = -1
    private val mSizeCache = HashMap<String, String>()
    private val mUiHandler: Handler
    private var mThreadManager: ThreadManager
    private var mFootViewManager: FootViewManager<MediaFileWrapper>
    private var mImgItemWith: Int = 0
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    constructor(content: Context, categoryType: Int, lifecycle: Lifecycle) : super(content) {
        mIsRtl = Utils.isRtl()
        mCategoryType = categoryType
        mUiHandler = Handler(Looper.getMainLooper())
        mThreadManager = ThreadManager(lifecycle)
        mFootViewManager = FootViewManager(this)
        lifecycle.addObserver(this)
    }

    fun setItemWith(with: Int) {
        mImgItemWith = with
    }

    fun setData(data: ArrayList<MediaFileWrapper>, selectionArray: ArrayList<Int>) {
        mFiles = data
        mSelectionArray = selectionArray
        if (mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
            mFootViewManager.addFootView(mFiles)
        }
        checkComputingAndExecute {
            notifyDataSetChanged()
        }
        mIsRtl = Utils.isRtl()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            BaseFileBean.TYPE_FILE_AD -> {
                AdViewHolder(LayoutInflater.from(parent.context).inflate(com.filemanager.common.R.layout.item_main_ad, parent, false))
            }
            (BaseFileBean.TYPE_FILE_LIST_FOOTER + CategoryHelper.CATEGORY_VIDEO) -> {
                NormalFooterVH(LayoutInflater.from(parent.context).inflate(R.layout.category_audio_gird_footer_item, parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + CategoryHelper.CATEGORY_AUDIO) -> {
                FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + CategoryHelper.CATEGORY_VIDEO) -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalGridVH.getLayoutId(), parent, false)).apply {
                    // 加上GlideLoader.ROUND_CONNER_ALL，秒边才能显示出来
                    setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL)
                }
            }
            KtConstants.SCAN_MODE_LIST -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false), mImgRadius)
            }
            else -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false)).apply {
                    setBorderRoundCornerType(true)
                }
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if ((AdvertManager.isAdEnabled()) && (mFiles[position].mFileWrapperViewType == BaseFileBean.TYPE_FILE_AD)
            && (holder is AdViewHolder)) {
            var adMgr: AdvertManager? = (mContext as CategoryAudioActivity)?.mAdManager
            adMgr?.let {
                holder.addAdView(adMgr.getAdView(mEntryName))
                holder.itemView.visibility = View.VISIBLE
            }
            return
        }
        val file = mFiles[position]
        when (holder) {
            is NormalListVH -> {
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                if (AdvertManager.isAdEnabled()) {
                    holder.itemView.visibility = View.VISIBLE
                }
                if (mCategoryType == CategoryHelper.CATEGORY_VIDEO) {
                    holder.updateDividerVisible(mFiles.size - 2, position)
                } else {
                    holder.updateDividerVisible(mFiles.size - 1, position)
                }
                holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
            }
            is FileBrowserGridVH -> {
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
            is NormalGridVH -> {
                holder.setItemWidth(mImgItemWith)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
            is NormalFooterVH -> {
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
        }
        updateDraggingView(file, holder)
    }

    @SuppressLint("ResourceAsColor")
    private fun updateDraggingView(
        file: MediaFileWrapper,
        holder: RecyclerView.ViewHolder
    ) {
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache?.clear()
        mUiHandler?.removeCallbacksAndMessages(null)
    }

    override fun getItemViewType(position: Int): Int {
        if (position < mFiles.size) {
            if (mFiles[position].mFileWrapperViewType == null) {
                if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
                    return KtConstants.SCAN_MODE_LIST
                } else {
                    return mScanViewModel + mCategoryType
                }
            } else {
                if(mFiles[position].mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_FOOTER) {
                    return BaseFileBean.TYPE_FILE_LIST_FOOTER + mCategoryType
                } else {
                    return mFiles[position].mFileWrapperViewType ?: 0
                }
            }
        }
        return super.getItemViewType(position)
    }

    override fun getItemKey(item: MediaFileWrapper, position: Int): Int? {
        return item?.mId ?: null
    }

    override fun getItemId(position: Int): Long {
        if (position >= 0 && position < mFiles.size) {
            val file = mFiles?.get(position)
            Log.d(TAG, "getItemId, final id: ${file?.mId?.toLong() ?: return SelectionTracker.NO_LONG_ITEM_ID} ")
            return file?.mId?.toLong() ?: return SelectionTracker.NO_LONG_ITEM_ID
        }
        return SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    override fun operation(): FootViewManager<MediaFileWrapper> {
        return mFootViewManager
    }

    override fun hasFootView(): Boolean {
        return mFootViewManager.hasFootView()
    }

    override fun getFootViewPosition(): Int {
        return mFootViewManager.getFootViewPosition()
    }

    override fun needShowFootView(): Boolean {
        return mScanViewModel == KtConstants.SCAN_MODE_GRID
    }

    override fun isFootView(position: Int): Boolean {
        return getItemViewType(position) == (BaseFileBean.TYPE_FILE_LIST_FOOTER + CategoryHelper.CATEGORY_VIDEO)
    }

    override fun getFootString(count: Int): String {
        val adViewCount : Int = AdvertManager.getAdViewCount(mFiles as ArrayList<MediaFileWrapper>)
        return Utils.formatMessage(MyApplication.sAppContext.resources.getQuantityString(
            com.filemanager.common.R.plurals.scan_vid_grid_num_des,
            count - adViewCount, count - adViewCount), Utils.RTL_POSITION_DOUBLE)
    }

    override fun getItemCount(): Int {
        if (!needShowFootView() && (mFiles.size > 0) && (mFiles.last().mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)) {
            return mFiles.size - 1
        }
        return mFiles.size
    }
}