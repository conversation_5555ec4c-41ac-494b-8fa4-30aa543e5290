rootProject.name = "FileManager"
include ':CloudConfig'
include ':SuperApp'
include ':FeedBack'
include ':Setting'
include ':OTA'
include ':SelectDir'
include ':RecycleBin'
include ':Common'
include ':Encrypt'
include ':FileBrowser'
include ':FilePreview'
include ':FileOperate'
include ':CategoryAudioVideo'
include ':CategoryGlobalSearch'
include ':Provider'
include ':CategoryAlbumSet'
include ':CategoryAlbum'
include ':CompressPreview'
include ':CategoryCompress'
include ':Main'
include ':CategoryDocument'
include ':CategoryApk'
include ':Ad:AdNull'
include ':Ad:Overseas'
include ':Ad:pangle'
include ':Oaps:MultiChannel'
include ':Oaps:OapsLib'
include ':FileChoose'
include ':KeyMove'
include ':PCConnect'
include ':app'
include ':fileservice'
include ':AppManager'
include ':LabelManager'
include ':BackupRestore'
include ':CardWidget'
include ':AppMarketManager'
include ':Questionnaire'
include ':FileOpenTime'
include ':DomesticInfo'
include ':FileDriveBrowser'
include ':framework:WeChatSdk'
include ':framework:CloudDrive'
include ':framework:HeytapAccount'
include ':ICPLicense:License'
include ':CategoryDFM'
include ':framework:DFM'
include ':framework:AppSwitch'
include ':framework:Dmp'
include ':framework:SimulateClickEngine'
include ':ApiProxyServices'
include ':exportedLibs:Thumbnail'
include ':exportedLibs:ThumbnailWpsCompat'
include ':exportedLibs:SimulateClickEngine'
include ':exportedLibs:DragDropSelection'
include ':exportedLibs:aar-wps-snapshot'
include ':framework:ExportDmp'
include ':exportUserAgreement'
include ':ShortcutFolder'
include ':framework:RemoteDevice'
include ':CategoryRemoteDevice'
include ':framework:RemoteDevice:remotecontrolsdk'
include ':framework:AddFilePanel'
include ':framework:CloudDriveKit'
include ':framework:TouchShare'
