/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.compresspreview.ui

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.FileGridLayoutManager
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.compresspreview.R
import com.filemanager.compresspreview.utils.DetachableClickListener
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.filemanager.fileoperate.compress.CompressConfirmDialog
import com.filemanager.fileoperate.compress.CompressConfirmDialog.Companion.DEFAULT_SAVE_PATH
import com.filemanager.fileoperate.compress.CompressConfirmType
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.google.android.material.appbar.AppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

class CompressPreviewFragment : RecyclerSelectionVMFragment<CompressPreviewViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener, View.OnClickListener {
    companion object {
        const val TAG = "CompressPreviewFragment"
        const val DELAYED_TIME = 10L
    }

    private var mAppBarLayout: AppBarLayout? = null
    private var mToolbar: COUIToolbar? = null
    private var mRootView: ViewGroup? = null
    private var mTitle: String? = null
    private var mCurrentPath: String? = null
    private var mPreviewRootPath: String? = null
    private var mPreviewErrorMsg: String? = null
    private var previewOverMsg: String? = null
    private var isPreviewOver: Boolean = false
    private var mPreviewDestPath: String? = null
    private var mAdapter: CompressPreviewAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mPathBar: BrowserPathBar? = null
    private var scrollviewOverError: ScrollView? = null
    private var mDeCompressDialog: CompressConfirmDialog? = null
    private var mCOUIButton: COUIButton? = null
    private var mIsUriTempPath: Boolean = false
    private var mIsSetInitLoad = false
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private var mButtonDivider: View? = null
    private val mHandler = Handler(Looper.getMainLooper())
    private var mButtonParent: View? = null
    private var hasShowEmpty: Boolean = false
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_MAIN,
            model, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(model))
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.decompress_preview_fragment_new
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mCurrentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
            mPreviewRootPath = bundle.getString(KtConstants.P_PREVIEW_ROOT_TITLE)
            if ((mPreviewRootPath == null) && (mCurrentPath != null)) {
                mPreviewRootPath = PathFileWrapper(mCurrentPath!!).mDisplayName
            }
            mPreviewErrorMsg = bundle.getString(CompressPreviewActivity.P_PREVIEW_ERROR_MSG)
            previewOverMsg = bundle.getString(CompressPreviewActivity.P_PREVIEW_OVER_MSG)
            isPreviewOver = TextUtils.isEmpty(previewOverMsg).not()
            mPreviewDestPath = bundle.getString(CompressPreviewActivity.P_PREVIEW_DEST_PATH)
            mIsSetInitLoad = bundle.getBoolean(KtConstants.P_INIT_LOAD, false)
            mAdapter = CompressPreviewAdapter(it, <EMAIL>)
            mAdapter!!.setHasStableIds(true)
            mTitle = mPreviewRootPath
        }
    }

    override fun initView(view: View) {
        mRootView = view.findViewById(R.id.coordinator_layout)
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mPathBar = view.findViewById<BrowserPathBar?>(com.oplus.selectdir.R.id.path_bar).apply {
            layoutParams.height =
                context.resources.getDimension(com.filemanager.common.R.dimen.dimen_36dp)
                    .toInt()
        }
        mCOUIButton = view.findViewById<COUIButton>(R.id.decompress_button)?.apply {
            setOnClickListener(this@CompressPreviewFragment)
            layoutParams.width =
                context.resources.getDimension(com.filemanager.common.R.dimen.fragment_primary_button_witch)
                    .toInt()
            updateLayoutParams<FrameLayout.LayoutParams> {
                val layoutLp = this as? ViewGroup.MarginLayoutParams
                layoutLp?.marginStart = 0
                layoutLp?.marginEnd = 0
            }
        }
        initOverErrorView(view)
        mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        toolbar = mToolbar
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        initToolbar()
        mButtonDivider = view.findViewById(R.id.button_divider)
        mButtonParent = view.findViewById(R.id.button_parent)
    }

    private fun initOverErrorView(view: View) {
        Log.d(TAG, "initOverErrorView $previewOverMsg")
        if (isPreviewOver) {
            Log.d(TAG, "initOverErrorView isPreviewOver")
            scrollviewOverError = view.findViewById(R.id.scrollview_over_error)
            scrollviewOverError?.visibility = View.VISIBLE
            val zipTypeIv: ImageView? = view.findViewById(R.id.zip_type_iv)
            val zipNameTv: TextView? = view.findViewById(R.id.zip_name_tv)
            val overMsgTv: TextView? = view.findViewById(R.id.over_msg_tv)
            setZipImage(zipTypeIv, mTitle)
            zipNameTv?.text = mTitle
            overMsgTv?.text = previewOverMsg
            mCOUIButton?.text = appContext.resources.getString(com.filemanager.common.R.string.menu_file_list_decompress)
        }
    }

    private fun setZipImage(zipTypeIv: ImageView?, mTitle: String?) {
        mTitle?.let {
            val type = ".${it.substringAfterLast(".")}"
            when (type) {
                CommonConstants.TYPE_RAR -> zipTypeIv?.setImageResource(com.filemanager.common.R.drawable.ic_file_compress_rar)
                CommonConstants.TYPE_JAR -> zipTypeIv?.setImageResource(com.filemanager.common.R.drawable.ic_file_compress_jar)
                CommonConstants.TYPE_7Z -> zipTypeIv?.setImageResource(com.filemanager.common.R.drawable.ic_file_compress_7z)
                else -> zipTypeIv?.setImageResource(com.filemanager.common.R.drawable.ic_file_compress_zip)
            }
        }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.compress_preview_menu)
        }
        mRootView?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(true)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    override fun onClick(view: View?) {
        when (view?.id) {
            R.id.decompress_button -> doCompress(mIsUriTempPath)
            else -> Log.e(TAG, "${view?.id}")
        }
    }

    override fun createViewModel(): CompressPreviewViewModel {
        return if (fragmentViewModel == null) {
            ViewModelProvider(this)[CompressPreviewViewModel::class.java]
        } else {
            fragmentViewModel as CompressPreviewViewModel
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let {
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.isVerticalFadingEdgeEnabled = true
            it.setFadingEdgeLength(appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.list_fading_edge_height))
            it.layoutManager = mLayoutManager!!
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { adapter ->
                fragmentRecyclerView!!.adapter = adapter
            }
            mToolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        (mCOUIButton?.measuredHeight
                            ?: 0) + appContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.content_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(mAppBarLayout), it.paddingRight, paddingBottom)
                }
            }
        }
        initPathBar()
        if (!mPreviewErrorMsg.isNullOrEmpty() && (baseVMActivity != null)) {
            COUIAlertDialogBuilder(baseVMActivity!!)
                    .setCancelable(false)
                    .setTitle(mPreviewErrorMsg)
                    .setPositiveButton(com.filemanager.common.R.string.positive_ok, DetachableClickListener.wrap { dialog, _ ->
                        dialog.dismiss()
                        activity?.apply {
                            if (!isFinishing && !isDestroyed) {
                                setResult(CompressPreviewActivity.RESULT_ERROR_CODE)
                                finish()
                            }
                        }
                    }).show()
        }
    }

    private fun initPathBar() {
        if (isPreviewOver) {
            mPathBar?.visibility = View.INVISIBLE
        }
        mPathBar?.let {
            val isPreviewDest = (mPreviewDestPath?.isNotEmpty() == true)
            if (isPreviewDest) {
                fragmentViewModel?.mPathHelp = FileBrowPathHelper(mPreviewDestPath!!)
                mCOUIButton?.visibility = View.GONE
                mButtonParent?.visibility = View.GONE
            } else if (!fragmentViewModel?.mPathHelp?.getRootPath().isNullOrEmpty()) {
                //do nothing. may be from new build activity by theme changing
                it.showRootPathString(fragmentViewModel!!.mPathHelp!!.getRootPath()!!)
            } else {
                if (mPreviewRootPath != null) {
                    fragmentViewModel?.mPathHelp = FileBrowPathHelper(mPreviewRootPath!!)
                    it.showRootPathString(mPreviewRootPath!!)
                }
            }
            it.setPathHelper(fragmentViewModel?.mPathHelp)
            it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                override fun onPathClick(index: Int, path: String?) {
                    fragmentViewModel?.clickPathBar(index)
                }
            }).setTextFocusChangeListener(object : BrowserPathBar.OnTextFocusColorChangeListener {
                override fun onFocusChange(currentFocusText: String) {
                    mTitle = currentFocusText
                    if (fragmentViewModel?.mNeedScroll == true) {
                        KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, mTitle)
                    } else {
                        mToolbar?.title = mTitle
                    }
                }
            }).show()
            if (isPreviewDest) {
                it.setCurrentPath(mPreviewDestPath!!)
            }
        }
    }

    private fun showNavigation() {
        (baseVMActivity as? NavigationInterface)?.showNavigation()
    }

    private fun hideNavigation() {
        (baseVMActivity as? NavigationInterface)?.hideNavigation()
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModuleChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { fileUiModel ->
                onFileUiModelChanged(fileUiModel, viewModule)
            }
            viewModule.mPositionModel.observe(this) { positionModel ->
                onPositionModelChanged(viewModule, positionModel)
            }
        }
    }

    private fun onListModuleChanged(viewModule: CompressPreviewViewModel, listModel: Int) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "mListModel=$listModel")
        if (listModel == KtConstants.LIST_SELECTED_MODE) {
            mCOUIButton?.visibility = View.GONE
            mButtonParent?.visibility = View.GONE
            showNavigation()
            mAdapter?.let {
                it.setSelectEnabled(true)
                it.setChoiceModeAnimFlag(true)
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
        } else {
            mCOUIButton?.visibility = View.VISIBLE
            mButtonParent?.visibility = View.VISIBLE
            mAdapter?.let {
                it.setSelectEnabled(false)
                it.setChoiceModeAnimFlag(false)
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            hideNavigation()
        }
    }

    private fun onFileUiModelChanged(
        fileUiModel: CompressPreviewViewModel.CompressPreviewUiModel,
        viewModule: CompressPreviewViewModel
    ) {
        Log.d(
            TAG, "mUiState =" + fileUiModel.fileList.size + ","
                    + fileUiModel.selectedList.size
        )
        if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.let(::refreshSelectToolbar)
            (fileUiModel.fileList as? MutableList<BaseDecompressFile>)?.let {
                mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                mAdapter?.setData(it, fileUiModel.selectedList, viewModule.mNeedScroll)
            }
        } else {
            if (fileUiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                mFileEmptyController.hideFileEmptyView()
            }
            mToolbar?.let(::updateToolbarNormalMode)
            mAdapter?.let {
                (fileUiModel.fileList as? MutableList<BaseDecompressFile>)?.let { list ->
                    mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                    it.setData(list, fileUiModel.selectedList, viewModule.mNeedScroll)
                }
            }
            if (fileUiModel.fileList.isEmpty()) {
                mButtonDivider?.alpha = 0f
            } else {
                val recyclerView = fragmentRecyclerView ?: return
                recyclerView.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        recyclerView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        mHandler.postDelayed(::updateButtonDivider, DELAYED_TIME)
                    }
                })
            }
        }
    }

    private fun updateButtonDivider() {
        val layoutManager = mLayoutManager ?: return
        val lastItem = layoutManager.findLastCompletelyVisibleItemPosition()
        val firstItem = layoutManager.findFirstCompletelyVisibleItemPosition()
        var count = -1
        mAdapter?.let { count = it.itemCount }
        if (lastItem < count - 1) {
            mButtonDivider?.alpha = 1f
        } else {
            if (firstItem > 0) {
                mButtonDivider?.alpha = 1f
            } else {
                mButtonDivider?.alpha = 0f
            }
        }
    }

    private fun onPositionModelChanged(
        viewModule: CompressPreviewViewModel,
        positionModel: CompressPreviewViewModel.PositionModel
    ) {
        if (!viewModule.mModeState.initState) {
            return
        }
        mPathBar?.let {
            var currentPath = mPreviewRootPath
            if (!TextUtils.isEmpty(positionModel.mCurrentPath)) {
                currentPath =
                    mPreviewRootPath + File.separator + positionModel.mCurrentPath
            }
            if (currentPath != null) {
                if (currentPath.endsWith(File.separator)) {
                    currentPath = currentPath.run { substring(0, lastIndexOf(File.separator)) }
                }
                if (it.getCurrentPath() != currentPath) {
                    it.setCurrentPath(currentPath)
                }
            }
        }
        mAppBarLayout?.setExpanded(false)
        mAppBarLayout?.postDelayed({
            mLayoutManager?.scrollToPositionWithOffset(
                positionModel.mPosition,
                positionModel.mOffset
            )
            viewModule.mPositionModel.value?.mPosition = 0
            viewModule.mPositionModel.value?.mOffset = 0
            viewModule.mNeedScroll = false
        }, BaseFolderAnimAdapter.FILE_BROWSER_FOLDER_ANIM_DELAY)
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        updateToolbarNormalMode(toolbar)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun updateToolbarNormalMode(toolbar: COUIToolbar) {
        if (toolbar.menu.findItem(R.id.actionbar_edit) == null) {
            toolbar.inflateMenu(R.menu.compress_preview_menu)
        }
        toolbar.menu.findItem(R.id.actionbar_edit)?.apply {
            isVisible = fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == false
            setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
            setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        if (checkedCount > 0 && !DragUtils.isDragging) {
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(isEnable = true, mHasDrm = false)
        } else {
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(isEnable = false, mHasDrm = false)
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    override fun onDestroy() {
        super.onDestroy()
        mPathBar?.setOnPathClickListener(null)
        mDeCompressDialog?.dismiss()
        mDeCompressDialog = null
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (mRootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, mRootView!!)
            hasShowEmpty = true
        }
        mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
        Log.d(TAG, "showEmptyView")
    }

    override fun onResumeLoadData() {
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    fun previewData() {
        Log.d(TAG, "previewData $mCurrentPath")
        mCurrentPath?.let {
            fragmentViewModel?.previewData(baseVMActivity, it, mPreviewDestPath.isNullOrEmpty())
        }
    }

    fun updateCurrentPath(currentPath: String) {
        mCurrentPath = currentPath
        mPreviewRootPath = PathFileWrapper(currentPath).mDisplayName
        if ((mPreviewRootPath != null)) {
            mPathBar?.showRootPathString(mPreviewRootPath!!)
            fragmentViewModel?.mPathHelp?.updateRootPath(mPreviewRootPath!!)
        }

    }

    override fun pressBack(): Boolean {
        val result = fragmentViewModel?.pressBack() ?: false
        if (!result) {
            return if (baseVMActivity is CompressPreviewActivity) {
                false
            } else {
                baseVMActivity?.let {
                    val mainAction = Injector.injectFactory<IMain>()
                    mainAction?.backPreviousFragment(CategoryHelper.CATEGORY_COMPRESS, it)
                }
                true
            }
        }
        return true
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            com.filemanager.common.R.id.navigation_decompress -> doCompress(mIsUriTempPath)
            else -> false
        }
    }

    private fun doCompress(isUriTempPath: Boolean): Boolean {
        Log.d(TAG, "doCompress mCurrentPath $mCurrentPath, isUriTempPath=$isUriTempPath")
        val currentPath = mCurrentPath ?: return false
        val parentPath = KtUtils.getParentFilePath(currentPath)
        val isPrivatePath = KtUtils.isPrivateDataPath(requireContext(), currentPath)
        mDeCompressDialog?.dismiss()
        mDeCompressDialog =
            CompressConfirmDialog(requireContext(), com.support.panel.R.style.COUIFitContentBottomSheetDialog, CompressConfirmType.DECOMPRESS).apply {
            val path = if (isPrivatePath.not()) {
                parentPath
            } else {
                VolumeEnvironment.getInternalSdPath(appContext) + DEFAULT_SAVE_PATH
            }
            setSavePath(path)
            setModifySavePathListener {
                if (activity is TransformNextFragmentListener) {
                    (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_DECOMPRESS)
                }
            }
            val fileName = if (mPreviewRootPath != null) {
                mPreviewRootPath!!.substringAfterLast('/').substringBeforeLast('.')
            } else {
                currentPath.substringAfterLast('/').substringBeforeLast('.')
            }
            setFileName(fileName)
            setClickButtonListener { saveName, savePath ->
                fragmentViewModel?.doDeCompress(baseVMActivity!!, currentPath, savePath, saveName)
                mDeCompressDialog?.dismiss()
            }
            Log.d(TAG, "savePath=$path, fileName=$fileName, isPrivatePath=$isPrivatePath")
            show()
        }
        return false
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }

        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        item.selectionKey?.let { key ->
            fragmentRecyclerView?.let {
                val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: it.paddingTop
                fragmentViewModel?.onItemClick(baseVMActivity, key, viewPosition, offset - it.paddingTop)
            }
        }
        return true
    }

    fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        baseVMActivity?.let {
            if (paths != null) {
                when (requestCode) {
                    MessageConstant.MSG_EDITOR_DECOMPRESS -> {
                        val path = paths.getOrNull(0) ?: return
                        mDeCompressDialog?.setSavePath(path)
                    }
                    else -> fileOperateController.onSelectPathReturn(it, requestCode, paths)
                }
            }
        }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        Log.d(TAG, "onActivityCreated $mIsSetInitLoad")
        if (mIsSetInitLoad && isPreviewOver.not()) {
            mIsSetInitLoad = false
            mRootView?.post {
                if (isAdded) {
                    previewData()
                }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
                fragmentViewModel?.onConfigurationChanged()
            }
        }
    }

    fun backToTop() {
        getRecyclerView()?.fastSmoothScrollToTop()
    }

    fun exitSelectionMode() {
        if (fragmentViewModel?.isInSelectMode() == true) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }
}