/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.compresspreview
 * * Version     : 1.0
 * * Date        : 2020/5/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.compresspreview.ui

import android.content.ContentResolver
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Environment.DIRECTORY_DOWNLOADS
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.text.TextUtils
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.updatePadding
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.deleteUriTempDirectory
import com.filemanager.common.fileutils.getDisplayNameByString
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.compresspreview.R
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.DecompressHelper
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_FAILED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_ENOUGH
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.decompress.P7ZipDecompressHelper
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.filemanager.fileoperate.previewcompress.FileActionPreviewCompress
import com.filemanager.fileoperate.previewcompress.FilePreviewCompressObserver
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import java.io.File

class CompressPreviewActivity : BaseVMActivity(), NavigationBarView.OnItemSelectedListener,
    NavigationInterface,
    TransformNextFragmentListener, BaseVMActivity.PermissonCallBack {
    companion object {
        const val P_PREVIEW_ERROR_MSG = "P_PREVIEW_ERROR_MSG"
        const val P_PREVIEW_OVER_MSG = "P_PREVIEW_OVER_MSG"
        const val P_PREVIEW_DEST_PATH = "P_PREVIEW_DEST_PATH"
        const val RESULT_ERROR_CODE = 2
        private const val TAG = "CompressPreviewActivity"
        private const val TAG_FILE_BROWSER = "file_browser_tag"
        private const val CALLER_PACKAGE_NAME = "package_name"
        private const val DEF_FILE_PROVIDER_HEADER_STRING = "/root"
        private const val DEF_FILE_PROVIDER_FILE_HEADER = "/file:"
        private const val DEF_FILE_PROVIDER_EXTFILE_HEADER = "/extfiles"
        private val DEF_DEST_DIRECTORY_PATH by lazy { Environment.getExternalStoragePublicDirectory(DIRECTORY_DOWNLOADS).toString() }
        private const val SUCCESS_CODE = 0
        private const val ERROR_INTENT_EMPTY = 1
        private const val ERROR_URI_TO_PATH = 2
        private const val ERROR_FILE_PERM = 3
        private const val ERROR_DIRECTORY_PERM = 4
        private const val ERROR_OTHER = 5
        private const val FILE_INTERFACE_TIME_OUT = 300L
    }

    private var mCompressFileUri: Uri? = null
    private var mCompressFile: BaseFileBean? = null
    private var mDestFile: BaseFileBean? = null
    private var mCompressFilesErrorMsg: String? = null
    private var mCurrentPath: String? = null
    private var mRootView: ViewGroup? = null
    private var mCompressPreviewFragment: CompressPreviewFragment? = null
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mIsInitData = true
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mNavigationController by lazy { NavigationController(lifecycle, NavigationType.DECOMPRESS_PREVIEW,R.id.navigation_tool) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_IMAGE,
            model, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(model))
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.file_browser_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        val intent = intent
        if (null == intent) {
            Log.v(TAG, "intent null")
            transitionFinish()
            return
        }
        mRootView = findViewById(R.id.coordinator_layout)
        mCurrentPath = IntentUtils.getString(intent, KtConstants.P_CURRENT_PATH)
        if (!mCurrentPath.isNullOrEmpty()) {
            initFragment(false)
        }
    }

    private fun initFragment(isInitLoad: Boolean, destPath: String? = null, msg: String? = null) {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_FILE_BROWSER)
        if ((fragment == null) || (fragment !is CompressPreviewFragment)) {
            fragment = CompressPreviewFragment()
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.P_CURRENT_PATH, mCurrentPath)
        bundle.putString(P_PREVIEW_ERROR_MSG, mCompressFilesErrorMsg)
        bundle.putString(P_PREVIEW_DEST_PATH, destPath)
        bundle.putString(P_PREVIEW_OVER_MSG, msg)
        bundle.putString(KtConstants.P_PREVIEW_ROOT_TITLE, mCompressFile?.mDisplayName)
        bundle.putBoolean(KtConstants.P_INIT_LOAD, isInitLoad)
        Log.d(TAG, "mCurrentPath $mCurrentPath destPath $destPath mDisplayName ${mCompressFile?.mDisplayName}")
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.content, fragment, TAG_FILE_BROWSER)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mCompressPreviewFragment = fragment
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (mCompressPreviewFragment != null) {
            mCompressPreviewFragment!!.onMenuItemSelected(item)
        } else {
            super.onOptionsItemSelected(item)
        }
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    private fun getIntentArgs(): Int {
        val errorCode: Int
        val receiverIntent = intent ?: return ERROR_INTENT_EMPTY
        val packageName = IntentUtils.getString(receiverIntent, CALLER_PACKAGE_NAME)
        if (!TextUtils.isEmpty(packageName)) {
            StatisticsUtils.nearMeStatisticsCompressPreviewCaller(this, packageName)
        }
        errorCode = getUriByIntent(receiverIntent)
        return if (errorCode != SUCCESS_CODE) {
            errorCode
        } else {
            getFileByUri()
        }
    }

    private fun getUriByIntent(intent: Intent): Int {
        mCompressFileUri = intent.data
        if (mCompressFileUri == null) {
            Log.w(TAG, "intent data empty.  error 1")
            return ERROR_INTENT_EMPTY
        } else {
            Log.d(TAG, "getUriByIntent mCompressFileUri=$mCompressFileUri")
        }
        if (!DecompressHelper.isValidPreviewFileNameWithoutHiddenFile(mCompressFileUri.toString())) {
            return ERROR_OTHER
        }
        return SUCCESS_CODE
    }

    private fun getFileByUri(): Int {
        val filePath = getPathByUri(mCompressFileUri)
        if (TextUtils.isEmpty(filePath)) {
            Log.w(TAG, "CompressFile path empty.  error 2")
            return ERROR_INTENT_EMPTY
        }
        val file = File(filePath!!)
        val result = runBlocking {
            try {
                withTimeout(FILE_INTERFACE_TIME_OUT) {
                    if (file.exists() && !file.isDirectory && file.canRead()) {
                        mCompressFile = PathFileWrapper(filePath)
                    } else {
                        mCompressFile = BaseFileBean()
                        mCompressFile?.apply {
                            mLocalFileUri = mCompressFileUri
                            mDisplayName = getDisplayNameByString(mCompressFileUri?.path ?: "")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "getFileByUri error ${e.message}")
                return@runBlocking ERROR_OTHER
            }
        }
        if (result == ERROR_OTHER) {
            return ERROR_OTHER
        }
        val dirPath = DEF_DEST_DIRECTORY_PATH
        mDestFile = PathFileWrapper(dirPath)
        mDestFile?.apply {
            if (!this.mIsDirectory || !JavaFileHelper.canRead(PathFileWrapper(dirPath))
                    || !JavaFileHelper.canWrite(PathFileWrapper(dirPath))) {
                return ERROR_DIRECTORY_PERM
            }
        }
        return SUCCESS_CODE
    }

    private fun getPathByUri(uri: Uri?): String? {
        var path: String? = null
        if (uri == null) {
            return null
        } else if (DocumentsContract.isDocumentUri(this, uri)) {
            path = FileMediaHelper.queryPathFromUri(this, uri)
            if (!TextUtils.isEmpty(path)) {
                val relPath = path!!.split(":".toRegex()).toTypedArray()
                path = Environment.getExternalStorageDirectory().toString() + File.separator + relPath[1]
            } else {
                Log.d(TAG, "DocumentUri:$uri  format fail")
            }
        } else if (ContentResolver.SCHEME_CONTENT.equals(uri.scheme, ignoreCase = true)) {
            if (MediaStore.AUTHORITY.equals(uri.authority, ignoreCase = true)) {
                path = FileMediaHelper.queryPathFromUri(this, uri)
            } else {
                path = uri.path
                if (TextUtils.isEmpty(path)) {
                    return null
                }
                if (path!!.startsWith(DEF_FILE_PROVIDER_HEADER_STRING)) {
                    path = path.replaceFirst(DEF_FILE_PROVIDER_HEADER_STRING.toRegex(), "")
                } else if (path.startsWith(DEF_FILE_PROVIDER_EXTFILE_HEADER)) {
                    path = path.replaceFirst(DEF_FILE_PROVIDER_EXTFILE_HEADER.toRegex(), "")
                    if (path.startsWith(DEF_FILE_PROVIDER_FILE_HEADER)) {
                        path = path.replaceFirst(DEF_FILE_PROVIDER_FILE_HEADER.toRegex(), "")
                    }
                }
            }
        } else if (ContentResolver.SCHEME_FILE.equals(uri.scheme, ignoreCase = true)) {
            path = uri.path
        }
        return if (TextUtils.isEmpty(path) || !DecompressHelper.isValidPreviewFileNameWithoutHiddenFile(path)) {
            ""
        } else {
            Uri.decode(path)
        }
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        mRootView?.post {
            if (mIsInitData) {
                mIsInitData = false
                if (mCurrentPath.isNullOrEmpty()) {
                    val errorCode = getIntentArgs()
                    if (errorCode == SUCCESS_CODE) {
                        mCompressFile?.let {
                            val decompressObserver = object : FileDecompressObserver(this@CompressPreviewActivity) {
                                override fun onActionDone(result: Boolean, data: Any?) {
                                    Log.d(TAG, "decompressObserver onActionDone $result , data=${data}")
                                    if (result) {
                                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                                        fileBrowser?.toFileBrowserActivity(this@CompressPreviewActivity, data as String)
                                    }
                                    transitionFinish()
                                }

                                override fun onActionCancelled() {
                                    Log.d(TAG, "decompressObserver onActionCancelled")
                                    transitionFinish()
                                }
                            }
                            decompressObserver.mIsNeedSkipShowClickToast = true
                            FileActionPreviewCompress(this, it, decompressObserver, PathFileWrapper(DEF_DEST_DIRECTORY_PATH)).execute(object : FilePreviewCompressObserver(this@CompressPreviewActivity) {
                                override fun onActionPreviewOverCount(msg: String?) {
                                    //如果文件过多或者文件过大，底部弹窗提示
                                    mCompressFile = it
                                    mCurrentPath = it.mData
                                    initFragment(true, msg = msg)
                                }

                                override fun onActionDone(result: Boolean, data: Any?) {
                                    Log.d(TAG, "FileActionPreviewCompress onActionDone $result,data=$data")
                                    if (result && (data is Pair<*, *>) && (data.second is Map<*, *>?)) {
                                        mCurrentPath = data.first as String?
                                        CompressPreviewCacheHelper.storeTransferPreviewFile(data.second as Map<String, MutableList<out BaseDecompressFile>?>?)
                                        initFragment(true)
                                    } else {
                                        if ((data as? Int) == PREVIEW_FAILED) {
                                            mCompressFilesErrorMsg = getString(com.filemanager.common.R.string.decompress_file_error)
                                        } else if ((data as? Int) == PREVIEW_NOT_ENOUGH) {
                                            mCompressFilesErrorMsg = getString(com.filemanager.common.R.string.storage_space_not_enough)
                                        }
                                        initFragment(true, mDestFile?.mData)
                                    }
                                }

                                override fun onActionCancelled() {
                                    super.onActionCancelled()
                                    Log.d(TAG, "FileActionPreviewCompress onActionCancelled")
                                    transitionFinish()
                                }
                            })
                        }
                    } else if ((errorCode == ERROR_INTENT_EMPTY) || (errorCode == ERROR_DIRECTORY_PERM) || (errorCode == ERROR_OTHER)) {
                        innerFinish(errorCode)
                        mCompressFilesErrorMsg = null
                    } else {
                        mCompressFilesErrorMsg = getString(com.filemanager.common.R.string.decompress_file_error)
                        initFragment(true, mDestFile?.mData)
                    }
                } else {
                    mCompressPreviewFragment?.previewData()
                }
            }
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mCurrentPath?.let {
            mCompressPreviewFragment!!.updateCurrentPath(it)
            mCompressPreviewFragment!!.previewData()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        //onDestroy(included decompress over),delete temp compress file from uri
        ThreadManager.sThreadManager.execute(FileRunnable(DeleteUriTempRunnable(), "DeleteUriTempDir"))
        mSelectPathController.onDestroy()
        mCompressPreviewFragment = null
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onBackPressed() {
        if ((mCompressPreviewFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mCompressPreviewFragment?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mCompressPreviewFragment?.onNavigationItemSelected(menuItem) ?: false
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mCompressPreviewFragment?.onResumeLoadData()
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mCompressPreviewFragment?.fromSelectPathResult(code, paths)
        fileOperateController.onSelectPathReturn(this, code, paths)
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    private fun innerFinish(errorCode: Int) {
        setResult(errorCode)
        transitionFinish(false)
    }

    /**
     * 用于关闭时，区分是否需要过度动画
     * @param isAnimSkip 是否跳过过度动画，默认不跳过
     */
    fun transitionFinish(isAnimSkip: Boolean = false) {
        if (isAnimSkip) {
            overridePendingTransition(0, 0)
        } else {
            overridePendingTransition(com.support.appcompat.R.anim.coui_close_slide_enter, com.support.appcompat.R.anim.coui_close_slide_exit)
        }
        P7ZipDecompressHelper.clearPassword()
        finish()
    }

    private class DeleteUriTempRunnable : Runnable {
        override fun run() {
            deleteUriTempDirectory()
        }
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mCompressPreviewFragment?.onUIConfigChanged(configList)
    }

    override fun isAdaptNavigationBar() = false

    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission")
        showSettingGuildDialog()
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        transitionFinish()
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
        if (navPaddingBottom > 0) {
            findViewById<View>(android.R.id.content)?.updatePadding(bottom = navPaddingBottom)
        }
    }
}