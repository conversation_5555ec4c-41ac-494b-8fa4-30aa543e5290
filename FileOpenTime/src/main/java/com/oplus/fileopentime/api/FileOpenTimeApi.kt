/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileOpenTimeApi.kt
 * * Description : FileOpenTimeApi
 * * Version     : 1.0
 * * Date        : 2022/12/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   luohan                2023/07/013       1      create
 ***********************************************************************/
package com.oplus.fileopentime.api

import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import androidx.annotation.Keep
import androidx.lifecycle.ViewModelStoreOwner
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.fileopentime.LoadFileUtil
import com.oplus.fileopentime.provider.FileTimeStore
import com.oplus.fileopentime.provider.requestAllFile
import com.oplus.fileopentime.utils.FileTimeUtil
import kotlinx.coroutines.runBlocking

object FileOpenTimeApi : IFileOpenTime {
    private const val TAG = "FileOpenTimeApi"

    @Keep
    override fun getAllFile(arg: String?, extras: Bundle?): Bundle? {
        val result: Bundle?
        runBlocking {
            result = requestAllFile()
        }
        return result
    }

    override fun loadAllFile(owner: ViewModelStoreOwner) {
        LoadFileUtil.loadAllFile(owner)
    }

    override fun addOrUpdateFileTime(path: String, lastModifyTime: Long) {
        FileTimeUtil.addOrUpdateFileTime(path, lastModifyTime)
    }

    override fun addOrUpdateFileTime(path: List<String>, lastModifyTime: List<Long>) {
        FileTimeUtil.addOrUpdateFileTime(path, lastModifyTime)
    }

    override fun renameFilePath(oldFilePath: String, newFilePath: String) {
        FileTimeUtil.renameFilePath(oldFilePath, newFilePath)
    }

    override fun batchRenameFilePath(oldFilePath: List<String?>, newFilePath: List<String?>) {
        FileTimeUtil.batchRenameFilePath(oldFilePath, newFilePath)
    }

    override fun deleteFileByFilePath(filePath: String) {
        FileTimeUtil.deleteFileByFilePath(filePath)
    }

    override fun initFileTimeStore() {
        FileTimeStore.init()
    }

    override fun getFileOpenTimeSql(): String {
        return "CREATE TABLE IF NOT EXISTS ${FileTimeStore.TABLE_NAME} " +
                "(${FileTimeStore.Columns.ID} INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "${FileTimeStore.Columns.FILE_PATH} TEXT NOT NULL, " +
                "${FileTimeStore.Columns.FILE_OPEN_TIME} INTEGER NOT NULL, " +
                "${FileTimeStore.Columns.FILE_CREATE_TIME} INTEGER NOT NULL, " +
                "${FileTimeStore.Columns.TEMP1} TEXT, " +
                "${FileTimeStore.Columns.TEMP2} TEXT, " +
                "${FileTimeStore.Columns.TEMP3} TEXT, " +
                "${FileTimeStore.Columns.TEMP4} TEXT, " +
                "${FileTimeStore.Columns.TEMP5} TEXT);"
    }

    override fun addFileOpenTime(path: String, openFileTime: Long, createTime: Long) {
        FileTimeUtil.addFileOpenTime(path, openFileTime, createTime)
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor {
        Log.d(TAG, "query")
        return FileTimeUtil.query(uri, projection, selection, selectionArgs, sortOrder)
    }

    override fun findLastOpenTime(list: MutableList<MediaFileWrapper>) {
        FileTimeUtil.findLastOpenTimeToList(list)
    }
}