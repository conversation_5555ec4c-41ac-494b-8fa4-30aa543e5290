/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileTimeStore.kt
 ** Description:  FileTimeStore
 ** Version: 1.0
 ** Date: 2023/07/05
 ** Author: v-<PERSON><PERSON><PERSON>@oppo.com
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.fileopentime.provider

import com.oplus.filemanager.provider.FileManagerProvider
import com.oplus.filemanager.provider.store.FileManagerStore

object FileTimeStore {
    const val TABLE_NAME = "file_open_time"
    private const val PATH_FILE_TIME = "file_time_path"
    private const val FILE_TIME_CODE = 6

    fun init() {
        FileManagerProvider.addURI(
            FileManagerStore.FILEMANAGER_AUTHORITY,
                "*/$PATH_FILE_TIME",
            FILE_TIME_CODE
        )
        FileManagerProvider.addContentInterface(FILE_TIME_CODE, FileTimeContent::class.java)
    }

    interface Columns {
        companion object {
            const val ID = "_id"
            const val FILE_PATH = "file_path"
            const val FILE_OPEN_TIME = "file_open_time"
            const val FILE_CREATE_TIME = "file_create_time"
            const val TEMP1 = "temp1"
            const val TEMP2 = "temp2"
            const val TEMP3 = "temp3"
            const val TEMP4 = "temp4"
            const val TEMP5 = "temp5"
        }
    }
}