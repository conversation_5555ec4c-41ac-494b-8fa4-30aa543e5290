/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FileActionBaseDialogObserver.kt
 * * Description:the lifecycle observer need to bind with the livedata about showing dialog
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       the lifecycle observer need to bind with the livedata about showing dialog
 ****************************************************************/
package com.filemanager.fileoperate.base

import android.content.Context
import android.content.DialogInterface
import android.view.ContextThemeWrapper
import android.view.KeyEvent
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Observer
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileActionObserver.Companion.TAG
import java.lang.ref.WeakReference

const val DISMISS_PROGRESS = -2002
const val UPDATE_PROGRESS = -2001
const val SHOW_PROGRESS = -2000
const val SHOW_TOAST = -2003
const val ACTION_DONE = -1000
const val ACTION_DONE_FOR_EXTERNAL_PATH = -1003
const val ACTION_FAILED = -1001
const val ACTION_CANCELLED = -1002
const val ACTION_SHOW_RENAME_DIALOG_RE = -3000

abstract class BaseFileActionObserver(context: ContextThemeWrapper): IFileActionObserver {
    companion object {
        internal const val TAG = "BaseFileActionObserver"
        internal const val MAX_PROGRESS = 100
        internal const val EXTERNAL_STORAGE_COPY_PROGRESS = 90
        internal const val EXTERNAL_STORAGE_COPY_ANIM_MILLS = 400L
    }

    var mProgressDialog: AlertDialog? = null
    var mLifecycle: FileActionObserverLifecycle? = null
    var mContext: WeakReference<ContextThemeWrapper> = WeakReference(context)
    private val mObserver = Observer<Pair<Any, Any>> { result ->
        if ((getContext() != null) && onChanged(getContext()!!, result).not()) {
            when (result.first) {
                SHOW_TOAST -> showToast(result.second)
                SHOW_PROGRESS -> showProgressDialog(result.second)
                UPDATE_PROGRESS -> updateProgressDialog(result.second)
                DISMISS_PROGRESS -> dismissProgressDialog()
                ACTION_DONE, ACTION_FAILED -> {
                    dismissProgressDialog()
                    onActionDone(result.first == ACTION_DONE, result.second)
                }
                ACTION_DONE_FOR_EXTERNAL_PATH -> updateProgressDialogExternalStorageEnd(context, result)
                ACTION_CANCELLED -> {
                    dismissProgressDialog()
                    onActionCancelled()
                }
                ACTION_SHOW_RENAME_DIALOG_RE -> {
                    onActionReShowDialog()
                }
            }
        }
    }

    fun getContext() = mContext.get()

    fun getObserver() = mObserver

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    open fun onChanged(context: Context, result: Pair<Any, Any>): Boolean = false

    override fun onActionDone(result: Boolean, data: Any?) {}

    override fun onActionCancelled() {}

    override fun onActionReloadData() {}

    @Suppress("EmptyFunctionBlock")
    override fun onActionReShowDialog() {}

    override fun isShowDialog(): Boolean {
        return mProgressDialog?.isShowing ?: false
    }

    private fun showToast(info: Any?) {
        CustomToast.showShort(info as String)
    }

    /**
     * 执行复制到otg或sd卡时 落盘时进度条90%-100%的动画
     */
    open fun updateProgressDialogExternalStorageEnd(
        context: ContextThemeWrapper,
        result: Pair<Any, Any>
    ) {}

    private fun showProgressDialog(info: Any?) {
        dismissProgressDialog()
        getContext()?.let {
            val runnable = Runnable {
                mLifecycle?.onProgressDialogCancel()
            }
            (info as? ProgressDialogBean)?.let { info ->
                mProgressDialog = if (info.indeterminate) {
                    DialogFactory.getIndeterminateProgressDialog(it, info.title, runnable)
                } else {
                    DialogFactory.getExplicitProgressDialog(it, info.title, MAX_PROGRESS, runnable)
                }
                if (info.indeterminate.not()) {
                    mProgressDialog?.show()
                }
            }
            if ((info is ProgressDialogBean) && (mProgressDialog is AlertDialog)) {
                val progressBar = mProgressDialog!!.window?.findViewById<View>(com.support.dialog.R.id.progress)
                if (progressBar is COUIHorizontalProgressBar) {
                    progressBar?.progress = info.currentProgress
                }
            }
        }
    }

    private fun updateProgressDialog(info: Any?) {
        if ((info is Int) && (mProgressDialog is AlertDialog)) {
            mProgressDialog!!.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress = info
        }
    }

    fun dismissProgressDialog() {
        try {
            mProgressDialog?.dismiss()
        } catch (e: Exception) {
        }
        mProgressDialog = null
    }

    fun setFileActionObserverLifecycle(lifecycle: FileActionObserverLifecycle) {
        mLifecycle = lifecycle
    }

    open fun recycle() {
        Log.d(TAG, "recycle")
        dismissProgressDialog()
        mLifecycle = null
        mContext?.clear()
    }

    interface FileActionObserverLifecycle {
        /**
         * Cancel in progress dialog
         */
        fun onProgressDialogCancel()
    }

    data class ProgressDialogBean(var title: String? = null, var indeterminate: Boolean,
                                  var currentProgress: Int = 0)
}

private object DialogFactory {

    fun getIndeterminateProgressDialog(
        context: Context, msg: String?,
        action: Runnable?
    ): AlertDialog {
        val alertDialog = COUIRotatingDialogBuilder(context, msg).show()
        alertDialog.apply {
            setOnCancelListener { action?.run() }
            setCanceledOnTouchOutside(false)
        }
        return alertDialog
    }

    fun getExplicitProgressDialog(
        context: Context, msg: String?, max: Int,
        action: Runnable?
    ): AlertDialog {
        val dialog = COUIAlertDialogBuilder(context, R.style.explicit_AlertDialog_Progress).create()
        initProgressDialog(dialog, msg, action)
        dialog.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.max = max
        return dialog
    }

    private fun initProgressDialog(dialog: AlertDialog, title: String?, action: Runnable?) {
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setTitle(title)
        dialog.setOnKeyListener(DialogInterface.OnKeyListener { _, keyCode, keyEvent ->
            if ((action != null) && (keyEvent.action == KeyEvent.ACTION_DOWN)
                && ((keyCode == KeyEvent.KEYCODE_BACK) || (keyCode == KeyEvent.KEYCODE_SEARCH))) {
                Log.d(TAG, "ProgressDialog OnKey()  action=${keyEvent.action},keyCode=${keyCode}")
                action!!.run()
                return@OnKeyListener true
            }
            return@OnKeyListener false
        })
        val cancelText = dialog.context.getString(android.R.string.cancel)
        dialog.setButton(DialogInterface.BUTTON_NEGATIVE, cancelText) { _, _ ->
            action?.run()
        }
        dialog.setOnDismissListener {
            dialog.setOnKeyListener(null)
            val listener: DialogInterface.OnClickListener? = null
            dialog.setButton(DialogInterface.BUTTON_NEGATIVE, cancelText, listener)
        }
    }
}