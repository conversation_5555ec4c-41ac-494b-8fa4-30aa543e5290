/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.fileoperate.NormalFileOperateController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/1/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.fileoperate

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.controller.FileEncryptController
import com.filemanager.common.controller.MoreItemController
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.CategoryHelper.CATEGORY_FILE_BROWSER
import com.filemanager.common.helper.CategoryHelper.CATEGORY_OTG_BROWSER
import com.filemanager.common.helper.CategoryHelper.CATEGORY_SEARCH
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.interfaces.fileoprate.IFileOperate.Companion.OP_CREATE_SHORTCUT
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.utils.ShortCutUtils.ShortCutCallback
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isInvalid
import com.filemanager.common.utils.stringResource
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.clouddriver.FileActionCloudDriver
import com.filemanager.fileoperate.clouddriver.FileCloudDriverObserver
import com.filemanager.fileoperate.compress.CompressConfirmDialog
import com.filemanager.fileoperate.compress.CompressConfirmDialog.Companion.DEFAULT_SAVE_PATH
import com.filemanager.fileoperate.compress.CompressConfirmType
import com.filemanager.fileoperate.compress.FileActionCompress
import com.filemanager.fileoperate.compress.FileCompressObserver
import com.filemanager.fileoperate.copy.FileActionCopy
import com.filemanager.fileoperate.copy.FileCopyObserver
import com.filemanager.fileoperate.createdir.FileActionCreateDir
import com.filemanager.fileoperate.createdir.FileCreateDirObserver
import com.filemanager.fileoperate.cut.FileActionCut
import com.filemanager.fileoperate.cut.FileCutObserver
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.FileActionDecompress
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.detail.FileActionDetail
import com.filemanager.fileoperate.detail.FileDetailObserver
import com.filemanager.fileoperate.encrypt.FileActionEncrypt
import com.filemanager.fileoperate.encrypt.FileSecurityEncryptObserver
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.filemanager.fileoperate.previewcompress.FileActionPreviewCompress
import com.filemanager.fileoperate.previewcompress.FilePreviewCompressObserver
import com.filemanager.fileoperate.rename.FileActionRename
import com.filemanager.fileoperate.rename.FileRenameObserver
import com.filemanager.fileoperate.save.FileActionSave
import com.filemanager.fileoperate.share.FileActionShare
import com.filemanager.fileoperate.share.FileShareObserver
import com.oplus.encrypt.EncryptActivity
import com.oplus.encrypt.EncryptUtils
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.compresspreview.ICompressPreview
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.ota.upgrade.OtaUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.io.File

/**
 * This file is used to unify package the file operations, and the caller can set a interceptor to
 * customize the operation process. The operation result will delivery by the OperateResultListener.
 */
open class NormalFileOperateController(
    lifecycle: Lifecycle,
    private val mCategoryType: Int,
    viewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>,
    private val mSortType: Int? = null
) : BaseLifeController, IFileOperate {
    companion object {
        private const val TAG = "FileOperateController"
        private const val LIMIT_COUNT = 500
        const val COMPRESS_CONFIRM_FRAGMENT_TAG = "COMPRESS_CONFIRM_FRAGMENT_TAG"

        @JvmStatic
        fun doOnCutActionDone(
            activity: ComponentActivity,
            destPath: String,
            sourceFiles: List<BaseFileBean>,
            result: Boolean
        ) {
            Log.i(TAG, "doOnCutActionDone result = $result")
            if (result) {
                if (activity is BaseVMActivity) {
                    activity.onRefreshData()
                }
                /**移动文件操作
                 * 更新文件路径，文件最近打开时间不变 selectCutFile.mData为原文件路径，destPath + "/" + selectCutFile.mDisplayName为移动后的文件路径
                 * */
                val oldFilePaths = sourceFiles.map { it.mData }
                val newFilePaths = sourceFiles.map { destPath + "/" + it.mDisplayName }
                val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
                fileOpenTimeAction?.batchRenameFilePath(oldFilePaths, newFilePaths)
                val info = OptimizeStatisticsUtil.OperationInfo(
                    sourceFiles.size.toString(),
                    OptimizeStatisticsUtil.OP_CUT,
                    Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                    destPath,
                    sourceFiles
                )
                OptimizeStatisticsUtil.allOperation(info)
            }
        }
    }

    init {
        lifecycle.addObserver(this)
    }

    private var mViewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>? = viewModel
    private var mInterceptor: IFileOperate? = null
    private var mListener: IFileOperate.OperateResultListener? = null
    private val mMoreItemController by lazy { MoreItemController(lifecycle) }
    private var mDeCompressDialog: CompressConfirmDialog? = null
    private var mCompressDialog: CompressConfirmDialog? = null
    private var mCompressFile: BaseFileBean? = null
    private var mAction: BaseFileAction<*>? = null
    private var mOperateAction: IFileOperateAction<IFileActionObserver>? = null
    private var fileDetailObserver: FileDetailObserver? = null
    var operationPage = ""
    var checkHasDynamicBeans: Boolean = false
    private var mLoadingDialog: AlertDialog? = null
    private var isPreviewOpen: Boolean = false

    private fun initLoadingView(activity: ComponentActivity) {
        mLoadingDialog = COUIRotatingDialogBuilder(activity, stringResource(R.string.os12_tagprogressbar)).show()
        mLoadingDialog?.setCanceledOnTouchOutside(false)
        mLoadingDialog?.setCancelable(false)
    }

    private fun dismissLoading() {
        mLoadingDialog?.isShowing?.let {
            mLoadingDialog?.dismiss()
            mLoadingDialog = null
        }
    }

    private fun isRecycled(activity: Activity): Boolean {
        return ((mViewModel == null) || activity.isFinishing || activity.isDestroyed).apply {
            if (this) {
                Log.w(TAG, "isRecycled: mViewModel=$mViewModel")
            }
        }
    }

    override fun setResultListener(listener: IFileOperate.OperateResultListener) {
        mListener = listener
    }

    override fun pickResultListener(): IFileOperate.OperateResultListener? = mListener

    override fun setInterceptor(interceptor: IFileOperate) {
        mInterceptor = interceptor
    }

    override fun onNavigationItemSelected(
        activity: ComponentActivity,
        item: MenuItem
    ): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onNavigationItemSelected(activity, item) == true)) {
            return true
        }
        when (item.itemId) {
            R.id.navigation_delete -> onDelete(activity)
            R.id.navigation_send -> {
                val share: View? = activity.findViewById(R.id.navigation_send)
                onShare(activity, share?.let { ViewHelper.getViewRect(share) })
            }
            R.id.navigation_label -> onAddLabel(activity)
            R.id.navigation_cut -> onSelectCutDir(activity)
            R.id.navigation_copy -> onSelectCopyDir(activity)
            R.id.navigation_detail -> onDetail(activity)
            R.id.navigation_more -> showMoreItemPopupWindow(activity)
            else -> {
                Log.d(TAG, "onNavigationItemSelected ignore: ${item.itemId}")
            }
        }
        return true
    }

    private fun showMoreItemPopupWindow(
        activity: ComponentActivity
    ) {
        val selectedList = mViewModel!!.getSelectItems()
        if (selectedList.size >= LIMIT_COUNT && checkHasDynamicBeans) {
            initLoadingView(activity)
        }
        CoroutineScope(Dispatchers.IO).launch {
            StatisticsUtils.onCommon(activity, StatisticsUtils.MORE_ITEM)
            val containDynamicBeans = runBlocking {
                checkHasDynamicBeans && NewFunctionSwitch.isSupportDfmSearch && KtUtils.checkHasDynamicBeans(selectedList)
            }
            val isSupportEncryption =
                mCategoryType != CategoryHelper.CATEGORY_DFM && !containDynamicBeans
            val isSupportShortCut = !containDynamicBeans
            val supportConfig = MoreItemController.SupportConfig(
                true,
                (Injector.injectFactory<ICloudDrive>()?.supportCloudDisk() == true),
                isSupportEncryption,
                isSupportShortCut
            )
            withContext(Dispatchers.Main) {
                mMoreItemController.showMoreItemPopupWindow(
                    activity,
                    selectedList,
                    supportConfig,
                    this@NormalFileOperateController
                )
                dismissLoading()
            }
        }
    }

    override fun onAddLabel(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onAddLabel(activity) == true)) {
            return true
        }
        if (activity is TransformNextFragmentListener) {
            mViewModel?.let {
                (activity as TransformNextFragmentListener).showEditLabelFragmentDialog(it.getSelectItems())
                val seleteItems = it.getSelectItems()
                Log.i(TAG, "onAddLabel seleteItems size ${seleteItems.size}, seleteItems $seleteItems")
                val isFromSearch = mCategoryType == CATEGORY_SEARCH
                if (isFromSearch) {
                    OptimizeStatisticsUtil.onLabel(getOperationPage(activity), seleteItems, isFromSearch)
                } else {
                    OptimizeStatisticsUtil.onLabel(getOperationPage(activity))
                }
            }
        }
        return true
    }

    override fun onSelectCompressDest(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onSelectCompressDest(activity) == true)) {
            return true
        }
        val isFromSearch = mCategoryType == CATEGORY_SEARCH
        val selectFiles = mViewModel?.getSelectItems() ?: emptyList()
        OptimizeStatisticsUtil.onCompress(getOperationPage(activity), selectFiles, isFromSearch)
        mCompressDialog?.dismiss()
        mCompressDialog = CompressConfirmDialog(activity, com.support.panel.R.style.COUIFitContentBottomSheetDialog).apply {
            if (mCategoryType == CATEGORY_FILE_BROWSER || mCategoryType == CATEGORY_OTG_BROWSER) {
                if (selectFiles.isNotEmpty() && selectFiles[0].mData.isNullOrEmpty().not()) {
                    val dir = selectFiles[0].mData?.let { File(it).parent }
                        ?: (VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) + DEFAULT_SAVE_PATH)
                    setSavePath(dir)
                }
            } else {
                setSavePath(VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) + DEFAULT_SAVE_PATH)
            }
            setModifySavePathListener {
                if (activity is TransformNextFragmentListener) {
                    (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_COMPRESS)
                }
            }
            mViewModel?.getSelectItems()?.let {
                if (it.size > 0) {
                    setFileName(it[0].mDisplayName?.substringBeforeLast(".") ?: "")
                }
            }
            setClickButtonListener { name, path ->
                if (onCompress(activity, path, name)) {
                    mCompressDialog?.dismiss()
                }
            }
            show()
        }
        return true
    }

    override fun onCompress(activity: ComponentActivity, destPath: String, fileName: String): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onCompress(activity, destPath, fileName) == true)) {
            return true
        }
        val selectFiles = mViewModel!!.getSelectItems()
        if (selectFiles.isEmpty()) {
            return true
        }
        var dir = destPath
        if (destPath.isNullOrEmpty() && selectFiles[0].mData.isNullOrEmpty().not()) {
            dir = selectFiles[0].mData.let { File(it).parent }
        }
        if (dir.isNullOrEmpty()) {
            Log.d(TAG, "onCompress failed: dir is null or empty.")
            return false
        }
        mAction = FileActionCompress(activity, selectFiles, PathFileWrapper(dir), fileName).execute(object :
            FileCompressObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                mListener?.onActionDone(IFileOperate.OP_COMPRESS, result, data)
                mViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                if (activity is BaseVMActivity) {
                    activity.onRefreshData()
                }
            }

            override fun onActionCancelled() {
                mListener?.onActionCancelled(IFileOperate.OP_COMPRESS)
            }

            override fun onActionReloadData() {
                mListener?.onActionReloadData(IFileOperate.OP_COMPRESS)
            }
        })
        return true
    }

    override fun onSelectDecompressDest(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onSelectDecompressDest(activity) == true)) {
            return true
        }
        showDecompressConfirmDialog(activity)
        return true
    }

    private fun showDecompressConfirmDialog(activity: ComponentActivity) {
        val isFromSearch = mCategoryType == CATEGORY_SEARCH
        val selectFiles = mViewModel?.getSelectItems() ?: emptyList()
        OptimizeStatisticsUtil.onDecompress(getOperationPage(activity), selectFiles, isFromSearch)
        mDeCompressDialog?.dismiss()
        mDeCompressDialog =
            CompressConfirmDialog(activity, com.support.panel.R.style.COUIFitContentBottomSheetDialog, type = CompressConfirmType.DECOMPRESS).apply {
            if ((selectFiles.size == 1) && (selectFiles[0].mLocalType == MimeTypeHelper.COMPRESSED_TYPE)) {
                val dir = selectFiles[0].mData?.let { File(it).parent }
                    ?: (VolumeEnvironment.getInternalSdPath(appContext) + DEFAULT_SAVE_PATH)
                setSavePath(dir)
            } else if (mCompressFile != null) {
                val parentPath = KtUtils.getParentFilePath(mCompressFile!!.mData ?: "")
                setSavePath(parentPath)
            } else {
                setSavePath(VolumeEnvironment.getInternalSdPath(appContext) + DEFAULT_SAVE_PATH)
            }

            setModifySavePathListener {
                if (activity is TransformNextFragmentListener) {
                    (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_DECOMPRESS)
                }
            }

            if (selectFiles.size == 1) {
                setFileName(selectFiles[0].mDisplayName?.substringBeforeLast(".") ?: "")
            } else if (mCompressFile != null) {
                setFileName(mCompressFile?.mDisplayName?.substringBeforeLast(".") ?: "")
            } else {
                Log.e(TAG, "file name is exception")
            }

            setClickButtonListener { name, path ->
                if (onDecompress(activity, path, name)) {
                    mDeCompressDialog?.dismiss()
                }
            }

            setOnDismissListener {
                mCompressFile = null
            }

            show()
        }
    }

    override fun onDecompress(activity: ComponentActivity, destPath: String?, fileName: String?): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onDecompress(activity, destPath, fileName) == true)) {
            return true
        }
        mViewModel!!.getSelectItems().let {
            if ((it.size == 1) && (it[0].mLocalType == MimeTypeHelper.COMPRESSED_TYPE)) {
                var dir = destPath
                if (destPath.isNullOrEmpty() && it[0].mData.isNullOrEmpty().not()) {
                    dir = it[0].mData?.let { it1 -> File(it1).parent }
                }
                if (dir.isNullOrEmpty()) {
                    Log.d(TAG, "onDecompress failed: dir is null or empty.")
                    return false
                }
                mAction = FileActionDecompress(
                    lifecycle = activity,
                    sourceFile = it[0],
                    destParentFile = PathFileWrapper(dir),
                    needShowCheckButton = needShowCheckButtonWhenDecompress(),
                    decompressFileName = fileName
                ).execute(
                    object : FileDecompressObserver(activity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            Log.d(TAG, "onDecompress onActionDone $result")
                            mListener?.onActionDone(IFileOperate.OP_DECOMPRESS, result, data)
                            if (activity is BaseVMActivity) {
                                activity.onRefreshData()
                            }
                        }

                        override fun onActionCancelled() {
                            mListener?.onActionCancelled(IFileOperate.OP_DECOMPRESS)
                        }

                        override fun onActionReloadData() {
                            mListener?.onActionReloadData(IFileOperate.OP_DECOMPRESS)
                        }
                    })
                mCompressFile = null
            }
        }
        if (mCompressFile != null) {
            mCompressFile!!.let {
                var dir = destPath
                if (destPath.isNullOrEmpty() && it.mData.isNullOrEmpty().not()) {
                    dir = it.mData?.let { it1 -> File(it1).parent }
                }
                if (dir.isNullOrEmpty()) {
                    Log.d(TAG, "onDecompress failed: dir is null or empty.")
                    return false
                }
                mAction = FileActionDecompress(
                    lifecycle = activity,
                    sourceFile = it,
                    destParentFile = PathFileWrapper(dir!!),
                    needShowCheckButton = needShowCheckButtonWhenDecompress(),
                    decompressFileName = fileName
                ).execute(
                        object : FileDecompressObserver(activity) {
                            override fun onActionDone(result: Boolean, data: Any?) {
                                Log.d(TAG, "onDecompress onActionDone $result")
                                mListener?.onActionDone(IFileOperate.OP_DECOMPRESS, result, data)
                            }

                            override fun onActionCancelled() {
                                mListener?.onActionCancelled(IFileOperate.OP_DECOMPRESS)
                            }

                            override fun onActionReloadData() {
                                mListener?.onActionReloadData(IFileOperate.OP_DECOMPRESS)
                            }
                        })
                mCompressFile = null
            }
        }

        return true
    }

    override fun onRename(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onRename(activity) == true)) {
            return true
        }
        mViewModel!!.getSelectItems().let {
            if (it.size != 1) {
                Log.d(TAG, "onRename: select item size not only one [${it.size}]")
                return false
            }
            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
            val isFromSearch = mCategoryType == CATEGORY_SEARCH
            if (isFromSearch) {
                OptimizeStatisticsUtil.onRename(getOperationPage(activity), seleteItems, isFromSearch)
            } else {
                OptimizeStatisticsUtil.onRename(getOperationPage(activity))
            }
            mAction = FileActionRename(activity, it[0]).execute(object : FileRenameObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    it[0].mData?.let { path ->
                        (activity as? IRefreshActivityDataForCreateDir)?.onRefreshDataForDir(path)
                    }
                    mListener?.onActionDone(IFileOperate.OP_RENAME, result, data)
                    val info = OptimizeStatisticsUtil.OperationInfo(
                        it.size.toString(),
                        OptimizeStatisticsUtil.OP_RENAME,
                        Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                        "",
                        it
                    )
                    OptimizeStatisticsUtil.allOperation(info)
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_RENAME)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_RENAME)
                }
            })
        }
        return true
    }

    override fun onEncrypt(activity: ComponentActivity): Boolean {
        fun doFileActionEncrypt(activity: EncryptActivity, service: FileEncryptController.FileManagerEncryptionInterface) {
            mViewModel?.getSelectItems()?.also { items ->
                mAction = FileActionEncrypt(activity, service, items).execute(
                    object : FileSecurityEncryptObserver(activity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            Log.d(TAG, "onEncrypt result $result")
                            if (result) {
                                mListener?.onActionDone(IFileOperate.OP_ENCRYPT, result, data)
                                activity.onRefreshData()
                                val info = OptimizeStatisticsUtil.OperationInfo(
                                    items.size.toString(),
                                    OptimizeStatisticsUtil.OP_ENCRYPT,
                                    Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                                    "",
                                    items
                                )
                                OptimizeStatisticsUtil.allOperation(info)
                            }
                        }

                        override fun onActionCancelled() {
                            mListener?.onActionCancelled(IFileOperate.OP_ENCRYPT)
                        }

                        override fun onActionReloadData() {
                            mListener?.onActionReloadData(IFileOperate.OP_ENCRYPT)
                        }
                    })
            }
        }

        if (isRecycled(activity) || (mInterceptor?.onEncrypt(activity) == true)) {
            return true
        }
        if (EncryptUtils.checkPrivateSafeEnabled(activity).not()) return false
        return if (activity is EncryptActivity) {
            activity.getFileEncryptController()
                ?.runEncryptTask(object : FileEncryptController.ServiceConnectedCallback {
                    override fun onServiceConnected(service: FileEncryptController.FileManagerEncryptionInterface) {
                        if (activity.isInvalid().not()) {
                            doFileActionEncrypt(activity, service)
                            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
                            val isFromSearch = mCategoryType == CATEGORY_SEARCH
                            if (isFromSearch) {
                                OptimizeStatisticsUtil.onEncrypt(getOperationPage(activity), seleteItems, isFromSearch)
                            } else {
                                OptimizeStatisticsUtil.onEncrypt(getOperationPage(activity))
                            }
                        }
                    }
                })
            true
        } else {
            Log.d(TAG, "onEncrypt: not EncryptActivity")
            false
        }
    }

    override fun onDetail(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onDetail(activity) == true)) {
            return true
        }
        fileDetailObserver = object : FileDetailObserver(activity, mCategoryType) {

            override fun onActionDone(result: Boolean, data: Any?) {
                mListener?.onActionDone(IFileOperate.OP_DETAIL, result, data)
            }

            override fun onActionCancelled() {
                mListener?.onActionCancelled(IFileOperate.OP_DETAIL)
            }

            override fun onActionReloadData() {
                mListener?.onActionReloadData(IFileOperate.OP_DETAIL)
            }
        }
        fileDetailObserver?.let {
            mAction = FileActionDetail(activity, mViewModel!!.getSelectItems()).execute(it)
            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
            val isFromSearch = mCategoryType == CATEGORY_SEARCH
            if (isFromSearch) {
                OptimizeStatisticsUtil.onDetail(getOperationPage(activity), seleteItems, isFromSearch)
            } else {
                OptimizeStatisticsUtil.onDetail(getOperationPage(activity))
            }
        }
        return true
    }

    override fun onOpenByOther(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onOpenByOther(activity) == true)) {
            return true
        }
        mViewModel!!.getSelectItems().let {
            if (it.size != 1) {
                Log.d(TAG, "onOpenByOther: select item size not only one [${it.size}]")
                return false
            }
            mAction = FileActionOpen.Companion.Builder(activity, it[0])
                .setSortMode(mSortType)
                .setIsOpenByOtherWay(true)
                .build()
                .execute(object : FileOpenObserver(activity) {
                    override fun onActionDone(result: Boolean, data: Any?) {
                        mListener?.onActionDone(IFileOperate.OP_OPEN_BY_OTHER, result, data)
                        val info = OptimizeStatisticsUtil.OperationInfo(
                            it.size.toString(),
                            OptimizeStatisticsUtil.OP_OPEN_WITH,
                            Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                            "",
                            it
                        )
                        OptimizeStatisticsUtil.allOperation(info)
                    }

                    override fun onActionCancelled() {
                        mListener?.onActionCancelled(IFileOperate.OP_OPEN_BY_OTHER)
                    }

                    override fun onActionReloadData() {
                        mListener?.onActionReloadData(IFileOperate.OP_OPEN_BY_OTHER)
                    }
                })
            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
            val isFromSearch = mCategoryType == CATEGORY_SEARCH
            if (isFromSearch) {
                OptimizeStatisticsUtil.onOpenWith(getOperationPage(activity), seleteItems, isFromSearch)
            } else {
                OptimizeStatisticsUtil.onOpenWith(getOperationPage(activity))
            }
        }
        return true
    }

    override fun onUploadCloudDisk(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onUploadCloudDisk(activity) == true)) {
            return true
        }
        val selectList = mViewModel!!.getSelectItems()
        mAction = FileActionCloudDriver(activity, selectList, activity).execute(
            object : FileCloudDriverObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    mListener?.onActionDone(IFileOperate.OP_UPLOAD_CLOUD_DISK, result, data)
                    val info = OptimizeStatisticsUtil.OperationInfo(
                        selectList.size.toString(),
                        OptimizeStatisticsUtil.OP_UPLOAD_CLOUD,
                        Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                        "",
                        selectList
                    )
                    OptimizeStatisticsUtil.allOperation(info)
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_UPLOAD_CLOUD_DISK)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_UPLOAD_CLOUD_DISK)
                }
            })
        val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
        val isFromSearch = mCategoryType == CATEGORY_SEARCH
        if (isFromSearch) {
            OptimizeStatisticsUtil.onUploadCloud(getOperationPage(activity), seleteItems, isFromSearch)
        } else {
            OptimizeStatisticsUtil.onUploadCloud(getOperationPage(activity))
        }
        return true
    }

    override fun onShare(activity: ComponentActivity, rect: Rect?): Boolean {
        if (Utils.isQuickClick()) {
            Log.d(TAG, "onShare isQuickClick")
            return true
        }
        if (isRecycled(activity) || (mInterceptor?.onShare(activity, rect) == true)) {
            return true
        }
        if (UIConfigMonitor.isZoomWindowShow()) {
            CustomToast.showLong(R.string.toast_opened_without_window_mode)
        }
        val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
        val isFromSearch = mCategoryType == CATEGORY_SEARCH
        if (isFromSearch) {
            OptimizeStatisticsUtil.onSend(getOperationPage(activity), seleteItems, isFromSearch)
        } else {
            OptimizeStatisticsUtil.onSend(getOperationPage(activity))
        }
        StatisticsUtils.onCommon(activity, StatisticsUtils.SEND_MENU_PRESSED)
        val shareBean = FileActionShare.FileActionShareBean(
            activity,
            mViewModel!!.getSelectItems(),
            activity,
            rect
        )
        mAction = FileActionShare(shareBean).execute(
            object : FileShareObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    mListener?.onActionDone(IFileOperate.OP_SHARE, result, data)
                    val info = OptimizeStatisticsUtil.OperationInfo(
                        shareBean.files.size.toString(),
                        OptimizeStatisticsUtil.OP_SEND,
                        Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                        "",
                        shareBean.files
                    )
                    OptimizeStatisticsUtil.allOperation(info)
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_SHARE)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_SHARE)
                }
            })
        return true
    }

    override fun onSelectCopyDir(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onSelectCopyDir(activity) == true)) {
            return true
        }
        if (activity is TransformNextFragmentListener) {
            (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_COPY)
            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
            val isFromSearch = mCategoryType == CATEGORY_SEARCH
            if (isFromSearch) {
                OptimizeStatisticsUtil.onCopy(getOperationPage(activity), seleteItems, isFromSearch)
            } else {
                OptimizeStatisticsUtil.onCopy(getOperationPage(activity))
            }
        }
        return true
    }

    override fun onCopy(activity: ComponentActivity, destPath: String): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onCopy(activity, destPath) == true)) {
            return true
        }
        val selectCopyFileList = mViewModel!!.getSelectItems()
        mAction = FileActionCopy(activity, selectCopyFileList, PathFileWrapper(destPath)).execute(
            object : FileCopyObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    mListener?.onActionDone(IFileOperate.OP_COPY, result, data)
                    doOnCopyActionDone(activity, destPath, selectCopyFileList, result)
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_COPY)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_COPY)
                }
            })
        return true
    }

    @VisibleForTesting
    fun doOnCopyActionDone(
        activity: ComponentActivity,
        destPath: String,
        sourceFiles: List<BaseFileBean>,
        result: Boolean
    ) {
        /*失败了就不需要刷新数据
        也不需要修改数据库
        也不需要成功埋点，但是需要失败埋点[柯卫标记]
         */
        Log.i(TAG, "doOnCopyActionDone result = $result")
        if (result) {
            if (activity is BaseVMActivity) {
                activity.onRefreshData()
            }
            /**复制文件操作
             * destPath + "/" + it.mDisplayName为复制后的文件路径
             * */
            val paths = sourceFiles.map { destPath + "/" + it.mDisplayName }
            val modifyTimes = sourceFiles.map { it.mDateModified }
            val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
            fileOpenTimeAction?.addOrUpdateFileTime(paths, modifyTimes)
            //埋点
            val info = OptimizeStatisticsUtil.OperationInfo(
                sourceFiles.size.toString(),
                OptimizeStatisticsUtil.OP_COPY,
                Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                destPath,
                sourceFiles
            )
            OptimizeStatisticsUtil.allOperation(info)
        }
    }

    override fun onSelectCutDir(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onSelectCutDir(activity) == true)) {
            return true
        }
        if (activity is TransformNextFragmentListener) {
            (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_CUT)
            val seleteItems = mViewModel?.getSelectItems() ?: emptyList()
            val isFromSearch = mCategoryType == CATEGORY_SEARCH
            if (isFromSearch) {
                OptimizeStatisticsUtil.onCut(getOperationPage(activity), seleteItems, isFromSearch)
            } else {
                OptimizeStatisticsUtil.onCut(getOperationPage(activity))
            }
        }
        return true
    }

    override fun onCut(activity: ComponentActivity, destPath: String): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onCut(activity, destPath) == true)) {
            return true
        }
        val selectCutFileList = mViewModel!!.getSelectItems()
        Log.d(TAG, "onCut selectCutFileList ${selectCutFileList.size}")
        mAction = FileActionCut(activity, selectCutFileList, PathFileWrapper(destPath), mCategoryType).execute(
            object : FileCutObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    mListener?.onActionDone(IFileOperate.OP_CUT, result, data)
                    doOnCutActionDone(activity, destPath, selectCutFileList, result)
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_CUT)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_CUT)
                }
            })
        return true
    }

    override fun onDelete(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onDelete(activity) == true)) {
            return true
        }
        mViewModel!!.getSelectItems().let { selectList ->
            val recycleBinAction = Injector.injectFactory<IRecycleBin>()
            mOperateAction =
                recycleBinAction?.getFileActionDelete(
                    activity,
                    selectList,
                    selectList.size == mViewModel!!.getRealFileSize(),
                    mCategoryType,
                )
            mOperateAction?.let {
                StatisticsUtils.onCommon(activity, StatisticsUtils.DELETE_MENU_PRESSED)
                val isFromSearch = mCategoryType == CATEGORY_SEARCH
                if (isFromSearch) {
                    OptimizeStatisticsUtil.onDelete(getOperationPage(activity), selectList, isFromSearch)
                } else {
                    OptimizeStatisticsUtil.onDelete(getOperationPage(activity))
                }
                it.execute(object : IFileActionObserver {

                    override fun onActionDone(result: Boolean, data: Any?) {
                        mOperateAction = null
                        mListener?.onActionDone(IFileOperate.OP_DELETE_TO_RECYCLE, result, data)
                        //最近页面删除文件不需要调用onRefreshData
                        if (mListener is IFileOperate.RecentOperatorListener) {
                            Log.d(TAG, "mListener is RecentOperatorListener")
                        } else if (activity is BaseVMActivity) {
                            activity.onRefreshData()
                        }
                        val info = OptimizeStatisticsUtil.OperationInfo(
                            selectList.size.toString(),
                            OptimizeStatisticsUtil.OP_DELETE,
                            Utils.getDateAndTimeFormat(activity, System.currentTimeMillis()),
                            "",
                            selectList
                        )
                        OptimizeStatisticsUtil.allOperation(info)
                    }

                    override fun onActionCancelled() {
                        mOperateAction = null
                        mListener?.onActionCancelled(IFileOperate.OP_DELETE_TO_RECYCLE)
                    }

                    override fun onActionReloadData() {
                        mOperateAction = null
                        mListener?.onActionReloadData(IFileOperate.OP_DELETE_TO_RECYCLE)
                    }

                    override fun onActionReShowDialog() {
                    }

                    override fun isShowDialog(): Boolean {
                        return false
                    }
                })
            }
            if (mOperateAction == null) {
                Log.w(TAG, "onDelete failed: action get null")
            }
        }
        return true
    }

    override fun onFileClick(
        activity: ComponentActivity,
        file: BaseFileBean,
        event: MotionEvent?,
        mediaIds: ArrayList<String>?
    ): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onFileClick(activity, file, event, mediaIds) == true)) {
            return true
        } else {
            when (file.mFileWrapperViewType) {
                null, BaseFileBean.TYPE_RELATED_FILE -> {
                }
                else -> {
                    Log.d(TAG, "onFileClick ignore: viewType=${file.mFileWrapperViewType}")
                    return true
                }
            }
        }
        mViewModel!!.launch {
            val isExits = file.checkExist()
            if (!isExits) {
                var showToast = false
                val path = file.mData
                if (KtUtils.checkIsDfmPath(path) && path != null) {
                    val isConnected = checkDfmConnection(path)
                    if (!isConnected) {
                        showToast = true
                        CustomToast.showShort(R.string.open_failed_by_device_disconnect)
                    }
                }
                if (!showToast) {
                    CustomToast.showShort(R.string.toast_file_not_exist)
                }
                return@launch
            } else {
                if (PCConnectAction.openFileOnRemote(file, event)) {
                    return@launch
                }
                OptimizeStatisticsUtil.clickEachCategoryFile(file, getOperationPage(activity))
                if (file.mLocalType == MimeTypeHelper.COMPRESSED_TYPE) {
                    if (!OtaUtils.checkOzipFile(activity, file)) {
                        val decompressObserver = object : FileDecompressObserver(activity) {
                            override fun onActionDone(result: Boolean, data: Any?) {
                                mListener?.onActionDone(IFileOperate.OP_DECOMPRESS, result, data)
                            }

                            override fun onActionCancelled() {
                                mListener?.onActionCancelled(IFileOperate.OP_DECOMPRESS)
                            }

                            override fun onActionReloadData() {
                                mListener?.onActionReloadData(IFileOperate.OP_DECOMPRESS)
                            }
                        }
                        mAction = FileActionPreviewCompress(
                            activity,
                            file,
                            decompressObserver,
                            needShowCheckButton = needShowCheckButtonWhenDecompress()
                        ).execute(object :
                            FilePreviewCompressObserver(activity) {
                            override fun onActionDone(result: Boolean, data: Any?) {
                                Log.d(TAG, "FileActionPreviewCompress onActionDone $result")
                                if (result && (data is Pair<*, *>) && (data.second is Map<*, *>?)) {
                                    CompressPreviewCacheHelper.storeTransferPreviewFile(data.second as Map<String, MutableList<out BaseDecompressFile>?>?)
                                    val mainAction = Injector.injectFactory<IMain>()
                                    if (mainAction?.isMainActivity(activity) == false) {
                                        Injector.injectFactory<ICompressPreview>()?.startCompressPreviewActivity(activity, file.mData)
                                    } else {
                                        val bundle = Bundle()
                                        bundle.putString(KtConstants.P_CURRENT_PATH, file.mData)
                                        mainAction?.enterNextFragment(CategoryHelper.CATEGORY_COMPRESS_PREVIEW, activity, bundle)
                                    }
                                }
                                mListener?.onActionDone(IFileOperate.OP_COMPRESS_PREVIEW, result, data)
                                if (!result) {
                                    CustomToast.showShort(R.string.compress_preview_failed)
                                }
                            }

                            override fun onActionCancelled() {
                                mListener?.onActionCancelled(IFileOperate.OP_COMPRESS_PREVIEW)
                            }

                            override fun onActionReloadData() {
                                mListener?.onActionReloadData(IFileOperate.OP_COMPRESS_PREVIEW)
                            }
                            override fun onActionPreviewOverCount(msg: String?) {
                                mCompressFile = file
                                showDecompressConfirmDialog(activity)
                            }
                        })
                    }
                } else {
                    mAction = FileActionOpen.Companion.Builder(activity, file)
                        .setSortMode(mSortType)
                        .setOrderMediaIdList(mediaIds)
                        .build()
                        .execute(FileOpenObserver(activity))
                }
            }
        }
        return true
    }

    private suspend fun checkDfmConnection(dfmPath: String): Boolean {
        var result = false
        try {
            withTimeout(BaseFileBean.FILE_TIME_OUT) {
                withContext(Dispatchers.IO) {
                    val dfmRootPath = KtUtils.getDfmRootPath(dfmPath)
                    if (File(dfmRootPath).exists()) {
                        result = true
                    }
                }
            }
        } catch (ex: TimeoutCancellationException) {
            Log.d(TAG, "checkDfmConnection timeout")
            result = false
        }
        return result
    }

    private fun needShowCheckButtonWhenDecompress(): Boolean {
        return when (mCategoryType) {
            CategoryHelper.CATEGORY_FILE_BROWSER,
            CategoryHelper.CATEGORY_OTG_BROWSER ->
                false
            else -> true
        }
    }

    override fun onCreateFolder(activity: ComponentActivity, currentPath: String): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onCreateFolder(activity, currentPath) == true)) {
            return true
        }
        mAction = FileActionCreateDir(activity, PathFileWrapper(currentPath)).execute(object : FileCreateDirObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                mListener?.onActionDone(IFileOperate.OP_CREATE_FOLDER, result, data)
            }

            override fun onActionCancelled() {
                mListener?.onActionCancelled(IFileOperate.OP_CREATE_FOLDER)
            }

            override fun onActionReloadData() {
                mListener?.onActionReloadData(IFileOperate.OP_CREATE_FOLDER)
            }
        })
        return true
    }

    override fun onSelectPathReturn(activity: ComponentActivity, requestCode: Int, paths: List<String>?): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onSelectPathReturn(activity, requestCode, paths) == true)) {
            return true
        }
        val path = paths?.getOrNull(0)
        if (paths.isNullOrEmpty().not()) {
            when (requestCode) {
                MessageConstant.MSG_EDITOR_CUT -> onCut(activity, path!!)
                MessageConstant.MSG_EDITOR_COPY -> onCopy(activity, path!!)
                MessageConstant.MSG_EDITOR_COMPRESS -> mCompressDialog?.setSavePath(path!!)
                MessageConstant.MSG_EDITOR_DECOMPRESS -> mDeCompressDialog?.setSavePath(path!!)
                MessageConstant.MSG_SAVE -> onSave(activity, path)
                MessageConstant.MSG_ADD_SHORTCUT_FOLDER -> onAddShortcutFolder(activity, paths)
                else -> {
                    Log.d(TAG, "onSelectPathReturn ignore: code=$requestCode")
                }
            }
        }
        return true
    }

    override fun createShortCut(activity: ComponentActivity) {
        val selectedList = mViewModel?.getSelectItems()
        if (selectedList?.isNotEmpty() == true) {
            activity.lifecycleScope.launch(Dispatchers.IO) {
                ShortCutUtils.createShortCut(activity, selectedList[0], object : ShortCutCallback {
                    override fun result(success: Boolean) {
                        Log.d(TAG, "createShortCut success $success")
                        launch(Dispatchers.Main) {
                            if (success) {
                                CustomToast.showShort(R.string.toast_create_shortcut_success)
                                mListener?.onActionCancelled(OP_CREATE_SHORTCUT)
                            } else {
                                CustomToast.showShort(R.string.toast_create_shortcut_repeat)
                            }
                        }
                        OptimizeStatisticsUtil.shortcutEvent(
                            StatisticsUtils.SHORTCUT_OPE_ADD,
                            selectedList[0].mLocalType.toString(),
                            OptimizeStatisticsUtil.getOptionPage(activity, "")
                        )
                    }
                })
            }
        }
    }

    override fun onDestroy() {
        mCompressDialog?.dismiss()
        mCompressDialog = null
        mDeCompressDialog?.dismiss()
        mDeCompressDialog = null
        if (mCategoryType != CategoryHelper.CATEGORY_FILE_PREVIEW) {
            mListener = null
            mInterceptor = null
            mViewModel = null
        }
        fileDetailObserver?.release()
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        mAction?.reShowDialog()
        mOperateAction?.hideDialog()
        fileDetailObserver?.onConfigurationChanged(newConfig)
    }

    override fun isShowDialog(): Boolean {
        if (mInterceptor != null) {
            Log.d(TAG, "isShowDialog interceptor not null")
            return mInterceptor!!.isShowDialog()
        }
        return mAction?.isShowDialog() ?: false || mOperateAction?.isShowDialog() ?: false
                || mDeCompressDialog?.isShowing ?: false || mCompressDialog?.isShowing ?: false || mLoadingDialog?.isShowing ?: false
    }

    private fun onSave(activity: Activity, path: String?) {
        Log.d(TAG, "onSave")
        if (path == null) {
            Log.d(TAG, "onSave dest path is null")
            return
        }
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val listBeans = ArrayList<BaseFileBean>()
            DefaultDropListener.getSelectListUri()?.let {
                if (it.size > 0) {
                    for (uri in it) {
                        val bean = FileOperateUtil.getBaseFileBeanForUri(activity, uri)
                        listBeans.add(bean)
                    }
                }
            }
            DefaultDropListener.getSelectListText()?.let {
                if (it.size > 0) {
                    for ((index, text) in it.withIndex()) {
                        val name = FileOperateUtil.getDateFileName(index, FileOperateApi.TEXT_EXT)
                        val bean = FileOperateUtil.getBaseFileBeanForString(text, activity, name)
                        listBeans.add(bean)
                    }
                }
            }
            DefaultDropListener.getSelectListHtml()?.let {
                if (it.size > 0) {
                    for ((index, html) in it.withIndex()) {
                        val name = FileOperateUtil.getDateFileName(index, FileOperateApi.HTML_EXT)
                        val bean = FileOperateUtil.getBaseFileBeanForString(html, activity, name)
                        listBeans.add(bean)
                    }
                }
            }
            if (listBeans.size > 0) {
                withContext(Dispatchers.Main) {
                    mAction = FileActionSave(activity, listBeans, PathFileWrapper(path)).execute(
                        object : FileCopyObserver(activity) {
                            override fun onActionDone(result: Boolean, data: Any?) {
                                activity.onRefreshData()
                            }

                            override fun onActionCancelled() {
                                Log.d(TAG, "onActionCancelled")
                                activity.onRefreshData()
                            }

                            override fun onActionReloadData() {
                                Log.d(TAG, "onActionReloadData")
                            }
                            override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                                return when (result.first) {
                                    ACTION_DONE -> {
                                        FileOperateUtil.savedFileNotice(activity, result.second as? String)
                                        false
                                    }
                                    ACTION_FAILED -> {
                                        when (result.second) {
                                            is Pair<*, *> -> super.onChanged(context, result)
                                            else -> {
                                                FileOperateUtil.saveFileFailed()
                                                false
                                            }
                                        }
                                    }
                                    else -> super.onChanged(context, result)
                                }
                            }
                        })
                }
            }
        }
    }

    private fun onAddShortcutFolder(activity: ComponentActivity, paths: List<String>?) {
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
            val id = shortcutFolderApi?.addShortcutFolders(paths)?.getOrNull(0) ?: -1L
            withContext(Dispatchers.Main) {
                mListener?.onActionDone(IFileOperate.OP_CREATE_SHORTCUT_FOLDER, id != -1L, Pair(id, paths?.get(0)))
            }
        }
    }

    private fun checkSearchCategory(): Boolean {
        return mCategoryType == CATEGORY_SEARCH
    }

    fun getOperationPage(activity: Activity): String {
        return OptimizeStatisticsUtil.getOptionPage(activity, operationPage)
    }

    fun setPreviewOpen(isOpen: Boolean) {
        isPreviewOpen = isOpen
        (pickResultListener() as? FileOperatorListenerImpl)?.setPreviewOpen(isOpen)
    }
}