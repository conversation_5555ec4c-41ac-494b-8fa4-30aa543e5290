/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DecompressHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.cpu.PerformanceManager
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.utils.Log
import java.io.Closeable
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

abstract class DecompressHelper<T : BaseDecompressFile> {
    private var mIsCancelled = AtomicBoolean(false)
    private var mDecompressCurrentSize = AtomicLong(0)
    private var mDecompressTotalSize = 0L
    private var mDecompressListener: OnDecompressListener? = null

    companion object {
        private const val TAG = "DecompressHelper"
        internal const val MAX_COMPRESS_FILE_NUM = 50
        //临时修改7z格式压缩包的可预览文件个数为20个，后续技术预研后优化
        internal const val MAX_COMPRESS_FILE_NUM_7Z = 20
        private const val MAX_COMPRESS_FILE_SIZE = 1024 * 1024 * 1024 //1G
        private val ILLEGAL_FILE_NAME = arrayOf(".", "..")

        const val PREVIEW_SUCCESS = 111
        const val PREVIEW_OVERSIZE = 112
        const val PREVIEW_OVERCOUNT = 113
        const val PREVIEW_FAILED = 114
        const val PREVIEW_NOT_ENOUGH = 115
        const val PREVIEW_UNSUPPORT = 116
        const val PREVIEW_NOT_EXIST = 117
        const val PREVIEW_NOT_EXIST_DFM_DISCONNECTED = 118

        fun isValidPreviewFileName(fileName: String?): Boolean {
            return if (fileName.isNullOrEmpty()) {
                false
            } else if (HiddenFileHelper.isNeedShowHiddenFile().not() && HiddenFileHelper.isHiddenFileByPath(fileName)) {
                false
            } else {
                var result = true
                for (illegal in ILLEGAL_FILE_NAME) {
                    if (fileName.split(File.separator).contains(illegal)) {
                        Log.d(TAG, "isValidateFile file=${fileName}")
                        result = false
                        break
                    }
                }
                result
            }
        }

        /**
         * 判断是否是有效预览文件，但不判断隐藏目录相关信息
         */
        fun isValidPreviewFileNameWithoutHiddenFile(fileName: String?): Boolean {
            return if (fileName.isNullOrEmpty()) {
                false
            } else {
                var result = true
                for (illegal in ILLEGAL_FILE_NAME) {
                    if (fileName.split(File.separator).contains(illegal)) {
                        Log.d(TAG, "isValidateFile file=${fileName}")
                        result = false
                        break
                    }
                }
                result
            }
        }
    }

    @WorkerThread
    fun preview(sourceFile: BaseFileBean): Pair<Int, Map<String, MutableList<T>?>?> {
        return when {
            sourceFile.mData.isNullOrEmpty() -> {
                Pair(PREVIEW_FAILED, null)
            }
            sourceFile.mSize > MAX_COMPRESS_FILE_SIZE -> {
                Pair(PREVIEW_OVERSIZE, null)
            }
            else -> {
                val result = internalPreview(sourceFile)
                result.second?.values?.forEach {
                    it?.forEach { file ->
                        if (file.mIsDirectory) {
                            file.childCount = result.second!![file.mData]?.size ?: 0
                        }
                    }
                }
                result
            }
        }
    }

    protected abstract fun createDecompressDirFile(path: String): T

    @WorkerThread
    protected abstract fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<T>?>?>

    /**
     * @param destParentFile Dest dir file
     */
    @WorkerThread
    fun decompress(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String? = null,
                   selectFiles: MutableList<T>? = null, listener: OnDecompressListener?) {
        if (sourceFile.mData.isNullOrEmpty() || destParentFile.mData.isNullOrEmpty()) {
            Log.d(TAG, "decompress false,sourceFile.mData=${sourceFile.mData},destParentFile.mData=${destParentFile.mData}")
            listener?.onFinished(false)
            return
        }
        mDecompressListener = listener
        mDecompressCurrentSize.set(0)

        initImproveCpu()
        var result: Boolean? = forceMkdirs(File(destParentFile.mData))
        if (result == true) {
            mDecompressListener?.onInPreparation()
            // result maybe null because of Rar decompress maybe multi-thread
            result = internalDecompress(sourceFile, destParentFile, password,
                    if (selectFiles == null) null else ArrayList(selectFiles), listener)
        }
        // If result is null, means the listener will be invoked by the child class
        result?.let {
            releaseImproveCpu()
            if (it) {
                mDecompressListener?.onFinished(true)
            } else if (isCancelled().not()) {
                if (this is P7ZipDecompressHelper) {
                    mDecompressListener?.onFinished(false)
                } else {
                    mDecompressListener?.onTryAgain()
                }
            }
            recycle()
        }
    }

    protected fun checkCanDecompress(path: String?, isDir: Boolean, selectFiles: MutableList<T>): Boolean {
        if (path.isNullOrEmpty()) {
            return false
        }
        var tempPath = path
        if (isDir && !(path!!.endsWith(File.separator))) {
            tempPath = path.plus(File.separator)
        }
        selectFiles.forEach {
            if ((it.mData == tempPath) || (it.mIsDirectory && tempPath.contains(it.mData!!))) {
                // If it is not a dir, remove it from the select list for reduce the cycles times
                if (it.mIsDirectory.not()) {
                    selectFiles.remove(it)
                }
                return true
            }
        }
        return false
    }

    open fun cancelDecompress() {
        Log.d(TAG, "cancelDecompress")
        if (isCancelled().not()) {
            mIsCancelled.set(true)
            mDecompressListener?.onCancelled()
        }
    }

    fun isCancelled() = mIsCancelled.get()

    protected abstract fun internalDecompress(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String?,
                                              selectFiles: MutableList<T>?, listener: OnDecompressListener?): Boolean?

    fun isEncrypted(sourceFile: BaseFileBean): Boolean {
        return internalIsEncrypted(sourceFile)
    }

    abstract fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean

    abstract fun checkIsSupport(sourceFile: BaseFileBean): Boolean

    fun verifyPassword(sourceFile: BaseFileBean, password: String?): Boolean {
        return internalVerifyPassword(sourceFile, password)
    }

    abstract fun internalVerifyPassword(sourceFile: BaseFileBean, password: String?): Boolean

    protected fun saveDecompressTotalSize(total: Long) {
        mDecompressTotalSize = total
    }

    protected fun updateDecompressProgress(addedSize: Long) {
        var progress: Int = 0
        if (mDecompressTotalSize > 0) {
            mDecompressCurrentSize.addAndGet(addedSize)
            progress = ((mDecompressCurrentSize.get() * 100) / mDecompressTotalSize).toInt()
        }
        mDecompressListener?.onDecompressing(progress)
    }

    protected fun forceMkdirs(file: File): Boolean {
        return file.exists() || file.mkdirs().also {
            if (!it) {
                Log.e(TAG, "forceMkdirs failed: ${file.name}")
            }
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public fun createPreviewFileTree(file: T, fileMap: MutableMap<String, MutableList<T>?>) {
        fun createParentFileTree(path: String?, file: T? = null) {
            if (path.isNullOrEmpty()) {
                return
            }
            val index = (if (path.endsWith(File.separator)) {
                path.substring(0, path.length - 1)
            } else path).lastIndexOf(File.separator)
            val parent = if (index < 0) {
                ""
            } else {
                path.substring(0, index + 1) // end of '/'
            }
            Log.d(TAG, "createParentFileTree: $path <=> $parent")
            var exist = false
            fileMap[parent]?.forEach { fileItem ->
                if (fileItem.mData == path) {
                    exist = true
                    file?.let {
                        if ((fileItem.mDateModified == 0L) && (it.mDateModified > 0)) {
                            fileItem.mDateModified = it.mDateModified
                        }
                    }
                    return@forEach
                }
            }
            if (!exist) {
                if (fileMap[parent] == null) {
                    fileMap[parent] = arrayListOf()
                }
                fileMap[parent]!!.add(file ?: createDecompressDirFile(path))
            }

            createParentFileTree(parent)
        }

        // Method body
        if (file.mData.isNullOrEmpty() || file.mDisplayName.isNullOrEmpty()) {
            Log.d(TAG, "createPreviewFileTree failed: file path/name is empty")
            return
        }
        createParentFileTree(file.mData, file)
    }

    private fun initImproveCpu() {
        PerformanceManager.setSceneAction(PerformanceManager.UN_OR_COMPRESS_ACTION)
    }

    private fun releaseImproveCpu() {
        PerformanceManager.release()
    }

    protected fun validateFileName(fileName: String?): Boolean {
        return if (fileName.isNullOrEmpty()) {
            false
        } else if (HiddenFileHelper.isNeedShowHiddenFile().not() && (HiddenFileHelper.isHiddenFileByPath(fileName))) {
            false
        } else {
            var result = true
            for (illegal in ILLEGAL_FILE_NAME) {
                if (fileName.split(File.separator).contains(illegal)) {
                    result = false
                    break
                }
            }
            result
        }
    }

    protected fun quietClose(stream: Closeable?) {
        try {
            stream?.close()
        } catch (e: Exception) {
        }
    }

    protected open fun recycle() {
        mDecompressListener = null
    }

    interface OnDecompressListener {
        fun onInPreparation() {}

        // max is 100
        fun onDecompressing(percent: Int) {}
        fun onFinished(result: Boolean) {}
        fun onCancelled() {}
        fun onTryAgain() {}
    }
}