/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileDetailDialog.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/3/2
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.detail

import android.content.Context
import android.content.res.Configuration
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean

class FileDetailDialog(
    val context: Context,
    private val categoryType: Int? = null
) {
    companion object {
        private const val TAG = "FileDetailDialog"
    }

    private var adapter: FileDetailDialogAdapter? = null
    private var dialog: AlertDialog? = null

    fun create() {
        adapter = FileDetailDialogAdapter(context, categoryType = categoryType)
        val builder = COUIAlertDialogBuilder(
            context, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment
        ).apply {
            setTitle(com.filemanager.common.R.string.detail_message)
            setAdapter(adapter, null)
            setNegativeButton(
                com.filemanager.common.R.string.dialog_cancel,
                { dialog, which -> dialog?.dismiss() }, false
            )
            setBlurBackgroundDrawable(true)
        }
        dialog = builder.show()
    }

    fun updateDetail(detailBean: FileDetailBean) {
        dialog?.setOnDismissListener(detailBean.mDismissListener)
        adapter?.updateDetail(detailBean, dialog)
    }

    fun show() {
        dialog?.show()
    }

    fun dismiss() {
        if (dialog?.isShowing == true) {
            dialog?.dismiss()
        }
    }

    fun updateLayoutWhileConfigChange(newConfig: Configuration) {
        (dialog as? COUIBottomSheetDialog)?.updateLayoutWhileConfigChange(newConfig)
    }
}