/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.fileoperate.decompress.FileDecompressPasswordDialogTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.fileoperate.decompress

import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.edittext.COUIInputView
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.Test

class FileDecompressPasswordDialogTest {

    @Test
    fun `should execute setPositive when onTextChanged`() {
        val passwordDialog = mockk<FileDecompressPasswordDialog>()
        val s = ""
        justRun { passwordDialog.setPositiveButtonEnabled(false) }
        every { passwordDialog.onTextChanged(s, 0, 0, 0) } answers { callOriginal() }
        passwordDialog.onTextChanged(s, 0, 0, 0)
        verify { passwordDialog.setPositiveButtonEnabled(false) }
    }

    @Test
    fun `should execute setPositive when onTextChanged if text is not empty`() {
        val passwordDialog = mockk<FileDecompressPasswordDialog>()
        val s = "some thing like that"
        justRun { passwordDialog.setPositiveButtonEnabled(true) }
        every { passwordDialog.onTextChanged(s, 0, 0, 0) } answers { callOriginal() }
        passwordDialog.onTextChanged(s, 0, 0, 0)
        verify { passwordDialog.setPositiveButtonEnabled(true) }
    }

    @Test
    fun should_setInputText() {
        val editText = mockk<COUIEditText>()
        val inputView = mockk<COUIInputView>()
        val passwordDialog = mockk<FileDecompressPasswordDialog>()
        every { passwordDialog.mInputView }.returns(inputView)
        every { inputView.editText }.returns(editText)
        every { editText.setText(any<String>()) }.answers { callOriginal() }
        every { passwordDialog.setInputText(any()) }.answers { callOriginal() }
        passwordDialog.setInputText("123456")
        verify { editText.setText(any<String>()) }
    }
}