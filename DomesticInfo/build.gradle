plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")


android {
    namespace "com.oplus.filemanager.domestic"

    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.appcompat
    implementation libs.google.material

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.scroll
    implementation libs.oplus.appcompat.scrollview
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.clickablespan
    implementation libs.oplus.appcompat.poplist
    implementation(libs.koin.android)

    implementation project(':Common')
}