<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">
    <com.coui.appcompat.scrollview.COUINestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="always"
        android:scrollbarSize="0dp"
        android:scrollbars="none"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        >
        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dimen_24dp"
            android:orientation="vertical" />
    </com.coui.appcompat.scrollview.COUINestedScrollView>

    <include layout="@layout/appbar_with_divider_layout_secondary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>