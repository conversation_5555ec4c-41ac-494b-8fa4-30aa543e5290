/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.list.LabelListFragment
 * * Description :  标签显示包含文件列表
 * * Version     : 1.0
 * * Date        : 2022/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.list

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.core.view.doOnNextLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.filelabel.controller.AddFileClickListener
import com.oplus.filemanager.filelabel.controller.AddFileController
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.parentchild.ui.MainCombineFragment
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.recent.entity.recent.ExpandGroupItemEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.task.RecentLoadCallback
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.recent.utils.RecentFileObserver
import com.oplus.labelmanager.AddLabelViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

@Suppress("TooGenericExceptionCaught")
class LabelFileListFragment : RecyclerSelectionVMFragment<LabelFileListViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener, RecentLoadCallback, IPreviewListFragment, IRefreshFragmentDataForDir {
    companion object {
        private const val TAG = "LabelListFragment"
        private const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
        private const val LOAD_DATA_DELAYED_TIME = 500L
    }

    private var mTitle: String? = null
    private var mLabelId: Long = 0L
    private var mIsFilter: Boolean = false
    private var mIsFromSearch: Boolean = false
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mAdapter: LabelFileListAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var sideCategoryType: Int = -1
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_LIST_LABEL)
    }
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    private var needLoadData = false
    private var isChildDisplay = false
    private var hasShowEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /** 是否显示底部工具栏，父子级时用于父级Fragment判断 */
    var isShowNav = false
    private var mAddFileController: AddFileController? = null
    private var mAddLabelViewModel: AddLabelViewModel? = null
    private var mAddFileDialogShow: Boolean = false
    private var mUpdatingFiles: Boolean = false
    private val mRecentFileObserver = RecentFileObserver()
    private var bySideRefreshScanMode = false

    /**
     * 触发 navigation 显示和隐藏
     */
    var triggerNavigation: Boolean = false

    private var previewOperate: IPreviewOperate? = null
    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.fragment_label_list
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(R.id.toolbar)
        }
        toolbar = mToolbar
        rootView = view.findViewById(R.id.coordinator_layout_label_file)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        initToolbar()
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.visibility = View.VISIBLE
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getLabelKey())
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onMenuItemSelected(menu)
        }
        mAddFileController = activity?.let { AddFileController() }
        mAddLabelViewModel = ViewModelProvider(this)[AddLabelViewModel::class.java]

        /*添加选中的文件到标签列表*/
        mAddFileController?.setAddFileClickListener(object : AddFileClickListener {
            override fun onAddFileClick(labelNames: List<String>, fileList: List<BaseFileBean>) {
                mUpdatingFiles = true
                mAddLabelViewModel?.mappingFileToLabel(labelNames, fileList)
            }
        })
        /*添加成功后，更新标签列表，并弹出Toast提示*/
        mAddLabelViewModel?.mUpdateLabels?.observe(this) {
            if (it == AddLabelViewModel.STATUS_LABELS_DONE && mUpdatingFiles) {
                mAddFileController?.dismissAddFileDialog()
                Handler(Looper.getMainLooper()).postDelayed({ fragmentViewModel?.loadData() }, LOAD_DATA_DELAYED_TIME)
                CustomToast.showShort(com.filemanager.common.R.string.label_add_files_successed)
                if (isChildDisplay && parentFragment is PreviewCombineFragment) {
                    ((parentFragment as PreviewCombineFragment).parentFragment as? MainCombineFragment)?.updateSideNavigationLabels()
                }
                mUpdatingFiles = false
            }
        }
        scrollHelper = DragScrollHelper(getRecyclerView())
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let { recyclerView ->
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        val isSingleLine =
                            (viewType == BaseFileBean.TYPE_FILE_LIST_HEADER)
                                    || (viewType == BaseFileBean.TYPE_LABEL_FILE)
                                    || (viewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)
                        return if (isSingleLine) spanCount else 1
                    }
                }
            }
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = mFolderTransformAnimator
            recyclerView.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            resetRecyclerViewHoriontalPadding(recyclerView)

            mAdapter?.let {
                recyclerView.adapter = it
            }
            appBarLayout?.doOnNextLayout {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(recyclerView.paddingLeft,
                        KtViewUtils.getRecyclerViewTopPadding(appBarLayout, 0),
                        recyclerView.paddingRight, paddingBottom)
                }
            }
            recyclerView.setLoadStateForScroll(this)
        }
        if (needLoadData) {
            onResumeLoadData()
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun resetRecyclerViewHoriontalPadding(recyclerView: FileManagerRecyclerView) {
        (activity as? LabelFileListActivity)?.let {
            if (UIConfigMonitor.getWindowType() == UIConfig.WindowType.LARGE) {
                recyclerView.setPadding(
                    appContext.resources.getDimensionPixelSize(com.oplus.labelmanager.R.dimen.dp_16),
                    recyclerView.paddingTop,
                    appContext.resources.getDimensionPixelSize(com.oplus.labelmanager.R.dimen.dp_16),
                    recyclerView.paddingBottom
                )
            } else {
                recyclerView.setPadding(0, recyclerView.paddingTop, 0, recyclerView.paddingBottom)
            }
        }
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        if (isChildDisplay) {
            toolbar.navigationIcon = null
        } else {
            toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            toolbar.setNavigationOnClickListener {
                clickNavigationIcon()
            }
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(R.menu.label_list_menu)
        displayActionByIsChild(toolbar)
        setToolbarEditIcon(toolbar, isChildDisplay)
        updateEditAndSortMenu(toolbar)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun displayActionByIsChild(toolbar: COUIToolbar) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = !isChildDisplay
        toolbar.menu.findItem(R.id.action_add)?.isVisible = true
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            it.setIcon(null)
            it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        }
    }

    private fun setSearchMenuStatus(status: Int?, isChildDisplay: Boolean) {
        toolbar?.menu?.findItem(R.id.actionbar_search)?.apply {
            if (status == KtConstants.LIST_SELECTED_MODE
                && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST
                && isChildDisplay) {
                icon = null
                setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_search)
                setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        if (!isChildDisplay) {
            toolbar.navigationIcon = null
            toolbar.setNavigationOnClickListener(null)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
                override fun onChanged(value: Int) {
                    if (!viewModule.mModeState.initState) {
                        mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                        return
                    }
                    Log.d(TAG, "mListModel=$value")
                    if (value == KtConstants.LIST_SELECTED_MODE) {
                        isShowNav = true
                        mAdapter?.setSelectEnabled(true)
                        previewEditedFiles(fragmentViewModel?.getSelectItems())
                        fragmentRecyclerView?.let {
                            val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                            it.setPadding(
                                it.paddingLeft,
                                it.paddingTop,
                                it.paddingRight,
                                KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                                        + appContext.resources.getDimensionPixelSize(
                                    com.filemanager.common.R.dimen.dimen_22dp)
                            )
                        }
                        mToolbar?.let {
                            changeActionModeAnim(it, {
                                initToolbarWithEditMode(it)
                                refreshSelectToolbar(it)
                            })
                            it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                        }
                        triggerNavigation {
                            (baseVMActivity as? NavigationInterface)?.let {
                                it.showNavigation()
                                fragmentViewModel?.setNavigateItemAble(it)
                            }
                        }
                    } else {
                        previewEditedFiles(null)
                        mAdapter?.setSelectEnabled(false)
                        fragmentRecyclerView?.let {
                            it.setPadding(it.paddingLeft,
                                it.paddingTop,
                                it.paddingRight,
                                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_52dp))
                        }
                        mToolbar?.let {
                            changeActionModeAnim(it, {
                                initToolbarNormalMode(it)
                                refreshScanModeItemIcon(it)
                            }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                            it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                        }
                        isShowNav = false
                        triggerNavigation {
                            (baseVMActivity as? NavigationInterface)?.hideNavigation()
                        }
                    }
                }
            })
            viewModule.uiState.observe(this) { fileUiModel ->
                Log.d(
                    TAG, "UiModel mUiState =" + fileUiModel.fileList.size + ","
                            + fileUiModel.selectedList.size + "," + fileUiModel.keyWord
                )
                sortEntryView?.setFileCount(viewModule.getRealFileSize())
                if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                    mToolbar?.let {
                        refreshSelectToolbar(it)
                    }
                    if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                        mFolderTransformAnimator.mIsFolderInAnimation = true
                        mAdapter?.checkComputingAndExecute {
                            mAdapter?.setData(
                                fileUiModel.fileList as ArrayList<BaseFileBean>,
                                fileUiModel.selectedList
                            )
                            previewEditedFiles(fragmentViewModel?.getSelectItems())
                        }
                    }
                } else {
                    previewClickedFile(fragmentViewModel?.previewClickedFileLiveData?.value, fragmentViewModel?.previewClickedFileLiveData)
                    if (fileUiModel.fileList.isEmpty()) {
                        showEmptyView()
                    } else {
                        mFileEmptyController.hideFileEmptyView()
                        fragmentRecyclerView?.visibility = View.VISIBLE
                    }
                    mToolbar?.let {
                        refreshScanModeItemIcon(it)
                        updateEditAndSortMenu(it)
                        setToolbarEditIcon(it, isChildDisplay)
                    }
                    if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                        mAdapter?.checkComputingAndExecute {
                            mAdapter?.let {
                                it.setKeyWord(fileUiModel.keyWord)
                                mFolderTransformAnimator.mIsFolderInAnimation = true
                                it.setData(
                                    fileUiModel.fileList as ArrayList<BaseFileBean>,
                                    fileUiModel.selectedList
                                )
                            }
                        }
                    }
                }
            }
            startScanModeObserver()
            startObserveLoadState()
            startSideNavigationStatusObserver()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            mLoadingController = LoadingController(it, this).apply {
                //中屏下这里的loading转圈不在最中间，这里传入一个rootView，让loadingView处于fragment中间，而不是屏幕中间
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
                bgColor?.let { color ->
                    setBackgroundColor(color)
                }
                setDeleyShowTime(LOAD_DATA_DELAYED_TIME)
                setShowAinimate(true)
                setDissapearAnimate(true)
                mLoadingController?.setShowLoadingTips(false)
                //这里蒋LoadingController和FolderTransformAnimator通过接口方式关联起来
                mLoadingController?.let { controller ->
                    mFolderTransformAnimator.registerNeedSkipAnimator(controller)
                }
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            if (isChildDisplay) {
                if (fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
                    || (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
                            && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST)) {
                    it.icon = null
                    it.title = desc
                    it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
                } else {
                    it.title = null
                    if (needSkipAnimation) {
                        it.setIcon(resId)
                    } else {
                        KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
                    }
                    it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
                }
            } else {
                it.icon = null
                it.title = desc
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                if (bySideRefreshScanMode) {
                    refreshScanModeItemIcon(it, isChildDisplay)
                }
                bySideRefreshScanMode = true
                setSearchMenuStatus(status, isChildDisplay)
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        var isEnable = fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() ?: false
        isEnable = isEnable && !DragUtils.isDragging
        if (baseVMActivity is NavigationInterface) {
            triggerNavigation {
                (baseVMActivity as NavigationInterface).setNavigateItemAble(
                    isEnable, hasDrmFile(fragmentViewModel?.getSelectItems())
                )
            }
        }
    }

    private fun triggerNavigation(callback: () -> Unit) {
        triggerNavigation = true
        callback.invoke()
        triggerNavigation = false
    }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private fun startScanModeObserver() {
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mAdapter?.updateScanMode()
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it) }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode, ItemDecorationFactory.GRID_ITEM_DECORATION_LIST_LABEL)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            mAdapter?.checkComputingAndExecute {
                notifyDataSetChanged()
            }
        }
        setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
        toolbar?.let { setToolbarEditIcon(it, isChildDisplay) }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getLabelKey())
        if (mAddFileDialogShow) {
            mAddFileDialogShow = false
            rootView?.post {
                showAddFileWindowAndLoadData()
            }
        }
    }
    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        mAddFileController = null
        mRecentFileObserver.destroy()
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            /*标签列表二级页面，无文件时，点击添加文件拉起添加文件面板*/
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
            mFileEmptyController.setEmptySummaryVisibilityAndContent(
                View.VISIBLE,
                appContext.resources.getString(com.filemanager.common.R.string.add_content_tips)
            )
            fragmentRecyclerView?.visibility = View.INVISIBLE
            hasShowEmpty = true
            listEmptyFile()
            Log.d(TAG, "showEmptyView")
        }
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            return
        }
        val bundle = arguments ?: return
        mTitle = bundle.getString(KtConstants.P_TITLE) ?: ""
        mLabelId = bundle.getLong(Constants.LABEL_ID)
        handleSideCategoryType(bundle)
        fragmentViewModel?.mLabelId?.value = mLabelId
        if (fragmentViewModel?.mModeState?.listModel?.value != KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.title = mTitle
        }
        if (PermissionUtils.hasStoragePermission().not()) {
            return
        }
        if (mLabelId == 0L) {
            Log.w(TAG, "onResumeLoadData mLabelId is zero")
        } else {
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this))
        }
    }

    override fun createViewModel(): LabelFileListViewModel {
        val vm = ViewModelProvider(this)[LabelFileListViewModel::class.java]
        vm.mLabelId.value = mLabelId
        vm.mIsFilter.value = mIsFilter
        val sortMode = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getLabelKey())
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_LABEL_FILE, vm, sortMode).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey] ?: return true
            activity?.let {
                if (baseFile.mIsDirectory) {
                    val fileBrowser = Injector.injectFactory<IFileBrowser>()
                    fileBrowser?.startFileLabelBrowserActivity(it, baseFile.mData)
                } else {
                    val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                    if (!previewResult) {
                        var mediaImgIds: ArrayList<String>? = null
                        // 判断当前点击是否是媒体库中的图片
                        if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                            // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                            mediaImgIds = FileMediaHelper.getMediaImgIds(baseFile, fragmentViewModel?.uiState?.value?.fileList)
                        }
                        mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                    }
                }
               fragmentViewModel?.viewModelScope?.launch(Dispatchers.IO) {
                   if (!baseFile.checkExist()) {
                       fragmentViewModel?.cleanAndLoadData()
                   }
               }
            }
        }
        return true
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mTitle = bundle.getString(KtConstants.P_TITLE) ?: ""
            mLabelId = bundle.getLong(Constants.LABEL_ID)
            mIsFilter = bundle.getBoolean(Constants.IS_FILTER_SEARCH_RESULT)
            mIsFromSearch = bundle.getBoolean(Constants.IS_FROM_SEARCH)
            mAddFileDialogShow = bundle.getBoolean(Constants.SHOW_ADD_FILE_DIALOG)
            mAdapter = LabelFileListAdapter(it, <EMAIL>)
            mAdapter!!.setHasStableIds(true)
            needLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            handleSideCategoryType(bundle)
        }
    }

    private fun handleSideCategoryType(bundle: Bundle) {
        val bundleCategoryType = bundle.getInt(Constants.SIDE_CATEGORY_TYPE, -1)
        if (sideCategoryType != -1 && bundleCategoryType != sideCategoryType) {
            rootView?.apply {
                super.setFragmentViewDragTag(this)
            }
        }
        sideCategoryType = bundleCategoryType
    }
    /**弹出添加文件面板，获取数据*/
    private fun showAddFileWindowAndLoadData() {
        loadRecentData()
        if (mTitle == null) return
        activity?.supportFragmentManager?.let { mAddFileController?.showAddFileDialog(mTitle!!, it, lifecycle) }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.label_list_menu)
            updateToolbarHeight(this)
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            if (previewOperate?.isSupportPreview() != true) {
                setOnMenuItemClickListener {
                    return@setOnMenuItemClickListener onMenuItemSelected(it)
                }
            }
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        displayToolbarNavigationIcon()
    }

    private fun displayToolbarNavigationIcon() {
        mToolbar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                Log.d(TAG, "current is in select mode")
            } else {
                if (isChildDisplay) {
                    navigationIcon = null
                    setNavigationOnClickListener(null)
                } else {
                    setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
                    setNavigationOnClickListener {
                        clickNavigationIcon()
                    }
                }
            }
        }
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        var hasPermission = true
        (baseVMActivity as? MainActivity)?.let {
            hasPermission = it.actionCheckPermission() == true
        }
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK) || hasPermission.not()) {
            return false
        }
        return when (item.itemId) {
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(baseVMActivity)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.LABEL_FILE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_LABEL_FILE)
                true
            }
            R.id.actionbar_edit -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_EDIT)
                    OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.LABEL_FILE)
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
                true
            }
            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.LABEL_FILE)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            0,
                            anchorView,
                            SortRecordModeFactory.getLabelKey(), object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            })
                    }
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.LABEL_FILE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_LABEL_FILE)
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                true
            }
            com.filemanager.common.R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            R.id.action_add -> {
                showAddFileWindowAndLoadData()
                true
            }
            else -> {
                false
            }
        }
    }

    private fun clickNavigationIcon() {
        if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        } else {
            baseVMActivity?.onBackPressed()
        }
    }

    override fun pressBack(): Boolean {
        val result = fragmentViewModel?.pressBack() ?: false
        if (result) {
            previewEditedFiles(null)
        }
        return result
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.label_list_menu, menu)
        mToolbar?.apply {
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            refreshScanModeAdapter(scanMode)
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
                if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
                    showEmptyView()
                }
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            updatePermissionView()
            fragmentRecyclerView?.let {
                resetRecyclerViewHoriontalPadding(it)
            }
            updateLeftRightMargin()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.updateScanMode()
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = fragmentViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fragmentViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, paths) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            fragmentViewModel?.loadData()
        }
    }

    override fun setIsHalfScreen(isChild: Boolean) {
        isChildDisplay = isChild
        displayToolbarNavigationIcon()
        mToolbar?.apply {
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
            refreshScanModeItemIcon(this)
        }
    }

    private fun updatePermissionView() {
         if (WindowUtils.isMiddleAndLargeScreen(context).not() && PermissionUtils.hasStoragePermission().not()) {
            setPermissionEmptyVisible(View.VISIBLE)
            return
        }
        setPermissionEmptyVisible(View.GONE)
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    fun getSelectItemCount(): Int {
        return fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
    }

    fun changeListMode() {
        if (KtConstants.LIST_SELECTED_MODE == fragmentViewModel?.mModeState?.listModel?.value) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    /**获取最近文件数据*/
    private fun loadRecentData() {
        Log.d(TAG, "loadRecentData")
        mRecentFileObserver.loadRecentData(RecentDataHelper.TYPE_MEDIA, null, this)
    }

        /**通过callback 返回最近文件的数据*/
        override fun loadSucc(type: Int, data: MutableList<ExpandGroupItemEntity>?) {
            val recentSelectionFiles: MutableList<Int> = mutableListOf()
            val recentFiles: MutableList<RecentFileEntity> = getRecentFilesFromSourceData(data)
            Log.d(TAG, "loadSucc recentFiles = " + recentFiles.size)
                doDuplicateData(recentFiles)
                Handler(Looper.getMainLooper()).postDelayed({
                    mAddFileController?.setData(recentFiles.toMutableList(), recentSelectionFiles)
                    if (recentFiles.size > 0) {
                        mAddFileController?.hideNoFileView()
                    } else {
                        mAddFileController?.showNoFileView()
                    }
                }, 0)
        }

        override fun loadFail(type: Int, msgObj: Any?) {
            Log.d(TAG, "loadFail type = $type" + "msgObj = $msgObj")
        }

        override fun loadInvalid() {
            Log.d(TAG, "loadInvalid")
        }

    /**去重数据*/
    private fun doDuplicateData(mRecentFiles: MutableList<RecentFileEntity>?) {
        Log.d(TAG, "start doDuplicateData")
        /**获取当前标签列表文件和最近文件去重*/
        val labelFileList = FileLabelMappingDBHelper.getFileListByLabelId(mLabelId)
        val iterator = mRecentFiles?.iterator()
        while (iterator?.hasNext() == true) {
            val file = iterator.next()
            labelFileList?.forEach { labelFile ->
                if (file.mAbsolutePath == labelFile.filePath) {
                    Log.d(TAG, "remove file path = " + file.mAbsolutePath + " filePath = " + labelFile.filePath)
                    iterator.remove()
                    return@forEach
                }
            }
        }
    }

    private fun getRecentFilesFromSourceData(sourceList: MutableList<ExpandGroupItemEntity>?): MutableList<RecentFileEntity> {
        val result: MutableList<RecentFileEntity> = mutableListOf()
        if (sourceList.isNullOrEmpty()) {
            return result
        }
        for (groupItemEntity in sourceList) {
            for (fileEntity in groupItemEntity.getChildList()) {
                if (fileEntity == null) {
                    continue
                }
                if (fileEntity.mSize == 0L) {
                    try {
                        //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
                        val file = File(fileEntity.mAbsolutePath)
                        fileEntity.mSize = file.length()
                        fileEntity.mLastModified =
                            file.lastModified() / KtConstants.SECONDS_TO_MILLISECONDS
                    } catch (e: Exception) {
                        Log.e(TAG, e.message)
                    }
                }
                result.add(fileEntity)
            }
        }
        return result
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getInstallPerMission() {
        super.getInstallPerMission()
         Log.d(TAG, "getInstallPerMission")
        (baseVMActivity as BaseVMActivity).checkGetInstalledAppsPermission()
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getFragmentCategoryType(): Int {
        return if (sideCategoryType == -1) {
            CategoryHelper.CATEGORY_LABEL_GROUP
        } else {
            return sideCategoryType
        }
    }

    fun getLabelId(): Long {
        return mLabelId
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (fragmentViewModel?.isInSelectMode() == true) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_LIST_LABEL
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun refreshDataForDir(path: String, category: Int) {
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean): String {
        return ""
    }

    override fun renameToLabel(newName: String, labelId: Long) {
        if (labelId == mLabelId) {
            arguments?.putString(KtConstants.P_TITLE, newName)
            onResumeLoadData()
        }
    }

    override fun onClickDir(path: String) {
        val fileBrowser = Injector.injectFactory<IFileBrowser>()
        activity?.let { fileBrowser?.startFileLabelBrowserActivity(it, path) }
    }
}