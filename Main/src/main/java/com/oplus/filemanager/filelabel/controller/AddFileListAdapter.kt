/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddFileListAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/4/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   luohan                2023/4/7       1
 ***********************************************************************/
package com.oplus.filemanager.filelabel.controller

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.viewholder.BaseFileBrowserVH
import com.oplus.filemanager.main.R
import java.util.Locale

private const val TAG = "AddFileListAdapter"
private const val NUM = 1024
private const val DECIMAL_NUM = 1024f

 class AddFileListAdapter(content: Context, lifecycle: Lifecycle) : BaseFolderAnimAdapter<RecyclerView.ViewHolder, BaseFileBean>(content) {
     private val mSizeCache = HashMap<String, String>()
     private var mThreadManager = ThreadManager(lifecycle)
     var listSelect = mutableListOf<Int>()

     @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
     fun onDestroy() {
         super.onRemoveCallBack()
         mSizeCache.clear()
     }

     override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseSelectionViewHolder {
         val convertView: View = LayoutInflater.from(parent.context).inflate(R.layout.add_file_list_item, parent, false)
         return AddFileViewHolder(convertView)
     }

     override fun getItemKey(item: BaseFileBean, position: Int): Int? {
         val path = item.mData
         if (path.isNullOrEmpty()) {
             return position.hashCode()
         }
         return path.toLowerCase(Locale.getDefault()).hashCode()
     }

     override fun initListChoiceModeAnimFlag(flag: Boolean) {
         Log.i(TAG, "initListChoiceModeAnimFlag")
     }

     override fun onBindViewHolder(holder: RecyclerView.ViewHolder, @SuppressLint("RecyclerView") position: Int) {
         if ((position < 0) || (position >= mFiles.size)) {
             return
         }
         val file = mFiles[position]
         if (holder is AddFileViewHolder) {
             holder.loadData(mContext, getItemKey(file, position),
                     mFiles[position], mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
             holder.mConvertView?.setOnClickListener(object : View.OnClickListener {
                 override fun onClick(view: View?) {
                     holder.mItemcheckbox.let {
                         if (it?.state == COUICheckBox.SELECT_NONE) {
                             it?.state = COUICheckBox.SELECT_ALL
                             listSelect.add(position)
                         } else {
                             it?.state = COUICheckBox.SELECT_NONE
                             listSelect.remove(position)
                         }
                     }
                     mOnItemSelectListener?.onItemSelect(listSelect)
                 }
             })
             if (listSelect.contains(position)) {
                 holder.mItemcheckbox?.state = COUICheckBox.SELECT_ALL
             } else {
                 holder.mItemcheckbox?.state = COUICheckBox.SELECT_NONE
             }
         }
     }

     class AddFileViewHolder(convertView: View) : BaseFileBrowserVH(convertView) {
         var mItemIcon: FileThumbView? = null
         var mItemTitle: TextViewSnippet? = null
         var mItemDetailL: TextView? = null
         var mItemcheckbox: COUICheckBox? = null
         val mImgRadius = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_4dp)
         var mConvertView: View? = null
         var rootView: ConstraintLayout

         init {
             mConvertView = convertView
             mItemIcon = convertView.findViewById(R.id.file_list_item_icon)
             mItemTitle = convertView.findViewById(R.id.file_list_item_title)
             mItemDetailL = convertView.findViewById(R.id.mark_file_list_item_detail)
             mItemcheckbox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
             mItemcheckbox?.visibility = View.VISIBLE
             rootView = convertView.findViewById(R.id.add_file_list_root)
         }

         @Suppress("ParameterStyleBracesRule")
         override fun updateViewHolder(
             context: Context,
             key: Int?,
             file: BaseFileBean,
             choiceMode: Boolean,
             selectionArray: MutableList<Int>,
             sizeCache: HashMap<String, String>,
             threadManager: ThreadManager,
             adapter: BaseSelectionRecycleAdapter<*, *>
         ) {
             val type = file.mLocalType
             mItemIcon?.let {
                 val borderSize = when (type) {
                     MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> KtViewUtils.getBorderSize()
                     else -> 0F
                 }
                 it.setBorderStyle(mImgRadius.toFloat(), borderSize)
                 FileImageLoader.sInstance.clear(context, it)
                 FileImageLoader.sInstance.displayDefault(file, it, 0, mImgRadius)
             }
             mItemTitle?.apply {
                 text = file.mDisplayName
                 setTextViewStyle()
                 this.post {
                     val title = file.mDisplayName ?: return@post
                     val moreOne = this.isMoreThanOneLine(title)
                     setIconConstraintSet(moreOne)
                 }
             }
             val dateAndTime = Utils.getDateFormat(context, file.mDateModified)
             val size = updateSizeFormat(file.mSize)
             mItemDetailL?.text = Utils.formatDetail(context, size, dateAndTime)
         }

         private fun setIconConstraintSet(isMoreThanOneLine: Boolean) {
             val constraintSet = ConstraintSet()
             constraintSet.apply {
                 clone(rootView)
                 clear(R.id.file_list_item_icon, ConstraintSet.TOP)
                 clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
                 if (isMoreThanOneLine) {
                     connect(
                         R.id.file_list_item_icon,
                         ConstraintSet.TOP,
                         R.id.rl_item_title,
                         ConstraintSet.TOP
                     )
                 } else {
                     connect(
                         R.id.file_list_item_icon,
                         ConstraintSet.TOP,
                         ConstraintSet.PARENT_ID,
                         ConstraintSet.TOP
                     )
                     connect(
                         R.id.file_list_item_icon,
                         ConstraintSet.BOTTOM,
                         ConstraintSet.PARENT_ID,
                         ConstraintSet.BOTTOM
                     )
                 }
                 applyTo(rootView)
             }
         }

         @VisibleForTesting
         fun updateSizeFormat(size: Long): String {
             return if (size < NUM) {
                 size.toString() + "B"
             } else if (size < NUM * NUM) {
                 String.format("%.1f", (size / DECIMAL_NUM)) + "KB"
             } else if (size < NUM * NUM * NUM) {
                 String.format("%.1f", (size / NUM / DECIMAL_NUM)) + "MB"
             } else {
                 String.format("%.1f", (size / NUM / NUM / DECIMAL_NUM)) + "GB"
             }
         }

         override fun drawDivider(): Boolean {
             return itemCount - 1 != bindingAdapterPosition
         }

         override fun getDividerStartAlignView(): View {
             return mItemTitle as View
         }

         override fun getDividerEndInset(): Int {
             return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
         }
     }
         var mOnItemSelectListener: OnItemSelectListener? = null
         interface OnItemSelectListener {
             fun onItemSelect(listSelect: List<Int>?)
         }
     }