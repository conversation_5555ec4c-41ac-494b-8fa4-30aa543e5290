/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/09, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.filelabel.di

import com.oplus.filemanager.filelabel.LabelApi
import com.oplus.filemanager.interfaze.filelabel.ILabelApi
import org.koin.dsl.module

object AutoDIForLabel {

    val labelModule = module {
        single<ILabelApi>(createdAtStart = true) {
            LabelApi
        }
    }
}