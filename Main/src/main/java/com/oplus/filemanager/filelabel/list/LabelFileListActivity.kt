/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.ui.LabelListActivity
 * * Description : 文件袋，标签二级文件列表页，与手机存储页面文件操作类似，展示一个标签下的所有文件并提供基本文件操作
 * * Version     : 1.0
 * * Date        : 2022/7/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.list

import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.main.R
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import org.json.JSONException
import org.json.JSONObject

class LabelFileListActivity : EncryptActivity(), NavigationInterface, NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, InstalledPermissionCallback, BaseVMActivity.PermissonCallBack, DragDropInterface,
    IRefreshActivityDataForCreateDir, IDraggingActionOperate, PerformClickDir {

    companion object {
        private const val TAG = "LabelListActivity"
        private const val TAG_LABEL_LIST = "label_list_tag"
    }

    private var mTitle: String? = ""
    private var mLabelId: Long = 0L
    private var mIsFilter: Boolean = false
    private var mIsFromSearch: Boolean = false
    private var mAddFileDialogShow: Boolean = false
    private var mLabelFileListFragment: LabelFileListFragment? = null
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mRootView: ViewGroup? = null
    private lateinit var mLabelFileListActivityViewModel: LabelFileListActivityViewModel

    override fun getLayoutResId(): Int {
        return R.layout.activity_label_list
    }

    override fun initView() {
        if (null == intent) {
            Log.v(TAG, "intent null")
            finish()
            return
        }
        mTitle = IntentUtils.getString(intent, Constants.TITLE)
        mLabelId = IntentUtils.getLong(intent, Constants.LABEL_ID, 0L)
        fromLabelCardData()
        mIsFilter = IntentUtils.getBoolean(intent, Constants.IS_FILTER_SEARCH_RESULT, false)
        mIsFromSearch = IntentUtils.getBoolean(intent, Constants.IS_FROM_SEARCH, false)
        mRootView = findViewById(R.id.coordinator_layout)
        mLabelFileListActivityViewModel = ViewModelProvider(this).get(LabelFileListActivityViewModel::class.java)
        // increase label view count by 1
        mLabelFileListActivityViewModel.increaseFileLabelViewCountByOne(mTitle)
        setFragment()
    }

    /**
     * 从标签卡片传来的打开数据，使用Json解析
     */
    private fun fromLabelCardData() {
        val paramsFromLabelCard = IntentUtils.getString(intent, Constants.TITLE_AND_LABEL_ID)
        if (!paramsFromLabelCard.isNullOrEmpty()) {
            try {
                val json = JSONObject(paramsFromLabelCard)
                mTitle = json.getString(Constants.TITLE)
                mLabelId = json.getLong(Constants.LABEL_ID)
                mAddFileDialogShow = json.getBoolean(Constants.SHOW_ADD_FILE_DIALOG)
            } catch (e: JSONException) {
                Log.d(TAG, "fromLabelCardData json $e")
            }
        }
        Log.d(TAG, "fromLabelCardData title:$mTitle  labelId:$mLabelId  paramsFromLabelCard:$paramsFromLabelCard")
    }

    override fun onResume() {
        super.onResume()
        PreferencesUtils.put(key = Constants.LABEL_FILE_LIST_NAME, value = mTitle)
    }

    @Suppress("UnsafeCallOnNullableType")
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        return if (mLabelFileListFragment != null) {
            mLabelFileListFragment!!.onCreateOptionsMenu(menu, menuInflater)
            true
        } else {
            super.onCreateOptionsMenu(menu)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return mLabelFileListFragment?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
    }

    override fun initData() {
        Log.d(TAG, "initData")
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mLabelFileListFragment?.onResumeLoadData()
    }

    private fun setFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_LABEL_LIST)
        if ((fragment == null) || (fragment !is LabelFileListFragment)) {
            fragment = LabelFileListFragment()
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.P_TITLE, mTitle)
        bundle.putLong(Constants.LABEL_ID, mLabelId)
        bundle.putBoolean(Constants.IS_FILTER_SEARCH_RESULT, mIsFilter)
        bundle.putBoolean(Constants.IS_FROM_SEARCH, mIsFromSearch)
        bundle.putBoolean(Constants.SHOW_ADD_FILE_DIALOG, mAddFileDialogShow)
        bundle.putInt(Constants.SIDE_CATEGORY_TYPE, IntentUtils.getInt(intent, Constants.SIDE_CATEGORY_TYPE, -1))
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.content, fragment, TAG_LABEL_LIST)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mLabelFileListFragment = fragment
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun getActivityType(): Int {
        return InstalledPermissionCallback.FILE_BROWSER_ACTIVITY
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        mRootView?.post {
            mLabelFileListFragment?.onResumeLoadData()
        }
    }

    override fun onBackPressed() {
        if ((mLabelFileListFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mLabelFileListFragment?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mLabelFileListFragment?.onNavigationItemSelected(menuItem) ?: false
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mLabelFileListFragment?.fromSelectPathResult(code, paths)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mLabelFileListFragment?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun onRefreshData() {
        mLabelFileListFragment?.onResumeLoadData()
    }

    override fun handleNoStoragePermission() {
        mLabelFileListFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    fun updateLabels() {
        mLabelFileListFragment?.onResumeLoadData()
    }

    fun getLabelId(): Long? {
        return mLabelFileListFragment?.getLabelId()
    }

    override fun onRefreshDataForDir(path: String) {
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean) {}

    override fun renameToLabel(newName: String, labelId: Long) {
        mLabelFileListFragment?.renameToLabel(newName, labelId)
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mLabelFileListFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mLabelFileListFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mLabelFileListFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }

    override fun onClickDir(path: String) {
        mLabelFileListFragment?.onClickDir(path)
    }
}