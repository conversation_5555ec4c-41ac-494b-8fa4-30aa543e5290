/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.list.LabelListActivityViewModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.list

import android.database.sqlite.SQLiteException
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.utils.Log
import com.oplus.filemanager.provider.FileLabelDBHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class LabelFileListActivityViewModel : BaseViewModel() {

    fun increaseFileLabelViewCountByOne(labelName: String?) {
        launch {
            withContext(Dispatchers.IO) {
                labelName?.let { name ->
                    val labelEntity = FileLabelDBHelper.getFileLabelByName(name)
                    labelEntity?.let {
                        it.viewCount = it.viewCount + 1
                        try {
                            FileLabelDBHelper.updateFileLabel(it)
                        } catch (e: SQLiteException) {
                            Log.e("LabelFileListActivityViewModel", "increaseFileLabelViewCountByOne:$e")
                        }
                    }
                }
            }
        }
    }
}