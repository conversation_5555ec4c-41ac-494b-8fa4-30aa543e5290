/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SuperAppBean
 * * Description : supper app sorting and switching state persistent entity classes
 * * Version     : 1.0
 * * Date        : 2024/12/11
 * * Author      : W9085798
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.bean

import androidx.annotation.Keep

@Keep
data class SuperAppBean(val packageName: String, val order: Int, val switchStatus: Boolean)
