/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileManagerOpenProvider
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/5/5 20:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/5/5       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.provider

import android.content.ContentProvider
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.router.RouterUtil
import java.lang.reflect.Method

class FileManagerOpenProvider : ContentProvider() {

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        Log.d(TAG, "query uri = $uri")
        if (uri.authority?.equals(AUTHORITIES) == true) {
            val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
            return fileOpenTimeAction?.query(uri, projection, selection, selectionArgs, sortOrder)
        }
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        val currentContext = context
        if (currentContext == null) {
            Log.e(TAG, "call -> invalid context.")
            return null
        }
        Log.d(TAG, "call -> callingPackage = $callingPackage")
        return internalCall(method, arg, extras)
    }

    private fun internalCall(method: String, arg: String?, extras: Bundle?): Bundle? {
        Log.d(TAG, "internalCall -> method = $method , currentThread = ${Thread.currentThread().name}")
        var result: Bundle? = null
        val moduleAndMethod = getModuleAndMethod(method)
        val moduleName = moduleAndMethod.first
        val methodName = moduleAndMethod.second
        Log.i(TAG, "internalCall moduleName = $moduleName, methodName = $methodName")
        runCatching {
            val instance = RouterUtil.getInterfaceByName(moduleName)
            if (instance != null) {
                val methodCall: Method? = instance.javaClass.getMethod(
                    moduleName,
                    Context::class.java,
                    Bundle::class.java
                )
                result = methodCall?.invoke(instance, arg, extras) as? Bundle
            } else {
                Log.i(TAG, "instance is null, method is $method")
            }
        }.onFailure {
            Log.e(TAG, "internalCall ${it.message}")
        }
        return result
    }

    private fun getModuleAndMethod(method: String): Pair<String, String> {
        return if (method.contains(":")) {
            val moduleAndMethod = method.split(Regex(":"))
            if (moduleAndMethod.size == 2) {
                val moduleName = moduleAndMethod[0]
                val methodName = moduleAndMethod[1]
                Pair(moduleName, methodName)
            } else {
                Pair(method, method)
            }
        } else {
            Pair(method, method)
        }
    }

    companion object {
        private const val TAG = "FileManagerOpenProvider"
        private const val AUTHORITIES = "com.oplus.filemanager.FileManagerOpenProvider"
    }
}