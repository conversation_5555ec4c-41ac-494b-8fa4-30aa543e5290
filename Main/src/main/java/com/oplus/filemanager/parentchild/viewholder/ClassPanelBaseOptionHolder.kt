/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ClassPanelBaseOptionHolder.kt
 ** Description: Build the holder of the classification
 ** Version: 1.0
 ** Date: 2025/1/23
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.ViewCompat
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.categoryappmarketamanager.ICategoryAppMarketManagerApi
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.category.MainCategoryHelper
import com.oplus.filemanager.main.view.CropImageView
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

open class ClassPanelBaseOptionHolder(context: Context, itemView: View) : BaseSelectableHolder(context, itemView) {
    companion object {
        const val EDIT_VIEW_ALPHA = 0.26F
        const val TAG = "BaseOptionHolder"
    }

    var isDragging: Boolean = false
    var isEdit = false

    val widgetFrame: LinearLayout
    val dragView: ImageView
    val optionTitle: TextView
    val optionSubtitle: TextView
    val optionIcon: ImageView
    private val categoryAppMarketManagerApi: ICategoryAppMarketManagerApi?
    private var mMainCategoryHelper: MainCategoryHelper? = null
    private val mUiHandler: Handler?
    private val widgetStartMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_widget_start_margin_end)
    private val widgetEndMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_widget_end_margin_end)
    private val iconStartMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_start_margin_start)
    private val iconEndMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_end_margin_start)
    private val isRtl = ViewCompat.getLayoutDirection(itemView) == ViewCompat.LAYOUT_DIRECTION_RTL
    private val mark = if (isRtl) 1 else -1
    private var itemViewAlpha = 1.0f

    init {
        optionTitle = itemView.findViewById(R.id.option_title)
        optionSubtitle = itemView.findViewById(R.id.option_subtitle)
        optionIcon = itemView.findViewById(R.id.option_icon)
        widgetFrame = itemView.findViewById(R.id.option_widget)
        dragView = itemView.findViewById(R.id.drag_view)
        wrapper = itemView.findViewById(R.id.option_wrapper)
        categoryAppMarketManagerApi = Injector.injectFactory<ICategoryAppMarketManagerApi>()
        mMainCategoryHelper = MainCategoryHelper()
        mUiHandler = Handler(Looper.getMainLooper())
    }

    fun bindData(
        bean: CategoryListBean,
        isEdit: Boolean,
        isDragging: Boolean,
        onClassPanelCountCallback: (type: Int, count: Long) -> Unit,
        onItemViewCallback: (itemView: View, categoryListBean: CategoryListBean) -> Unit
    ) {
        this.isEdit = isEdit
        this.isDragging = isDragging
        onItemViewCallback(itemView, bean)
        if (bean.subTitle.toLong() >= 0) {
            optionSubtitle.text = bean.subTitle
        }
        asyncItemsCount(bean, onClassPanelCountCallback)
        categoryType = bean.categoryType
        optionTitle.text = bean.name
        wrapper?.tag = DropTag(bean.categoryType, DropTag.Type.ITEM_VIEW_PROHIBIT)
        if (bean.groupCategoryType == CategoryHelper.CATEGORY_CLASSIFICATION_GROUP) {
            optionSubtitle.setTag(R.id.option_subtitle, bean.groupCategoryType)
        }
        if (bean.iconRes != 0) {
            (optionIcon as? CropImageView)?.enableCropping = false
            optionIcon.setImageDrawable(AppCompatResources.getDrawable(context, bean.iconRes))
        } else if (bean.drawable != null) {
            (optionIcon as? CropImageView)?.enableCropping = true
            optionIcon.setImageDrawable(bean.drawable)
        } else {
            (optionIcon as? CropImageView)?.enableCropping = false
            val bitmap = AppCompatResources.getDrawable(context, R.drawable.ic_download)
            optionIcon.setImageDrawable(bitmap)
        }
        optionIcon.contentDescription = bean.name
        optionTitle.setTextColor(normalTitleColor)
        widgetFrame.visibility = View.GONE
        dragView.visibility = View.GONE
        dragView.contentDescription = bean.name
        setItemAlpha()
        val fraction = if (this.isEdit) 0f else 1f
        onAnimationUpdate(fraction, this.isEdit)
    }

    private fun setItemAlpha() {
        itemView.isEnabled = !isDragging
        if (this.isDragging) {
            itemView.alpha = EDIT_VIEW_ALPHA
        } else {
            itemView.alpha = 1f
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun asyncItemsCount(data: CategoryListBean, onClassPanelCountCallback: (type: Int, count: Long) -> Unit) {
        optionTitle.tag = data.name
        if (data.subTitle.toLong() >= 0) {
            optionSubtitle.text = data.subTitle
            optionSubtitle.setTextColor(subTitleColor)
        }
        Log.d(BaseOptionHolder.TAG, "asyncItemsCount type=${data.categoryType}")
        (context as? BaseVMActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
            var mCount: Long = 0
            if (data.order >= 0) {
                try {
                    mMainCategoryHelper?.let {
                        mCount = it.getCountByCategory(data.categoryType)
                        CategoryHelper.saveCategoryCountData(data.categoryType, mCount, 0)
                    }
                } catch (e: Exception) {
                    Log.e(BaseOptionHolder.TAG, "asyncItemsCount err : " + e.message)
                    return@launch
                }
            }
            CollectPrivacyUtils.collectMedia(data.categoryType, mCount)
            mUiHandler?.post {
                if (data.categoryType == optionTitle.getTag(R.id.option_subtitle)) {
                    data.subTitle = mCount.toString()
                    optionSubtitle.text = data.subTitle
                    optionSubtitle.setTextColor(subTitleColor)
                    onClassPanelCountCallback(data.categoryType, mCount)
                    setItemAlpha()
                }
            }
        }
    }

    override fun onEditStateChange(isEdit: Boolean) {
        this.isEdit = isEdit
    }

    /**
     * 进入编辑，enterEdit 为true，fraction 0 -> 1变化
     * 退出编辑，enterEdit 为false，fraction 1 -> 0变化
     */
    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        val disableAlpha = BaseOptionHolder.EDIT_VIEW_ALPHA
        itemViewAlpha = if (!isDragging) {
            (1 - disableAlpha) * (1 - fraction) + disableAlpha
        } else {
            disableAlpha
        }
        itemView.alpha = itemViewAlpha
        if (isEdit) {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark
            val iconEndValue = 0
            dragView.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
                alpha = fraction
            }
            optionIcon.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
            optionTitle.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
            val tag = optionSubtitle.getTag(R.id.option_subtitle) ?: 0
            if (tag == 0 || tag != CategoryHelper.CATEGORY_CLASSIFICATION_GROUP) {
                optionSubtitle.alpha = 1f - fraction
            }
            val widgetStartValue = (widgetEndMargin - widgetStartMargin) * mark * -1
            val widgetEndValue = 0
            widgetFrame.apply {
                translationX = widgetEndValue + (widgetStartValue - widgetEndValue) * (1 - fraction)
                alpha = fraction
            }
        } else {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark * -1
            val iconEndValue = 0
            val widgetStartValue = widgetStartMargin
            val widgetEndValue = widgetEndMargin
            dragView.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            dragView.alpha = fraction
            optionIcon.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            optionTitle.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            widgetFrame.translationX = (widgetEndValue + (widgetStartValue - widgetEndValue) * fraction) * mark * -1
            widgetFrame.alpha = fraction
        }
        dragView.isEnabled = isEdit
        widgetFrame.isEnabled = isEdit
    }
}