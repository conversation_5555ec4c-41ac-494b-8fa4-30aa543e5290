/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/09/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper

open class BaseSelectableHolder(val context: Context, itemView: View) : RecyclerView.ViewHolder(itemView), ItemAnimationHelper.AnimationListener {

    var categoryType: Int = -1
    var wrapper: View? = null

    protected val normalTitleColor = ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_primary_neutral)
    protected val subTitleColor = ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_label_tertiary)

    open fun setItemSelected(selected: Boolean, animated: Boolean) {}

    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {}

    override fun onEditStateChange(isEdit: Boolean) {}

    override fun itemEnableStateChange(isEdit: Boolean) {}
}