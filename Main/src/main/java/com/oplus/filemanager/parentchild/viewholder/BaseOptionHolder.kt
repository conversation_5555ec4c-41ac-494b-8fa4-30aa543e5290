/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/09/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.view.isVisible
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.CategoryHelper.CATEGORY_ADD_FOLDER
import com.filemanager.common.helper.CategoryHelper.CATEGORY_ADD_LABEL
import com.filemanager.common.helper.CategoryHelper.isRemoteMacDeviceType
import com.filemanager.common.talkback.TalkbackUtils
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.view.CropImageView
import com.oplus.filemanager.main.view.SideEditText
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import java.io.File

open class BaseOptionHolder(context: Context, val activity: ComponentActivity, itemView: View) : BaseSelectableHolder(context, itemView) {

    companion object {
        const val EDIT_VIEW_ALPHA = 0.26F
        const val TAG = "BaseOptionHolder"
    }

    private var bean: CategoryListBean? = null
    var isEdit = false

    val widgetFrame: LinearLayout
    val dragView: ImageView
    val optionTitle: TextView
    val optionSubtitle: TextView
    val optionIcon: ImageView
    init {
        optionTitle = itemView.findViewById(R.id.option_title)
        optionSubtitle = itemView.findViewById(R.id.option_subtitle)
        optionIcon = itemView.findViewById(R.id.option_icon)
        widgetFrame = itemView.findViewById(R.id.option_widget)
        dragView = itemView.findViewById(R.id.drag_view)
        TalkbackUtils.setAccessibilityDelegate(dragView, Button::class.java.name)
        wrapper = itemView.findViewById(R.id.option_wrapper)
    }

    override fun setItemSelected(selected: Boolean, animated: Boolean) {
        (wrapper as SideNavigationItemContainer).setIsSelected(selected, animated)
    }

    fun bindData(bean: CategoryListBean, isEdit: Boolean, onItemViewCallback: (itemView: View, categoryListBean: CategoryListBean) -> Unit) {
        this.isEdit = isEdit
        this.bean = bean
        onItemViewCallback(itemView, bean)
        categoryType = bean.categoryType
        if (bean.categoryType == CATEGORY_ADD_FOLDER || bean.categoryType == CATEGORY_ADD_LABEL || isRemoteMacDeviceType(categoryType)) {
            itemView.tag = null
        } else {
            itemView.tag = DropTag(categoryType, DropTag.Type.ITEM_VIEW)
        }

        itemView.isEnabled = !isEdit
        optionTitle.text = bean.name
        if (bean.iconRes != 0) {
            (optionIcon as? CropImageView)?.enableCropping = false
            optionIcon.setImageDrawable(AppCompatResources.getDrawable(context, bean.iconRes))
        } else if (bean.drawable != null) {
            (optionIcon as? CropImageView)?.enableCropping = true
            optionIcon.setImageDrawable(bean.drawable)
        } else {
            (optionIcon as? CropImageView)?.enableCropping = false
            val bitmap = AppCompatResources.getDrawable(context, R.drawable.ic_download)
            optionIcon.setImageDrawable(bitmap)
        }
        setEditTextData(bean)
        changeTitleMode(isEdit)
        optionTitle.setTextColor(normalTitleColor)
        optionTitle.setHintTextColor(normalTitleColor)
        optionSubtitle.text = bean.subTitle
        optionSubtitle.setTextColor(subTitleColor)
        widgetFrame.visibility = View.GONE
        dragView.visibility = View.GONE
        dragView.contentDescription = bean.name
        wrapper?.contentDescription = bean.name + "\u3000" + if (optionSubtitle.isVisible) {
            bean.subTitle
        } else {
            ""
        }
        setItemEditState()
    }

    private fun setEditTextData(data: CategoryListBean) {
        val editText = (optionTitle as? SideEditText)
        editText?.activity = context as? ComponentActivity
        editText?.apply {
            if (CategoryHelper.isShortcutFolderType(data.categoryType)) {
                name = data.name.ifEmpty {
                    File(data.filePath).name.toString()
                }
                fileType = data.categoryType
                filePath = data.filePath
            } else if (CategoryHelper.isLabelType(data.categoryType)) {
                name = data.name
                fileType = data.categoryType
            }
        }
    }

    open fun setItemEditState() {
        val alpha = if (isEdit) EDIT_VIEW_ALPHA else 1f
        wrapper?.alpha = alpha
    }

    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        //先判断如果是正在从mac拖出文件，需要根据tag执行不同的itemView的alpha处理，处理完return
        if (MainApi.isDragging(activity)) {
            val tag: DropTag? = itemView.tag as? DropTag
            if (tag == null) {
                wrapper?.alpha = EDIT_VIEW_ALPHA
            } else {
                wrapper?.alpha = 1f
            }
            return
        }
        val disableAlpha = EDIT_VIEW_ALPHA
        val alpha = (1 - disableAlpha) * (1 - fraction) + disableAlpha
        wrapper?.alpha = alpha
    }

    override fun onEditStateChange(isEdit: Boolean) {
        this.isEdit = isEdit
        changeTitleMode(this.isEdit)
    }

    override fun itemEnableStateChange(isEdit: Boolean) {
        itemView.isEnabled = !isEdit
    }

    fun changeTitleMode(isEdit: Boolean) {
        this.isEdit = isEdit
        (optionTitle as? SideEditText)?.apply {
            changeEditTextMode(isEdit)
        }
    }

    override fun onMacDragStateChange(isMacDragging: Boolean, fraction: Float) {
        if (categoryType == CATEGORY_ADD_FOLDER || categoryType == CATEGORY_ADD_LABEL || isRemoteMacDeviceType(
                categoryType
            )
        ) {
            itemView.alpha = if (isMacDragging) {
                1f - (1f - EDIT_VIEW_ALPHA) * fraction
            } else {
                EDIT_VIEW_ALPHA + (1f - EDIT_VIEW_ALPHA) * fraction
            }
        }
        super.onMacDragStateChange(isMacDragging, fraction)
    }
}