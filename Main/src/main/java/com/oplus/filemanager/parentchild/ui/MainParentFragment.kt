/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MainParentFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou      2022/10/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.parentchild.ui

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.view.DragEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.RelativeLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.ViewCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIActionMenuView
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.compat.PrivacyPasswordSettingCompat
import com.filemanager.common.compat.PrivacyPasswordSettingCompat.isRecentlyDeletedSwitchOpen
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.CategoryHelper.isCloudDiskCategoryType
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ZoomWindowConfig
import com.filemanager.common.managers.SPManagerUtil.getValue
import com.filemanager.common.managers.SPManagerUtil.putValue
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.ArrayUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.DialogUtil
import com.oplus.encrypt.FileEncryptFactor
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.HomePageAdMgr
import com.oplus.filemanager.filelabel.ui.MainLabelViewModel
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.interfaze.questionnaire.IQuestionnaire
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.adapter.ExpandItemEvent
import com.oplus.filemanager.main.behavior.PrimaryTitleBehavior
import com.oplus.filemanager.main.ui.BaseMainFragment
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.category.MainCategoryHelper
import com.oplus.filemanager.main.ui.category.MainCategoryViewModel
import com.oplus.filemanager.main.ui.category.launchAndRepeatWithViewLifecycle
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.utils.ClassBeanUtil
import com.oplus.filemanager.main.utils.StorageInfoUtils
import com.oplus.filemanager.parentchild.adapter.SideExpandableAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.bean.CategoryListBean.Companion.TYPE_OPTIONS
import com.oplus.filemanager.parentchild.bean.CategoryListBean.Companion.TYPE_SOURCE_PANEL
import com.oplus.filemanager.parentchild.bean.CategoryListBeanFactory
import com.oplus.filemanager.parentchild.drag.ItemDisableDragAnimator
import com.oplus.filemanager.parentchild.util.GroupCollapseUtil
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewmodel.MainParentViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap

class MainParentFragment : BaseMainFragment(), SideExpandableAdapter.ItemClickListener {

    companion object {
        private const val TAG = "MainParentFragment"
        private const val TOOLBAR_ANIM_TIME = 150L

        fun newInstance(categoryType: Int): MainParentFragment {
            val fragment = MainParentFragment()
            fragment.arguments = Bundle().apply {
                putInt(KtConstants.P_SELECT_CATEGORY_TYPE, categoryType)
                putBoolean(KtConstants.P_NEED_LOAD_DATA, true)
            }
            return fragment
        }

        private const val DEFAULT_ANIMATION_DURATION = 350L
        private const val DEFAULT_ANIMATION_DELAY = 100L
        private const val STEP_SIZE_Y: Int = 5
        private const val TOUCH_SCROLL_THRESHOLD = 200
        private val DEFAULT_ANIMATION_INTERPOLATOR = COUIEaseInterpolator()
    }

    private var parentViewModel: MainParentViewModel? = null
    private var labelViewModel: MainLabelViewModel? = null
    private var mAdManager: HomePageAdMgr? = null

    private var scrollAppBarLayout: RelativeLayout? = null
    private var behavior: PrimaryTitleBehavior? = null

    private var mSavedInstanceState: Bundle? = null
    private var hasInjectQuestionnaire = false
    private var questionnaireView: View? = null
    private var cdpView: View? = null

    private var mOtgState = KtConstants.STATE_UNMOUNTED
    private var mSdCardState = KtConstants.STATE_UNMOUNTED
    private var dfmStorage: Storage.DFMStorage? = null
    private var threadManager: ThreadManager = ThreadManager(lifecycle)
    private val uiHandler: Handler = Handler(Looper.getMainLooper())
    private val categoryHelper by lazy { MainCategoryHelper() }
    private var selectedCategoryParams = CategoryHelper.CATEGORY_RECENT

    private var mNeedLoadData = false
    private var isIconAnimatorStarted = false
    private var categoryRecyclerView: COUIRecyclerView? = null
    var categoryAdapter: SideExpandableAdapter? = null
    private var menuView: View? = null
    private var lastRecyclerViewScrollTime = 0L
    private var expandableRecyclerViewScrollState: Int = 0
    private val itemDisableDragAnimator = ItemDisableDragAnimator()
    private val remotePcControlPath = arrayOf("Download/Remote PC Control/")
    private var isFirstOnResumeLoadDataFromParent = true
    private var scrollHelper: DragScrollHelper? = null

    private val onLabelItemDeleteCallback: (bean: CategoryListBean) -> Unit = {
        val labelId = it.labelEntry?.id
        if (labelId != null) {
            getMainCombineFragment()?.setShowDeleteDialogState(labelId)
        }
    }

    /**用于新建标签，当新建的标签后侧导加载出新标签，需要选中新建的标签页*/
    private var newLabelName: String? = null
    private var newShortcutFolderID: Long = -1L
    private val isAsyncCountMap by lazy {
        val map = ConcurrentHashMap<Int, Boolean>()
        SideExpandableAdapter.CLASS_ITEM_TYPE.forEach {
            map[it] = false
        }
        map
    }
    private val questionnaire: IQuestionnaire? by lazy {
        Injector.injectFactory<IQuestionnaire>()
    }

    private val onDeleteLabelCallback: (labelId: Long) -> Unit = { labelId ->
        categoryAdapter?.deleteLabelData(labelId)
        val labelEntry = labelViewModel?.uiState?.value?.mAllLabelsWithFilesList?.find { it.label.id == labelId }?.label
        labelViewModel?.deleteLabel(labelEntry)
        getMainCombineFragment()?.setShowDeleteDialogState(null)
    }
    private val onCreateLabelCallback: (newLabelName: String) -> Unit = {
        Log.d(TAG, "onCreateLabelCallback newLabelName $it")
        newLabelName = it
        labelViewModel?.addLabel(it)
        getMainCombineFragment()?.setShowAddLabelDialogState(false)
    }
    private val updateCategoryTypeCallback: (categoryType: Int) -> Unit = {
        getMainCombineFragment()?.categoryType = it
    }

    private val superApp: ISuperApp? by lazy {
        Injector.injectFactory<ISuperApp>()
    }

    private var launcher: ActivityResultLauncher<Intent> = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        Log.d(TAG, "launcher privacy password result ${it.resultCode}")
        if (it.resultCode == Activity.RESULT_OK) {
            changeSelectedCategoryType(CategoryHelper.CATEGORY_RECYCLE_BIN)
        }
    }

    fun refreshDataForShortcutFolders(path: String) {
        parentViewModel?.shortcutFolderItemList?.value?.withIndex()?.let {
            for ((index, categoryBean) in it) {
                if (categoryBean.name.contains(path)) {
                    parentViewModel?.loadShortcutFolders()
                    break
                }
            }
        }
    }

    fun onResumeLoadDataFromParent() {
        Log.d(TAG, "onResumeLoadDataFromParent")
        if (isFirstOnResumeLoadDataFromParent) {
            isFirstOnResumeLoadDataFromParent = false
            return
        }
        onResumeLoadData()
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData ")
        if (parentViewModel != null) {
            parentViewModel?.loadMainCategoryItem(PermissionUtils.hasStoragePermission())
            parentViewModel?.fetchStorageInfo(true)
            parentViewModel?.loadRecycleBinSize()
            parentViewModel?.loadSuperAppData()
            parentViewModel?.loadShortcutFolders()
            labelViewModel?.loadLabels()
            baseVMActivity?.let { activity ->
                mAdManager?.requestMainAd(activity as Activity)
                initQuestionnaire()
            }
        }
        loadClassPanelSortData()
        categoryAdapter?.updateClassPanel(parentViewModel?.mMainClassPanelItemList?.value)
        refreshCategoryCount()
        getMainCombineFragment()?.onCreateLabelCallback = onCreateLabelCallback
    }

    private fun loadClassPanelSortData() {
        val isNeedLoadClassPanel = appContext.getValue(KtConstants.CLASS_ITEM_TYPE_NEED_LOAD, false)
        if (isNeedLoadClassPanel) {
            appContext.putValue(KtConstants.CLASS_ITEM_TYPE_NEED_LOAD, false)
            parentViewModel?.mMainClassPanelItemList?.value = ClassBeanUtil.mainParentClassPanelInitView()
        }
    }

    override fun onTabSelected() {
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.ACTIVITY_QUICK_CLICK) || baseVMActivity == null) {
            return false
        }
        when (item.itemId) {

            R.id.action_search -> {
                baseVMActivity?.let {
                    StatisticsUtils.onCommon(it, StatisticsUtils.ACTION_SEARCH)
                    StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_MAIN)
                    val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                    categoryGlobalSearchApi?.startGlobalSearch(it)
                }
            }

            R.id.action_setting -> {
                StatisticsUtils.onCommon(appContext, StatisticsUtils.ACTION_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.HOME_PAGE_FILE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_MAIN)
                Injector.injectFactory<ISetting>()?.startSettingActivity(baseVMActivity)
                return true
            }

            R.id.actionbar_owork -> {
                (activity as? MainActivity)?.clickOworkMenu()
                return true
            }

            R.id.action_edit -> {
                //进入编辑模式埋点
                StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPER_APP_ENTER_EDIT_MODE)
                changeEditState()
                StatisticsUtils.onCommon(appContext, StatisticsUtils.PAD_SLIDE_EDIT_CLICK_EVENT)
                return true
            }

            R.id.finish_edit -> {
                completeEditState()
                return true
            }
        }
        return false
    }

    override fun fromActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return false
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val bundle = arguments ?: return
        selectedCategoryParams = bundle.getInt(KtConstants.P_SELECT_CATEGORY_TYPE, CategoryHelper.CATEGORY_RECENT)
        mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA)
        Log.d(TAG, "onAttach mSelectedCategory:$selectedCategoryParams")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        savedInstanceState?.let {
            selectedCategoryParams = it.getInt(MainCombineFragment.KEY_CATEGORY_TYPE, CategoryHelper.CATEGORY_RECENT)
        }
        super.onCreate(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val categoryType = categoryAdapter?.getCurrentCategoryType()
        categoryType?.let {
            outState.putInt(MainCombineFragment.KEY_CATEGORY_TYPE, categoryType)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        activity?.let { activity ->
            parentViewModel = ViewModelProvider(activity)[MainParentViewModel::class.java]
            labelViewModel = ViewModelProvider(activity)[MainLabelViewModel::class.java]
        }
        return view
    }

    override fun getLayoutResId(): Int {
        return R.layout.main_parent_fragment
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView(view: View) {
        rootView = view as? ViewGroup
        categoryRecyclerView = view.findViewById(R.id.main_recycle_view)
        scrollHelper = DragScrollHelper(categoryRecyclerView)
        toolbar = rootView?.findViewById(R.id.toolbar)
        initToolbar()
        initCategoryRecyclerview()
        initSuperApp()
        initShortcutFolders()
        initLabelList()

        /** to do 广告**/
        if (AdvertManager.isAdEnabled() && HomePageAdMgr.isSupportHomeAd()) {
            categoryRecyclerView?.let {
                mAdManager = HomePageAdMgr(<EMAIL>).apply {
                    setContainer(it, R.id.main_ad_vsub)
                }
            }
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
    }

    /**
     * 初始化侧导列表recyclerView
     */
    private fun initCategoryRecyclerview() {
        val context = baseVMActivity ?: return
        categoryAdapter = SideExpandableAdapter(context, baseVMActivity!!)
        categoryAdapter?.onLabelItemDropCallback = {
            labelViewModel?.saveSideEditLabelData(it)
        }
        categoryAdapter?.onLabelItemDeleteCallback = onLabelItemDeleteCallback
        categoryAdapter?.onSourceItemDropCallback = {
            parentViewModel?.saveParentSuperAppData(it)
        }
        categoryAdapter?.onSourceItemSwitchCallback = {
            parentViewModel?.saveParentSuperAppData(it)
        }
        categoryAdapter?.onClassPanelDataList = {
            parentViewModel?.mMainClassPanelItemList?.postValue(it)
            categoryAdapter?.updateClassPanel(it, false)
        }
        categoryAdapter?.onClassPanelCountCallback = { type, count ->
            parentViewModel?.mMainClassPanelItemList?.value?.forEach {
                if (it.categoryType == type) {
                    it.subTitle = count.toString()
                }
            }
        }
        categoryAdapter?.updateCategoryTypeCallback = updateCategoryTypeCallback
        categoryAdapter?.setOnItemClickListener(this@MainParentFragment)
        categoryAdapter?.classPanelEvent = object : ExpandItemEvent<CategoryListBean>() {
            override fun onItemClick(position: Int, bean: CategoryListBean) {}

            override fun onDeleteItem(id: Long) {}

            override fun onItemDragged(list: MutableList<CategoryListBean>) {
                parentViewModel?.launch {
                    withContext(Dispatchers.IO) {
                        val orderList = ArrayList<Int>()
                        list.forEach { orderList.add(it.order) }
                        appContext.putValue(KtConstants.CLASS_ITEM_TYPE, GsonUtil.toJson(orderList))
                    }
                }
            }
        }

        categoryAdapter?.onItemMoveCallback = { viewHolder, target ->
            parentViewModel?.mMainClassPanelItemList?.value?.let {
                categoryAdapter?.classPanelEvent?.moveItem(viewHolder, target, it)
            }
        }

        categoryAdapter?.onItemDropCallback = {
            parentViewModel?.mMainClassPanelItemList?.value?.let {
                categoryAdapter?.classPanelEvent?.onItemDragged(it)
                categoryAdapter?.updateClassPanel(it, false)
            }
        }

        categoryAdapter?.shortcutFolderEvent = object : ExpandItemEvent<CategoryListBean>() {

            override fun onItemClick(position: Int, bean: CategoryListBean) {
            }

            override fun onDeleteItem(id: Long) {
                Log.d(TAG, "onDeleteItem $id")
                getMainCombineFragment()?.showDeleteShortcutFolderDialog(id) {
                    if (it) {
                        newShortcutFolderID = categoryAdapter?.getClosestShortcutFolderId(id) ?: -1L
                        parentViewModel?.loadShortcutFolders()
                    }
                }
            }

            override fun onItemDragged(list: MutableList<CategoryListBean>) {
                parentViewModel?.launch {
                    withContext(Dispatchers.IO) {
                        val result = list.mapNotNull { it.dbID }
                        val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
                        shortcutFolderApi?.updateShortcutFolderUseTime(result)
                    }
                }
            }
        }
        categoryAdapter?.onQuestionnaireInitViewCallback = {
            questionnaireView = it
            if (!hasInjectQuestionnaire) {
                initQuestionnaire()
            }
        }
        categoryAdapter?.setSelectCategoryType(selectedCategoryParams)
        categoryRecyclerView?.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = categoryAdapter
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    if (SystemClock.elapsedRealtime() - lastRecyclerViewScrollTime > KtConstants.TIME_GAP_ONE_SECOND
                        && expandableRecyclerViewScrollState == RecyclerView.SCROLL_STATE_DRAGGING
                    ) {
                        val imm =
                            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        imm.hideSoftInputFromWindow(<EMAIL>, 0)
                    }
                    lastRecyclerViewScrollTime = SystemClock.elapsedRealtime()
                }
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    expandableRecyclerViewScrollState = newState
                }
            })
        }
        openPositionGroupIfNeed()
        parentViewModel?.loadInitCategoryList()
    }

    private fun initSuperApp() {
        val initSuperAppList = superApp?.getMainSuperInitList()
        if (!initSuperAppList.isNullOrEmpty()) {
            val list = parentViewModel?.getSuperAppCategory(initSuperAppList) ?: ArrayList()
            categoryAdapter?.updateSuperAppData(list)
        }
    }

    private fun initShortcutFolders() {
        val list = ArrayList<CategoryListBean>()
        // 添加快捷文件夹
        list.add(CategoryListBeanFactory.createAddShortcutFolderSlideBean())
        categoryAdapter?.updateShortcutFolders(list)
    }

    /**
     * 初始化标签列表，添加默认的新建标签
     */
    private fun initLabelList() {
        val list = ArrayList<CategoryListBean>()
        val addLabelBean = CategoryListBeanFactory.createAddLabelBean()
        list.add(addLabelBean)
        categoryAdapter?.updateLabelData(list)
    }

    override fun initData(savedInstanceState: Bundle?) {
        mSavedInstanceState = savedInstanceState
    }

    override fun startObserve() {
        //位置
        startStoragePanelObserver()

        //最近删除
        startRecycleBinViewObserver()

        //来源
        startSuperAppObserver()

        // 快捷文件夹
        startShortcutFolderObserver()

        //标签数据
        startLabelDataObserve()

        //编辑状态
        startEditStateObserve()

        //侧导分组数据
        parentViewModel?.sideGroupListLiveData?.observe(this) {
            categoryAdapter?.setData(it)
        }
    }

    private fun startStoragePanelObserver() {
        // Storage Info Part
        launchAndRepeatWithViewLifecycle {
            parentViewModel?.storageUiState?.collect { storage ->
                Log.d(TAG, "startObserve storageItems ${storage.storageItems}")
                refreshStorageInfo(storage.storageItems)
            }
        }
    }

    private fun startSuperAppObserver() {
        parentViewModel?.mMainSuperAppItemList?.observe(this) { appList ->
            Log.d(TAG, "startObserve appList ${appList.size}")
            val showList = appList.filter { it.isShow }.toMutableList()
            val list = parentViewModel?.getSuperAppCategory(showList, false) ?: ArrayList()
            //更新
            categoryAdapter?.updateSuperAppData(list)
            //list在这里更新，categoryType被赋值，因此在此处理外部应用跳转
            parentFragment?.let {
                if (it is MainCombineFragment && it.isOutToRemoteMac) {
                    outJumpRemoteMac(appList)
                    it.isOutToRemoteMac = false
                    if ((activity as? MainActivity) != null) {
                        (activity as MainActivity).isOutJumpSuperRemotePC = false
                    }
                    scrollToRemoteMacItem()
                }
                refreshShowType()
            }
        }
    }
    /**
     * 侧导滚动到远程电脑文件
     * */
    private fun scrollToRemoteMacItem() {
        var i = 0
        categoryAdapter?.getCategoryDataList()?.forEach { categoryListBean ->
            if (categoryListBean.categoryType == TYPE_SOURCE_PANEL) {
                categoryRecyclerView?.smoothScrollToPosition(i)//侧导滚动到远程电脑文件
                return
            }
            i++
        }
    }

    /**
     * 外部应用跳到远程电脑
     * */
    private fun outJumpRemoteMac(appList: MutableList<MainCategoryItemsBean>?) {
        val data = appList?.find { it.itemType == CategoryHelper.CATEGORY_REMOTE_PC_CONTROL }
        if (data != null) {
            val bundle = Bundle().apply {
                putString(
                    Constants.TITLE,
                    getString(com.filemanager.common.R.string.remote_computer_file)
                )
                putString(
                    KtConstants.P_PACKAGE,
                    getString(com.filemanager.common.R.string.remote_computer_file)
                )
                putStringArray(KtConstants.P_SUPER_PATH_LIST, remotePcControlPath)
                putInt(KtConstants.SUPER_DIR_DEPTH, 0)
                putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.remote_computer_file)
            }
            changeSelectedCategoryType(data.sideCategoryType ?: 0, bundle)
        }
    }

    private fun startRecycleBinViewObserver() {
        parentViewModel?.mDeleteState?.observe(this) {
            Log.d(TAG, "startObserve delete count ${it.second}")
            lifecycleScope.launch(Dispatchers.IO) {
                val showLockIcon = PrivacyPasswordSettingCompat.checkPrivacySettingPassword(context)
                        && isRecentlyDeletedSwitchOpen(appContext)
                withContext(Dispatchers.Main) {
                    categoryAdapter?.updateRecycleBinCount(it.second, showLockIcon)
                }
            }
        }
    }


    private fun startShortcutFolderObserver() {
        parentViewModel?.shortcutFolderItemList?.observe(this) {
            val list = it.map { item ->
                val bean = CategoryListBean(item.sideCategoryType, CategoryListBean.TYPE_EDIT_OPTIONS)
                if (item.sideCategoryType == CategoryHelper.CATEGORY_ADD_FOLDER) {
                    bean.layoutType = CategoryListBean.TYPE_OPTIONS
                } else {
                    bean.dbID = item.dbID
                    bean.filePath = item.fileList.get(0)
                }
                bean.iconRes = item.iconId
                bean.name = item.name
                bean
            }
            categoryAdapter?.updateShortcutFolders(list)

            var selected = false
            list.forEach {
                if (it.dbID == newShortcutFolderID) {
                    selected = true
                    categoryAdapter?.setSelectCategoryType(it.categoryType)
                    onSideCategoryItemClick(it)
                }
            }
            val currentSelectType = categoryAdapter?.getCurrentCategoryType() ?: CategoryHelper.CATEGORY_RECENT
            val aloneAdd = list.size <= 1
            Log.d(TAG, "startShortcutFolderObserver select:$selected currentSelect:$currentSelectType aloneAdd:$aloneAdd")
            // 处理只有一个快捷文件夹，并且上一次被选中，点击删除后，右侧界面显示为最近界面
            if (aloneAdd && CategoryHelper.isShortcutFolderType(currentSelectType)) {
                categoryAdapter?.setSelectCategoryType(CategoryHelper.CATEGORY_RECENT)
                onSideCategoryItemClick(CategoryListBean(CategoryHelper.CATEGORY_RECENT, TYPE_OPTIONS))
            }
            newShortcutFolderID = -1L
        }
    }

    private fun startLabelDataObserve() {
        labelViewModel?.uiState?.observe(this) { uiModel ->
            val isEdit = parentViewModel?.isEdit() ?: false
            Log.d(TAG, "startObserve label list ${uiModel.mAllLabelsWithFilesList.size} isEdit $isEdit newLabelName $newLabelName")
            val categoryList = mapLabelSideCategoryList(uiModel)
            categoryAdapter?.updateLabelData(categoryList)
            if (!isEdit && newLabelName != null) {
                val newLabelItem = categoryList.find { it.name == newLabelName }
                if (newLabelItem != null) {
                    categoryAdapter?.setSelectCategoryType(newLabelItem.categoryType)
                    onLabelItemClick(newLabelItem)
                    StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_LABEL, Constants.PAGE_MAIN)
                }
                newLabelName = null
            }
        }

        labelViewModel?.errorState?.observe(this) { errorCode ->
            if (errorCode == MainLabelViewModel.ERROR_ADD_FAILED) {
                CustomToast.showShort(com.filemanager.common.R.string.phone_storage_can_not_save)
                labelViewModel?.errorState?.value = 0
            }
        }
    }

    private fun startEditStateObserve() {
        parentViewModel?.editState?.observe(this) { editState ->
            val isEdit = parentViewModel?.isEdit() ?: false
            Log.d(TAG, "startEditStateObserve editState $editState isEdit $isEdit")
            if (editState != MainCategoryViewModel.EDIT_STATE_IDLE) {
                (parentFragment as? MainCombineFragment)?.setEditState(isEdit)
                toolbar?.let {
                    inflateMenu(it, isEdit)
                }
                if (isEdit) {
                    setEditCallback()
                    enterUiEditState()
                } else {
                    exitUiEditState()
                    checkSuperAppSelected()
                    activity?.lifecycle?.coroutineScope?.launch(Dispatchers.IO) {
                        delay(ItemAnimationHelper.DEFAULT_ENTER_EDIT_ANIMATION_DURATION) {
                            parentViewModel?.loadShortcutFolders()
                            labelViewModel?.loadLabels()
                        }
                    }
                }
                categoryAdapter?.isEdit = isEdit
                categoryAdapter?.startOptionAnimation()
                parentViewModel?.updateCategoryList()
                categoryAdapter?.updateSuperAppData()
            }
        }
    }

    private fun checkSuperAppSelected() {
        val selectedIsHide = categoryAdapter?.getCurrentSuperAppIsHide() ?: false
        if (selectedIsHide.not()) return
        val newSuperAppBean = categoryAdapter?.getSuperAppNewBean()
        val newSelectCategoryType = newSuperAppBean?.categoryType ?: -1
        Log.d(TAG, "newSuperAppBean=${newSuperAppBean?.name},newSelectCategoryType=$newSelectCategoryType")
        if (null == newSuperAppBean || -1 == newSelectCategoryType) {
            categoryAdapter?.setRecentItemSelect()
        } else {
            categoryAdapter?.setSuperAppItemSelect(newSuperAppBean)
        }
    }

    private fun setEditCallback() {
        getMainCombineFragment()?.onDeleteLabelCallback = onDeleteLabelCallback
        getMainCombineFragment()?.checkLabelMaxCountCallback = {
            labelViewModel?.isReachLabelMaxCount() ?: false
        }
    }

    private fun refreshCategoryCount() {
        SideExpandableAdapter.CLASS_ITEM_TYPE.forEach {
            asyncItemsCount(it)
        }
    }

    private fun asyncItemsCount(categoryType: Int) {
        if (isAsyncCountMap[categoryType] == false) {
            isAsyncCountMap[categoryType] = true
            threadManager.execute(FileRunnable(Runnable {
                var itemCount: Long = -1L
                runCatching {
                    itemCount = categoryHelper.getCountByCategory(categoryType)
                    CategoryHelper.saveCategoryCountData(categoryType, itemCount, 0)
                }.onFailure {
                    isAsyncCountMap[categoryType] = false
                    Log.e(TAG, "asyncItemsCount $it")
                }
                if (itemCount == -1L) return@Runnable

                CollectPrivacyUtils.collectMedia(categoryType, itemCount)
                uiHandler.post {
                    categoryAdapter?.categoryCounts?.set(categoryType, itemCount)
                    categoryAdapter?.updateClassPanel(categoryType, itemCount)
                    isAsyncCountMap[categoryType] = false
                }
            }, TAG))
        }
    }

    /**
     * 改变侧导的编辑模式
     */
    private fun changeEditState() {
        parentViewModel?.changeEditState()
    }

    fun completeEditState(): Boolean {
        Log.d(TAG, "completeEditState...")
        val isEdit = parentViewModel?.isEdit() ?: false
        if (isEdit) {
            changeEditState()
        }
        return isEdit
    }

    /**
     * 退出编辑状态
     */
    private fun exitUiEditState() {
        val titleString = resources.getString(com.filemanager.common.R.string.app_name)
        behavior?.setToolbarTitle(titleString, true)
        categoryAdapter?.setItemSelected(categoryAdapter?.currentSelectItem, selected = true, animated = true)
        if (iconAnimator.isRunning) {
            iconAnimator.cancel()
        }
        reverseIconAnimator.apply {
            startDelay = DEFAULT_ANIMATION_DELAY
            start()
        }
    }

    /**
     * 进入编辑状态
     */
    private fun enterUiEditState() {
        val titleEdit = stringResource(com.filemanager.common.R.string.menu_recent_file_edit)
        behavior?.setToolbarTitle(titleEdit, true)
        categoryAdapter?.setItemSelected(categoryAdapter?.currentSelectItem, selected = false, animated = true)
        if (reverseIconAnimator.isRunning) {
            reverseIconAnimator.cancel()
        }
        iconAnimator.apply {
            startDelay = 0
            start()
        }
    }

    /**
     * 远程mac进入拖拽模式
     */
    private fun enterMacDragState() {
        if (reverseIconAnimator.isRunning) {
            reverseIconAnimator.cancel()
        }
        iconAnimator.apply {
            startDelay = 0
            start()
        }
    }

    /**
     * 远程mac退出拖拽模式
     */
    private fun exitMacDragState() {
        if (iconAnimator.isRunning) {
            iconAnimator.cancel()
        }
        reverseIconAnimator.apply {
            startDelay = 0
            start()
        }
    }

    private val iconAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            val fraction = it.animatedFraction
            val expandIcon = (parentFragment as? MainCombineFragment)?.getExpandIcon()
            if (fraction == 1f) {
                expandIcon?.isClickable = false
                expandIcon?.jumpDrawablesToCurrentState()
            } else if (fraction == 0f) {
                expandIcon?.isClickable = true
            }
            expandIcon?.alpha = 1 - fraction

            val maskView = (parentFragment as? MainCombineFragment)?.getMaskView()
            maskView?.alpha = fraction
            maskView?.visibility = View.VISIBLE
        }
        duration = DEFAULT_ANIMATION_DURATION
        interpolator = DEFAULT_ANIMATION_INTERPOLATOR
    }

    private val reverseIconAnimator = ValueAnimator.ofFloat(1f, 0f).apply {
        addUpdateListener {
            val fraction = it.animatedValue as Float
            val expandIcon = (parentFragment as? MainCombineFragment)?.getExpandIcon()
            if (fraction == 1f) {
                expandIcon?.isClickable = false
            } else if (fraction == 0f) {
                expandIcon?.isClickable = true
                expandIcon?.isPressed = false
            }
            expandIcon?.alpha = 1 - fraction

            val maskView = (parentFragment as? MainCombineFragment)?.getMaskView()
            maskView?.alpha = fraction
            if (fraction == 0f) {
                maskView?.visibility = View.GONE
            }
        }
        duration = DEFAULT_ANIMATION_DURATION
        interpolator = DEFAULT_ANIMATION_INTERPOLATOR
    }

    /**
     * 将加载出来的标签数据映射为侧导可以渲染的数据
     */
    private fun mapLabelSideCategoryList(uiModel: MainLabelViewModel.LabelUiModel): ArrayList<CategoryListBean> {
        val labelEntryWrapperList = uiModel.mAllLabelsWithFilesList
        val categoryList = ArrayList<CategoryListBean>()
        var categoryType = CategoryHelper.CATEGORY_LABEL_GROUP + 1
        for (wrapper in labelEntryWrapperList) {
            val sideBean = CategoryListBean(categoryType, CategoryListBean.TYPE_EDIT_OPTIONS)
            sideBean.name = wrapper.name
            sideBean.subTitle = wrapper.filesCount.toString()
            sideBean.iconRes = com.filemanager.common.R.drawable.color_tool_menu_ic_label_new
            sideBean.labelEntry = wrapper.label
            categoryList.add(sideBean)
            categoryType++
        }
        val addLabelBean = CategoryListBeanFactory.createAddLabelBean()
        categoryList.add(addLabelBean)

        return categoryList
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        var config = configList.find { it is ScreenFoldConfig }
        if (config == null) {
            config = configList.find { it is ZoomWindowConfig }
        }
        Log.d(TAG, "onUIConfigChanged config $config")
        if (config != null) {
            labelViewModel?.loadLabels()
        }
    }

    override fun getViewModel(): ViewModel? {
        return parentViewModel
    }

    override fun getRecyclerView(): RecyclerView? {
        return null
    }

    override fun pressBack(): Boolean {
        return completeEditState()
    }

    override fun onResume() {
        refreshCleanGarbageView()
        refreshShowType()
        super.onResume()
    }

    private fun refreshShowType() {
        val activity = baseVMActivity ?: return
        parentFragment?.let {
            if (it is MainCombineFragment) {
                Log.w(TAG, "refreshShowType recBin:${it.needSetTypeRecBin} doc:${it.needSetTypeDoc} super:${it.needSetTypeSuper}")
                if (it.needSetTypeRecBin) {
                    it.needSetTypeRecBin = false
                    changeSelectedCategoryType(CategoryHelper.CATEGORY_RECYCLE_BIN)
                } else if (it.needSetTypeDoc) {
                    it.needSetTypeDoc = false
                    changeSelectedCategoryType(CategoryHelper.CATEGORY_DOC)
                } else if (it.needSetTypeSuper) {
                    val superAppList = parentViewModel?.mMainSuperAppItemList?.value
                    val superApp = superAppList?.find { item -> item.itemType == it.setSuperCategoryType } ?: return
                    it.needSetTypeSuper = false
                    val dir = File(it.externalSuperPath).parent
                    val relativePath = PathUtils.getRelativePath(activity, dir) + File.separator
                    superApp.fileList = ArrayUtils.add(superApp.fileList, relativePath)
                    superApp.externalPath = it.externalSuperPath
                    Log.w(TAG, "checkJumpToSuperApp categoryType:${it.setSuperCategoryType} item:$superApp")
                    categoryAdapter?.setSuperAppItemSelect(CategoryListBean(superApp.sideCategoryType, CategoryListBean.TYPE_EDIT_OPTIONS))
                    //这里加上toolbar的Margin的设置，解决在预览模式下，进入远程电脑文件的列表时，toolbar的icon和title重合的问题
                    it.adapterFragmentsToolbar()
                    scrollToRemoteMacItem()
                }
            }
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        questionnaire?.releaseSpace(cdpView)
        super.onDestroy()
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        val isEdit = parentViewModel?.isEdit() ?: false
        toolbar?.let {
            inflateMenu(it, isEdit)
        }
    }

    private fun inflateMenu(toolbar: COUIToolbar, isEdit: Boolean) {
        if (!isEdit) {
            toolbar.menu.clear()
            toolbar.inflateMenu(R.menu.main_category_menu)
            updateOWorkVisible()
        } else {
            toolbar.menu.clear()
            toolbar.inflateMenu(R.menu.main_category_edit_menu)
        }
        toolbar.setOnMenuItemClickListener { menu ->
            onMenuItemSelected(menu)
        }

        for (i in 0 until toolbar.childCount) {
            val child = toolbar.getChildAt(i)
            if (child is COUIActionMenuView) {
                menuView = child
                break
            }
        }
    }

    private fun initToolbar() {
        scrollAppBarLayout = rootView?.findViewById(R.id.appBarLayout)
        behavior = (scrollAppBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior as? PrimaryTitleBehavior
        behavior?.initBehavior(rootView, R.id.main_recycle_view)
        val title = appContext.getString(com.filemanager.common.R.string.app_name)
        behavior?.setToolbarTitle(title, true)
        toolbar?.apply {
            isTitleCenterStyle = true
            setIsInsideSideNavigationBar(true)
            val isEdit = parentViewModel?.isEdit() ?: false
            inflateMenu(this, isEdit)
        }
        rootView?.apply {
            val paddingTop = COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity)
            setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
        }
    }

    private fun initQuestionnaire() {
        if (PrivacyPolicyController.hasAgreeUseNet().not()) {
            return
        }
        if (!hasInjectQuestionnaire && (questionnaire?.isSupportQuestionnaire() == true)) {
            val container = questionnaireView?.findViewById<ConstraintLayout>(R.id.cdp_container)
            Log.d(TAG, "container $container")
            container?.let {
                cdpView = questionnaire?.getCdpView(baseVMActivity, container)
                hasInjectQuestionnaire = true
                it.addView(cdpView)
            }
        }
        questionnaire?.updateSpace(lifecycleScope, cdpView)
    }

    /**
     * 更新位置部分数据
     */
    private fun refreshStorageInfo(storageItems: List<Storage>) {
        var storageStateChange = false
        var hasRemoteDeviceList = false
        storageItems.forEach { item ->
            when (item) {
                is Storage.OTGStorage -> {
                    storageStateChange = storageStateChange || mOtgState != item.state
                    mOtgState = item.state
                    checkOTGState(item.state)
                }

                is Storage.SDCardStorage -> {
                    storageStateChange = storageStateChange || mSdCardState != item.state
                    mSdCardState = item.state
                }

                is Storage.DFMStorage -> {
                    storageStateChange = storageStateChange || dfmStorage?.state != item.state
                    dfmStorage = item
                    directJumpDFM(item.state, item.path)
                }

                is Storage.RemoteDeviceStorage -> {
                    checkCurrentRemoteMacDeviceIsExist(item.deviceList)
                    hasRemoteDeviceList = item.deviceList.isNullOrEmpty().not()
                }

                else -> {}
            }
        }

        //更新位置存储部分数据
        categoryAdapter?.updateStoragePanelData(storageItems)

        if (hasRemoteDeviceList) {
            jumpRemoteDeviceFileIfNeed()
        }
    }

    private fun checkOTGState(storageState: Int) {
        if (storageState != KtConstants.STATE_MOUNTED) {
            Log.w(TAG, "checkOTGState -> unmount!!")
            val currentSelectType = categoryAdapter?.getCurrentCategoryType() ?: CategoryHelper.CATEGORY_RECENT
            //判断类型为OTG 且为大屏 or 中屏
            val screenState = UIConfigMonitor.getWindowType()
            if (currentSelectType == CategoryHelper.CATEGORY_OTG_BROWSER &&
                (screenState == UIConfig.WindowType.LARGE || screenState == UIConfig.WindowType.MEDIUM)) {
                categoryAdapter?.setRecentItemSelect()
                //中大屏退出OTG页面，默认切到最近，需要重新设置toolbar的间距
                (parentFragment as? MainCombineFragment)?.adapterFragmentsToolbar()
            }
            return
        }
    }

    /**
     * 直接跳转到dfm
     */
    private fun directJumpDFM(storageState: Int, mountPath: String) {
        if (storageState != KtConstants.STATE_MOUNTED) { // 没有挂载上
            Log.w(TAG, "directJumpDFM -> unmount!!")
            val currentSelectType = categoryAdapter?.getCurrentCategoryType() ?: CategoryHelper.CATEGORY_RECENT
            if (currentSelectType == CategoryHelper.CATEGORY_DFM) {
                categoryAdapter?.setRecentItemSelect()
                (parentFragment as? MainCombineFragment)?.adapterFragmentsToolbar()
            }
            return
        }
        val activity = baseVMActivity ?: return
        val intent = activity.intent
        val isFromDevice = IntentUtils.getBoolean(intent, Constants.KEY_IS_FROM_DEVICE, false)
        val remoteDeviceId = IntentUtils.getString(intent, Constants.KEY_REMOTE_DEVICE_ID) ?: return
        Log.d(TAG, "directJumpDFM -> isFromDevice:$isFromDevice id:$remoteDeviceId")
        if (isFromDevice && mountPath.endsWith(remoteDeviceId)) { // 跳转到DFM页面
            intent.putExtra(Constants.KEY_IS_FROM_DEVICE, false)
            val bean = CategoryListBean(CategoryHelper.CATEGORY_DFM, CategoryListBean.TYPE_STORAGE)
            onSideCategoryItemClick(bean)
        }
    }

    private fun checkCurrentRemoteMacDeviceIsExist(deviceList: List<RemoteDeviceInfo>?) {
        val currentDeviceId = (parentFragment as? MainCombineFragment)?.currentSelectedRemoteDeviceId
        val currentSelectType = categoryAdapter?.getCurrentCategoryType() ?: CategoryHelper.CATEGORY_RECENT
        val screenState = UIConfigMonitor.getWindowType()
        if (screenState == UIConfig.WindowType.SMALL || !CategoryHelper.isRemoteMacDeviceType(currentSelectType)) {
            return
        }
        //类型为远程 Mac 设备，且为中大屏
        var currentRemoteDeviceIsExist = false
        var newCategoryType = CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC
        if (!deviceList.isNullOrEmpty() && !currentDeviceId.isNullOrEmpty()) {
            for (device in deviceList) {
                newCategoryType++
                if (device.deviceId?.equals(currentDeviceId) == true) {
                    currentRemoteDeviceIsExist = true
                    break
                }
            }
        }
        if (!currentRemoteDeviceIsExist) {
            //当前远程 Mac 设备不存在，跳到最近页面，重新设置toolbar的间距
            Log.w(TAG, "current remote mac device not exist, jump to recent page")
            categoryAdapter?.setRecentItemSelect()
            (parentFragment as? MainCombineFragment)?.currentSelectedRemoteDeviceId = null
            (parentFragment as? MainCombineFragment)?.adapterFragmentsToolbar()
        } else if (newCategoryType != currentSelectType) {
            //设备存在，且设备状态（已连接/可连接/已离线）发生变化，排序会发生变化，导致type和当前的不一致，需要更新下type
            categoryAdapter?.setSelectCategoryType(newCategoryType)
            updateCategoryTypeCallback.invoke(newCategoryType)
        }
    }

    private fun openPositionGroupIfNeed() {
        val groupExpanded = GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_POSITION_GROUP)
        if (groupExpanded) {
            return
        }
        val activity = baseVMActivity ?: return
        val intent = activity.intent
        val fromRemoteControlJumpType = IntentUtils.getInt(intent, Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
        val deviceId = IntentUtils.getString(intent, Constants.KEY_REMOTE_DEVICE_ID)
        val deviceName = IntentUtils.getString(intent, Constants.KEY_DEVICE_NAME)
        Log.d(TAG, "openPositionGroupIfNeed fromRemoteControlJumpType:$fromRemoteControlJumpType id:$deviceId name:$deviceName")
        if (fromRemoteControlJumpType != Constants.REMOTE_CONTROL_JUMP_TYPE_MAC_FILE || deviceId.isNullOrEmpty()) {
            return
        }
        GroupCollapseUtil.setGroupState(CategoryHelper.CATEGORY_POSITION_GROUP, true)
    }

    private fun jumpRemoteDeviceFileIfNeed() {
        val activity = baseVMActivity ?: return
        val intent = activity.intent
        val fromRemoteControlJumpType = IntentUtils.getInt(intent, Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
        val deviceId = IntentUtils.getString(intent, Constants.KEY_REMOTE_DEVICE_ID)
        val deviceName = IntentUtils.getString(intent, Constants.KEY_DEVICE_NAME)
        Log.d(TAG, "jumpRemoteDeviceFileIfNeed jumpType:$fromRemoteControlJumpType id:$deviceId name:$deviceName")
        if (fromRemoteControlJumpType != Constants.REMOTE_CONTROL_JUMP_TYPE_MAC_FILE || deviceId.isNullOrEmpty()) {
            return
        }
        val deviceList: List<CategoryListBean> = categoryAdapter?.getRemoteDeviceDataList() ?: arrayListOf()
        var deviceBean: CategoryListBean? = null
        deviceList.forEach {
            if (it.deviceId == deviceId) {
                deviceBean = it
                return@forEach
            }
        }
        Log.d(TAG, "jumpRemoteDeviceFileIfNeed deviceBean:$deviceBean")
        deviceBean?.let {
            // 跳转到远程设备文件页面
            onRemoteDeviceItemClick(it)
            categoryAdapter?.setSelectCategoryType(it.categoryType)
            updateCategoryTypeCallback.invoke(it.categoryType)
            intent.putExtra(Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
            (parentFragment as? MainCombineFragment)?.adapterFragmentsToolbar()
        }
    }

    fun updateRemoteDeviceStatus(deviceId: String, status: Int) {
        parentViewModel?.updateDeviceStatus(deviceId, status)
    }

    fun selectCategoryTypeItem(type: Int) {
        val selectType = categoryAdapter?.getCurrentCategoryType()
        Log.d(TAG, "selectCategoryType old:$selectType new:$type")
        //otg列表和OTG文件列表切换是不需要改变父子级左侧选中项
        if (((selectType == CategoryHelper.CATEGORY_OTG_BROWSER) && (type == CategoryHelper.CATEGORY_MORE_STORAGE))
                || ((selectType == CategoryHelper.CATEGORY_MORE_STORAGE) && (type == CategoryHelper.CATEGORY_OTG_BROWSER))) {
            return
        }
        categoryAdapter?.setSelectCategoryType(type)
    }

    private fun startPrivacyPasswordSetting(type: Int, data: Bundle? = null) {
        baseVMActivity?.let { act ->
            lifecycleScope.launch(Dispatchers.IO) {
                val isPrivacy = PrivacyPasswordSettingCompat.checkPrivacySettingPassword(act) && isRecentlyDeletedSwitchOpen(appContext)
                withContext(Dispatchers.Main) {
                    if (isPrivacy) {
                        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_PRIVACY_PWD, Constants.PAGE_MAIN)
                        launcher.launch(PrivacyPasswordSettingCompat.privacySettingPasswordIntent(act))
                    } else {
                        changeSelectedCategoryType(type, data)
                    }
                }
            }
        }
    }

    private fun changeSelectedCategoryType(type: Int, data: Bundle? = null) {
        selectCategoryTypeItem(type)
        val selectType = categoryAdapter?.getCurrentCategoryType()
        parentFragment?.let {
            if (it is MainCombineFragment) {
                if (selectType == CategoryHelper.CATEGORY_OTG_BROWSER) {
                    val otgSize = parentViewModel?.mOtgPaths?.size ?: 1
                    if (otgSize > 1) {
                        it.setCurrentChildFragment(CategoryHelper.CATEGORY_MORE_STORAGE, data)
                    } else {
                        it.setCurrentChildFragment(type, data)
                    }
                } else {
                    it.setCurrentChildFragment(type, data)
                }
            }
        }
        val page = Constants.getPage(type)
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", page, Constants.PAGE_MAIN)
    }

    fun setOTGStateChecking() {
        parentViewModel?.updateOTGState()
    }

    fun refreshDataWithMount() {
        parentViewModel?.apply {
            loadMainCategoryItem(checkParentPermission())
            fetchStorageInfo(true)
        }
    }

    private fun startToDFM() {
        val path = dfmStorage?.path ?: return
        val bundle = Bundle()
        bundle.putString(KtConstants.P_TITLE, dfmStorage?.deviceName)
        bundle.putString(KtConstants.CURRENT_PATH, path)
        changeSelectedCategoryType(CategoryHelper.CATEGORY_DFM, bundle)
        StatisticsUtils.clickDFMEntry(appContext, dfmStorage?.deviceName ?: "", dfmStorage?.connectTime ?: 0L)
    }

    fun getCategoryCount(categoryType: Int): Long {
        return categoryAdapter?.categoryCounts?.get(categoryType) ?: 0
    }

    /**
     * 点击侧导对应的类别
     * @param categoryListBean 侧导点击item对应的数据
     */
    override fun onSideCategoryItemClick(categoryListBean: CategoryListBean) {
        val categoryType = categoryListBean.categoryType
        val isEdit = parentViewModel?.isEdit() ?: false
        Log.d(TAG, "onSideCategoryItemClick categoryType $categoryListBean isEdit:$isEdit")

        if (onSuperAppItemClick(categoryType)) return

        if (onShortcutFolderItemClick(categoryListBean)) return

        if (onLabelItemClick(categoryListBean)) return

        val activity = baseVMActivity ?: return
        if (onRemoteDeviceItemClick(categoryListBean) && !MacDragUtil.MacDragObject.isDraggingFromMac) {
            OptimizeStatisticsUtil.clickHomeRemoteDeviceEvent(
                categoryListBean.deviceStatus,
                StatisticsUtils.REMOTE_VALUE_DEV_TYPE_MAC
            )
            return
        }

        when (categoryType) {
            CategoryHelper.CATEGORY_CLEAN_GARBAGE -> {
                StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.FILE_CLEANUP)
                StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_CLEAR_UP, Constants.PAGE_MAIN)
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                val bundle = Bundle().apply {
                    putBoolean("same_task_animation", true)
                    putString(KtConstants.FILEMANAGER_TO_SECURESAFE_STRING, getString(com.filemanager.common.R.string.garbage_cleanup))
                }
                baseVMActivity?.let { it1 -> KtAppUtils.startPhoneManager(it1, bundle) }
            }

            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                if (MacDragUtil.MacDragObject.isDraggingFromMac) {
                    Log.d(TAG, "is dragging, return")
                    return
                }
                startPrivacyPasswordSetting(categoryType)
            }

            CategoryHelper.CATEGORY_FILE_BROWSER,
            CategoryHelper.CATEGORY_SDCARD_BROWSER,
            CategoryHelper.CATEGORY_OTG_BROWSER,
            CategoryHelper.CATEGORY_RECENT,
            in SideExpandableAdapter.CLASS_ITEM_TYPE -> changeSelectedCategoryType(categoryType)

            CategoryHelper.CATEGORY_DFM -> {
                activity?.let {
                    DialogUtil.showPermissionDialog(
                        activity, com.filemanager.common.R.string.use_net_tips_for_dfs
                    ) {
                        startToDFM()
                    }
                }
            }

            CategoryHelper.CATEGORY_CLOUD -> {
                //只有mac拖拽时，云盘不可点击
                if (isCloudDiskCategoryType(categoryType) && MacDragUtil.MacDragObject.isDraggingFromMac) {
                    Log.d(TAG, "setItemOnClickListener -> is drag from mac state, return!")
                    return
                }
                baseVMActivity?.let { act ->
                    parentViewModel?.openCloudDisk(act)
                }
                //fortest，点击云盘跳转新的远程页面
                /*parentFragment?.let {
                    if (it is MainCombineFragment) {
                        it.setCurrentChildFragment(CategoryHelper.CATEGORY_REMOTE_MAC, null)
                    }
                }*/
            }

            CategoryHelper.CATEGORY_PRIVATE_SAVE -> {
                baseVMActivity?.let { act ->
                    StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.ACTION_ENCRYPT)
                    FileEncryptFactor.startFileSafeActivity(act)
                }
            }

            CategoryHelper.CATEGORY_ADD_FOLDER -> {
                if (MainApi.isDragging(activity)) {
                    Log.d(TAG, "is dragging, return")
                    return
                }
                getMainCombineFragment()?.showSelectShortcutFolderDialog()
            }

            CategoryHelper.CATEGORY_ADD_LABEL -> {
                if (MainApi.isDragging(activity)) {
                    Log.d(TAG, "is dragging, return")
                    return
                }
                getMainCombineFragment()?.setShowAddLabelDialogState(true)
            }

            else -> {}
        }
    }

    private fun onRemoteDeviceItemClick(categoryBean: CategoryListBean): Boolean {
        if (!CategoryHelper.isRemoteMacDeviceType(categoryBean.categoryType)) {
            return false
        }
        Log.d(TAG, "onRemoteDeviceItemClick name:${categoryBean.name} status:${categoryBean.deviceStatus}")
        val deviceId = categoryBean.deviceId
        MacDragUtil.MacDragObject.deviceId = deviceId
        val name = categoryBean.name
        val deviceStatus = categoryBean.deviceStatus
        val deviceIsSameAccount = categoryBean.deviceIsSameAccount
        val bundle = Bundle().apply {
            putString(KtConstants.P_REMOTE_DEVICE_ID, deviceId)
            putString(KtConstants.P_REMOTE_DEVICE_NAME, name)
            putInt(KtConstants.P_REMOTE_DEVICE_STATUS, deviceStatus)
            putInt(KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, deviceIsSameAccount)
        }
        (parentFragment as? MainCombineFragment)?.currentSelectedRemoteDeviceId = deviceId
        (parentFragment as? MainCombineFragment)?.setCurrentChildFragment(categoryBean.categoryType, bundle)
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_REMOTE_FILE_LIST, Constants.PAGE_MAIN)
        return true
    }

    private fun onShortcutFolderItemClick(categoryBean: CategoryListBean): Boolean {
        if (!CategoryHelper.isShortcutFolderType(categoryBean.categoryType)) {
            return false
        }
        val dbId = categoryBean.dbID
        val name = categoryBean.name
        val path = categoryBean.filePath
        Log.d(TAG, "onShortcutFolderItemClick name:$name dbID:$dbId path:$path")
        val bundle = Bundle().apply {
            putString(KtConstants.P_TITLE, name)
            putString(KtConstants.FILE_PATH, path)
            putLong(Constants.DB_ID, dbId)
            putInt(Constants.SIDE_CATEGORY_TYPE, categoryBean.categoryType)
        }
        (parentFragment as? MainCombineFragment)?.setCurrentChildFragment(categoryBean.categoryType, bundle)
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_SHORTCUT_FOLDER, Constants.PAGE_MAIN)
        return true
    }

    private fun onLabelItemClick(categoryListBean: CategoryListBean): Boolean {

        if (!CategoryHelper.isLabelType(categoryListBean.categoryType)) {
            return false
        }

        val labelId = categoryListBean.labelEntry?.id ?: 0L
        val name = categoryListBean.labelEntry?.name ?: ""
        val bundle = Bundle().apply {
            putString(KtConstants.P_TITLE, name)
            putLong(Constants.LABEL_ID, labelId)
            putInt(Constants.SIDE_CATEGORY_TYPE, categoryListBean.categoryType)
        }
        (parentFragment as? MainCombineFragment)?.setCurrentChildFragment(categoryListBean.categoryType, bundle)
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_LABEL, Constants.PAGE_MAIN)
        return true
    }

    private fun onSuperAppItemClick(categoryType: Int): Boolean {
        val isDocCategoryType = categoryType == CategoryHelper.CATEGORY_TENCENT_DOCS || categoryType == CategoryHelper.CATEGORY_K_DOCS
        if (CategoryHelper.isSuperAppType(categoryType) || isDocCategoryType) {
            val superAppList = parentViewModel?.mMainSuperAppItemList?.value
            val data = superAppList?.find { it.sideCategoryType == categoryType }
            data?.let { superData ->
                if (superData.itemType == CategoryHelper.CATEGORY_TENCENT_DOCS || superData.itemType == CategoryHelper.CATEGORY_K_DOCS) {
                    val bundle = Bundle().apply {
                        putString(Constants.TITLE, superData.name)
                        putInt(Constants.TITLE_RES_ID, superData.nameResId)
                    }
                    bundle.putInt(Constants.KEY_FILE_DRIVE_TYPE, superData.itemType)
                    bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, false)
                    (parentFragment as? MainCombineFragment)?.setCurrentChildFragment(superData.itemType, bundle)
                } else {
                    val bundle = Bundle().apply {
                        putString(KtConstants.FILE_PATH, superData.externalPath)
                        putString(Constants.TITLE, superData.name)
                        putString(KtConstants.P_PACKAGE, superData.packageName)
                        putStringArray(KtConstants.P_SUPER_PATH_LIST, superData.fileList)
                        putInt(KtConstants.SUPER_DIR_DEPTH, superApp?.getDirDepthByItem(superData) ?: 0)
                        putInt(Constants.TITLE_RES_ID, superData.nameResId)
                        putInt(Constants.SIDE_CATEGORY_TYPE, superData.sideCategoryType)
                    }
                    (parentFragment as? MainCombineFragment)?.setCurrentChildFragment(categoryType, bundle)
                }
                StatisticsUtils.statisticsPageExposure(activity, "", superData.name, Constants.PAGE_MAIN)
            }
            return true
        }
        return false
    }

    fun updateLabels() {
        labelViewModel?.loadLabels()
    }

    fun setEditState() {
        val isEdit = parentViewModel?.isEdit() ?: false
        Log.d(TAG, "setEditState isEdit $isEdit")
        if (!isEdit) {
            parentViewModel?.changeEditState()
        }
    }

    private fun getMainCombineFragment(): MainCombineFragment? {
        return (parentFragment as? MainCombineFragment)
    }

    fun onSelectShortcutFolder(paths: List<String>?) {
        parentViewModel?.launch {
            withContext(Dispatchers.IO) {
                val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
                newShortcutFolderID = shortcutFolderApi?.addShortcutFolders(paths)?.getOrNull(0) ?: -1L
                parentViewModel?.loadShortcutFolders()
            }
        }
    }

    private fun refreshCleanGarbageView() {
        baseVMActivity?.apply {
            val cleanGarbage: View? = findViewById(R.id.right_item)
            cleanGarbage?.let {
                if (parentViewModel != null) {
                    it.visibility = if (StorageInfoUtils.getCleanupGarbageVisibility()) View.VISIBLE else View.GONE
                }
            }
        }
    }

    fun getLabelIdByCategoryType(categoryType: Int): Long? {
        val labelList = categoryAdapter?.getLabelDataList()
        val labelBean = labelList?.find { it.categoryType == categoryType }
        return labelBean?.labelEntry?.id
    }

    fun updateLabelCategoryType(labelId: Long) {
        categoryAdapter?.updateSelectedLabelId(labelId)
    }

    fun getSuperAppFilePathsByCategoryType(categoryType: Int): Array<String>? {
        val superAppItem = parentViewModel?.mMainSuperAppItemList?.value?.find { it.sideCategoryType == categoryType }
        return superAppItem?.fileList
    }

    fun getShortcutFilePathByCategoryType(categoryType: Int): String? {
        val itemBean = parentViewModel?.shortcutFolderItemList?.value?.find { it.sideCategoryType == categoryType }
        return itemBean?.fileList?.getOrNull(0)
    }

    fun setToolbarVisibility(visibility: Int) {
        toolbar?.clearAnimation()
        val animator = if (visibility == View.VISIBLE) {
            toolbar?.visibility = View.VISIBLE
            toolbar?.alpha = 0.0f
            ObjectAnimator.ofFloat(toolbar, "alpha", 0f, 1f)
        } else {
            ObjectAnimator.ofFloat(toolbar, "alpha", 1f, 0f)
        }
        animator.setDuration(TOOLBAR_ANIM_TIME)
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                toolbar?.visibility = visibility
                animator.removeListener(this)
                Log.d(TAG, "animator.removeListener: $this")
            }
        })
        animator.start()
    }

    fun getSelectedCategoryType(): Int {
        return categoryAdapter?.getCurrentCategoryType() ?: -1
    }

    fun getEditState(): Boolean {
        return parentViewModel?.isEdit() ?: false
    }

    fun handleDragStart() {
        categoryAdapter?.isDragging = true
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            enterMacDragState()
            categoryAdapter?.startDragAnimation()
            isIconAnimatorStarted = true
        }
        val isEdit = parentViewModel?.isEdit() ?: false
        itemDisableDragAnimator.onDragStart(isEdit, getAlphaChangeCallBack())
    }

    fun handleDragEnd() {
        categoryAdapter?.isDragging = false
        exitMacDragState()
        categoryAdapter?.endDragAnimation()
        isIconAnimatorStarted = false
        val isEdit = parentViewModel?.isEdit() ?: false
        itemDisableDragAnimator.onDragEnd(isEdit, getAlphaChangeCallBack())
    }

    private fun getAlphaChangeCallBack(): (alpha: Float, enterDrag: Boolean) -> Unit {
        val dragAlphaChangedCallback: (alpha: Float, enterDrag: Boolean) -> Unit = { alpha, _ ->
            val classViewHolder = categoryAdapter?.classPanelViewHolder
            categoryAdapter?.isDragging?.let { classViewHolder?.setItemViewAlpha(alpha, it) }
            val storageViewHolder = categoryAdapter?.storagePanelViewHolder
            //标签
            val labelPanelViewHolder = categoryAdapter?.labelPanelViewHolder
            //快捷文件夹
            val shortcutFolderVH = categoryAdapter?.shortcutFolderVH
            storageViewHolder?.setRemoteMacItemViewAlpha(alpha)
            labelPanelViewHolder?.setItemViewAlpha(alpha)
            shortcutFolderVH?.setItemViewAlpha(alpha)
        }
        return dragAlphaChangedCallback
    }

    fun handleDragScroll(event: DragEvent?): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    fun resetDragStatus() {
        scrollHelper?.resetDragStatus()
    }

    fun switchRecentItem() {
        categoryAdapter?.setRecentItemSelect()
        (parentFragment as? MainCombineFragment)?.adapterFragmentsToolbar()
    }
}