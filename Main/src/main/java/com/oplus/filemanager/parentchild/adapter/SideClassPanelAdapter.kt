/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: SideClassPanelAdapter.kt
 ** Description: Build the adapter of the classification
 ** Version: 1.0
 ** Date: 2025/1/23
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.parentchild.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.RecyclerView
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.utils.ClassBeanUtil
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.ClassPanelBaseOptionHolder
import com.oplus.filemanager.parentchild.viewholder.ClassPanelEditOptionHolder

class SideClassPanelAdapter(
    private val context: Context,
    private val activity: ComponentActivity,
    private val animationHelper: ItemAnimationHelper,
    private val onItemViewCallback: (itemView: View, categoryListBean: CategoryListBean) -> Unit,
    private val onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit = {}
) : SideBaseAdapter()  {

    private val typePosition: HashMap<Int, Int>
    var isDragging: Boolean = false
    var onClassPanelCountCallback: (type: Int, count: Long) -> Unit = { _, _ -> }
    var onClassPanelDataList: (MutableList<CategoryListBean>) -> Unit = {}

    init {
        typePosition = HashMap()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val holder: RecyclerView.ViewHolder
        val itemView = inflater.inflate(R.layout.item_side_options, parent, false)
        typePosition.clear()
        holder = if (viewType == CategoryListBean.TYPE_EDIT_OPTIONS) {
            ClassPanelEditOptionHolder(context, itemView)
        } else {
            ClassPanelBaseOptionHolder(context, itemView)
        }
        return holder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val data = dataList[position]
        typePosition.set(data.categoryType, position)
        if (data.layoutType == CategoryListBean.TYPE_EDIT_OPTIONS) {
            (holder as? ClassPanelEditOptionHolder)?.let {
                it.bindData(data, animationHelper.isEdit, isDragging, onClassPanelCountCallback, onItemViewCallback, onItemDragCallback)
            }
        } else {
            (holder as? ClassPanelBaseOptionHolder)?.bindData(data, animationHelper.isEdit, isDragging, onClassPanelCountCallback, onItemViewCallback)
        }
    }

    override fun getItemViewType(position: Int): Int {
        val model: CategoryListBean = dataList[position]
        return model.layoutType
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        super.onViewAttachedToWindow(holder)
        if (holder is ItemAnimationHelper.AnimationListener) {
            animationHelper.addListener(holder, animationHelper.isEdit)
        }
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)
        if (holder is ItemAnimationHelper.AnimationListener) {
            animationHelper.removeListener(holder)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun setData(newList: MutableList<CategoryListBean>?) {
        newList?.let {
            if (dataList != newList) {
                dataList = newList
                notifyDataSetChanged()
            }
        }
    }

    fun setData() {
        dataList = ClassBeanUtil.mainParentClassPanelInitView()
        onClassPanelDataList(dataList)
        notifyDataSetChanged()
    }

    fun setIsDragging(isDragging: Boolean) {
        this.isDragging = isDragging
    }

    fun updateClassPanel(type: Int, count: Long) {
        for ((index, bean) in dataList.withIndex()) {
            if (bean.categoryType == type) {
                if (dataList[index].subTitle != count.toString()) {
                    dataList[index].subTitle = count.toString()
                    notifyItemChanged(index)
                    onClassPanelCountCallback(type, count)
                }
            }
        }
    }

    fun getClassPanelData(): MutableList<CategoryListBean> {
        return dataList
    }
}