/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/09/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.isDraggingFromMac
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.view.HorizontalProgressBar
import com.oplus.filemanager.parentchild.adapter.EditItemTouchCallback
import com.oplus.filemanager.parentchild.adapter.SideRemoteDeviceListAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ExternalStorageHelper
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import com.oplus.filemanager.parentchild.view.SideNavigationStorageContainer
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder.Companion.EDIT_VIEW_ALPHA

class StoragePanelViewHolder(
    itemView: View,
    private val context: Context,
    private val activity: ComponentActivity,
    private val onItemViewCallback: (itemView: View?, categoryListBean: CategoryListBean, storage: Storage?) -> Unit
) : RecyclerView.ViewHolder(itemView), ItemAnimationHelper.AnimationListener {


    private var isEdit: Boolean = false

    private val storageItemViewList = HashSet<View?>()

    private val externalStorageHelper by lazy { ExternalStorageHelper() }
    private val normalTitleColor = ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_primary_neutral)
    private var remoteDeviceRecyclerView: COUIRecyclerView? = null
    private var remoteDeviceListAdapter: SideRemoteDeviceListAdapter? = null
    private var animationHelper: ItemAnimationHelper? = null

    fun bindData(storageList: List<Storage>, isEdit: Boolean, animationHelper: ItemAnimationHelper) {
        this.isEdit = isEdit
        this.animationHelper = animationHelper
        storageItemViewList.clear()
        setStorageData(itemView, storageList)

        val alpha = if (isEdit) EDIT_VIEW_ALPHA else 1f
        for (storageItemView in storageItemViewList) {
            storageItemView?.alpha = alpha
            storageItemView?.isEnabled = !isEdit
        }
    }

    @Suppress("LongMethod")
    fun setStorageData(itemView: View?, storages: List<Storage>) {
        storages.forEach { storage ->
            when (storage) {
                is Storage.PhoneStorage -> {
                    val storageWrapper = itemView?.findViewById<SideNavigationStorageContainer>(R.id.side_phone_storage)
                    val garbageView = storageWrapper?.findViewById<ImageView>(R.id.right_item)

                    storageWrapper?.visibility = View.VISIBLE
                    val name = stringResource(com.filemanager.common.R.string.string_all_files)
                    val iconRes = if (ModelUtils.isTablet()) {
                        com.filemanager.common.R.drawable.ic_storage_pad
                    } else {
                        com.filemanager.common.R.drawable.ic_storage_phone
                    }
                    setStorageInfo(storageWrapper, name, iconRes, storage.phoneAvailableSize, storage.phoneTotalSize)
                    if (storage.showCleanGarbage) {
                        garbageView?.visibility = View.VISIBLE
                    } else {
                        garbageView?.visibility = View.GONE
                    }
                    val bean = CategoryListBean(CategoryHelper.CATEGORY_FILE_BROWSER, CategoryListBean.TYPE_STORAGE)
                    storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_FILE_BROWSER, DropTag.Type.ITEM_VIEW)
                    val garbage = CategoryListBean(CategoryHelper.CATEGORY_CLEAN_GARBAGE, CategoryListBean.TYPE_STORAGE)
                    onItemViewCallback(storageWrapper, bean, null)
                    onItemViewCallback(garbageView, garbage, null)
                }

                is Storage.OTGStorage -> {
                    val storageWrapper = itemView?.findViewById<SideNavigationStorageContainer>(R.id.side_otg_storage)
                    val resId = com.filemanager.common.R.drawable.ic_otg_icon
                    val bean = CategoryListBean(CategoryHelper.CATEGORY_OTG_BROWSER, CategoryListBean.TYPE_STORAGE)
                    onItemViewCallback(storageWrapper, bean, storage)
                    storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_OTG_BROWSER, DropTag.Type.ITEM_VIEW)

                    if (storage.state == KtConstants.STATE_MOUNTED) {
                        val availableSize = storage.otgSize?.second ?: 0L
                        val totalSize = storage.otgSize?.first ?: 0L
                        setStorageInfo(storageWrapper, "OTG", resId, availableSize, totalSize)
                    } else if (storage.state == KtConstants.STATE_CHECKING) {
                        setStorageCheckState(storageWrapper, resId)
                    } else {
                        storageWrapper?.visibility = View.GONE
                    }
                }

                is Storage.RemoteDeviceStorage -> {
                    val storageWrapper = itemView?.findViewById<View>(R.id.side_remote_device)
                    storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_FILE_REMOTE_MAC, DropTag.Type.ITEM_VIEW_PROHIBIT)
                    if (storage.deviceList.isNullOrEmpty()) {
                        storageWrapper?.visibility = View.GONE
                        remoteDeviceListAdapter?.setData(arrayListOf())
                    } else {
                        setRemoveDeviceList(storageWrapper, storage.deviceList)
                    }
                }

                is Storage.SDCardStorage -> {
                    val storageWrapper = itemView?.findViewById<SideNavigationStorageContainer>(R.id.side_sd_card_storage)
                    storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_SDCARD_BROWSER, DropTag.Type.ITEM_VIEW)
                    val bean = CategoryListBean(CategoryHelper.CATEGORY_SDCARD_BROWSER, CategoryListBean.TYPE_STORAGE)
                    onItemViewCallback(storageWrapper, bean, storage)
                    storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_SDCARD_BROWSER, DropTag.Type.ITEM_VIEW)

                    val resIcon = com.filemanager.common.R.drawable.ic_sd_storage
                    if (storage.state == KtConstants.STATE_MOUNTED) {
                        val name = stringResource(com.filemanager.common.R.string.storage_external)
                        setStorageInfo(storageWrapper, name, resIcon, storage.sdCardAvailableSize, storage.sdCardTotalSize)
                        StatisticsUtils.nearMeStatistics(MyApplication.appContext, StatisticsUtils.SDCARD)
                    } else if (storage.state == KtConstants.STATE_CHECKING) {
                        setStorageCheckState(storageWrapper, resIcon)
                    } else {
                        storageWrapper?.visibility = View.GONE
                    }
                }

                is Storage.DFMStorage -> {
                    val storageWrapper = itemView?.findViewById<SideNavigationStorageContainer>(R.id.side_dfm_storage)
                    if (storage.state == KtConstants.STATE_MOUNTED) {
                        val bean = CategoryListBean(CategoryHelper.CATEGORY_DFM, CategoryListBean.TYPE_STORAGE)
                        onItemViewCallback(storageWrapper, bean, null)
                        storageWrapper?.tag = DropTag(CategoryHelper.CATEGORY_DFM, DropTag.Type.ITEM_VIEW)

                        val name = storage.deviceName
                        val resIcon = if (storage.deviceType == KtConstants.DFM_PAD_TYPE) {
                            com.filemanager.common.R.drawable.ic_dfs_storage_pad
                        } else {
                            com.filemanager.common.R.drawable.ic_dfs_storage_phone
                        }
                        setStorageInfo(storageWrapper, name, resIcon, storage.availableSize, storage.totalSize)
                        StatisticsUtils.showDFMEntry(MyApplication.appContext, storage.deviceName)
                    } else {
                        storageWrapper?.visibility = View.GONE
                    }
                }

                is Storage.CloudDiskStorage -> {
                    val storageWrapper =
                        itemView?.findViewById<SideNavigationItemContainer>(R.id.side_cloud)
                    if (storage.isSingedIn) {
                        val bean = CategoryListBean(
                            CategoryHelper.CATEGORY_CLOUD,
                            CategoryListBean.TYPE_OPTIONS
                        )
                        onItemViewCallback(storageWrapper, bean, null)
                        if (!isDraggingFromMac) {
                            val dropTag = DropTag(CategoryHelper.CATEGORY_CLOUD, DropTag.Type.ITEM_VIEW)
                            dropTag.canPenetrate = false
                            storageWrapper?.tag = dropTag
                        } else {
                            storageWrapper?.tag = null
                        }
                        val name = stringResource(com.filemanager.common.R.string.string_cloud_disk)
                        val iconRes = com.filemanager.common.R.drawable.ic_cloud_file
                        setBaseItemInfo(storageWrapper, name, iconRes)
                    } else {
                        storageWrapper?.visibility = View.GONE
                    }
                }
                else -> {}
            }
        }
    }

    private fun setRemoveDeviceList(storageWrapper: View?, deviceList: List<RemoteDeviceInfo>?) {
        storageWrapper?.let { view ->
            view.visibility = View.VISIBLE
            initRemoteDeviceRecyclerView(storageWrapper)
            val categoryList = ArrayList<CategoryListBean>()
            deviceList?.let { devices ->
                val resId = com.filemanager.common.R.drawable.ic_remote_device
                var categoryType = CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC + 1
                for (device in devices) {
                    val categoryBean = CategoryListBean(categoryType, CategoryListBean.TYPE_OPTIONS)
                    categoryBean.name = device.deviceName ?: ""
                    categoryBean.iconRes = resId
                    categoryBean.groupCategoryType = CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC
                    categoryBean.deviceId = device.deviceId ?: ""
                    categoryBean.deviceStatus = device.deviceStatus
                    categoryBean.deviceIsSameAccount = device.sameAccount
                    categoryList.add(categoryBean)
                    categoryType++
                }
            }
            remoteDeviceListAdapter?.setData(categoryList)
        }
    }

    private fun initRemoteDeviceRecyclerView(storageWrapper: View) {
        if (remoteDeviceRecyclerView != null) {
            return
        }
        remoteDeviceRecyclerView = storageWrapper.findViewById(R.id.recycler_view)
        animationHelper?.let {
            remoteDeviceListAdapter = SideRemoteDeviceListAdapter(context, activity, it) { itemView, sideCategoryBean ->
                    onItemViewCallback(itemView, sideCategoryBean, null)
                }
        }
        val editItemTouchCallback = EditItemTouchCallback(activity, this)
        remoteDeviceRecyclerView?.apply {
            adapter = remoteDeviceListAdapter
            isLongClickable = false
            isUseNativeOverScroll = true
            layoutManager = LinearLayoutManager(context)
            isNestedScrollingEnabled = false
            setOverScrollEnable(false)
            ItemTouchHelper(editItemTouchCallback).attachToRecyclerView(this)
        }
    }

    fun getRemoteDeviceDataList(): List<CategoryListBean>? {
        return remoteDeviceListAdapter?.getData()
    }

    private fun setStorageInfo(storageWrapper: SideNavigationStorageContainer?, name: String, iconRes: Int, availableSize: Long, totalSize: Long) {
        val titleView = storageWrapper?.findViewById<TextView>(R.id.title_item)
        val iconView = storageWrapper?.findViewById<ImageView>(R.id.icon_item)
        val descView = storageWrapper?.findViewById<TextView>(R.id.desc_item)
        val spaceProgress = storageWrapper?.findViewById<HorizontalProgressBar>(R.id.space_progress)

        storageWrapper?.visibility = View.VISIBLE
        titleView?.text = name
        iconView?.setImageDrawable(AppCompatResources.getDrawable(context, iconRes))
        externalStorageHelper.setStorageSize(descView, spaceProgress, availableSize, totalSize, context)

        storageItemViewList.add(storageWrapper)
    }

    private fun setStorageCheckState(storageWrapper: SideNavigationStorageContainer?, iconRes: Int) {
        val titleView = storageWrapper?.findViewById<TextView>(R.id.option_title)
        val iconView = storageWrapper?.findViewById<ImageView>(R.id.option_icon)

        storageWrapper?.visibility = View.VISIBLE
        titleView?.text = stringResource(com.filemanager.common.R.string.sdcard_checking)
        iconView?.setImageDrawable(AppCompatResources.getDrawable(context, iconRes))

        storageItemViewList.add(storageWrapper)
    }

    private fun setBaseItemInfo(storageWrapper: SideNavigationItemContainer?, name: String, iconRes: Int) {
        val titleView = storageWrapper?.findViewById<TextView>(R.id.option_title)
        val iconView = storageWrapper?.findViewById<ImageView>(R.id.option_icon)

        storageWrapper?.visibility = View.VISIBLE
        titleView?.text = name
        titleView?.setTextColor(normalTitleColor)
        iconView?.setImageDrawable(AppCompatResources.getDrawable(context, iconRes))

        storageItemViewList.add(storageWrapper)
    }

    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        //先判断如果是正在从mac拖出文件，只需要处理云盘和远程mac的itemView的alpha，处理完return
        if (MainApi.isDragging(activity)) {
            setRemoteMacItemViewAlpha(EDIT_VIEW_ALPHA)
            if (isDraggingFromMac) {
                setItemViewAlpha(EDIT_VIEW_ALPHA)
            }
            return
        }
        val disableAlpha = EDIT_VIEW_ALPHA
        val alpha = (1 - disableAlpha) * (1 - fraction) + disableAlpha
        for (storageItemView in storageItemViewList) {
            storageItemView?.alpha = alpha
        }
    }

    override fun onEditStateChange(isEdit: Boolean) {
        for (storageItemView in storageItemViewList) {
            storageItemView?.alpha = alpha
            storageItemView?.isEnabled = !isEdit
        }
        this.isEdit = isEdit
    }

    private fun setItemViewAlpha(alpha: Float) {
        val itemCloud = itemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud)
        if (isDraggingFromMac || (!isDraggingFromMac && itemCloud?.alpha != 1f)) {
            itemCloud.alpha = alpha
        }
    }

    fun setRemoteMacItemViewAlpha(alpha: Float) {
        val layoutManager = remoteDeviceRecyclerView?.layoutManager
        if (layoutManager != null) {
            for (i in 0 until layoutManager.childCount) {
                val childView = layoutManager.getChildAt(i)
                childView?.let {
                    val viewHolder = remoteDeviceRecyclerView?.getChildViewHolder(it)
                    // 在这里对ViewHolder执行操作
                    viewHolder?.itemView?.alpha = alpha
                }
            }
        }
    }

    override fun onMacDragStateChange(isMacDragging: Boolean, fraction: Float) {
        val alpha = if (isMacDragging) {
            1f - (1f - EDIT_VIEW_ALPHA) * fraction
        } else {
            EDIT_VIEW_ALPHA + (1f - EDIT_VIEW_ALPHA) * fraction
        }
        val itemCloud = itemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud)
        itemCloud?.tag = if (isDraggingFromMac) {
            null
        } else {
            val dragTag = DropTag(CategoryHelper.CATEGORY_CLOUD, DropTag.Type.ITEM_VIEW)
            dragTag.canPenetrate = false
            dragTag
        }
        setRemoteMacItemViewAlpha(alpha)
        setItemViewAlpha(alpha)
        super.onMacDragStateChange(isMacDragging, fraction)
    }
}