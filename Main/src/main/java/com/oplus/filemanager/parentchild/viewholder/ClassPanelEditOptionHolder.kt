/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ClassPanelEditOptionHolder.kt
 ** Description: Build the holder of the classification
 ** Version: 1.0
 ** Date: 2025/1/23
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.parentchild.viewholder

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.filemanager.main.R
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer

class ClassPanelEditOptionHolder(context: Context, itemView: View) : ClassPanelBaseOptionHolder(context, itemView) {
    private val iconStartMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_start_margin_start)
    private val iconEndMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_end_margin_start)
    private val isRtl = ViewCompat.getLayoutDirection(itemView) == ViewCompat.LAYOUT_DIRECTION_RTL
    private val mark = if (isRtl) 1 else -1

    init {
        val inflater = LayoutInflater.from(context)
        inflater.inflate(R.layout.widget_sidebar_operate, widgetFrame)
    }

    @SuppressLint("ClickableViewAccessibility")
    fun bindData(
        bean: CategoryListBean,
        isEdit: Boolean,
        isDragging: Boolean,
        onClassPanelCountCallback: (type: Int, count: Long) -> Unit,
        onItemViewCallback: (itemView: View, categoryListBean: CategoryListBean) -> Unit,
        onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit
    ) {
        bindData(bean, isEdit, isDragging, onClassPanelCountCallback, onItemViewCallback)
        dragView.visibility = View.VISIBLE
        dragView.setOnLongClickListener {
            if (this.isEdit) {
                onItemDragCallback.invoke(this)
                return@setOnLongClickListener true
            }
            return@setOnLongClickListener false
        }
        dragView.contentDescription = bean.name
        (wrapper as? SideNavigationItemContainer)?.setEditState(this.isEdit)
        val fraction = if (this.isEdit) 1f else 0f
        itemView.isEnabled = !this.isEdit
        onAnimationUpdate(fraction, this.isEdit)
    }

    /**
     * 进入编辑，enterEdit 为true，fraction 0 -> 1变化
     * 退出编辑，enterEdit 为false，fraction 1 -> 0变化
     */
    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        if (isEdit) {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark
            val iconEndValue = 0
            dragView.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
                alpha = fraction
            }
            optionIcon.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
            optionTitle.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
        } else {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark * -1
            val iconEndValue = 0
            dragView.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            dragView.alpha = fraction
            optionIcon.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            optionTitle.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
        }
        dragView.isEnabled = isEdit
    }

    override fun onEditStateChange(isEdit: Boolean) {
        this.isEdit = isEdit
        dragView.isEnabled = isEdit
        itemView.isEnabled = !isEdit
        (wrapper as? SideNavigationItemContainer)?.setEditState(isEdit)
    }
}
