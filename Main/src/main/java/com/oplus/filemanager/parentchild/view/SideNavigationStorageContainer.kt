/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/08/05, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.view

import android.content.Context
import android.graphics.Color
import android.graphics.RectF
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.state.COUIMaskEffectDrawable
import com.coui.appcompat.state.COUIMaskEffectDrawable.MASK_EFFECT_TYPE_CONTAINER_WIDGET
import com.coui.appcompat.state.COUIStateEffectDrawable
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.filemanager.main.R

class SideNavigationStorageContainer : ViewGroup {

    private var childView: View = LayoutInflater.from(context).inflate(R.layout.item_side_storage, null)
    private val layoutBackgroundRect = RectF()
    private var maskDrawable: COUIMaskEffectDrawable? = null
    private var stateEffectDrawable: COUIStateEffectDrawable? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        addView(childView)
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        COUIThemeOverlay.getInstance().applyThemeOverlays(context)
        initBackground()
    }

    private fun initBackground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            defaultFocusHighlightEnabled = false
        }
        maskDrawable = COUIMaskEffectDrawable(context, MASK_EFFECT_TYPE_CONTAINER_WIDGET).apply {
            val radius = context.resources.getDimension(R.dimen.oplus_doc_sidebar_list_item_wrapper_corner_radius)
            setMaskRect(layoutBackgroundRect, radius, radius)
        }
        val drawables = arrayOf(ColorDrawable(Color.TRANSPARENT), maskDrawable)
        stateEffectDrawable = COUIStateEffectDrawable(drawables)
        super.setBackground(stateEffectDrawable)
    }

    override fun setBackground(background: Drawable?) {
        if (stateEffectDrawable != null) {
            if (background == null) {
                stateEffectDrawable?.setViewBackground(ColorDrawable(Color.TRANSPARENT))
            } else {
                stateEffectDrawable?.setViewBackground(background)
            }
        } else {
            super.setBackground(background)
        }
    }

    fun setIsSelected(selected: Boolean, animated: Boolean) {
        maskDrawable?.setTouchEnterStateLocked(selected, selected, animated)
    }

    fun setIsHover(isTouched: Boolean, animated: Boolean) {
        maskDrawable?.setHoverStateLocked(isTouched, isTouched, animated)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val paddingHorizontal = context.resources.getDimension(R.dimen.oplus_doc_sidebar_list_item_wrapper_padding_horizontal)
        layoutBackgroundRect.set(paddingHorizontal, 0F, w - paddingHorizontal, h.toFloat())
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        childView.measure(widthMeasureSpec, heightMeasureSpec)
        val width = childView.measuredWidth
        val height = childView.measuredHeight
        setMeasuredDimension(width, height)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        childView.layout(paddingStart, 0, measuredWidth - paddingEnd, measuredHeight)
    }
}