/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/21, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewmodel

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel

class MainCombineViewModel : BaseViewModel() {

    var showCreateLabelDialogState = MutableLiveData(false)
    var showDeleteLabelDialogState = MutableLiveData<Long>()
}