/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/18, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.animation.addListener
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.isDraggingFromMac
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder.Companion.EDIT_VIEW_ALPHA

class MenuPanelViewHolder(
    private val activity: ComponentActivity,
    itemView: View,
    private val context: Context,
    private val onItemViewCallback: (itemView: View?, categoryListBean: CategoryListBean, storage: Storage?) -> Unit
) : RecyclerView.ViewHolder(itemView), ItemAnimationHelper.AnimationListener {

    companion object {
        const val COLOR_CHANGE_NORMAL = 300L
        const val COLOR_CHANGE_SPECIAL = 100L
    }

    private var isEdit: Boolean = false
    private var isLock: Boolean = false

    private val storageItemViewList = HashSet<View?>()
    private var recycleBinSubTitleView: TextView? = null
    private var lockIcon: ImageView? = null

    private val normalTitleColor = ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_primary_neutral)
    private val subTitleColor = ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_label_tertiary)

    fun bindData(storageList: List<Storage>, recycleBinCount: Long, isEdit: Boolean, isLock: Boolean) {
        this.isEdit = isEdit
        this.isLock = isLock
        storageItemViewList.clear()
        setRecycleBinCount(itemView, recycleBinCount, isLock)
        setPrivateSaveData(itemView, storageList)

        val alpha = if (isEdit) BaseOptionHolder.EDIT_VIEW_ALPHA else 1f
        for (storageItemView in storageItemViewList) {
            storageItemView?.alpha = alpha
            storageItemView?.isEnabled = !isEdit
        }
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            val recycleBinView =
                itemView.findViewById<SideNavigationItemContainer>(R.id.side_recycle_bin)
            recycleBinView.alpha = EDIT_VIEW_ALPHA
            recycleBinView.isEnabled = false
        }
    }

    fun setRecycleBinCount(itemView: View, count: Long, showLockIcon: Boolean) {
        val recycleBinView =
            itemView.findViewById<SideNavigationItemContainer>(R.id.side_recycle_bin)
        if (!FeatureCompat.sIsLightVersion) {

            val bean =
                CategoryListBean(CategoryHelper.CATEGORY_RECYCLE_BIN, CategoryListBean.TYPE_OPTIONS)
            onItemViewCallback(recycleBinView, bean, null)
            recycleBinView?.contentDescription = bean.name
            recycleBinView?.visibility = View.VISIBLE
            if (!MacDragUtil.MacDragObject.isDraggingFromMac) {
                val dropTag = DropTag(CategoryHelper.CATEGORY_RECYCLE_BIN, DropTag.Type.ITEM_VIEW)
                dropTag.canPenetrate = false
                recycleBinView?.tag = dropTag
            } else {
                recycleBinView?.tag = null
            }
            val titleView = recycleBinView?.findViewById<TextView>(R.id.option_title)
            recycleBinSubTitleView = recycleBinView?.findViewById(R.id.option_subtitle)
            val iconView = recycleBinView?.findViewById<ImageView>(R.id.option_icon)
            lockIcon = recycleBinView?.findViewById(R.id.lock_icon)
            val resLockId = com.filemanager.common.R.drawable.privacy_psd_lock_new
            lockIcon?.setImageDrawable(AppCompatResources.getDrawable(context, resLockId))
            lockIcon?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
            titleView?.text = stringResource(com.filemanager.common.R.string.text_recycle_bin)
            titleView?.setTextColor(normalTitleColor)
            recycleBinSubTitleView?.text = "$count"
            recycleBinSubTitleView?.setTextColor(subTitleColor)
            if (isEdit) {
                recycleBinSubTitleView?.setTextColor(normalTitleColor)
                lockIcon?.alpha = 1F
            } else {
                recycleBinSubTitleView?.setTextColor(subTitleColor)
                lockIcon?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
            }
            val resId = R.drawable.ic_recently_deleted_category
            iconView?.setImageDrawable(AppCompatResources.getDrawable(context, resId))
            if (showLockIcon) {
                lockIcon?.visibility = View.VISIBLE
                recycleBinSubTitleView?.visibility = View.GONE
            } else {
                lockIcon?.visibility = View.GONE
                recycleBinSubTitleView?.visibility = View.VISIBLE
            }
            storageItemViewList.add(recycleBinView)
        } else {
            recycleBinView?.visibility = View.GONE
        }
    }

    fun setPrivateSaveData(itemView: View?, storages: List<Storage>) {
        storages.forEach { storage ->
            if (storage is Storage.PrivateSafeStorage) {
                val storageWrapper = itemView?.findViewById<SideNavigationItemContainer>(R.id.side_private_safe)
                val dividerLine = itemView?.findViewById<View>(R.id.divider_line)
                if (storage.isShowing || !FeatureCompat.sIsLightVersion) {
                    dividerLine?.visibility = View.VISIBLE
                } else {
                    dividerLine?.visibility = View.GONE
                }
                if (storage.isShowing) {
                    val bean = CategoryListBean(CategoryHelper.CATEGORY_PRIVATE_SAVE, CategoryListBean.TYPE_OPTIONS)
                    val dropTag = DropTag(CategoryHelper.CATEGORY_PRIVATE_SAVE, DropTag.Type.ITEM_VIEW)
                    dropTag.canPenetrate = false
                    storageWrapper?.tag = dropTag
                    onItemViewCallback(storageWrapper, bean, null)

                    val name = stringResource(com.filemanager.common.R.string.string_encrypt_menu)
                    val iconRes = R.drawable.ic_encrypt_category
                    setBaseItemInfo(storageWrapper, name, iconRes)
                    storageWrapper?.contentDescription = bean.name
                } else {
                    storageWrapper?.visibility = View.GONE
                }
            }
        }
    }

    private fun setBaseItemInfo(storageWrapper: SideNavigationItemContainer?, name: String, iconRes: Int) {
        val titleView = storageWrapper?.findViewById<TextView>(R.id.option_title)
        val iconView = storageWrapper?.findViewById<ImageView>(R.id.option_icon)

        storageWrapper?.visibility = View.VISIBLE
        titleView?.text = name
        titleView?.setTextColor(normalTitleColor)
        iconView?.setImageDrawable(AppCompatResources.getDrawable(context, iconRes))

        storageItemViewList.add(storageWrapper)
    }

    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        //先判断如果是正在从mac拖出文件，只需要处理最近删除的itemView的alpha，处理完return
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            setItemViewAlpha(EDIT_VIEW_ALPHA)
            return
        }
        val disableAlpha = BaseOptionHolder.EDIT_VIEW_ALPHA
        val alpha = (1 - disableAlpha) * (1 - fraction) + disableAlpha
        for (storageItemView in storageItemViewList) {
            storageItemView?.alpha = alpha
        }
    }

    override fun onEditStateChange(isEdit: Boolean) {
        this.isEdit = isEdit
        changeViewColor()
        for (storageItemView in storageItemViewList) {
            storageItemView?.isEnabled = !isEdit
        }
        val animator = ObjectAnimator.ofFloat(lockIcon, "alpha", if (isEdit) 1f else BaseOptionHolder.EDIT_VIEW_ALPHA)
        animator.duration = if (isEdit) COLOR_CHANGE_NORMAL else COLOR_CHANGE_SPECIAL
        animator.addUpdateListener { lockIcon?.alpha = if (isEdit) 1f else BaseOptionHolder.EDIT_VIEW_ALPHA }
        animator.addListener({ lockIcon?.alpha = if (isEdit) 1f else BaseOptionHolder.EDIT_VIEW_ALPHA })
        animator.start()
    }

    override fun onMacDragStateChange(isMacDragging: Boolean, fraction: Float) {
        val alpha = if (isMacDragging) {
            1f - (1f - EDIT_VIEW_ALPHA) * fraction
        } else {
            EDIT_VIEW_ALPHA + (1f - EDIT_VIEW_ALPHA) * fraction
        }
        setItemViewAlpha(alpha)
        super.onMacDragStateChange(isMacDragging, fraction)
    }

    private fun changeViewColor() {
        if (isEdit) {
            colorAnimation(recycleBinSubTitleView, COLOR_CHANGE_NORMAL, normalTitleColor)
        } else {
            colorAnimation(recycleBinSubTitleView, COLOR_CHANGE_SPECIAL, subTitleColor)
        }
    }

    private fun colorAnimation(view: TextView?, continueTime: Long, changeColor: Int) {
        view?.let { v ->
            val animator = ValueAnimator.ofObject(ArgbEvaluator(), v.currentTextColor, normalTitleColor)
            animator.duration = continueTime
            animator.addUpdateListener { animation ->
                v.setTextColor(animation.animatedValue as Int)
            }
            animator.addListener({ v.setTextColor(changeColor) })
            animator.start()
        }
    }
    private fun setItemViewAlpha(alpha: Float) {
        //当只有从远程mac中拖出文件时，才需要改变alpha，从别的地方拖出 不需要
        val recycleBinView = this.itemView.findViewById<SideNavigationItemContainer>(R.id.side_recycle_bin)
        if (alpha < 1f) {
            recycleBinView?.isEnabled = false
        } else {
            recycleBinView?.isEnabled = true
        }
        if (isDraggingFromMac || (!isDraggingFromMac && recycleBinView?.alpha != 1f)) {
            recycleBinView.alpha = alpha
        }
    }
}