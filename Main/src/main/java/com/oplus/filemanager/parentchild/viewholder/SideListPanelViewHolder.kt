/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/09/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.main.R
import com.oplus.filemanager.parentchild.adapter.EditItemTouchCallback
import com.oplus.filemanager.parentchild.adapter.SideCategoryAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper

class SideListPanelViewHolder(
    itemView: View,
    private val context: Context,
    private val activity: ComponentActivity,
    private val onItemViewCallback: (itemView: View, sideCategoryBean: CategoryListBean) -> Unit,
    private val onItemOperateCallback: (bean: CategoryListBean) -> Unit = { },
    private val onItemMoveCallback: (viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) -> Unit = { _, _ -> },
    private val onItemDropCallback: () -> Unit = {}
) : RecyclerView.ViewHolder(itemView) {

    private var recyclerView: COUIRecyclerView? = null
    private var listAdapter: SideCategoryAdapter? = null
    private var editTouchHelper: ItemTouchHelper? = null
    var listType: Int = -1


    private val onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit = { viewHolder ->
        editTouchHelper?.startDrag(viewHolder)
    }

    init {
        recyclerView = itemView.findViewById(R.id.recycler_view)
    }

    fun bindData(newList: ArrayList<CategoryListBean>, animationHelper: ItemAnimationHelper) {
        listAdapter = SideCategoryAdapter(context, activity, animationHelper, onItemViewCallback, onItemOperateCallback, onItemDragCallback)
        recyclerView?.apply {
            adapter = listAdapter
            isLongClickable = false
            isUseNativeOverScroll = true
            layoutManager = LinearLayoutManager(context)
            isNestedScrollingEnabled = false
            setOverScrollEnable(false)
        }
        val editTouchCallback = EditItemTouchCallback(activity, this)
        editTouchCallback.onItemMoveCallback = onItemMoveCallback
        editTouchCallback.onItemDropCallback = onItemDropCallback
        editTouchHelper = ItemTouchHelper(editTouchCallback)
        editTouchHelper?.attachToRecyclerView(recyclerView)
        listAdapter?.setData(newList, listType)
    }

    fun setData(newList: ArrayList<CategoryListBean>) {
        listAdapter?.setData(newList, listType)
    }

    fun updateTargetBean(bean: CategoryListBean) {
        listAdapter?.updateTargetBean(bean)
    }

    fun setItemViewAlpha(alpha: Float) {
        listAdapter?.getAllList()?.forEachIndexed { index, bean ->
            if (bean.categoryType == CategoryHelper.CATEGORY_ADD_LABEL || bean.categoryType == CategoryHelper.CATEGORY_ADD_FOLDER) {
                val viewHolder = recyclerView?.findViewHolderForAdapterPosition(index)
                viewHolder?.itemView?.alpha = alpha
                viewHolder?.itemView?.isEnabled = alpha > 1f
            }
        }
    }
}
