/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MainCompainFragment.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou      2022/10/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.parentchild.ui

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.view.DragEvent
import android.view.Gravity
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.Toast
import androidx.annotation.Px
import androidx.appcompat.app.AlertDialog
import androidx.core.view.ViewCompat
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentContainerView
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.rippleutil.COUIRippleDrawableUtil
import com.coui.appcompat.sidenavigation.COUISideNavigationBar
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_COVER_PATH
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_RELATIVE_PATH
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_MAIN_LABEL
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_RECENT
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.DeleteSoundUtil
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.VibratorUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.NavigationView
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.oplus.filemanager.filelabel.dialog.LabelDialogBean
import com.oplus.filemanager.filelabel.dialog.LabelNameDialog
import com.oplus.filemanager.filelabel.list.LabelFileListFragment
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.BaseMainFragment
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.category.MainCategoryFragment
import com.oplus.filemanager.main.utils.CategoryFragmentFactory
import com.oplus.filemanager.main.view.BottomNavigationAnimController
import com.oplus.filemanager.parentchild.util.GroupCollapseUtil
import com.oplus.filemanager.parentchild.viewmodel.MainCombineViewModel
import com.oplus.filemanager.parentchild.viewmodel.MainParentViewModel
import com.oplus.filemanager.recent.ui.MainRecentFragment
import com.oplus.filemanager.router.RouterUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.function.Consumer

@Suppress("LargeClass")
class MainCombineFragment : BaseMainFragment(), COUISideNavigationBar.COUISideNavigationBarListener, IRefreshFragmentDataForDir {

    companion object {
        private const val TAG = "MainCombineFragment"
        const val TAG_MAIN_CATEGORY = "main_category_fragment"
        const val KEY_CATEGORY_TYPE = "key_category_type"
        const val KEY_PREVIOUS_CATEGORY_TYPE = "key_previous_category_type"
        const val KEY_LIST_CATEGORY_TYPE = "key_list_category_type"
        const val KEY_LIST_CATEGORY_TYPE_SIZE = "key_list_category_type_size"
        const val KEY_CURRENT_SELECTED_REMOTE_DEVICE_ID = "key_current_selected_remote_device_id"
        const val SIDE_NAVIGATION_VIEW_TAG = "SideNavigationView"
        const val LOAD_FILE_DELAY_TIME = 500L

        const val ANIMATION_EASE_TIME = 300L
        const val ANIMATION_TIME = 350L

        private const val DRAWER_STATE_KEY = "drawer_state_key"
    }

    var isOutToRemoteMac: Boolean = false
    /**
     * 是否需要切换到文档分类
     */
    var needSetTypeDoc: Boolean = false
    /**
     * 是否需要切换到最近删除分类
     */
    var needSetTypeRecBin: Boolean = false

    /**
     * 是否需要切换到来源目录下
     */
    var needSetTypeSuper: Boolean = false
    var setSuperCategoryType = 0
    var externalSuperPath = ""
    var mainFragment: Fragment? = null
    var secondFragment: Fragment? = null
    var categoryType: Int = CategoryHelper.CATEGORY_RECENT
    var viewModel: MainCombineViewModel? = null
    private var listFragments = ArrayList<Int>()
    private lateinit var sideNavigationContainer: COUISideNavigationBar
    private var sideExpandIcon: ImageView? = null
    private var masterFragmentContainer: FragmentContainerView? = null
    private var detailFragmentContainer: FragmentContainerView? = null
    private var detailContainer: FrameLayout? = null
    private var containerMaskView: ImageView? = null
    private val navigationControllerForChild by lazy { NavigationController(lifecycle, id = R.id.navigation_tool_for_child) }
    private var isHalfScreen = true
    private var previousCategory: Int = -1
    private var mSavedInstanceState: Bundle? = null
    private var navigationToolForChild: NavigationView? = null
    private var handler: Handler? = Handler(Looper.getMainLooper())
    private var isLoadAllFile = false
    private var drawerSavedState: Boolean = false
    private var mRootView: ViewGroup? = null
    private var createLabelDialog: LabelNameDialog? = null
    private var deleteLabelDialog: AlertDialog? = null
    private var deleteShortcutFolderDialog: AlertDialog? = null
    private var isEdit = false
    private var shortCutFolderCurrentPath = ""

    var onCreateLabelCallback: (newLabelName: String) -> Unit = {}
    var onDeleteLabelCallback: (labelId: Long) -> Unit = {}
    var checkLabelMaxCountCallback: () -> Boolean = { false }

    private var isDoingGridAnim = false
    private var isDoingOpenOrClosePreviewAnim = false

    private var coverAnimaView: View? = null
    private var coverIconLeft: View? = null
    private var coverSeparatorLine: View? = null
    private var coverIconRight: View? = null
    private val easeInterpolator = COUIEaseInterpolator()
    private val moveInterpolator = COUIMoveEaseInterpolator()

    private val documentExtensionType: IDocumentExtensionType? by lazy {
        Injector.injectFactory<IDocumentExtensionType>()
    }

    /**
     * 当前选择远程设备页面的设备Id
     */
    var currentSelectedRemoteDeviceId: String? = null

    override fun onResumeLoadData() {
        if ((activity as? MainActivity) != null) {
            isOutToRemoteMac = (activity as MainActivity).isOutJumpSuperRemotePC
        }
        Log.d(TAG, "onResumeLoadData")
        mainFragment?.let {
            if (it is BaseMainFragment) {
                Log.d(TAG, "parent onResumeLoadData")
                if (mainFragment is MainParentFragment) {
                    (it as? MainParentFragment)?.onResumeLoadDataFromParent()
                } else {
                    it.onResumeLoadData()
                }
            }
        }
        secondFragment?.let {
            if (it is MainCategoryFragment) {
                Log.d(TAG, "mainCategory $it onResumeLoadData")
                it.ensureReloadData()
            } else if (it is BaseMainFragment) {
                Log.d(TAG, "main $it onResumeLoadData")
                it.onResumeLoadData()
            } else {
                Log.d(TAG, "child $it onResumeLoadData")
                if (PermissionUtils.hasStoragePermission()) {
                    RouterUtil.onResumeLoadData(it, categoryType)
                }
            }
        }
        if (PermissionUtils.hasStoragePermission() && !isLoadAllFile) {
            Log.d(TAG, "LoadAllFile")
            val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
            handler?.postDelayed({
                fileOpenTimeAction?.loadAllFile(parentFragment ?: this)
            }, LOAD_FILE_DELAY_TIME)
            isLoadAllFile = true
        }
    }

    override fun onResume() {
        super.onResume()
        val showRenameLabelDialog =
            PreferencesUtils.getBoolean(key = Constants.FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG)
        Log.d(TAG, "onResume showRenameLabelDialog = $showRenameLabelDialog")
        if (showRenameLabelDialog) {
            PreferencesUtils.put(
                key = Constants.FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG,
                value = false
            )
            setShowAddLabelDialogState(true)
        }
    }

    override fun onTabSelected() {
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        secondFragment?.let {
            if (it is BaseMainFragment) {
                return it.onMenuItemSelected(item)
            }
            return RouterUtil.onMenuItemSelected(it, item, categoryType)
        }
        return false
    }

    override fun fromActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        secondFragment?.let {
            if (it is BaseMainFragment) {
                return it.onNavigationItemSelected(item)
            }
            return RouterUtil.onNavigationItemSelected(it, item, categoryType)
        }
        return false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        drawerSavedState = savedInstanceState?.getBoolean(DRAWER_STATE_KEY, false) ?: false
        bindFragments()
    }

    override fun getLayoutResId(): Int {
        return R.layout.main_combine_fragment
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView(view: View) {
        mRootView = view.findViewById(R.id.root_view)
        sideNavigationContainer = view.findViewById(R.id.side_navigation_container)
        masterFragmentContainer = view.findViewById(R.id.fragment_container_view_master)
        detailFragmentContainer = view.findViewById(R.id.fragment_container_view_detail)
        detailContainer = view.findViewById(R.id.fragment_container_view_detail_container)
        navigationToolForChild = view.findViewById(R.id.navigation_tool_for_child)
        containerMaskView = view.findViewById(R.id.container_mask_view)
        containerMaskView?.setOnTouchListener { _, _ ->
            return@setOnTouchListener isEdit
        }
        (activity as? MainActivity)?.updateWindowInsets()
        sideNavigationContainer.apply {
            doOnLayout {
                val isSideNavigationOpen = GroupCollapseUtil.isSideNavigationOpen()
                if (!isSideNavigationOpen || WindowUtils.isSmallScreen(context)) {
                    sideNavigationContainer.closeDrawer(sideNavigationContainer.drawerView, false)
                    updateSideNavigationStatus(KtConstants.SIDE_NAVIGATION_CLOSE)
                } else {
                    sideNavigationContainer.openDrawer(sideNavigationContainer.drawerView, false)
                    updateSideNavigationStatus(KtConstants.SIDE_NAVIGATION_OPEN)
                }
            }
            isParentChildHierarchy = false
            handlerEditModeMask = false
            addSideNavigationBarListener(this@MainCombineFragment)
        }
        initSideExpandIcon(view)
        //切换暗亮色模式，确保toolbar的MarginStart
        adapterFragmentsToolbar()
        baseVMActivity?.sideNavigationContainer = sideNavigationContainer

        coverAnimaView = view.findViewById(R.id.cover_anima_view)
        coverAnimaView?.setOnClickListener {}
        coverIconLeft = view.findViewById(R.id.cover_icon_left)
        coverSeparatorLine = view.findViewById(R.id.cover_separator_line)
        coverIconRight = view.findViewById(R.id.cover_icon_right)
    }

    private fun updateSideNavigationStatus(sideNavigationStatus: Int) {
        baseVMActivity?.let {
            FileImageVHUtils.changedListMargin(it, sideNavigationContainer.drawerViewWidth, sideNavigationStatus)
            it.sideNavigationStatus.value = sideNavigationStatus
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
    }

    override fun startObserve() {
        //创建弹窗
        startShowDialogObserve()
    }

    override fun pressBack(): Boolean {
        Log.d(TAG, "pressBack 1 secondFragment $secondFragment categoryType $categoryType")
        var result = if (secondFragment is MainCategoryFragment) {
            (secondFragment as MainCategoryFragment).pressBack()
        } else {
            (mainFragment as? MainParentFragment)?.pressBack() ?: false
        }
        if (result) {
            return true
        }
        Log.d(TAG, "pressBack 2 secondFragment $secondFragment categoryType $categoryType")
        result = if (secondFragment != null) {
            RouterUtil.pressBack(secondFragment!!, categoryType)
        } else {
            false
        }
        if (result) {
            return true
        }
        Log.d(TAG, "pressBack 3 second:$secondFragment categoryType $categoryType")
        if (secondFragment !is MainCategoryFragment) {
            return backToShowCategoryFragment()
        }
        return false
    }

    private fun startShowDialogObserve() {
        Log.e(TAG, "startShowDialogObserve")
        viewModel?.showCreateLabelDialogState?.observe(this) {
            Log.d(TAG, "showCreateLabelDialogState show create Dialog $it")
            if (it) {
                showAddLabelDialog()
            }
        }
        viewModel?.showDeleteLabelDialogState?.observe(this) { labelId ->
            Log.d(TAG, "showDeleteLabelDialogState show delete Dialog -> showDialog labelId  $labelId")
            if (labelId != null) {
                showDeleteLabelDialog(labelId)
            }
        }
    }

    private fun initSideExpandIcon(view: View) {
        sideExpandIcon = view.findViewById(R.id.side_navigation_icon)
        sideExpandIcon?.setOnClickListener {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@setOnClickListener
            }
            val context = baseVMActivity ?: return@setOnClickListener
            if (WindowUtils.isSmallScreen(context)) {
                Log.d(TAG, "small screen expand onClick return !!")
                return@setOnClickListener
            }
            if (sideNavigationContainer.isDrawerOpening) {
                //侧导收起
                handleSideNavigationCloseAnim()
            } else {
                //侧导展开
                handleSideNavigationExpandAnim()
            }
            StatisticsUtils.onCommon(
                appContext, StatisticsUtils.PAD_SLIDE_EXPAND_CLICK_EVENT, mapOf(
                    StatisticsUtils.CLICK_STATE_KEY to if (sideNavigationContainer.isDrawerOpening) {
                        StatisticsUtils.OPEN_VALUE
                    } else {
                        StatisticsUtils.CLOSE_VALUE
                    }
                )
            )
        }
        sideExpandIcon?.apply {
            val radius = COUIMaskRippleDrawable.getMaskRippleRadiusByType(context, COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS)
            COUIRippleDrawableUtil.setIconPressRippleDrawable(this, radius)
        }

        val context = baseVMActivity ?: return
        val statusBarHeight = COUIPanelMultiWindowUtils.getStatusBarHeight(context)
        val expandParams = sideExpandIcon?.layoutParams as? ViewGroup.MarginLayoutParams
        expandParams?.let {
            it.topMargin = statusBarHeight
            it.marginStart = resources.getDimensionPixelOffset(R.dimen.oplus_doc_sidebar_icon_expanded_margin_start_medium)
            sideExpandIcon?.layoutParams = it
        }
    }

    private fun handleSideNavigationExpandAnim() {
        if (isNeedClosePreview()) {
            if (!isDoingOpenOrClosePreviewAnim) {
                doClosePreviewAnimation()
                Log.d(TAG, "handleSideNavigationExpandAnim ClosePreview setToolbarVisibility")
                (mainFragment as? MainParentFragment)?.setToolbarVisibility(View.VISIBLE)
            }
            return
        }
        if (!onSideNavigationClicked(true)) {
            sideNavigationContainer.open()
            GroupCollapseUtil.setSideNavigationState(true)
            (mainFragment as? MainParentFragment)?.setToolbarVisibility(View.VISIBLE)
        } else if (!isDoingGridAnim) {
            isDoingGridAnim = true
            GroupCollapseUtil.setSideNavigationState(true)
            (mainFragment as? MainParentFragment)?.setToolbarVisibility(View.VISIBLE)
        }
    }

    private fun handleSideNavigationCloseAnim() {
        if (isNeedOpenPreview()) {
            if (!isDoingOpenOrClosePreviewAnim) {
                doOpenPreviewAnimation()
            }
            return
        }

        if (!onSideNavigationClicked(false)) {
            sideNavigationContainer.close()
            GroupCollapseUtil.setSideNavigationState(false)
            (mainFragment as? MainParentFragment)?.setToolbarVisibility(View.INVISIBLE)
        } else if (!isDoingGridAnim) {
            isDoingGridAnim = true
            GroupCollapseUtil.setSideNavigationState(false)
            (mainFragment as? MainParentFragment)?.setToolbarVisibility(View.INVISIBLE)
        }
    }

    private fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        val result = if (secondFragment != null) {
            RouterUtil.onSideNavigationClicked(secondFragment!!, categoryType, isOpen)
        } else {
            false
        }
        return result
    }

    private fun isNeedOpenPreview(): Boolean {
        val prevFragment = secondFragment as? PreviewCombineFragment
        prevFragment?.let {
            return it.isSupportPreview() && it.isLargeScreen() && it.isListMode() && !it.isSelectionMode()
                    && it.isRemoteDeviceNotConnected().not()
        }
        return false
    }

    private fun isNeedClosePreview(): Boolean {
        val prevFragment = secondFragment as? PreviewCombineFragment
        prevFragment?.let {
            return it.isPreviewOpen()
        }
        return false
    }

    /**
     *侧导收起，开启预览的图标遮盖动画
     */
    private fun doOpenPreviewAnimation() {
        isDoingOpenOrClosePreviewAnim = true
        var screenWidth = 0f
        coverAnimaView?.visibility = View.VISIBLE
        baseVMActivity?.let {
            screenWidth = KtViewUtils.getWindowSize(it).x.toFloat()
        }
        val coverDisappearAnimSet = createCoverDisappearAnimSet()
        coverDisappearAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                sideNavigationContainer.alpha = 0f
            }
            override fun onAnimationEnd(animation: Animator) {
                coverAnimaView?.visibility = View.GONE
                coverDisappearAnimSet.removeAllListeners()
                isDoingOpenOrClosePreviewAnim = false
            }
        })
        val iconWidth = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_56dp)
        val sideNavigationWidth = sideNavigationContainer.drawerViewWidth.toFloat()
        val detailContainerWidth = screenWidth - sideNavigationWidth
        var coverSeparatorLineToTranslationX = 0f
        var coverIconLeftToTranslationX = 0f
        var coverIconRightToTranslationX = 0f
        val isRTL: Boolean = ViewCompat.getLayoutDirection(sideNavigationContainer) == ViewCompat.LAYOUT_DIRECTION_RTL
        if (isRTL) {
            coverSeparatorLine?.translationX = detailContainerWidth
            coverIconLeft?.translationX = detailContainerWidth + (sideNavigationWidth / 2 - (iconWidth / 2))
            coverIconRight?.translationX = detailContainerWidth / 2 - (iconWidth / 2)
            coverSeparatorLineToTranslationX = screenWidth
            coverIconLeftToTranslationX = (sideNavigationWidth / 2 - (iconWidth / 2)) + screenWidth
            coverIconRightToTranslationX = screenWidth / 2 - (iconWidth / 2)
        } else {
            coverSeparatorLine?.translationX = -detailContainerWidth
            coverIconLeft?.translationX = -(detailContainerWidth + (sideNavigationWidth / 2 - (iconWidth / 2)))
            coverIconRight?.translationX = -(detailContainerWidth / 2 - (iconWidth / 2))
            coverSeparatorLineToTranslationX = -screenWidth
            coverIconLeftToTranslationX = -(sideNavigationWidth / 2 - (iconWidth / 2)) - screenWidth
            coverIconRightToTranslationX = -(screenWidth / 2 - (iconWidth / 2))
        }
        val coverMoveAnimSet = createCoverMoveAnimSet(coverSeparatorLineToTranslationX, coverIconLeftToTranslationX, coverIconRightToTranslationX)
        coverMoveAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                coverDisappearAnimSet.start()
                coverMoveAnimSet.removeAllListeners()
            }
        })
        val coverAppearAnimSet = createCoverAppearAnimSet()
        coverAppearAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onSideNavigationClicked(false)
                sideNavigationContainer.closeDrawer(sideNavigationContainer.drawerView, false)
                GroupCollapseUtil.setSideNavigationState(false)
                coverMoveAnimSet.start()
                coverAppearAnimSet.removeAllListeners()
            }
        })
        coverAppearAnimSet.start()
    }

    /**
     *侧导展开，关闭预览的图标遮盖动画
     */
    private fun doClosePreviewAnimation() {
        isDoingOpenOrClosePreviewAnim = true
        var screenWidth = 0f
        coverAnimaView?.visibility = View.VISIBLE
        baseVMActivity?.let {
            screenWidth = KtViewUtils.getWindowSize(it).x.toFloat()
        }
        val coverDisappearAnimSet = createCoverDisappearAnimSet()
        coverDisappearAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                sideNavigationContainer.alpha = 0f
            }
            override fun onAnimationEnd(animation: Animator) {
                coverAnimaView?.visibility = View.GONE
                isDoingOpenOrClosePreviewAnim = false
            }
        })
        val iconWidth = coverIconLeft?.width?.toFloat() ?: 0F
        val sideNavigationWidth = sideNavigationContainer.drawerViewWidth.toFloat()
        val detailContainerWidth = screenWidth - sideNavigationWidth

        var coverSeparatorLineToTranslationX = 0f
        var coverIconLeftToTranslationX = 0f
        var coverIconRightToTranslationX = 0f
        val isRTL: Boolean = ViewCompat.getLayoutDirection(sideNavigationContainer) == ViewCompat.LAYOUT_DIRECTION_RTL
        if (isRTL) {
            coverSeparatorLine?.translationX = screenWidth
            coverIconLeft?.translationX = (sideNavigationWidth / 2 - (iconWidth / 2)) + screenWidth
            coverIconRight?.translationX = screenWidth / 2 - (iconWidth / 2)
            coverSeparatorLineToTranslationX = detailContainerWidth
            coverIconLeftToTranslationX = detailContainerWidth + (sideNavigationWidth / 2 - (iconWidth / 2))
            coverIconRightToTranslationX = detailContainerWidth / 2 - (iconWidth / 2)
        } else {
            coverSeparatorLine?.translationX = -screenWidth
            coverIconLeft?.translationX = -(sideNavigationWidth / 2 - (iconWidth / 2)) - screenWidth
            coverIconRight?.translationX = -(screenWidth / 2 - (iconWidth / 2))
            coverSeparatorLineToTranslationX = -detailContainerWidth
            coverIconLeftToTranslationX = -(detailContainerWidth + (sideNavigationWidth / 2 - (iconWidth / 2)))
            coverIconRightToTranslationX = -(detailContainerWidth / 2 - (iconWidth / 2))
        }
        val coverMoveAnimSet = createCoverMoveAnimSet(coverSeparatorLineToTranslationX, coverIconLeftToTranslationX, coverIconRightToTranslationX)
        coverMoveAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                coverDisappearAnimSet.start()
                coverMoveAnimSet.removeAllListeners()
            }
        })
        val coverAppearAnimSet = createCoverAppearAnimSet()
        coverAppearAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onSideNavigationClicked(true)
                sideNavigationContainer.openDrawer(sideNavigationContainer.drawerView, false)
                GroupCollapseUtil.setSideNavigationState(true)
                coverMoveAnimSet.start()
                coverAppearAnimSet.removeAllListeners()
            }
        })
        coverAppearAnimSet.start()
    }

    private fun createCoverAppearAnimSet(): AnimatorSet {
        val coverAppearAnim = ObjectAnimator.ofFloat(coverAnimaView, "alpha", 0f, 1f)
        val contentDisappearAnim = ObjectAnimator.ofFloat(sideNavigationContainer, "alpha", 1f, 0f)
        val animViewAppearAnimSet = AnimatorSet().apply {
            interpolator = easeInterpolator
            duration = ANIMATION_EASE_TIME
            playTogether(coverAppearAnim, contentDisappearAnim)
        }
        return animViewAppearAnimSet
    }

    private fun createCoverMoveAnimSet(separatorLineToX: Float, iconLeftToX: Float, iconRightToX: Float): AnimatorSet {
        val separatorLineAnim = coverSeparatorLine?.let {
            ObjectAnimator.ofFloat(
                it,
                "translationX",
                it.translationX,
                separatorLineToX
            ).apply {
                interpolator = moveInterpolator
            }
        }

        val coverIconLeftAnim = coverIconLeft?.let {
            ObjectAnimator.ofFloat(
                it,
                "translationX",
                it.translationX,
                iconLeftToX
            ).apply {
                interpolator = moveInterpolator
            }
        }

        val coverIconRightAnim = coverIconRight?.let {
            ObjectAnimator.ofFloat(
                it,
                "translationX",
                it.translationX,
                iconRightToX
            ).apply {
                interpolator = moveInterpolator
            }
        }
        val coverMoveAnimSet = AnimatorSet().apply {
            duration = ANIMATION_TIME
            interpolator = moveInterpolator
            playTogether(separatorLineAnim, coverIconLeftAnim, coverIconRightAnim)
        }
        return coverMoveAnimSet
    }

    private fun createCoverDisappearAnimSet(): AnimatorSet {
        val alphaAnimViewDisappear = ObjectAnimator.ofFloat(coverAnimaView, "alpha", 1f, 0f)
        val alphaContentAppear = ObjectAnimator.ofFloat(sideNavigationContainer, "alpha", 0f, 1f)
        val animViewDisappearAnimSet = AnimatorSet().apply {
            interpolator = easeInterpolator
            duration = ANIMATION_EASE_TIME
            playTogether(alphaAnimViewDisappear, alphaContentAppear)
        }
        return animViewDisappearAnimSet
    }

    /**
     * 如果当前是大屏切换到小屏，当前显示的是ChildFragment是回退栈中的最后一个
     * 按下back键时，需要显示MainCategoryFragment
     */
    fun backToShowCategoryFragment(isEdit: Boolean = false): Boolean {
        if (UIConfigMonitor.isCurrentSmallScreen()) {
            // 切换为MainCategoryFragment，有可能是新建的，需要延时重新加载数据
            CategoryFragmentFactory.switchToCategoryFragment(this, isEdit)
            categoryType = CategoryHelper.CATEGORY_MAIN
            listFragments.clear()
            mRootView?.post {
                (secondFragment as? MainCategoryFragment)?.ensureReloadData()
                (secondFragment as? MainCategoryFragment)?.onResumeLoadData()
                if (isEdit) {
                    (secondFragment as? MainCategoryFragment)?.setEditState()
                }
            }
            hideNavigationDefault(categoryType)
            baseVMActivity.let {
                if (it is MainActivity) {
                    it.setNavigationTabVisible()
                }
            }
            return true
        }
        return false
    }

    fun backToShowCategoryFragmentInBackground(): Boolean {
        if (UIConfigMonitor.isCurrentSmallScreen()) {
            // 切换为MainCategoryFragment，有可能是新建的，需要延时重新加载数据
            CategoryFragmentFactory.switchToCategoryFragment(this, false)
            categoryType = CategoryHelper.CATEGORY_MAIN
            listFragments.clear()
            mRootView?.post {
                (secondFragment as? MainCategoryFragment)?.ensureReloadData()
            }
            hideNavigationDefault(categoryType)
            baseVMActivity.let {
                if (it is MainActivity) {
                    it.setNavigationTabVisible()
                }
            }
            return true
        }
        return false
    }

    override fun getViewModel(): ViewModel? {
        return null
    }

    override fun getRecyclerView(): RecyclerView? {
        return null
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        Log.d(TAG, "onUIConfigChanged mainFragment $mainFragment")
        configList.forEach {
            if (it is ScreenSizeConfig) {
                CategoryFragmentFactory.switch(this, categoryType, isEdit, isAddDialogIsShowing())
            }
        }
        if (categoryType == CategoryHelper.CATEGORY_RECENT) {
            sideNavigationContainer.post {
                calculateBorder(sideNavigationContainer.drawerView)
            }
        }
        mainFragment?.let {
            if (it is MainParentFragment) {
                it.onUIConfigChanged(configList)
            }
        }
        secondFragment?.let {
            if (it is BaseVMFragment<*>) {
                it.onUIConfigChanged(configList)
            }
        }
        for (i in 0 until listFragments.size) {
            val it = childFragmentManager.findFragmentByTag(getFragmentTag(listFragments[i]))
            if (it is BaseVMFragment<*>) {
                it.onUIConfigChanged(configList)
            }
        }
        mRootView?.post {
            adjustFragmentsToolbar(isHalfScreen)
            updateLabelCategoryType()
        }
    }

    /**
     * 小屏转大屏时，由于在标签页面添加标签，会更改标签的顺序，导致标签类别的categoryType重新复制，需要在转屏时重新大屏侧导的标签类别选中项，否则选中结果或出错
     */
    private fun updateLabelCategoryType() {
        val screenState = UIConfigMonitor.getCurrentScreenState()
        // 从小屏切换到大屏
        if (screenState == UIConfigMonitor.SCREEN_SMALL_TO_LARGE) {
            val prevFragment = (secondFragment as? PreviewCombineFragment)?.getPreviewFragment()
            val labelId = (prevFragment as? LabelFileListFragment)?.getLabelId()
            if (labelId != null) {
                (mainFragment as? MainParentFragment)?.updateLabelCategoryType(labelId)
            }
        }
    }

    override fun onDestroy() {
        listFragments.clear()
        handler?.removeCallbacksAndMessages(null)
        secondFragment = null
        baseVMActivity?.sideNavigationContainer = null
        super.onDestroy()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        savedInstanceState?.let {
            categoryType = it.getInt(KEY_CATEGORY_TYPE, CategoryHelper.CATEGORY_RECENT)
            previousCategory = it.getInt(KEY_PREVIOUS_CATEGORY_TYPE, -1)
            val size = it.getInt(KEY_LIST_CATEGORY_TYPE_SIZE, 0)
            if (size > 0) {
                for (i in 0 until size) {
                    val value = it.getInt(KEY_LIST_CATEGORY_TYPE + i, -1)
                    if (value > 0) {
                        listFragments.add(value)
                    }
                }
            }
            currentSelectedRemoteDeviceId = it.getString(KEY_CURRENT_SELECTED_REMOTE_DEVICE_ID, "")
            mSavedInstanceState = it
        }
        super.onCreate(savedInstanceState)
        baseVMActivity?.let {
            viewModel = ViewModelProvider(it)[MainCombineViewModel::class.java]
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(KEY_CATEGORY_TYPE, categoryType)
        outState.putInt(KEY_PREVIOUS_CATEGORY_TYPE, previousCategory)
        outState.putBoolean(DRAWER_STATE_KEY, true)
        if (listFragments.isNotEmpty()) {
            outState.putInt(KEY_LIST_CATEGORY_TYPE_SIZE, listFragments.size)
            for (i in 0 until listFragments.size) {
                outState.putInt(KEY_LIST_CATEGORY_TYPE + i, listFragments[i])
            }
        }
        outState.putString(KEY_CURRENT_SELECTED_REMOTE_DEVICE_ID, currentSelectedRemoteDeviceId)
        super.onSaveInstanceState(outState)
    }


    /**
     * 设置是否显示侧导
     * @param showMaster 是否显示左边侧导，小屏不显示，大屏显示
     * @param showDetail 是否显示右边子页面，目前都为 true
     */
    fun setContainerVisible(showMaster: Boolean, showDetail: Boolean) {
        isHalfScreen = showMaster && showDetail
        masterFragmentContainer?.isVisible = showMaster
        detailContainer?.isVisible = showDetail
        if (showMaster) {
            //展开侧导
            sideExpandIcon?.visibility = View.VISIBLE
            detailContainer?.layoutParams?.width = 0
            //大小屏转屏时，需要记忆上次侧导展开关闭情况
            val isSideNavigationOpen = GroupCollapseUtil.isSideNavigationOpen()
            baseVMActivity?.sideNavigationStatus?.value =
                if (isSideNavigationOpen) KtConstants.SIDE_NAVIGATION_OPEN else KtConstants.SIDE_NAVIGATION_CLOSE
            if (isSideNavigationOpen) {
                sideNavigationContainer.openDrawer(sideNavigationContainer.drawerView, false)
            } else {
                sideNavigationContainer.closeDrawer(sideNavigationContainer.drawerView, false)
            }
            selectCategoryTypeItem(categoryType)
        } else {
            //关闭侧导
            sideExpandIcon?.visibility = View.GONE
            detailContainer?.layoutParams?.width = ViewGroup.LayoutParams.MATCH_PARENT
            sideNavigationContainer.closeDrawer(sideNavigationContainer.drawerView, false)
        }

        /**
         * 左右都显示，显示底部导航栏
         * 有右边显示时，显示底部导航栏
         * 只有左边显示时，不显示底部导航栏
         */
        val screenState = UIConfigMonitor.getCurrentScreenState()
        if (screenState != UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE) {

            // 通知子fragment 当前是否处于半屏
            secondFragment?.let {
                RouterUtil.setIsHalfScreen(it, categoryType, isHalfScreen)
            }
        }
    }

    private fun bindFragments() {
        if (mSavedInstanceState != null) {
            val currentWindowType = WindowUtils.getCurrentWindowType(activity)
            val screenChangeState = UIConfigMonitor.getCurrentScreenState()
            Log.d(TAG, "currentWindowType $currentWindowType screenChangeState $screenChangeState")
            if (UIConfigMonitor.isCurrentSmallScreen() || currentWindowType == WindowUtils.SMALL) {
                if (categoryType == CategoryHelper.CATEGORY_MAIN) {
                    secondFragment = childFragmentManager.findFragmentByTag(getFragmentTag()) ?: CategoryFragmentFactory.create()
                } else {
                    activity?.let {
                        secondFragment = childFragmentManager.findFragmentByTag(getFragmentTag()) ?: RouterUtil.getFragment(it, categoryType)
                    }
                }
                setContainerVisible(showMaster = false, showDetail = true)
            } else {
                val mainFragment = childFragmentManager.findFragmentByTag(TAG_MAIN_CATEGORY)
                when (mainFragment) {
                    is MainParentFragment -> {
                        mainFragment.arguments?.putInt(KtConstants.P_SELECT_CATEGORY_TYPE, categoryType)
                        this.mainFragment = mainFragment
                        setContainerVisible(showMaster = true, showDetail = true)
                        // 父子布局时，才创建第二个fragment
                        activity?.let {
                            //bug6100645 页面重建时，如果是父子布局，子级是CATEGORY_MAIN时需要更换为CATEGORY_RECENT
                            if (categoryType == CategoryHelper.CATEGORY_MAIN) {
                                Log.d(TAG, "bindFragments second is CATEGORY_MAIN")
                                categoryType = CategoryHelper.CATEGORY_RECENT
                                selectCategoryTypeItem(categoryType)
                                setCurrentChildFragment(CategoryHelper.CATEGORY_RECENT, null)
                            } else {
                                secondFragment = childFragmentManager.findFragmentByTag(getFragmentTag())
                                if (secondFragment == null) {
                                    secondFragment = RouterUtil.getFragment(it, categoryType)
                                    initFragment()
                                }
                            }
                        }
                    }

                    is MainCategoryFragment -> {
                        categoryType = CategoryHelper.CATEGORY_MAIN
                        secondFragment = mainFragment
                        setContainerVisible(showMaster = false, showDetail = true)
                    }

                    null -> {
                        setCurrentChildFragment(CategoryHelper.CATEGORY_RECENT, null)
                        ensureParentFragment()
                    }
                }
            }
            baseVMActivity.let {
                if (it is MainActivity) {
                    it.setNavigationTabVisible()
                }
            }
        } else {
            val mainFragment = childFragmentManager.findFragmentByTag(TAG_MAIN_CATEGORY) ?: CategoryFragmentFactory.create()
            if (mainFragment is MainParentFragment) {
                mainFragment.arguments?.putInt(KtConstants.P_SELECT_CATEGORY_TYPE, categoryType)
                this.mainFragment = mainFragment
                childFragmentManager.beginTransaction().replace(R.id.fragment_container_view_master, mainFragment, TAG_MAIN_CATEGORY).commit()
                setContainerVisible(showMaster = true, showDetail = true)
                // 父子布局时，才创建第二个fragment
                activity?.let {
                    secondFragment = childFragmentManager.findFragmentByTag(getFragmentTag()) ?: RouterUtil.getFragment(it, categoryType)
                    if (secondFragment is Fragment) {
                        initFragment()
                        childFragmentManager.beginTransaction().replace(R.id.fragment_container_view_detail, secondFragment!!, getFragmentTag())
                            .commit()
                    }
                }
            } else if (mainFragment is MainCategoryFragment) {
                categoryType = CategoryHelper.CATEGORY_MAIN
                secondFragment = mainFragment
                childFragmentManager.beginTransaction().replace(R.id.fragment_container_view_detail, secondFragment!!, getFragmentTag()).commit()
                setContainerVisible(showMaster = false, showDetail = true)
            }
        }
        Log.d(TAG, "bindFragments main:$mainFragment second:$secondFragment")
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        secondFragment?.let {
            if (it is BaseMainFragment) {
                it.onCreateOptionsMenu(menu, inflater)
            } else {
                RouterUtil.onCreateOptionsMenu(it, menu, inflater, categoryType)
            }
        }
        mainFragment?.onCreateOptionsMenu(menu, inflater)
        super.onCreateOptionsMenu(menu, inflater)
    }

    private fun initFragment() {
        updateChildFragmentBundle(null)
    }

    private fun updateBrowserFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        val path: String
        if (externalBundle == null) {
            path = Environment.getExternalStorageDirectory().absolutePath
            bundle.putString(KtConstants.P_CURRENT_PATH, path)
            bundle.putBoolean(KtConstants.FROM_DETAIL, false)
            bundle.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, false)
        } else {
            path = externalBundle.getString(KtConstants.CURRENT_DIR, "")
            bundle.putString(KtConstants.P_CURRENT_PATH, path)
            bundle.putBoolean(KtConstants.FROM_DETAIL, externalBundle.getBoolean(KtConstants.FROM_DETAIL))
            bundle.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, externalBundle.getBoolean(KtConstants.FROM_SHORTCUT_FOLDER))
        }
        secondFragment?.let {
            RouterUtil.setCurrentFilePath(it, CategoryHelper.CATEGORY_FILE_BROWSER, path)
        }
    }

    private fun updateAudioVideoFragmentData(bundle: Bundle) {
        if (categoryType == CategoryHelper.CATEGORY_VIDEO) {
            bundle.putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_videos)
        } else {
            bundle.putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_audio)
        }
        bundle.putInt(KtConstants.P_CATEGORY_TYPE, categoryType)
        bundle.putString(KtConstants.P_URI, UriHelper.geCategoryUri(categoryType).toString())
        bundle.putBoolean(KtConstants.P_IS_NEED_FILTER, true)
        if (mainFragment is MainParentFragment) {
            bundle.putLong(KtConstants.P_CATEGORY_COUNT, (mainFragment as MainParentFragment).getCategoryCount(categoryType))
        }
    }

    private fun updateDocFragmentData(bundle: Bundle) {
        bundle.apply {
            putString(KtConstants.P_URI, UriHelper.geCategoryUri(CategoryHelper.CATEGORY_DOC).toString())
            putInt(Constants.TEMP_SORT_TYPE, -1)
            putStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY, documentExtensionType?.getDocumentFormat(appContext))
            putString(Constants.SQL, documentExtensionType?.getDocumentCountSqlQuery(appContext))
        }
        if (mainFragment is MainParentFragment) {
            bundle.putLong(KtConstants.P_CATEGORY_COUNT, (mainFragment as MainParentFragment).getCategoryCount(categoryType))
        }
    }

    private fun updateApkFragmentData(bundle: Bundle) {
        bundle.apply {
            putString(KtConstants.P_URI, UriHelper.geCategoryUri(CategoryHelper.CATEGORY_APK).toString())
            putString(Constants.SQL, MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_APK))
            putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_apk)
            putInt(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_APK)
        }
    }

    private fun updateCompressFragmentData(bundle: Bundle) {
        if (mainFragment is MainParentFragment) {
            bundle.putLong(KtConstants.P_CATEGORY_COUNT, (mainFragment as MainParentFragment).getCategoryCount(categoryType))
        }
    }

    private fun updateCompressPreviewFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        bundle.apply {
            putBoolean(KtConstants.P_INIT_LOAD, true)
            externalBundle?.let {
                putString(KtConstants.P_CURRENT_PATH, it.getString(KtConstants.P_CURRENT_PATH))
                putString(KtConstants.P_PREVIEW_ROOT_TITLE, it.getString(KtConstants.P_PREVIEW_ROOT_TITLE))
            }
        }
    }

    private fun updateImageDisplayFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            var path = it.getString(Constants.BUCKETDATA)
            if (path.isNullOrEmpty()) {
                path = it.getString(KEY_IMAGE_RELATIVE_PATH)
            }
            val coverPath = it.getString(KEY_IMAGE_COVER_PATH)
            bundle.putString(KEY_IMAGE_RELATIVE_PATH, path)
            bundle.putString(KEY_IMAGE_COVER_PATH, coverPath)
            bundle.putString(Constants.TITLE, it.getString(Constants.TITLE))
        }
    }

    private fun updateOtgFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            val list = it.getStringArrayList(KtConstants.P_PATH_LIST)
            list?.let { paths ->
                if (paths.isNotEmpty()) {
                    if (paths.size > 1) {
                        bundle.putStringArrayList(KtConstants.P_PATH_LIST, paths)
                        bundle.putString(
                            KtConstants.P_TITLE, appContext.getString(
                                com.filemanager.common.R.string.storage_otg
                            )
                        )
                    } else {
                        bundle.putString(KtConstants.P_CURRENT_PATH, paths[0])
                        bundle.putString(
                            KtConstants.P_TITLE, appContext.getString(
                                com.filemanager.common.R.string.storage_otg
                            )
                        )
                        bundle.putBoolean(KtConstants.FROM_OTG_LIST, externalBundle.getBoolean(KtConstants.FROM_OTG_LIST))
                        secondFragment?.let { fragment ->
                            RouterUtil.setCurrentFilePath(fragment, CategoryHelper.CATEGORY_OTG_BROWSER, list[0])
                        }
                    }
                    bundle.putBoolean(KtConstants.FROM_DETAIL, externalBundle.getBoolean(KtConstants.FROM_DETAIL))
                }
            }
            return
        }
        val size = getOTGSize()
        if (size > 1) {
            bundle.putStringArrayList(KtConstants.P_PATH_LIST, getOTGPaths())
            bundle.putString(KtConstants.P_TITLE, appContext.getString(com.filemanager.common.R.string.storage_otg))
        } else {
            val list = getOTGPaths()
            if (list.size > 0) {
                bundle.putString(KtConstants.P_CURRENT_PATH, list[0])
                bundle.putString(KtConstants.P_TITLE, appContext.getString(com.filemanager.common.R.string.storage_otg))
                secondFragment?.let { fragment ->
                    RouterUtil.setCurrentFilePath(fragment, CategoryHelper.CATEGORY_OTG_BROWSER, list[0])
                }
            }
        }
    }

    private fun updateSdcardFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        val path: String? = if (externalBundle != null && externalBundle.containsKey(KtConstants.P_CURRENT_PATH)) {
            Log.d(TAG, "bundle path:${externalBundle.getString(KtConstants.P_CURRENT_PATH)}")
            externalBundle.getString(KtConstants.P_CURRENT_PATH)
        } else {
            getSdCardPath()
        }
        bundle.putString(KtConstants.P_CURRENT_PATH, path)
        bundle.putString(Constants.TITLE, getString(com.filemanager.common.R.string.storage_external))
        secondFragment?.let {
            RouterUtil.setCurrentFilePath(it, CategoryHelper.CATEGORY_SDCARD_BROWSER, path)
        }
    }

    private fun updateDFMFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putAll(externalBundle)
        }
    }

    private fun updateRemoteMacDeviceFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putAll(externalBundle)
        }
    }

    private fun updateLabelFileFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putString(KtConstants.P_TITLE, it.getString(KtConstants.P_TITLE))
            bundle.putLong(Constants.LABEL_ID, it.getLong(Constants.LABEL_ID))
            bundle.putInt(Constants.SIDE_CATEGORY_TYPE, it.getInt(Constants.SIDE_CATEGORY_TYPE))
        }
    }

    private fun updateShortcutFolderFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putAll(externalBundle)
        }
    }

    private fun updateFileSourceFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putAll(externalBundle)
        }
    }

    private fun updateFileCloudDriveFragmentData(bundle: Bundle, externalBundle: Bundle?) {
        externalBundle?.let {
            bundle.putAll(it)
        }
    }

    private fun updateImageFragmentData(bundle: Bundle) {
        bundle.putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_photos)
    }

    private fun updateChildFragmentBundle(bun: Bundle?) {
        if (secondFragment?.isStateSaved == true) {
            Log.w(TAG, "updateChildFragmentBundle(), mSecondFragment is saved and return. mSecondFragment:$secondFragment")
            return
        }
        val bundle = secondFragment?.arguments ?: Bundle()
        bundle.putBoolean(KtConstants.P_NEED_LOAD_DATA, PermissionUtils.hasStoragePermission())
        bundle.putBoolean(KtConstants.P_CHILD_DISPLAY, isHalfScreen)
        when (categoryType) {
            CategoryHelper.CATEGORY_FILE_BROWSER -> updateBrowserFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_IMAGE -> updateImageFragmentData(bundle)
            CategoryHelper.CATEGORY_VIDEO, CategoryHelper.CATEGORY_AUDIO -> updateAudioVideoFragmentData(bundle)
            CategoryHelper.CATEGORY_DOC -> updateDocFragmentData(bundle)
            CategoryHelper.CATEGORY_COMPRESS -> updateCompressFragmentData(bundle)
            CategoryHelper.CATEGORY_APK -> updateApkFragmentData(bundle)
            CategoryHelper.CATEGORY_COMPRESS_PREVIEW -> updateCompressPreviewFragmentData(bundle, bun)
            RouterUtil.CATEGORY_IMAGE_DISPLAY -> updateImageDisplayFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                bundle.putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.text_recycle_bin)
            }

            CategoryHelper.CATEGORY_OTG_BROWSER -> updateOtgFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_MORE_STORAGE -> updateOtgFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_SDCARD_BROWSER -> updateSdcardFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_DFM -> updateDFMFragmentData(bundle, bun)
            CategoryHelper.CATEGORY_TENCENT_DOCS, CategoryHelper.CATEGORY_K_DOCS -> updateFileCloudDriveFragmentData(bundle, bun)
        }
        if (CategoryHelper.isSuperAppType(categoryType)) {
            updateFileSourceFragmentData(bundle, bun)
        }
        if (CategoryHelper.isShortcutFolderType(categoryType)) {
            updateShortcutFolderFragmentData(bundle, bun)
        }
        if (CategoryHelper.isLabelType(categoryType)) {
            updateLabelFileFragmentData(bundle, bun)
        }
        if (CategoryHelper.isRemoteMacDeviceType(categoryType)) {
            updateRemoteMacDeviceFragmentData(bundle, bun)
        }
        secondFragment?.arguments = bundle
    }

    /**
     * 确保父级fragment会被创建，如果mainFragment不为空，需要重新加载数据
     * 小屏首页切换到大屏，需要切换到最近页面
     */
    fun ensureParentFragment() {
        Log.d(TAG, "ensureParentFragment $categoryType")
        if (mainFragment != null) {
            (mainFragment as? MainParentFragment)?.onResumeLoadData()
        } else {
            mainFragment = childFragmentManager.findFragmentByTag(TAG_MAIN_CATEGORY) ?: MainParentFragment.newInstance(categoryType)
            childFragmentManager.beginTransaction().replace(R.id.fragment_container_view_master, mainFragment!!, TAG_MAIN_CATEGORY)
                .commitAllowingStateLoss()
        }
        if (categoryType == CategoryHelper.CATEGORY_MAIN) {
            listFragments.clear()
            categoryType = CategoryHelper.CATEGORY_RECENT
        }
        selectCategoryTypeItem(categoryType)
    }

    /**
     * 侧导进入编辑状态
     */
    fun enterParentFragmentEditState() {
        mRootView?.post {
            (mainFragment as? MainParentFragment)?.setEditState()
        }
    }

    /**
     * 侧导退出编辑状态
     */
    fun exitEditStateInParentFragment() {
        (mainFragment as? MainParentFragment)?.completeEditState()
    }

    /**
     * 小屏文件退出编辑状态
     */
    fun exitEditStateInCategoryFragment() {
        (secondFragment as? MainCategoryFragment)?.completeEditState()
    }

    private fun updateChildFragmentData(type: Int, bundle: Bundle?, needUpdateBundle: Boolean = true) {
        Log.d(TAG, "updateChildFragmentData -> type = $type ; needUpdateBundle = $needUpdateBundle ; bundle = $bundle")
        if (CategoryHelper.isSuperAppType(type)) {
            //文件来源，重复利用一个Fragment显示
            baseVMActivity?.let {
                val fragment = childFragmentManager.findFragmentByTag(getFragmentTag()) ?: RouterUtil.getFragment(it, categoryType)
                bundle?.putBoolean(KtConstants.P_CHILD_DISPLAY, isHalfScreen)
                bundle?.putBoolean(KtConstants.P_NEED_LOAD_DATA, PermissionUtils.hasStoragePermission())
                fragment.arguments = bundle
                RouterUtil.onResumeLoadData(fragment, type)
            }
        } else {
            if (needUpdateBundle) {
                updateChildFragmentBundle(bundle)
            }
            secondFragment?.let { fragment ->
                if (PermissionUtils.hasStoragePermission()) {
                    when (fragment) {
                        is MainCategoryFragment -> fragment.ensureReloadData()

                        is BaseMainFragment -> fragment.onResumeLoadData()

                        else -> RouterUtil.onResumeLoadData(fragment, type)
                    }
                }
            }
        }
    }

    private fun getSecondFragment(activity: Activity, type: Int): Fragment {
        return when (type) {
            CategoryHelper.CATEGORY_MAIN -> MainCategoryFragment()
            CategoryHelper.CATEGORY_MORE_STORAGE, CategoryHelper.CATEGORY_OTG_BROWSER -> {
                val fileBrowser = Injector.injectFactory<IFileBrowser>()
                fileBrowser?.getOTGFragment(activity, type) ?: Fragment()
            }

            else -> RouterUtil.getFragment(activity, type)
        }
    }

    private fun displayChildFragment(bundle: Bundle?, prevFragment: Fragment?, replace: Boolean = true) {
        Log.d(TAG, "displayChildFragment category:$categoryType prev:$previousCategory replace $replace")
        secondFragment?.let {
            updateChildFragmentBundle(bundle)
            val transaction = childFragmentManager.beginTransaction()
            if (replace) {
                transaction.replace(R.id.fragment_container_view_detail, it, getFragmentTag()).commitAllowingStateLoss()
            } else {
                transaction.setCustomAnimations(
                    com.support.appcompat.R.anim.coui_open_slide_enter, com.support.appcompat.R.anim.coui_open_slide_exit,
                    com.support.appcompat.R.anim.coui_close_slide_enter, com.support.appcompat.R.anim.coui_close_slide_exit
                )
                if ((previousCategory == CategoryHelper.CATEGORY_MAIN) && (prevFragment is MainCategoryFragment)) {
                    Log.d(TAG, "displayChildFragment hide MainCategory")
                    transaction.hide(prevFragment)
                }
                transaction.add(R.id.fragment_container_view_detail, it, getFragmentTag()).addToBackStack("f$categoryType").commitAllowingStateLoss()
            }
        }
        baseVMActivity.let {
            if (it is MainActivity) {
                it.setNavigationTabVisible()
            }
        }
    }

    /**
     * 侧导不同页面的类型全局唯一
     */
    fun setCurrentChildFragment(type: Int, bundle: Bundle?) {
        Log.d(TAG, "setCurrentChildFragment type $type categoryType $categoryType")
        val isSameGroupType = CategoryHelper.isSameGroupType(type, categoryType)
        if (categoryType == type && !isSameGroupType) {
            updateChildFragmentData(type, bundle)
            Log.d(TAG, "setCurrentChildFragment same type return!")
            return
        }

        listFragments.clear()
        hideNavigationDefault(type)

        if ((isSameGroupType) && (secondFragment != null)) {
            Log.d(TAG, "setCurrentChildFragment -> updateChildFragmentData")
            categoryType = type
            updateChildFragmentData(type, bundle)
            return
        }

        //从OTGlist跳转到单个OTG 不需要替换
        val replace = !(categoryType == CategoryHelper.CATEGORY_MORE_STORAGE && type == CategoryHelper.CATEGORY_OTG_BROWSER)
        //最近的viewmodel是基于activity的，中大屏下从最近切换到其他的二级页面时，需要重置最近的数据和选中状态
        if (categoryType == CategoryHelper.CATEGORY_RECENT) {
            Log.d(TAG, "reset RecentViewModel")
            ((secondFragment as? PreviewCombineFragment)?.getPreviewFragment() as? MainRecentFragment)?.resetViewModel()
        }
        categoryType = type
        val prevFragment = secondFragment
        activity?.let {
            secondFragment = getSecondFragment(it, type)
            displayChildFragment(bundle, prevFragment, replace)
        }
    }

    /**
     * 侧导不同页面的类型全局唯一
     */
    fun enterNextFragment(nextCategory: Int, bundle: Bundle) {
        Log.d(TAG, "enterNextFragment nextCategory $nextCategory categoryType $categoryType")
        val isSameType = CategoryHelper.isSameGroupType(nextCategory, categoryType)
        previousCategory = categoryType
        if (isSameType || nextCategory == categoryType) {
            if (CategoryHelper.isStorageCategoryType(categoryType)) {
                bundle.putBoolean(KtConstants.FROM_DETAIL, false)
            }
            categoryType = nextCategory
            updateChildFragmentData(nextCategory, bundle)
        } else {
            secondFragment?.let {
                if (categoryType != CategoryHelper.CATEGORY_MAIN) {
                    listFragments.add(categoryType)
                }
            }
            fragmentExitSelectionMode()
            setNextChildFragment(nextCategory, bundle)
        }
        mRootView?.post {
            adjustShowFragmentsToolbar(isHalfScreen)
        }
    }

    /**
     * 当通过拖拽文件后，点击查看进入其他页面，需要退出上一个页面的选择模式
     */
    fun fragmentExitSelectionMode() {
        Log.i(TAG, "fragmentExitSelectionMode secondFragment $secondFragment")
        secondFragment?.let {
            RouterUtil.exitSelectionMode(it, categoryType)
        }
    }

    private fun setNextChildFragment(nextCategory: Int, bundle: Bundle) {
        activity?.let {
            categoryType = nextCategory
            val prevFragment = secondFragment
            secondFragment = getSecondFragment(it, nextCategory)
            displayChildFragment(bundle, prevFragment, false)
        }
    }

    fun backPreviousFragment(preType: Int) {
        Log.e(TAG, "backPreviousFragment category:$categoryType pre：$preType prev:$previousCategory")
        categoryType = if (listFragments.isNotEmpty()) {
            //如果mListFragments不为空，直接取mListFragments末位的categoryType
            listFragments[listFragments.size - 1]
        } else {
            if (preType == -1 || preType == CategoryHelper.CATEGORY_COMPRESS) {
                previousCategory
            } else {
                preType
            }
        }
        secondFragment = childFragmentManager.findFragmentByTag(getFragmentTag())
        Log.d(TAG, "backPreviousFragment categoryType: $categoryType secondFragment $secondFragment")
        secondFragment?.let {
            it.arguments?.apply {
                putBoolean(KtConstants.P_CHILD_DISPLAY, isHalfScreen)
                if (shortCutFolderCurrentPath.isNullOrEmpty().not() && CategoryHelper.isShortcutFolderType(categoryType)) {
                    putString(KtConstants.FILE_PATH, shortCutFolderCurrentPath)
                    putString(KtConstants.P_TITLE, File(shortCutFolderCurrentPath).name)
                    shortCutFolderCurrentPath = ""
                }
            }
            RouterUtil.setIsHalfScreen(it, categoryType, isHalfScreen)
            if (listFragments.isNotEmpty()) {
                listFragments.remove(categoryType)
            }
            it.arguments?.putBoolean(KtConstants.P_RESET_TOOLBAR, true)
            updateChildFragmentData(categoryType, it.arguments, false)
            if (categoryType == CategoryHelper.CATEGORY_MAIN) {
                (baseVMActivity as? MainActivity)?.setNavigationTabVisible()
            }
            adjustShowFragmentsToolbar(isHalfScreen)
        }
        if (childFragmentManager.isStateSaved) {
            Log.e(TAG, "backPreviousFragment fragment state is saved")
            return
        }
        childFragmentManager.popBackStack()
    }

    fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult requestCode:$requestCode mSecondFragment = $secondFragment categoryType:$categoryType")
        if (requestCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            val mainFragment = mainFragment as? MainParentFragment
            if (mainFragment != null) {
                mainFragment.onSelectShortcutFolder(paths)
                return
            }
        }
        secondFragment?.let {
            if (it is MainCategoryFragment) {
                it.fromSelectPathResult(requestCode, paths)
            } else {
                RouterUtil.fromSelectPathResult(it, requestCode, paths, categoryType)
            }
        }
    }

    private fun getOTGSize(): Int {
        var size = 1
        mainFragment?.let { fragment ->
            if (fragment is MainParentFragment) {
                val viewModel = fragment.getViewModel() as? MainParentViewModel
                viewModel?.let { model ->
                    size = model.mOtgPaths?.size ?: 1
                }
            }
        }
        return size
    }

    private fun getOTGPaths(): ArrayList<String> {
        val otgList: ArrayList<String> = ArrayList()
        mainFragment?.let { fragment ->
            if (fragment is MainParentFragment) {
                val viewModel = fragment.getViewModel() as? MainParentViewModel
                viewModel?.let { model ->
                    model.mOtgPaths?.let { otgList.addAll(it) }
                }
            }
        }
        return otgList
    }

    fun setOTGStateChecking() {
        //父子级时mMainFragment是MainParentFragment
        (mainFragment as? MainParentFragment)?.let {
            it.setOTGStateChecking()
            return
        }
        //直板机界面时mSecondFragment是MainCategoryFragment
        (secondFragment as? MainCategoryFragment)?.setOTGStateChecking()
    }

    fun refreshDataWithMount() {
        Log.d(TAG, "refreshDataWithMount main:$mainFragment second:$secondFragment")
        mainFragment?.let {
            if (it is MainParentFragment) {
                it.refreshDataWithMount()
            } else if (it is MainCategoryFragment) {
                it.refreshDataWithMount()
            }
        }
        secondFragment?.let {
            if (it is MainCategoryFragment) {
                it.refreshDataWithMount()
            } else {
                val fragment = childFragmentManager.findFragmentByTag(getFragmentTag(CategoryHelper.CATEGORY_MAIN))
                Log.d(TAG, "refreshDataWithMount find $fragment")
                if (fragment != null && fragment is MainCategoryFragment) {
                    fragment.refreshDataWithMount()
                }
            }
        }
    }

    fun getSdCardPath(): String? {
        if (isHalfScreen) {
            mainFragment?.let { fragment ->
                if (fragment is MainParentFragment) {
                    val viewModel = fragment.getViewModel() as? MainParentViewModel
                    viewModel?.let { model ->
                        return model.mExternalPath
                    }
                }
            }
        } else {
            secondFragment?.let { fragment ->
                if (fragment is MainCategoryFragment) {
                    val viewModel = fragment.getCategoryViewModel()
                    viewModel?.let { model ->
                        return model.mExternalPath
                    }
                }
            }
        }
        return null
    }

    fun getOTGPath(): String? {
        if (isHalfScreen) {
            val viewModel = (mainFragment as? MainParentFragment)?.getViewModel() as? MainParentViewModel
            val otgPaths = viewModel?.mOtgPaths
            Log.d(TAG, "getOTGPath isHalfScreen $isHalfScreen otgPaths $otgPaths")
            if (!otgPaths.isNullOrEmpty()) {
                return otgPaths[0]
            }
        } else {
            val viewModel = (secondFragment as? MainCategoryFragment)?.getCategoryViewModel()
            val otgPaths = viewModel?.mOtgPaths
            Log.d(TAG, "getOTGPath isHalfScreen $isHalfScreen otgPaths $otgPaths")
            if (!otgPaths.isNullOrEmpty()) {
                return otgPaths[0]
            }
        }
        return null
    }

    fun refreshParentData() {
        mainFragment?.let {
            if (it is BaseMainFragment) {
                it.onResumeLoadData()
            }
        }
        secondFragment?.let {
            if (it is BaseMainFragment) {
                it.onResumeLoadData()
            } else {
                RouterUtil.onResumeLoadData(it, categoryType)
            }
        }
    }

    fun getWindowWidth(context: Context, category: Int): Int {
        if (category == GRID_ITEM_DECORATION_RECENT || category == GRID_ITEM_DECORATION_MAIN_LABEL) {
            val showByScreen = UIConfigMonitor.isShowBottomTabByScreenSize(context)
            if (showByScreen) {
                return 0
            }
        }
        return getDetailContainerWidth()
    }

    @Px
    private fun getDetailContainerWidth(): Int {
        val showMaster = masterFragmentContainer?.isVisible ?: false
        val showDetail = detailContainer?.isVisible ?: false
        if (showDetail) {
            val screenWidth = UIConfigMonitor.getScreenWidth()
            val masterWidth: Int = if (showMaster) { //都显示
                sideNavigationContainer.drawerViewWidth
            } else { // 只显示右边 detail
                0
            }
            // screenWidth 是 dp 单位, masterWidth 是 px 单位
            return ViewHelper.dip2px(baseVMActivity, screenWidth) - masterWidth
        }
        // 只显示左边 parent
        return 0
    }

    fun updateLabels() {
        secondFragment?.let {
            if (it is MainCategoryFragment) {
                it.ensureReloadData()
            } else if (it is BaseMainFragment) {
                it.onResumeLoadData()
            } else {
                if (PermissionUtils.hasStoragePermission()) {
                    RouterUtil.onResumeLoadData(it, categoryType)
                }
            }
        }
        (mainFragment as? MainParentFragment)?.updateLabels()
    }

    fun backToTop() {
        secondFragment?.let {
            RouterUtil.backToTop(it, categoryType)
        }
    }

    fun showNavigation(controller: BottomNavigationAnimController?) {
        activity?.let {
            when (categoryType) {
                CategoryHelper.CATEGORY_COMPRESS_PREVIEW -> {
                    navigationControllerForChild.modifyMenuType(
                        NavigationType.DECOMPRESS_PREVIEW, it
                    )
                }

                CategoryHelper.CATEGORY_K_DOCS, CategoryHelper.CATEGORY_TENCENT_DOCS -> {
                    navigationControllerForChild.modifyMenuType(
                        NavigationType.FILE_DRIVE, it
                    )
                }

                CategoryHelper.CATEGORY_DFM -> navigationControllerForChild.modifyMenuType(NavigationType.DFM, it)

                else -> {
                    if (CategoryHelper.isRemoteMacDeviceType(categoryType)) {
                        navigationControllerForChild.modifyMenuType(NavigationType.REMOTE_MAC, it)
                    } else {
                        navigationControllerForChild.modifyMenuType(NavigationType.DEFAULT, it)
                    }
                }
            }
            controller?.setSubNavigationTool(navigationControllerForChild)
            controller?.showToolNav(it, detailContainer?.width ?: 0)
            updateNavToolChildPaddingBottom(NavigationType.DEFAULT)
        }
    }

    fun showNavigation(type: NavigationType, controller: BottomNavigationAnimController?, mainTabNavAnim: Boolean) {
        activity?.let {
            navigationControllerForChild.modifyMenuType(type, it)
            controller?.setSubNavigationTool(navigationControllerForChild)
            val width = detailContainer?.width ?: 0
            if (mainTabNavAnim) {
                controller?.showToolNav(it, width)
            } else {
                controller?.showToolNavDirectly(it, width)
            }
            //此时需要更新navtoolchild padding
            updateNavToolChildPaddingBottom(type)
        }
    }

    fun hideNavigation(type: NavigationType, controller: BottomNavigationAnimController?, callback: () -> Unit) {
        activity?.let {
            controller?.setAnimEndConsumer(object : BottomNavigationAnimController.OnceAnimConsumer() {
                override fun run(t: Boolean) {
                    callback.invoke()
                    navigationControllerForChild.modifyMenuType(type, it)
                    controller.showToolNavDirectly(it, 0)
                }
            })
            controller?.setSubNavigationTool(navigationControllerForChild)
            controller?.hideToolNav(it)
        }
    }

    private fun hideNavigationDefault(type: Int) {
        if (type != CategoryHelper.CATEGORY_COMPRESS_PREVIEW && type != CategoryHelper.CATEGORY_RECYCLE_BIN) {
            activity?.let {
                navigationControllerForChild.hideNavigationDirectly(it)
            }
        }
    }

    fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean,
        mHasSelectedLabelsAllPin: Boolean,
        mHasSelectedFileEmpty: Boolean
    ) {
        val isLabelCategory = CategoryHelper.isLabelType(categoryType)
        if (isLabelCategory) {
            navigationControllerForChild.setNavigateItemAble(
                isEnable, mHasDrm, mHasSelectedMultiLabels, mHasSelectedLabelsAllPin
            )
            return
        }
        when (categoryType) {
            CategoryHelper.CATEGORY_COMPRESS_PREVIEW -> {
                navigationControllerForChild.setNavigateItemAble(
                    isEnable, mHasDrm, mHasSelectedMultiLabels, mHasSelectedLabelsAllPin
                )
            }

            CategoryHelper.CATEGORY_K_DOCS, CategoryHelper.CATEGORY_TENCENT_DOCS -> {
                navigationControllerForChild.setNavigateItemAble(
                    isEnable, mHasDrm, mHasSelectedMultiLabels, mHasSelectedLabelsAllPin, mHasSelectedFileEmpty
                )
            }

            else -> navigationControllerForChild.setNavigateItemAble(isEnable, mHasDrm)
        }
    }

    fun hideNavigation(controller: BottomNavigationAnimController?) {
        activity?.let {
            controller?.setSubNavigationTool(navigationControllerForChild)
            controller?.hideToolNav(it)
        }
    }

    fun permissionSuccess() {
        secondFragment?.let {
            RouterUtil.permissionSuccess(it, categoryType)
        }
    }

    private fun selectCategoryTypeItem(type: Int) {
        mainFragment?.let {
            if (it is MainParentFragment) {
                if (listFragments.isNotEmpty()) {
                    it.selectCategoryTypeItem(listFragments[0])
                } else {
                    it.selectCategoryTypeItem(type)
                }
            }
        }
    }

    fun isStorageFragment(): Boolean {
        return (categoryType == CategoryHelper.CATEGORY_FILE_BROWSER || categoryType == CategoryHelper.CATEGORY_SDCARD_BROWSER
                || categoryType == CategoryHelper.CATEGORY_OTG_BROWSER)
    }

    fun isScreenHalf(): Boolean {
        return isHalfScreen
    }

    private fun getFragmentTag(): String {
        return getFragmentTag(categoryType)
    }

    fun getFragmentTag(type: Int): String {
        return if (CategoryHelper.isSuperAppType(type)) {
            "mainf${CategoryHelper.CATEGORY_SOURCE_GROUP}"
        } else if (CategoryHelper.isLabelType(type)) {
            "mainf${CategoryHelper.CATEGORY_LABEL_GROUP}"
        } else if (CategoryHelper.isShortcutFolderType(type)) {
            "mainf${CategoryHelper.CATEGORY_FOLDER_GROUP}"
        }  else {
            "mainf$type"//TAG_MAIN_SECOND
        }
    }

    fun getRootViewNew(): ViewGroup? {
        val view = secondFragment?.view?.findViewById<View>(com.filemanager.common.R.id.coordinator_layout)
        return if (view is ViewGroup) view else null
    }

    fun isNavigationTabShow(): Boolean {
        if (!isHalfScreen && categoryType == CategoryHelper.CATEGORY_MAIN) {
            return true
        }
        if (isHalfScreen && (!navigationControllerForChild.isShowNavigation() || navigationControllerForChild.isDefaultDisplay())) {
            return true
        }
        return false
    }

    /**
     * 切换导航栏高度时
     */
    fun updateNavPaddingBottom(mainNavShow: Boolean) {
        (activity as? MainActivity)?.let {
            Log.d(TAG, "updateNavPaddingBottom categoryType: $categoryType")
            //父子级下，删除或解压预览界面时 mainNavigation显示时，删除的toolNavigation不需要设置padding
            if (mainNavShow) {
                if (categoryType == CategoryHelper.CATEGORY_RECYCLE_BIN || categoryType == CategoryHelper.CATEGORY_COMPRESS_PREVIEW) {
                    navigationToolForChild?.updatePaddingBottom(0)
                } else {
                    navigationToolForChild?.updatePaddingBottom(it.systemBarInsetsBottom)
                    navigationToolForChild?.let { nav ->
                        nav.updateLayoutParams<FrameLayout.LayoutParams> {
                            bottomMargin = -nav.measuredHeight - it.systemBarInsetsBottom
                        }
                    }
                }
            } else {
                navigationToolForChild?.updatePaddingBottom(it.systemBarInsetsBottom)
                navigationToolForChild?.let { nav ->
                    if (nav.marginBottom < 0) {
                        nav.updateLayoutParams<FrameLayout.LayoutParams> {
                            bottomMargin = -nav.measuredHeight - it.systemBarInsetsBottom
                        }
                    }
                }
            }
        }
    }

    /**
     * 显示隐藏工具栏时
     */
    private fun updateNavToolChildPaddingBottom(type: NavigationType) {
        Log.d(TAG, "updateNavToolChildPaddingBottom  type $type")
        (activity as? MainActivity)?.let {
            navigationToolForChild?.updatePaddingBottom(it.systemBarInsetsBottom)
        }
    }

    /**
     * 更新父子级下FragmentContainerView的padding bottom
     */
    fun updateChildFragmentPadding(isMainNavShow: Boolean, showTaskBar: Boolean) {
        Log.d(TAG, "updateChildFragmentPadding isMainNavShow $isMainNavShow  showTaskBar $showTaskBar")
        (activity as? MainActivity)?.let {
            if (!isMainNavShow && showTaskBar) {
                Log.d(TAG, "updateChildFragmentPadding add padding")
                if (categoryType != CategoryHelper.CATEGORY_RECYCLE_BIN) {
                    detailFragmentContainer?.updatePadding(bottom = it.systemBarInsetsBottom)
                }
                masterFragmentContainer?.updatePadding(bottom = it.systemBarInsetsBottom)
            } else {
                Log.d(TAG, "updateChildFragmentPadding remove padding")
                if (categoryType != CategoryHelper.CATEGORY_RECYCLE_BIN) {
                    detailFragmentContainer?.updatePadding(bottom = 0)
                }
                masterFragmentContainer?.updatePadding(bottom = 0)
            }
        }
    }

    fun getCurrentPath(): String {
        val fragment = secondFragment ?: return ""
        return Injector.injectFactory<IFileBrowser>()?.getCurrentPath(fragment) ?: ""
    }

    fun updateRedDot() {
        (mainFragment as? MainParentFragment)?.updateOWorkVisible()
        (secondFragment as? MainCategoryFragment)?.updateOWorkVisible()
    }

    override fun setPermissionEmptyVisible(visible: Int) {
        super.setPermissionEmptyVisible(visible)
        (secondFragment as? BaseVMFragment<*>)?.setPermissionEmptyVisible(visible)
    }

    override fun onDrawerSlide(drawerView: View, slideOffset: Float, slideOffsetPixel: Int, mode: Int) {
        calculateBorder(drawerView)
    }

    override fun onDrawerOpened(drawerView: View) {
        Log.d(TAG, "onDrawerOpened")
        isDoingGridAnim = false
        calculateBorder(sideNavigationContainer.drawerView)
        updateSideNavigationStatus(KtConstants.SIDE_NAVIGATION_OPEN)
    }

    override fun onDrawerClosed(drawerView: View?) {
        Log.d(TAG, "onDrawerClosed")
        isDoingGridAnim = false
        calculateBorder(sideNavigationContainer.drawerView)
        updateSideNavigationStatus(KtConstants.SIDE_NAVIGATION_CLOSE)
    }

    override fun onDrawerStateChanged(newState: Int) {
        Log.d(TAG, "onDrawerStateChanged newState $newState")
    }

    override fun onDrawerWidthChanged(oldWidth: Int, newWidth: Int, mode: Int) {
        Log.d(TAG, "onDrawerWidthChanged oldWidth $oldWidth newWidth $newWidth mode $mode")
        sideNavigationContainer.post {
            if (mode == COUISideNavigationBar.DRAWER_MODE_FIXED) {
                calculateBorder(sideNavigationContainer.drawerView)
            }
        }
    }

    override fun onDrawerModeChanged(mode: Int) {
        sideNavigationContainer.post {
            if (mode == COUISideNavigationBar.DRAWER_MODE_FIXED && sideNavigationContainer.isDrawerOpening) {
                calculateBorder(sideNavigationContainer.drawerView)
            }
        }
    }

    private fun calculateBorder(drawerView: View) {
        if (drawerView.layoutDirection == COUISideNavigationBar.LAYOUT_DIRECTION_RTL) {
            calculateBorder(sideNavigationContainer.width - drawerView.left, sideNavigationContainer.drawerViewWidth)
        } else {
            calculateBorder(drawerView.right, sideNavigationContainer.drawerViewWidth)
        }
    }

    private fun calculateBorder(right: Int, drawerWidth: Int) {
        val params = detailContainer?.layoutParams as? ViewGroup.MarginLayoutParams
        params?.marginStart = right
        params?.let {
            detailContainer?.layoutParams = it
        }
        adjustFragmentsToolbar(right, drawerWidth)
    }

    /**
     * 大屏显示最近页面，切换到小屏，需要显示最近页面的tab
     */
    fun switchToRecentTab() {
        (baseVMActivity as? MainActivity)?.setCurrentRecentFragment()
    }

    /**
     * 设置编辑状态
     */
    fun setEditState(isEdit: Boolean) {
        this.isEdit = isEdit
    }

    fun getMaskView(): ImageView? {
        return containerMaskView
    }

    fun getExpandIcon(): ImageView? {
        return sideExpandIcon
    }

    /**
     * 更新侧导标签数据
     */
    fun updateSideNavigationLabels() {
        (mainFragment as? MainParentFragment)?.updateLabels()
    }

    /**
     * 显示新建标签弹窗
     */
    private fun showAddLabelDialog() {
        val activity = baseVMActivity ?: return

        if (createLabelDialog?.isShowing() == true) {
            Log.d(TAG, "showAddLabelDialog -> dialog is showing,return")
            return
        }

        if (checkLabelMaxCountCallback.invoke()) {
            Toast.makeText(activity, getString(com.filemanager.common.R.string.exceed_label_counts), Toast.LENGTH_SHORT).show()
            return
        }

        StatisticsUtils.onCommon(appContext, StatisticsUtils.CRATE_NEW_LABEL)
        val labelDialogBean =
            LabelDialogBean(type = LabelDialogBean.TYPE_CREATE_LABEL, resultListener = object : BaseFileNameDialog.OnButtonClickListener {
                @SuppressLint("SuspiciousIndentation")
                override fun onClick(dialog: AlertDialog, buttonId: Int, inputValue: String?) {
                    when (buttonId) {
                        DialogInterface.BUTTON_POSITIVE -> {
                            inputValue?.let {
                                onCreateLabelCallback.invoke(it)
                            }
                            createLabelDialog?.dismiss()

                            StatisticsUtils.onCommon(appContext, StatisticsUtils.CRATE_NEW_LABEL_CLICK_POSITIVE_BUTTON)
                        }

                        DialogInterface.BUTTON_NEGATIVE -> setShowAddLabelDialogState(false)

                        else -> createLabelDialog?.dismiss()
                    }
                }
            })
        createLabelDialog = LabelNameDialog(activity, lifecycleScope, labelDialogBean)
        createLabelDialog?.show()
    }

    /**
     * 删除标签的item
     */
    private fun showDeleteLabelDialog(labelId: Long) {
        Log.d(TAG, "showDeleteLabelDialog -> delete labelId $labelId ")
        val activity = baseVMActivity ?: return

        if (deleteLabelDialog?.isShowing == true) {
            Log.d(TAG, "showDeleteLabelDialog -> dialog is showing,return")
            return
        }

        val title = stringResource(com.filemanager.common.R.string.delete_one_label_title)
        val deleteStr = stringResource(com.filemanager.common.R.string.menu_file_list_delete)
        val builder = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom).setWindowGravity(Gravity.BOTTOM)
            .setTitle(title)
            .setMessage(com.filemanager.common.R.string.delete_label_tips).setNeutralButton(deleteStr) { _, _ ->
                StatisticsUtils.onCommon(
                    appContext, StatisticsUtils.LABEL_SELECT_OPERATION,
                    hashMapOf(StatisticsUtils.LABEL_KEY_OPERATION to StatisticsUtils.LABEL_VALUE_OPERATION_DELETE)
                )
                onDeleteLabelCallback.invoke(labelId)
                DeleteSoundUtil.playDeleteSound()
                VibratorUtil.vibrate()
            }.setNegativeButton(com.filemanager.common.R.string.alert_dialog_no) { _, _ ->
                setShowDeleteDialogState(null)
            }.setOnCancelListener {
                setShowDeleteDialogState(null)
            }
        deleteLabelDialog = builder.show()
    }

    /**
     * 显示选择添加快捷文件夹的弹窗
     */
    fun showSelectShortcutFolderDialog() {
        Log.d(TAG, "showSelectShortcutFolderDialog")
        (baseVMActivity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_ADD_SHORTCUT_FOLDER)
    }

    /**
     * 删除快捷文件夹弹窗
     */
    fun showDeleteShortcutFolderDialog(id: Long, deleteCallback: Consumer<Boolean>? = null) {
        Log.d(TAG, "showDeleteShortcutFolderDialog -> delete dbID $id ")
        val activity = baseVMActivity ?: return

        if (deleteShortcutFolderDialog?.isShowing == true) {
            Log.d(TAG, "showDeleteShortcutFolderDialog -> dialog is showing,return")
            return
        }

        val title = stringResource(com.filemanager.common.R.string.remove_one_shortcut_folder_title)
        val deleteStr = stringResource(com.filemanager.common.R.string.remove_one_shortcut_sure)
        val builder = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom).setWindowGravity(Gravity.BOTTOM)
            .setTitle(title)
            .setMessage(com.filemanager.common.R.string.remove_one_shortcut_folder_title_tips).setNeutralButton(deleteStr) { _, _ ->
                // 删除数据库数据
                viewModel?.launch {
                    withContext(Dispatchers.IO) {
                        val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
                        val success = shortcutFolderApi?.deleteShortcutFolder(id) ?: false
                        Log.d(TAG, "showDeleteShortcutFolderDialog delete $id result:$success")
                        deleteCallback?.accept(success)
                    }
                }

                DeleteSoundUtil.playDeleteSound()
                VibratorUtil.vibrate()
            }.setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, null)
        deleteShortcutFolderDialog = builder.show()
    }

    private fun isAddDialogIsShowing(): Boolean {
        return createLabelDialog?.isShowing() == true
    }

    fun setShowAddLabelDialogState(isShow: Boolean) {
        viewModel?.showCreateLabelDialogState?.postValue(isShow)
    }

    fun setShowDeleteDialogState(labelId: Long?) {
        viewModel?.showDeleteLabelDialogState?.postValue(labelId)
    }

    /**
     * 适配子页面toolbar的marginStart
     * 当中大屏关闭侧导，全屏显示页面时，切换亮暗色，子页面的toolbar需要设置marginStart，否则会和侧导的图标重合
     *
     */
    fun adapterFragmentsToolbar() {
        Log.d(TAG, "adapterFragmentsToolbar")
        mRootView?.post {
            adjustFragmentsToolbar(isHalfScreen)
        }
    }

    /**
     * 大小屏切换时，需要单独适配子页面fragment的toolbar
     * @param isHalfScreen 是否是父子级中大屏
     */
    private fun adjustFragmentsToolbar(isHalfScreen: Boolean) {
        val params = getDrawerParams(isHalfScreen)
        val right = params.first
        val drawerWidth = params.second
        adjustFragmentsToolbar(right, drawerWidth)
    }

    /**
     * 中大屏父子级展开或则收起，适配子页面fragment的toolbar
     * 但中大屏关闭侧导时，子页面的toolbar需要设置marginStart，否则toolbar上的返回箭头会和开启关闭侧导的图标重合
     * @param right 侧导分割线到屏幕的距离（展开收起过程中会动态变化）
     * @param drawerWidth 侧导面板宽度（初始化后侧导固定宽度）
     */
    private fun adjustFragmentsToolbar(right: Int, drawerWidth: Int) {
        if (isAdded) {
            val fragment = childFragmentManager.findFragmentByTag(getFragmentTag(categoryType))
            (fragment as? BaseVMFragment<*>)?.adjustToolBarMarginStart(right, drawerWidth)
        }
    }

    private fun adjustShowFragmentsToolbar(isHalfScreen: Boolean) {
        val params = getDrawerParams(isHalfScreen)
        val right = params.first
        val drawerWidth = params.second
        val fragment = childFragmentManager.findFragmentByTag(getFragmentTag(categoryType))
        (fragment as? BaseVMFragment<*>)?.adjustToolBarMarginStart(right, drawerWidth)
    }

    /**
     * 获取侧导的参数
     * right ： 侧导分割线到屏幕的距离（展开收起过程中会动态变化）
     * drawerWidth ： 侧导面板宽度（初始化后侧导固定宽度）
     */
    private fun getDrawerParams(isHalfScreen: Boolean): Pair<Int, Int> {
        val drawerWidth = sideNavigationContainer.drawerViewWidth
        val drawerView = sideNavigationContainer.drawerView
        val right = if (isHalfScreen) {
            if (sideNavigationContainer.drawerView.layoutDirection == COUISideNavigationBar.LAYOUT_DIRECTION_RTL) {
                sideNavigationContainer.width - drawerView.left
            } else {
                drawerView.right
            }
        } else {
            drawerWidth
        }
        return Pair(right, drawerWidth)
    }

    fun getLabelIdByCategoryType(categoryType: Int): Long? {
        Log.d(TAG, "getLabelIdByCategoryType categoryType $categoryType isHalfScreen $isHalfScreen secondFragment $secondFragment")
        //大屏
        return if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.getLabelIdByCategoryType(categoryType)
        } else {
            //小屏
            if (secondFragment is MainCategoryFragment) {
                (secondFragment as? MainCategoryFragment)?.getLabelIdByCategoryType(categoryType)
            } else {
                //显示的页面
                val previewFragment = (secondFragment as? PreviewCombineFragment)?.getPreviewFragment()
                (previewFragment as? LabelFileListFragment)?.getLabelId()
            }
        }
    }

    /**
     * 判断是否是小屏，是否显示文件tab
     */
    fun isShowMainCategoryFragment(): Boolean {
        return secondFragment is MainCategoryFragment
    }

    fun getShowingLabelId(): Long? {
        Log.d(TAG, "getShowingLabelId categoryType $categoryType")
        return getLabelIdByCategoryType(categoryType)
    }

    fun getSuperAppFilePathsByCategoryType(categoryType: Int): Array<String>? {
        Log.d(TAG, "getSuperAppFilePathsByCategoryType categoryType $categoryType isHalfScreen $isHalfScreen secondFragment $secondFragment")
        if (!CategoryHelper.isSuperAppType(categoryType)) {
            return null
        }
        //大屏
        return if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.getSuperAppFilePathsByCategoryType(categoryType)
        } else {
            //小屏
            if (secondFragment is MainCategoryFragment) {
                (secondFragment as? MainCategoryFragment)?.getSuperAppFilePathsByCategoryType(categoryType)
            } else {
                //显示的页面
                val previewFragment = (secondFragment as? PreviewCombineFragment)?.getPreviewFragment()
                Injector.injectFactory<ISuperApp>()?.getShowingSuperAppFilePaths(previewFragment)
            }
        }
    }

    fun getShowingSuperAppFilePaths(): Array<String>? {
        Log.d(TAG, "getShowingSuperAppFilePaths categoryType $categoryType")
        return getSuperAppFilePathsByCategoryType(categoryType)
    }

    fun getShortcutFilePathByCategoryType(categoryType: Int): String? {
        Log.d(TAG, "getShortcutFilePathByCategoryType categoryType $categoryType isHalfScreen $isHalfScreen secondFragment $secondFragment")
        //大屏
        return if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.getShortcutFilePathByCategoryType(categoryType)
        } else {
            //小屏
            if (secondFragment is MainCategoryFragment) {
                (secondFragment as? MainCategoryFragment)?.getShortcutFilePathByCategoryType(categoryType)
            } else {
                //显示的页面
                secondFragment?.let { Injector.injectFactory<IShortcutFolderApi>()?.getCurrentPath(it) }
            }
        }
    }

    fun getShowingShortCutFilePath(): String? {
        Log.d(TAG, "getShowingShortCutFilePath categoryType $categoryType")
        return getShortcutFilePathByCategoryType(categoryType)
    }

    fun getCurrentRecentFragment(): MainRecentFragment? {
        Log.d(TAG, "getCurrentRecentFragment secondFragment $secondFragment")
        return (secondFragment as? PreviewCombineFragment)?.getPreviewFragment() as? MainRecentFragment
    }

    fun getSideSelectedCategoryType(): Int {
        val categoryType = (mainFragment as? MainParentFragment)?.getSelectedCategoryType()
        return categoryType ?: -1
    }

    fun getEditState(): Boolean {
        return if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.getEditState() ?: false
        } else {
            (secondFragment as? MainCategoryFragment)?.getEditState() ?: false
        }
    }

    fun handleDragStart() {
        if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.handleDragStart()
        } else {
            (secondFragment as? MainCategoryFragment)?.handleDragStart()
        }
    }

    fun handleDragEnd() {
        if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.handleDragEnd()
        } else {
            (secondFragment as? MainCategoryFragment)?.handleDragEnd()
        }
    }

    fun exitEditMode() {
        if (isHalfScreen) {
            (mainFragment as? MainParentFragment)?.completeEditState()
        } else {
            (secondFragment as? MainCategoryFragment)?.completeEditState()
        }
    }

    /**
     * 获取需要检测的view
     * 小屏：返回当前显示的fragment对应的view
     * 大屏：根据坐标检测，若拖拽到侧导，返回侧导的view，否则返回子页面的view
     */
    fun getDropDetectionView(rootView: View?, event: DragEvent?): View? {

        if (!isHalfScreen) {
            return secondFragment?.view
        }

        val x = event?.x ?: 0f
        val y = event?.y ?: 0f
        val rootViewLoc = IntArray(2)
        rootView?.getLocationOnScreen(rootViewLoc)
        val rawX = rootViewLoc[0] + x
        val rawY = rootViewLoc[1] + y

        val parentView = (mainFragment as? MainParentFragment)?.view
        val parentViewLoc = IntArray(2)
        parentView?.getLocationOnScreen(parentViewLoc)
        val left = parentViewLoc[0]
        val top = parentViewLoc[1]
        val right = left + (parentView?.width ?: 0)
        val bottom = top + (parentView?.height ?: 0)

        if (rawX >= left && rawX <= right && rawY >= top && rawY <= bottom) {
            return parentView.also { it?.tag = SIDE_NAVIGATION_VIEW_TAG }
        }

        return secondFragment?.view
    }

    fun handleDragEvent(detectView: View?, event: DragEvent?): Boolean? {
        if (detectView?.tag == SIDE_NAVIGATION_VIEW_TAG) {
            secondFragment?.let { RouterUtil.resetDragStatus(it) }
            return (mainFragment as? MainParentFragment)?.handleDragScroll(event)
        } else {
            (mainFragment as? MainParentFragment)?.resetDragStatus()
            return if (secondFragment is MainCategoryFragment) {
                (secondFragment as? MainCategoryFragment)?.handleDragScroll(event)
            } else {
                secondFragment?.let { RouterUtil.handleDragScroll(it, event) }
            }
        }
    }

    fun resetDragStatus() {
        (mainFragment as? MainParentFragment)?.resetDragStatus()
        (secondFragment as? MainCategoryFragment)?.resetDragStatus()
    }

    override fun refreshDataForDir(path: String, category: Int) {
        if (mainFragment is MainParentFragment) {
            (mainFragment as? MainParentFragment)?.refreshDataForShortcutFolders(path)
        }
        secondFragment?.let {
            RouterUtil.onRefreshForDir(it, category, path)
        }
    }

    override fun renameToShortCutFolder(newPath: String, file: BaseFileBean): String {
        if (mainFragment is MainParentFragment) {
            file.mData?.let {
                (mainFragment as? MainParentFragment)?.refreshDataForShortcutFolders(it)
            }
        }
        if (secondFragment is MainCategoryFragment) {
            file.mData?.let {
                (secondFragment as? MainCategoryFragment)?.refreshDataForShortcutFolders(it)
            }
        }
        secondFragment?.let {
            val currentPath =
                RouterUtil.renameToShortCutFolder(it, categoryType, newPath, file)
            if (currentPath.isNotEmpty() && currentPath.contains(newPath) && CategoryHelper.CATEGORY_FILE_BROWSER == categoryType) {
                shortCutFolderCurrentPath = newPath
            }
        }
        return shortCutFolderCurrentPath
    }

    override fun renameToLabel(newName: String, labelId: Long) {
        secondFragment?.let {
            RouterUtil.renameLabel(it, categoryType, newName, labelId)
        }
    }

    fun switchRecentItem() {
        if (isHalfScreen) {
            Log.d(TAG, "switchRecentPage categoryType=$categoryType")
            (mainFragment as? MainParentFragment)?.switchRecentItem()
        }
    }

    fun refreshSideNavigationData() {
        mainFragment?.let {
            if (it is BaseMainFragment) {
                Log.d(TAG, "refreshSideNavigationData onResumeLoadData")
                it.onResumeLoadData()
            }
        }
    }
    override fun onClickDir(path: String) {
        secondFragment?.let {
            RouterUtil.onClickDir(path, it)
        }
    }

    fun getSelectedItemView(): ArrayList<View>? {
        return secondFragment?.let {
            RouterUtil.getSelectedItemView(it)
        }
    }

    fun setNavigateItemAble() {
        secondFragment?.let {
            RouterUtil.setNavigateItemAble(it)
        }
    }

    fun updateSideRemoteDeviceStatus(deviceId: String, status: Int) {
        if (mainFragment is MainParentFragment) {
            (mainFragment as MainParentFragment).updateRemoteDeviceStatus(deviceId, status)
        }
    }
}
