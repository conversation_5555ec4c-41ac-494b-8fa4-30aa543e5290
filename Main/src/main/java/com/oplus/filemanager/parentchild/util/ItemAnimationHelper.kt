/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/09/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.util

import android.animation.ValueAnimator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import java.util.concurrent.CopyOnWriteArrayList

class ItemAnimationHelper {

    companion object {
        const val TAG = "ItemAnimationHelper"
        const val DEFAULT_ENTER_EDIT_ANIMATION_DURATION = 350L
        private const val DEFAULT_EXIT_EDIT_ANIMATION_DURATION = 200L
        private val DEFAULT_ANIMATOR_INTERPOLATOR = COUIMoveEaseInterpolator()
    }

    var isEdit = false
    private var currentFraction = 0f
    private val optionAnimationUpdateListeners = CopyOnWriteArrayList<AnimationListener>()

    private val enterEditAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            currentFraction = it.animatedFraction
            for (listener in optionAnimationUpdateListeners) {
                listener.onAnimationUpdate(it.animatedFraction, true)
            }
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = COUIMoveEaseInterpolator()
    }

    private val exitEditAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            currentFraction = 1f - it.animatedFraction
            for (listener in optionAnimationUpdateListeners) {
                listener.onAnimationUpdate(1f - it.animatedFraction, false)
            }
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = DEFAULT_ANIMATOR_INTERPOLATOR
    }

    private val enterMacDragAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            for (listener in optionAnimationUpdateListeners) {
                listener.onMacDragStateChange(true, it.animatedFraction)
            }
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = COUIMoveEaseInterpolator()
    }

    private val exitMacDragAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
        addUpdateListener {
            for (listener in optionAnimationUpdateListeners) {
                listener.onMacDragStateChange(false, it.animatedFraction)
            }
        }
        duration = DEFAULT_ENTER_EDIT_ANIMATION_DURATION
        interpolator = DEFAULT_ANIMATOR_INTERPOLATOR
    }

    fun startAnimation() {
        if (isEdit) {
            exitEditAnimator.cancel()
            enterEditAnimator.setCurrentFraction(currentFraction)
            for (listener in optionAnimationUpdateListeners) {
                listener.onEditStateChange(true)
                listener.itemEnableStateChange(true)
            }
            enterEditAnimator.start()
        } else {
            enterEditAnimator.cancel()
            exitEditAnimator.setCurrentFraction(1 - currentFraction)
            for (listener in optionAnimationUpdateListeners) {
                listener.onEditStateChange(false)
                listener.itemEnableStateChange(false)
            }
            exitEditAnimator.start()
        }
    }

    fun startDragAnimation() {
        exitMacDragAnimator.cancel()
        enterMacDragAnimator.start()
    }

    fun endDragAnimation() {
        exitMacDragAnimator.start()
        enterMacDragAnimator.cancel()
    }

    fun addListener(listener: AnimationListener, isEdit: Boolean) {
        optionAnimationUpdateListeners.add(listener)
        listener.onAnimationUpdate(currentFraction, isEdit)
        listener.onAnimationUpdate(
            ((currentFraction * DEFAULT_ENTER_EDIT_ANIMATION_DURATION).toLong().coerceAtMost(
                DEFAULT_EXIT_EDIT_ANIMATION_DURATION
            ) / DEFAULT_EXIT_EDIT_ANIMATION_DURATION).toFloat(), isEdit
        )
    }

    fun removeListener(listener: AnimationListener) {
        optionAnimationUpdateListeners.remove(listener)
    }

    interface AnimationListener {
        fun onAnimationUpdate(fraction: Float, enterEdit: Boolean)
        fun onEditStateChange(isEdit: Boolean)
        fun onMacDragStateChange(isMacDragging: Boolean, fraction: Float) {
        }
        fun itemEnableStateChange(isEdit: Boolean)
    }
}
