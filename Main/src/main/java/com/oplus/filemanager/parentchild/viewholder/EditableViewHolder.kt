/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/10, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.viewholder

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import androidx.activity.ComponentActivity
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.rippleutil.COUIRippleDrawableUtil
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.talkback.TalkbackUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.main.R
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer

class EditableViewHolder(context: Context, activity: ComponentActivity, itemView: View) : BaseOptionHolder(context, activity, itemView) {

    private val widgetStartMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_widget_start_margin_end)
    private val widgetEndMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_widget_end_margin_end)
    private val iconStartMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_start_margin_start)
    private val iconEndMargin: Int = context.resources.getDimensionPixelOffset(R.dimen.coui_sidebar_list_item_icon_end_margin_start)
    private val isRtl = ViewCompat.getLayoutDirection(itemView) == ViewCompat.LAYOUT_DIRECTION_RTL
    private val mark = if (isRtl) 1 else -1

    init {
        val inflater = LayoutInflater.from(context)
        inflater.inflate(R.layout.widget_sidebar_operate, widgetFrame)
    }

    @SuppressLint("ClickableViewAccessibility")
    fun bindData(
        bean: CategoryListBean,
        isEdit: Boolean,
        onItemViewCallback: (itemView: View, categoryListBean: CategoryListBean) -> Unit,
        onItemOperateCallback: (bean: CategoryListBean) -> Unit,
        onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit
    ) {
        bindData(bean, isEdit, onItemViewCallback)
        val switchView = widgetFrame.findViewById<COUISwitch>(R.id.switch_widget)
        val deleteView = widgetFrame.findViewById<ImageView>(R.id.delete_widget)
        TalkbackUtils.setAccessibilityDelegate(deleteView, Button::class.java.name)
        if (CategoryHelper.isShortcutFolderType(bean.categoryType)) {
            deleteView.contentDescription = stringResource(com.filemanager.common.R.string.remove_one_shortcut_sure) + "\u3000" + bean.name
        } else if (CategoryHelper.isLabelType(bean.categoryType)) {
            deleteView.contentDescription = stringResource(com.filemanager.common.R.string.menu_file_list_delete) + "\u3000" + bean.name
        } else if (CategoryHelper.isSuperAppType(bean.categoryType)) {
            switchView.contentDescription = bean.name
        }
        if (bean.groupCategoryType == CategoryHelper.CATEGORY_SOURCE_GROUP) {
            switchView.visibility = View.VISIBLE
            switchView.isChecked = bean.superAppSwitch
            deleteView.visibility = View.GONE
        } else {
            deleteView.visibility = View.VISIBLE
            switchView.visibility = View.GONE
        }

        itemView.alpha = 1.0f
        dragView.visibility = View.VISIBLE
        dragView.setOnLongClickListener {
            if (this.isEdit) {
                onItemDragCallback.invoke(this)
                return@setOnLongClickListener true
            }
            return@setOnLongClickListener false
        }
        changeTitleMode(this.isEdit)
        widgetFrame.visibility = View.VISIBLE
        widgetFrame.let {
            // 来源
            if (bean.groupCategoryType == CategoryHelper.CATEGORY_SOURCE_GROUP) {
                switchView.setOnClickListener {
                    if (this.isEdit) {
                        uploadSourceSwitch(bean, switchView)
                        bean.superAppSwitch = switchView.isChecked
                        onItemOperateCallback.invoke(bean)
                    }
                }
                return@let
            }

            val radius = COUIMaskRippleDrawable.getMaskRippleRadiusByType(context, COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS)
            COUIRippleDrawableUtil.setIconPressRippleDrawable(it, radius)
            it.setOnClickListener {
                if (this.isEdit) {
                    if (CategoryHelper.isLabelType(bean.categoryType)) {
                        onItemOperateCallback.invoke(bean)
                    }
                    if (CategoryHelper.isShortcutFolderType(bean.categoryType)) {
                        onItemOperateCallback.invoke(bean)
                    }
                }
            }
        }

        (wrapper as? SideNavigationItemContainer)?.setEditState(this.isEdit)
        val fraction = if (this.isEdit) 1f else 0f
        onAnimationUpdate(fraction, this.isEdit)
    }
    /**
     * 来源开关埋点，24小时内，每个开关上传1次
     * */
    private fun uploadSourceSwitch(data: CategoryListBean, switchView: COUISwitch?) {
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_LAST_STATUS] =
            if (switchView!!.isChecked) "1" else "0"
        map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_NAME] = data.packageName
        StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPER_APP_SOURCE_SWITCH, map)
    }
    /**
     * 进入编辑，enterEdit 为true，fraction 0 -> 1变化
     * 退出编辑，enterEdit 为false，fraction 1 -> 0变化
     */
    override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
        if (isEdit) {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark
            val iconEndValue = 0
            dragView.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
                alpha = fraction
            }
            optionIcon.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
            optionTitle.apply {
                translationX = iconEndValue + (iconStartValue - iconEndValue) * (1 - fraction)
            }
            optionSubtitle.alpha = 1f - fraction
            val widgetStartValue = (widgetEndMargin - widgetStartMargin) * mark * -1
            val widgetEndValue = 0
            widgetFrame.apply {
                translationX = widgetEndValue + (widgetStartValue - widgetEndValue) * (1 - fraction)
                alpha = fraction
            }
        } else {
            val iconStartValue = (iconEndMargin - iconStartMargin) * mark * -1
            val iconEndValue = 0
            val widgetStartValue = widgetStartMargin
            val widgetEndValue = widgetEndMargin
            dragView.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            dragView.alpha = fraction
            optionSubtitle.alpha = 1f - fraction
            optionIcon.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            optionTitle.translationX = iconEndValue + (iconStartValue - iconEndValue) * fraction
            widgetFrame.translationX = (widgetEndValue + (widgetStartValue - widgetEndValue) * fraction) * mark * -1
            widgetFrame.alpha = fraction
        }
        dragView.isEnabled = isEdit
        widgetFrame.isEnabled = isEdit
    }

    override fun onEditStateChange(isEdit: Boolean) {
        this.isEdit = isEdit
        (wrapper as? SideNavigationItemContainer)?.setEditState(isEdit)
        dragView.isEnabled = isEdit
        widgetFrame.isEnabled = isEdit
        changeTitleMode(this.isEdit)
        itemView.isEnabled = !isEdit
    }
}