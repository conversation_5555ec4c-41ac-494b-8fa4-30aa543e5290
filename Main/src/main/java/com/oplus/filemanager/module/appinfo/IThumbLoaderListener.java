/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : IThumbLoaderListener.java
 * * Description : FileManager, Interface providing UI refresh
 * * and add anniversary to remind.
 * * Version     : 1.0
 * * Date        : 2017/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/8/1  1.0    Interface providing UI refresh
 ***********************************************************************/
package com.oplus.filemanager.module.appinfo;

import android.graphics.Bitmap;
import android.widget.ImageView;
import android.widget.TextView;

public interface IThumbLoaderListener {
    void onLoad(ImageView view, Bitmap bitmap, String name, int type);

    void onLoadTextView(TextView view, TextView detailView, String title, String detail, String name);

    Bitmap fetchImage(String name, int type);

    String getTitleString();

    String getApkVersion();
}
