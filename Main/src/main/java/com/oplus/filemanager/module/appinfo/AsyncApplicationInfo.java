/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AsyncApplicationInfo.java
 * * Description : FileManager, Parsing application information
 * * and add anniversary to remind.
 * * Version     : 1.0
 * * Date        : 2017/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/8/1  1.0    Parsing application information
 ***********************************************************************/
package com.oplus.filemanager.module.appinfo;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.collection.LruCache;

import com.filemanager.common.DiskLruCache;
import com.filemanager.common.MyApplication;
import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.imageloader.BitmapCacheManager;
import com.filemanager.common.utils.Log;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;


public class AsyncApplicationInfo {
    protected static final String TAG = "AsyncApplicationInfo";

    public static final HashMap<String, String> sTitleCache = new HashMap<String, String>();
    protected Object mLock = new Object();
    private static final int CACHE_SIZE = 1024;

    private static final int DEFAULT_CACHE_SIZE = 1024 * 3;
    private static HashMap<String, String> sDetailCache = new HashMap<String, String>();
    private static AsyncApplicationInfo sInstance;

    private LruCache<String, Bitmap> mLruCache = new LruCache<String, Bitmap>(DEFAULT_CACHE_SIZE) {
        @Override
        protected int sizeOf(String key, Bitmap bitmap) {
            return (bitmap.getByteCount() / CACHE_SIZE);
        }

        @Override
        protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
            // modify for bug 693901, may use a recycled bitmap
            if (oldValue == newValue) {
                return;
            }
            if ((null != oldValue) && (!oldValue.isRecycled())) {
                Log.v(TAG, "entryRemoved");
                oldValue.recycle();
                oldValue = null;
            }
        }
    };

    private volatile boolean mStarted;
    private LoadThread mLoadThread;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private BlockingQueue<ImageViewName> mImageQueue = new LinkedBlockingQueue<ImageViewName>();

    private AsyncApplicationInfo() {
        loadThread();
    }

    public static synchronized AsyncApplicationInfo getInstance() {
        if (sInstance == null) {
            sInstance = new AsyncApplicationInfo();
        }
        return sInstance;
    }

    public void load(int type, final String name, final String oldPath, final ImageView imageView, TextView titleView,
                     TextView detailView, final IThumbLoaderListener imageLoader, File file) {
        if (type == MimeTypeHelper.UNKNOWN_TYPE) {
            type = MimeTypeHelper.getMediaType(oldPath);
        }
        String newName = name + file.lastModified();
        Bitmap bitmap = mLruCache.get(newName);
        final String title = sTitleCache.get(newName);
        final String detail = sDetailCache.get(newName);
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            if (null == bitmap) {
                try {
                    mImageQueue.put(new ImageViewName(name, imageView, titleView, detailView, imageLoader, type));
                } catch (InterruptedException e) {
                    Log.w(TAG, "put image view be interrupted.");
                }
            } else {
                if (checkPathIsChanged(name, imageView)) {
                    imageLoader.onLoad(imageView, bitmap, name, type);
                    imageLoader.onLoadTextView(titleView, detailView, title, detail, name);
                }
            }
        } else {
            if (null == bitmap) {
                try {
                    mImageQueue.put(new ImageViewName(name, imageView, null, null, imageLoader, type));
                } catch (InterruptedException e) {
                    Log.w(TAG, "put image view be interrupted.");
                }
            } else {
                if (checkPathIsChanged(name, imageView)) {
                    imageLoader.onLoad(imageView, bitmap, name, type);
                }
            }
        }
    }

    /**
     * Check the item of list view whether changed.If the imageView's tag is not
     * equals the last path ,it means the item is changed , image view not need
     * set image.
     *
     * @param name      the last path.
     * @param imageView
     * @return
     */
    private boolean checkPathIsChanged(final String name, final ImageView imageView) {
        return name.equals(imageView.getTag());
    }

    /**
     * Start a thread to fetch image view's bitmap and post thumb loader
     * runnable.
     */
    private void loadThread() {
        if (mLoadThread == null) {
            mLoadThread = new LoadThread(this);
            mLoadThread.start();
        }
    }

    private static class LoadThread extends Thread {
        private boolean mClosed = false;
        private WeakReference<AsyncApplicationInfo> mAsyncApplicationInfoWeakReference;

        public LoadThread(AsyncApplicationInfo asyncApplicationInfo) {
            mAsyncApplicationInfoWeakReference = new WeakReference<>(asyncApplicationInfo);
        }


        @Override
        public void run() {
            while (!mClosed) {
                if (mAsyncApplicationInfoWeakReference.get() != null) {
                    mAsyncApplicationInfoWeakReference.get().realLoad();
                }
            }
            if (mAsyncApplicationInfoWeakReference.get() != null) {
                mAsyncApplicationInfoWeakReference.get().clearAllInfo();
            }
        }

        public void close() {
            mClosed = true;
        }
    }

    private void stopLoadThread() {
        if (mLoadThread != null) {
            mLoadThread.close();
            mLoadThread.interrupt();
            mLoadThread = null;
        }
    }

    private void realLoad() {
        try {
            ImageViewName ivn = mImageQueue.take();
            if (ivn != null) {
                final String name = ivn.mName;
                final ImageView imageView = ivn.mView;
                final TextView textView = ivn.mTextView;
                final TextView detailView = ivn.mDetailView;
                final int type = ivn.mType;
                if (imageView.getVisibility() != View.VISIBLE) {
                    ivn = null;
                    return;
                }
                // fetch thumb image, if the the image of the
                // current list item 's file path ,not need fetch
                // it.
                if ((name != null) && checkPathIsChanged(name, imageView)) {
                    final File file = new File(name);
                    final String newName = name + file.lastModified();
                    final IThumbLoaderListener loader = ivn.mLoader;
                    DiskLruCache sDiskLruCache = BitmapCacheManager.getDiskLruCache(MyApplication.getSAppContext(), "bitmap");
                    Bitmap bitmap = BitmapCacheManager.getFromDiskCache(newName, sDiskLruCache);
                    if (bitmap == null) {
                        bitmap = loader.fetchImage(name, type);
                    }
                    final Bitmap fetchImage = bitmap;
                    final String title = loader.getTitleString();
                    final String version = loader.getApkVersion();
                    sTitleCache.put(newName, title);
                    sDetailCache.put(newName, version);
                    // If the the image of the current list item 's
                    // file path ,not need load it.
                    if (checkPathIsChanged(name, imageView) && (fetchImage != null)) {
                        if ((type != MimeTypeHelper.APPLICATION_TYPE) && (sDiskLruCache != null)) {
                            try {
                                BitmapCacheManager.addToDisk(newName, fetchImage, sDiskLruCache);
                            } catch (Exception e) {
                                Log.e(TAG, e.getMessage());
                            }
                        }
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                mLruCache.put(newName, fetchImage);
                                loader.onLoad(imageView, fetchImage, name, type);
                                loader.onLoadTextView(textView, detailView, title, version, name);
                            }
                        });
                    } else if (fetchImage == null) {
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                loader.onLoad(imageView, null, name, type);
                                loader.onLoadTextView(textView, detailView, title, version, name);
                            }
                        });
                    } else {
                        mLruCache.remove(name);
                        fetchImage.recycle();
                    }
                    try {
                        if (sDiskLruCache != null) {
                            sDiskLruCache.close();
                        }
                    } catch (IOException e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }

            if (!mStarted) {
                synchronized (mLock) {
                    while (!mStarted) {
                        mLock.wait();
                    }
                }
            }
        } catch (NullPointerException e) {
            clearAllInfo();
            Log.w(TAG, "load thumb image failed");
        } catch (InterruptedException e) {
            clearAllInfo();
            Log.w(TAG, "asynchronous thumbnail loader thread interrupted");
        }
    }

    private void clearAllInfo() {
        sInstance = null;
        clearAllCache();
    }

    public void clearAllCache() {
        if (null != mLruCache) {
            mLruCache.evictAll();
        }
        if (null != sTitleCache) {
            sTitleCache.clear();
        }
        if (null != sDetailCache) {
            sDetailCache.clear();
        }
    }

    public static void clearTitleAndDetailCache() {
        if (null != sTitleCache) {
            sTitleCache.clear();
        }
        if (null != sDetailCache) {
            sDetailCache.clear();
        }
        if (null != sInstance) {
            sInstance.stopLoadThread();
            sInstance = null;
        }
    }

    private static class ImageViewName {
        String mName;
        ImageView mView;
        TextView mTextView;
        TextView mDetailView;
        IThumbLoaderListener mLoader;
        int mType;

        public ImageViewName(String name, ImageView view, TextView textView, TextView detailView,
                             IThumbLoaderListener loader, int type) {
            this.mName = name;
            this.mView = view;
            this.mTextView = textView;
            this.mDetailView = detailView;
            this.mLoader = loader;
            this.mType = type;
        }
    }

    public void start() {
        mStarted = true;
        synchronized (mLock) {
            mLock.notifyAll();
        }
    }

    public void stop() {
        mStarted = false;
        synchronized (mLock) {
            mLock.notifyAll();
        }
    }
}
