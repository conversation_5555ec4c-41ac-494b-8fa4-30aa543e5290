/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: EditTextView.kt
 ** Description: create EditText
 ** Version: 1.0
 ** Date: 2025/1/14
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.text.InputFilter
import android.text.InputType
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import androidx.activity.ComponentActivity
import androidx.appcompat.widget.AppCompatEditText
import androidx.lifecycle.coroutineScope
import com.coui.appcompat.tooltips.COUIToolTips
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RenameErrorTipUtil
import com.filemanager.common.utils.RenameErrorTipUtil.ERROR_FILE_NAME_DUPLICATE
import com.filemanager.common.utils.RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG
import com.filemanager.common.utils.ShortCutUtils
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.provider.FileLabelDBHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File
import java.nio.charset.StandardCharsets

@SuppressLint("ViewConstructor")
class SideEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle
) : AppCompatEditText(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "SideEditText"
    }

    private var toolTips: COUIToolTips? = null
    private var layoutBackground: Drawable?
    private var bean: BaseFileBean? = null
    var fileServiceAction: IFileService? = null

    var exceededUpperLimit = false
    var isEdit = false

    //快捷文件夹的初始路径
    var filePath = ""
    var fileType = -1

    //快捷文件夹或标签名称
    var name = ""
    var activity: ComponentActivity? = null
    private var lastY: Int? = null
    private var lastX: Int? = null
    private var isNeedAddFilterAndListener: Boolean = true

    //记录的最新快捷文件夹和标签名称
    private var labelNewName = ""
    private var shortNewName = ""

    init {
        isEnabled = false
        layoutBackground = background
        background = null
        setSelectAllOnFocus(true)
        changeEditTextMode(isEdit)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return if (isEnabled) {
            super.onTouchEvent(event)
        } else {
            isEnabled
        }
    }

    @SuppressLint("Range")
    fun addOnScrollChangeListener() {
        viewTreeObserver.addOnGlobalLayoutListener {
            val positionList = intArrayOf(0, 0)
            getLocationOnScreen(positionList)
            if (lastY == null || lastX == null) {
                lastY = positionList[1]
                lastX = positionList[0]
            }
            if (positionList[1] != lastY || lastX != positionList[0]) {
                lastY = positionList[1]
                lastX = positionList[0]
                dismissToolTips()
            }
        }
    }

    @SuppressLint("ResourceAsColor")
    fun showNotice(type: Int) {
        val resources = context.resources ?: return
        val notice = RenameErrorTipUtil.getRenameErrorTips(type, resources)
        showToolTips(notice)
    }

    private fun onFocusChangeListener() {
        onFocusChangeListener = OnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                post {
                    dismissToolTips()
                    if (CategoryHelper.isShortcutFolderType(fileType)) {
                        renameShortCutFolder()
                    } else if (CategoryHelper.isLabelType(fileType)) {
                        renameLabel()
                    }
                }
            }
        }
    }

    private fun showToolTips(notice: String, view: View? = null) {
        if (toolTips == null) {
            toolTips = COUIToolTips(context, COUIToolTips.MODE_INFO)
        }
        dismissToolTips()
        toolTips?.apply {
            isFocusable = false
            setContent(notice)
            setDismissOnTouchOutside(false)
            setContentTextColor(resources.getColor(com.support.appcompat.R.color.Red50))
            if (view == null) {
                show(this@SideEditText)
            } else {
                show(view)
            }
        }
    }

    private fun dismissToolTips() {
        if (toolTips?.isShowing == true) {
            toolTips?.dismiss()
        }
    }

    private fun setBackground() {
        background = null
    }

    fun renameTo(): Boolean {
        return if (CategoryHelper.isShortcutFolderType(fileType)) {
            renameShortCutFolder()
        } else if (CategoryHelper.isLabelType(fileType)) {
            renameLabel()
        } else {
            false
        }
    }

    fun renameShortCutFolder(): Boolean {
        if (this.isEdit && name != text.toString()) {
            dismissToolTips()
            bean = BaseFileBean()
            bean?.mData = filePath
            bean?.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            if (CategoryHelper.isShortcutFolderType(fileType)) {
                var stateCode = -1
                if (text?.toString().isNullOrEmpty()) {
                    setText(name)
                    clearFocus()
                } else {
                    shortNewName = text.toString().trim()
                    runBlocking {
                        launch(Dispatchers.IO) {
                            val oldFile = File(bean?.mData)
                            val newFile = File(oldFile.parent, shortNewName)
                            if (!newFile.exists()) {
                                stateCode = fileServiceAction?.renameTo(context, oldFile, newFile) ?: -1
                            } else {
                                if (newFile.absolutePath != oldFile.absolutePath) {
                                    stateCode = ERROR_FILE_NAME_DUPLICATE
                                }
                            }
                            if (stateCode == KtConstants.OPERATE_OK) {
                                name = shortNewName
                                filePath = newFile.absolutePath
                            }
                        }
                    }
                }
                if (stateCode != KtConstants.OPERATE_OK) {
                    showNoticeByStateCode(stateCode)
                    return true
                }
                refreshCurrentPage()
            } else {
                Log.d(TAG, "cannot rename file")
            }
            clearFocus()
        }
        return true
    }

    private fun renameLabel(): Boolean {
        if (this.isEdit && name != text.toString()) {
            dismissToolTips()
            if (text?.isEmpty() == true) {
                setText(name)
                clearFocus()
                return true
            }
            labelNewName = text.toString().trim()
            activity?.lifecycle?.coroutineScope?.launch(Dispatchers.IO) {
                val label = FileLabelDBHelper.getFileLabelByName(labelNewName)
                if (label != null) {
                    withContext(Dispatchers.Main) {
                        if (isEdit) {
                            showToolTips(resources.getString(com.filemanager.common.R.string.toast_label_exist))
                        }
                    }
                    return@launch
                }
                val labelEntity = FileLabelDBHelper.getFileLabelByName(name)
                if (labelEntity != null) {
                    labelNewName.let {
                        FileLabelDBHelper.rename(labelEntity, it)
                        name = it
                    }
                }
                withContext(Dispatchers.Main) {
                    hint = labelNewName
                    labelEntity?.id?.let { refreshCurrentPage(it) }
                    clearFocus()
                }
            }
        }
        return true
    }

    private fun refreshCurrentPage(labelId: Long = -1) {
        if (CategoryHelper.isShortcutFolderType(fileType)) {
            bean?.let {
                (activity as? IRefreshActivityDataForCreateDir)?.renameToShortCutFolder(
                    File(File(filePath).parent, shortNewName).absolutePath, it
                )
            }
        } else if (CategoryHelper.isLabelType(fileType)) {
            (activity as? IRefreshActivityDataForCreateDir)?.renameToLabel(labelNewName, labelId)
        }
    }

    private fun showNoticeByStateCode(state: Int) {
        val notice = when (state) {
            KtConstants.FILE_NOT_EXIST,
            KtConstants.FILE_NO_EXIST -> context.resources.getString(com.filemanager.common.R.string.toast_file_not_exist)
            KtConstants.FILE_NAME_NULL -> context.resources.getString(com.filemanager.common.R.string.name_invalid_empty)
            ERROR_FILE_NAME_DUPLICATE -> context.resources.getString(com.filemanager.common.R.string.toast_folder_exist)
            KtConstants.OPERATE_FAILED -> {
                setText(name)
                context.resources.getString(com.filemanager.common.R.string.toast_rename_folder_error)
            }
            else -> ""
        }
        if (notice.isNotEmpty()) showToolTips(notice)
    }

    fun changeEditTextMode(isEdit: Boolean) {
        if (CategoryHelper.isShortcutFolderType(fileType)
            || CategoryHelper.isLabelType(fileType)) {
            inputType = InputType.TYPE_CLASS_TEXT
            if (isEdit) {
                setEditTextState(isEdit)
                setBackground()
                if (isNeedAddFilterAndListener) {
                    addInputFilter(this, mIllegalFilter)
                    addOnScrollChangeListener()
                    isNeedAddFilterAndListener = false
                }
                setText(name)
                hint = null
                ellipsize = null
                onFocusChangeListener()
            } else {
                setEditTextState(isEdit)
                clearFocus()
                renameTo()
                hint = name
                text = null
                ellipsize = TextUtils.TruncateAt.END
                onFocusChangeListener = null
                dismissToolTips()
                background = null
            }
            this.isEdit = isEdit
        }
    }

    private fun setEditTextState(enableEdit: Boolean) {
        isEnabled = enableEdit
        isClickable = enableEdit
        isFocusableInTouchMode = enableEdit
        isFocusable = enableEdit
    }

    private val mIllegalFilter = InputFilter { source, start, end, dest, dstart, dend ->
        dismissToolTips()
        if (EmojiUtils.containsIllegalCharFileName(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_INPUT_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        } else if (EmojiUtils.isContainEmoji(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        } else if ((start < end) && checkTextLength(source, dest, dend, dstart)) {
            showNotice(ERROR_FILE_NAME_TOO_LONG)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        }
        source
    }

    /**
     * source为即将输入的字符，dest为已经在输入框的字符，
     * 在计算长度时，要将选中的字符截取掉，然后计算长度，才能在选中部分文字时正确判断长度是否超出限制
     */
    private fun checkTextLength(source: CharSequence, dest: CharSequence, dend: Int, dstart: Int): Boolean {
        val newDest = dest.removeRange(dstart, dend)
        val charsLen = newDest.length + source.length
        val bytesLen = source.toString().toByteArray(StandardCharsets.UTF_8).size + newDest.toString().toByteArray(StandardCharsets.UTF_8).size
        if (CategoryHelper.isShortcutFolderType(fileType)) {
            val charLenExceedLimit = (charsLen > CommonConstants.CUSTOM_NAME_LEN)
            val charByteLenExceedLimit = (bytesLen > CommonConstants.NAME_BYTES_LEN)
            exceededUpperLimit = charLenExceedLimit || charByteLenExceedLimit
        } else if (CategoryHelper.isLabelType(fileType)) {
            val charLenExceedLimit = (charsLen > CommonConstants.LABEL_NAME_LEN)
            val charByteLenExceedLimit = (bytesLen > CommonConstants.LABEL_NAME_BYTES_LEN)
            exceededUpperLimit = charLenExceedLimit || charByteLenExceedLimit
        }
        return exceededUpperLimit
    }

    private fun addInputFilter(editText: EditText, inputFilter: InputFilter) {
        val filters: Array<InputFilter> = editText.filters
        var tmpFilter: Array<InputFilter?>? = null
        var length = 0
        if (filters == null) {
            tmpFilter = arrayOfNulls(1)
        } else {
            length = filters.size
            tmpFilter = arrayOfNulls(length + 1)
            System.arraycopy(filters, 0, tmpFilter, 0, length)
        }
        tmpFilter[length] = inputFilter
        editText.filters = tmpFilter
    }
}