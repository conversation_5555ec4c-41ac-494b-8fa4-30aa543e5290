/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : category fragment of home page
 * * Version     : 1.0
 * * Date        : 2020/6/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.ui.category

import android.animation.AnimatorSet
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewStub
import android.view.ViewTreeObserver
import android.view.inputmethod.InputMethodManager
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.collection.arrayMapOf
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PrivacyPasswordSettingCompat
import com.filemanager.common.compat.PrivacyPasswordSettingCompat.isRecentlyDeletedSwitchOpen
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.CURRENT_DIR
import com.filemanager.common.constants.KtConstants.STATE_MOUNTED
import com.filemanager.common.constants.KtConstants.TIME_GAP_ONE_SECOND
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragDropSelectionViewModel
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.isDraggingFromMac
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.DarkThemeLevelConfig
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.managers.SPManagerUtil.putValue
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.ArrayUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.EncryptViewUtils
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.LanguageUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.DialogUtil
import com.filemanager.common.view.SmoothRoundedCornersConstraintLayout
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.oplus.encrypt.FileEncryptFactor
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.HomePageAdMgr
import com.oplus.filemanager.filelabel.ui.MainLabelViewModel
import com.oplus.filemanager.interfaze.categorydfm.ICategoryDFMApi
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.clouddrivekit.ICloudDriveKit
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import com.oplus.filemanager.interfaze.questionnaire.IQuestionnaire
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CLOUD_DISK
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PRIVATE_SAFE
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.adapter.ExpandItemEvent
import com.oplus.filemanager.main.adapter.MainCategoryAdapter
import com.oplus.filemanager.main.adapter.MainExpandableAdapter
import com.oplus.filemanager.main.adapter.MainRemoteDeviceListAdapter
import com.oplus.filemanager.main.behavior.PrimaryTitleBehavior
import com.oplus.filemanager.main.ui.BaseMainFragment
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.category.MainCategoryViewModel.Companion.EDIT_STATE_ON
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.utils.MainAnimationUtil
import com.oplus.filemanager.main.utils.STORE_LARGE_FONT_SIZE
import com.oplus.filemanager.main.utils.StorageInfoUtils
import com.oplus.filemanager.main.utils.amplifyFont
import com.oplus.filemanager.main.utils.setStorageInfo
import com.oplus.filemanager.main.view.FormatTextView
import com.oplus.filemanager.parentchild.adapter.EditItemTouchCallback
import com.oplus.filemanager.parentchild.drag.ItemDisableDragAnimator
import com.oplus.filemanager.parentchild.ui.MainCombineFragment
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder
import com.oplus.filemanager.utils.checkIfExceedDays
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class MainCategoryFragment : BaseMainFragment(), View.OnClickListener {

    private var expandableRecyclerViewScrollState: Int = 0
    private var mRootView: View? = null
    private var mSavedInstanceState: Bundle? = null
    private var scrollAppBarLayout: RelativeLayout? = null
    private var behavior: PrimaryTitleBehavior? = null

    private var expandableRecyclerView: COUIRecyclerView? = null
    private var expandableAdapter: MainExpandableAdapter? = null
    private var isInitCategoryRecyclerView: Boolean = false

    //本地存储、OTG、SD card 、DFM 、云盘
    private var mStorageContentView: View? = null
    private var mMainStorageScrollView: HorizontalScrollView? = null
    private var mMainStorageLayout: ConstraintLayout? = null
    private var mLayoutPhoneSpace: ConstraintLayout? = null
    private var mPhoneSpaceTv: FormatTextView? = null
    private var mPhoneSpaceProgressBar: ProgressBar? = null
    private var mOtgSpaceProgressBar: ProgressBar? = null
    private var mSdCardSpaceProgressBar: ProgressBar? = null
    private var dfmSpaceProgressBar: ProgressBar? = null
    private var cdpView: View? = null
    private var iconCloud: ImageView? = null
    private var iconChipEncryptPrivateSafe: ImageView? = null
    private var isShowCloudIcon = false
    private var isShowPrivateSafeIcon = false
    private var mStorageLayoutTwo: ConstraintLayout? = null
    private var mSdcardTitleTv: TextView? = null
    private var mSdcardDesTv: FormatTextView? = null
    private var dfmStorageLayout: ConstraintLayout? = null
    private var dfmTitleTv: TextView? = null
    private var dfmSpaceTv: FormatTextView? = null
    private var mCloudDiskLayout: ConstraintLayout? = null
    private var mCloudDiskTitleTv: TextView? = null
    private var mCloudDiskDescTv: TextView? = null
    private var mStorageLayoutOne: ConstraintLayout? = null
    private var mOtgDesTv: FormatTextView? = null
    private var mOtgTitleTv: TextView? = null
    private var mCurrentMinTextSize: Int = STORE_LARGE_FONT_SIZE
    private var mOtgState = KtConstants.STATE_UNMOUNTED
    private var mSdCardState = KtConstants.STATE_UNMOUNTED
    private var mCloudDiskState = false
    private var mCheckBothUnInstall: Boolean = true
    private var mStorageOne: Storage? = null
    private var mStorageTwo: Storage? = null
    private var dfmStorage: Storage.DFMStorage? = null
    private var enterDrag: Boolean = false

    //远程设备
    private var remoteDeviceLayout: RelativeLayout? = null
    private var remoteDeviceRecyclerView: COUIRecyclerView? = null
    private var remoteDeviceAdapter: MainRemoteDeviceListAdapter? = null

    //支持芯片加密的私密保险箱，位于全部文件位置处
    private var mChipEncryptPrivateSafeLayout: View? = null
    private var mChipEncryptPrivateSafeTitleTv: TextView? = null
    private var mChipEncryptPrivateSafeDescTv: TextView? = null

    //分类部分视图
    private var itemView: View? = null
    private var mCategoryRecyclerView: COUIRecyclerView? = null
    private var mCategoryAdapter: MainCategoryAdapter? = null
    private var mCategoryDecoration: SpaceItemDecoration? = null

    //不支持芯片加密的私密保险箱，位于底部，在最近删除附近
    private var mPrivateSafeLayout: COUICardListSelectedItemLayout? = null
    private var mBrowserRootLayout: View? = null
    private var mLayoutRecycleBin: COUICardListSelectedItemLayout? = null
    private var mRecycleBinDesTv: TextView? = null
    private var mPrivacyLockIv: ImageView? = null

    private var fbaClear: COUIFloatingButton? = null
    private var scrollHelper: DragScrollHelper? = null

    private var labelViewModel: MainLabelViewModel? = null
    private var newLabelName: String? = null

    private var mMainCategoryViewModel: MainCategoryViewModel? = null
    private var mAppShowAnimatorSet: AnimatorSet? = null
    private var mAppHideAnimatorSet: AnimatorSet? = null
    private var mAdManager: HomePageAdMgr? = null
    private var mIsNeedSdLayoutAnim = false
    private val mStorageItemHelper by lazy { StorageLayoutCompatHelper() }
    private var hasInjectQuestionnaire = false
    private var isScrollToEnd = false
    private val handler: Handler = Handler(Looper.getMainLooper())
    private val mCategoryHelper by lazy { MainCategoryHelper() }
    private val itemDisableDragAnimator = ItemDisableDragAnimator()
    private val animationHelper = ItemAnimationHelper()
    private val mCloudStatisticsMap = arrayMapOf(
        appContext.resources.getString(com.filemanager.common.R.string.save_to_cloud_safely) to "0",
        appContext.resources.getString(com.filemanager.common.R.string.cloud_disk_subtitle_option_1) to "1",
        appContext.resources.getString(com.filemanager.common.R.string.cloud_disk_subtitle_option_4) to "4",
    )
    // 是否正在请求云盘剩余空间
    private var isProcessing: Boolean = false
    private var lastRecyclerViewScrollTime = 0L
    private val fileOperateController by lazy {
        val model = DragDropSelectionViewModel()
        NormalFileOperateController(
            lifecycle, CategoryHelper.CATEGORY_MAIN,
            model, SortHelper.FILE_TIME_REVERSE_ORDER
        ).also {
            it.setResultListener(object : FileOperatorListenerImpl(model) {
                override fun onActionDone(opType: Int, result: Boolean, data: Any?) {
                    super.onActionDone(opType, result, data)
                    if (opType == IFileOperate.OP_CREATE_SHORTCUT_FOLDER && result) {
                        val pair = data as? Pair<Long, String> ?: return
                        val name = File(pair.second).name
                        val number = (mMainCategoryViewModel?.shortcutFolderItemList?.value?.size ?: 0) + 1
                        startShortcutFolder(pair.first, name, pair.second, CategoryHelper.CATEGORY_FOLDER_GROUP + number)
                    }
                }
            })
        }
    }
    private val fileBrowser: IFileBrowser? by lazy {
        Injector.injectFactory<IFileBrowser>()
    }

    private val questionnaire: IQuestionnaire? by lazy {
        Injector.injectFactory<IQuestionnaire>()
    }

    private val categoryRemote: ICategoryRemoteDeviceApi? by lazy {
        Injector.injectFactory<ICategoryRemoteDeviceApi>()
    }

    private var mCategoryRecyclerViewClickListener = object : OnRecyclerItemClickListener {
        override fun onItemClick(view: View, position: Int) {
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            val isDragging = DragUtils.isDragging
            if (isEdit || isDragging || isDraggingFromMac) {
                Log.w(TAG, "mCategoryRecyclerViewClickListener -> is edit state, return")
                return
            }
            baseVMActivity?.let {
                mMainCategoryViewModel?.onCategoryItemClick(it, position)
            }
        }

        override fun onItemLongClick(view: View, position: Int) {}
    }
    private var mSupperRecyclerViewClickListener = object : OnRecyclerItemClickListener {
        override fun onItemClick(view: View, position: Int) {}

        override fun onItemLongClick(view: View, position: Int) {}

        override fun onSuperAppItemClick(data: MainCategoryItemsBean) {
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            if (isEdit) {
                Log.w(TAG, "onSuperAppItemClickListener -> is edit state, return")
                return
            }
            baseVMActivity?.let {
                mMainCategoryViewModel?.onSupperItemClick(it, data)
            }
        }

        override fun onSuperAppItemSwitchClick(dataList: MutableList<MainCategoryItemsBean>) {
            mMainCategoryViewModel?.saveMainSuperAppData(dataList)
        }
    }

    private var remoteDeviceRecyclerViewClickListener = object : OnRecyclerItemClickListener {
        override fun onItemClick(view: View, position: Int) {
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            //只要是拖拽状态，mac都不可点击
            if (isEdit || MainApi.isDragging(context as Activity)) {
                Log.w(TAG, "remoteDeviceRecyclerViewClickListener -> is edit state, return")
                return
            }
            baseVMActivity?.let { act ->
                val deviceBean = remoteDeviceAdapter?.getItem(position)
                deviceBean?.let { device ->
                    val deviceId = deviceBean.deviceId
                    MacDragUtil.MacDragObject.deviceId = deviceId
                    startRemoteDeviceFile(act, device)
                    OptimizeStatisticsUtil.clickHomeRemoteDeviceEvent(device.deviceStatus, StatisticsUtils.REMOTE_VALUE_DEV_TYPE_MAC)
                }
            }
        }

        override fun onItemLongClick(view: View, position: Int) {}
    }

    private fun startRemoteDeviceFile(activity: Activity, device: MainCategoryItemsBean) {
        val categoryRemoteDeviceApi = Injector.injectFactory<ICategoryRemoteDeviceApi>()
        getMainCombineFragment()?.currentSelectedRemoteDeviceId = device.deviceId
        if (FeatureCompat.isSmallScreenPhone) {
            categoryRemoteDeviceApi?.startFileBrowserActivity(
                activity,
                device.deviceId,
                device.name,
                device.deviceStatus,
                device.deviceSameAccount
            )
        } else {
            categoryRemoteDeviceApi?.startRemoteFragment(
                activity,
                device.sideCategoryType,
                device.deviceId,
                device.name,
                device.deviceStatus,
                device.deviceSameAccount
            )
        }
        StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_REMOTE_FILE_LIST, Constants.PAGE_MAIN)
    }

    private val editAnimationListener = object : ItemAnimationHelper.AnimationListener {

        /**
         * 进入编辑，enterEdit 为true，fraction 0 -> 1变化
         * 退出编辑，enterEdit 为false，fraction 1 -> 0变化
         */
        override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
            val disableAlpha = BaseOptionHolder.EDIT_VIEW_ALPHA
            val alpha = (1 - disableAlpha) * (1 - fraction) + disableAlpha
            mLayoutRecycleBin?.alpha = alpha
            mPrivateSafeLayout?.alpha = alpha
            mMainStorageScrollView?.alpha = alpha
            val isDragging = (baseVMActivity as? MainActivity)?.isDragging ?: false
            if (isDragging) { mCategoryRecyclerView?.alpha = disableAlpha }
            if (isDraggingFromMac) {
                mLayoutRecycleBin?.alpha = disableAlpha
            }
        }

        override fun onEditStateChange(isEdit: Boolean) {
            val isDragging = (baseVMActivity as? MainActivity)?.isDragging ?: false
            mCategoryRecyclerView?.isEnabled = !isDragging
            mLayoutRecycleBin?.setBackgroundAnimationEnabled(!isEdit)
            mPrivateSafeLayout?.setBackgroundAnimationEnabled(!isEdit)
            (mLayoutPhoneSpace as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
            (mStorageLayoutOne as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
            (mStorageLayoutTwo as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
            (dfmStorageLayout as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
            (mCloudDiskLayout as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
            (mChipEncryptPrivateSafeLayout as? SmoothRoundedCornersConstraintLayout)?.isEdit = isEdit
        }
    }

    private val onDeleteLabelCallback: (labelId: Long) -> Unit = { labelId ->
        expandableAdapter?.deleteLabelData(labelId)
        val labelEntry = labelViewModel?.uiState?.value?.mAllLabelsWithFilesList?.find { it.label.id == labelId }?.label
        labelViewModel?.deleteLabel(labelEntry)
        getMainCombineFragment()?.setShowDeleteDialogState(null)
    }
    private val onCreateLabelCallback: (newLabelName: String) -> Unit = {
        newLabelName = it
        labelViewModel?.addLabel(it)
        getMainCombineFragment()?.setShowAddLabelDialogState(false)
    }

    private var launcher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            Log.d(TAG, "launcher privacy password result ${it.resultCode}")
            if (it.resultCode == Activity.RESULT_OK) {
                baseVMActivity?.let { act ->
                    startRecycleBin(act)
                }
            }
        }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(IS_FOLD, UIConfigMonitor.instance.isScreenFold())
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ResponsiveUIConfig.getDefault(context)?.uiStatus?.value?.let {
            if ((savedInstanceState?.getBoolean(IS_FOLD) == true) && (it == UIConfig.Status.UNFOLD)) {
                Log.i(TAG, "ResponsiveUIConfig  flush")
                ResponsiveUIConfig.getDefault(context).flush(context)
            }
        }
        activity?.let {
            labelViewModel = ViewModelProvider(it)[MainLabelViewModel::class.java]
            mMainCategoryViewModel = ViewModelProvider(it)[MainCategoryViewModel::class.java]
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.main_category_fragment
    }

    fun getCategoryViewModel() = mMainCategoryViewModel

    override fun getViewModel(): ViewModel? = null

    override fun getRecyclerView(): RecyclerView? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            if (it is BaseVMActivity) {
                baseVMActivity = it
                mCategoryAdapter =
                    MainCategoryAdapter(it, <EMAIL>).apply {
                        setHasStableIds(true)
                    }
                remoteDeviceAdapter = MainRemoteDeviceListAdapter().apply {
                    setHasStableIds(true)
                    setOnRecyclerItemClickListener(remoteDeviceRecyclerViewClickListener)
                }
            } else {
                Log.w(TAG, "onAttach: activity not instance BaseVMActivity")
            }
        }
    }

    override fun initView(view: View) {
        mRootView = view
        initToolbar()
        initExpandableRecyclerview()
        initSuperLayout()
        fbaClear = view.findViewById(R.id.fba_clear)
        fbaClear?.mainFloatingButton?.contentDescription = appContext.resources.getString(
            com.filemanager.common.R.string.settings_function_menu_cleanup
        )
        fbaClear?.setFloatingButtonClickListener {
            if (Utils.isQuickClick()) return@setFloatingButtonClickListener
            cleanupGarbage()
        }
        (parentFragment as? MainCombineFragment)?.getMaskView()?.alpha = 0f
        (parentFragment as? MainCombineFragment)?.getMaskView()?.visibility = View.GONE
        setScrollBottomView()
    }

    /**
     * 初始化可折叠列表recyclerView
     */
    private fun initExpandableRecyclerview() {
        val context = baseVMActivity ?: return
        expandableAdapter = MainExpandableAdapter(context)
        setExpandableAdapterCallback()
        expandableRecyclerView = mRootView?.findViewById(R.id.main_expandable_recyclerview)
        expandableRecyclerView?.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = expandableAdapter
        }
        // 加载主页分组数据
        mMainCategoryViewModel?.loadMainCategoryList()
        //控件组提供的方法处理标题栏滚动不自然问题
        expandableRecyclerView?.apply {
            val rv = this
            rv.isUseNativeOverScroll = false
            onFlingListener = object : RecyclerView.OnFlingListener() {
                override fun onFling(velocityX: Int, velocityY: Int): Boolean {
                    return if (!rv.canScrollVertically(-1)) {
                        velocityY < FLING_SPEED_THRESHOLD
                    } else {
                        false
                    }
                }
            }
            addOnScrollListener(object : OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    if (SystemClock.elapsedRealtime() - lastRecyclerViewScrollTime > TIME_GAP_ONE_SECOND
                        && expandableRecyclerViewScrollState == RecyclerView.SCROLL_STATE_DRAGGING
                    ) {
                        val imm =
                            context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                        imm.hideSoftInputFromWindow(<EMAIL>, 0)
                    }
                    lastRecyclerViewScrollTime = SystemClock.elapsedRealtime()
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    expandableRecyclerViewScrollState = newState
                }
            })
        }
        scrollHelper = DragScrollHelper(expandableRecyclerView)
    }

    /**
     * 设置各个类别的回调
     */
    private fun setExpandableAdapterCallback() {
        //存储位置和分类
        expandableAdapter?.onStoragePanelInitViewCallback = {
            if (!isInitCategoryRecyclerView) {
                initStorageAndCategoryView(it)
                initDataInternal()
                initQuestionnaire()

                val storageItems = mMainCategoryViewModel?.storageUiState?.value?.storageItems
                storageItems?.let {
                    refreshStorageInfo(storageItems)
                }
                refreshCleanGarbageView()

                val isEdit = mMainCategoryViewModel?.isEdit() ?: false
                val isDragging = (baseVMActivity as? MainActivity)?.isDragging ?: false
                if (isDragging) {
                    it.isEnabled = false
                    mCategoryRecyclerView?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
                    mCategoryRecyclerView?.isEnabled = false
                } else {
                    it.isEnabled = true
                    mCategoryRecyclerView?.alpha = 1f
                    mCategoryRecyclerView?.isEnabled = true
                }
                val alpha = if (isEdit) BaseOptionHolder.EDIT_VIEW_ALPHA else 1.0f
                mMainStorageScrollView?.alpha = alpha

                isInitCategoryRecyclerView = true

                /*
                * 在此处检查是否跳转是为了让界面跳转有一个从主页到最近删除页的过渡。
                */
                if (!FeatureCompat.sIsLightVersion) {
                    checkJumpToRecycleBin()
                } else {
                    Log.d(TAG, "Light version no recycle bin, Can't jump.")
                }
            }
        }

        //来源
        expandableAdapter?.sourceItemClickListener = mSupperRecyclerViewClickListener
        expandableAdapter?.onSourceItemDropCallback = {
            mMainCategoryViewModel?.saveMainSuperAppData(it)
        }

        // 快捷文件夹
        expandableAdapter?.shortcutFolderEvent = setShortcutEvent()

        //标签
        expandableAdapter?.onLabelItemDeleteCallback = {
            getMainCombineFragment()?.setShowDeleteDialogState(it)
        }
        expandableAdapter?.onLabelItemDropCallback = {
            labelViewModel?.saveMainEditLabelData(it)
        }
        expandableAdapter?.onLabelItemClickCallback = {
            handleLabelItemClick(it)
        }

        //最近删除和私密保险箱
        expandableAdapter?.onMenuInitViewCallback = {
            initBrowserItemView(it)
        }

        expandableAdapter?.addEditAnimationListener(editAnimationListener)
    }

    private fun setShortcutEvent(): ExpandItemEvent<MainCategoryItemsBean> {
        val event = object : ExpandItemEvent<MainCategoryItemsBean>() {

            override fun onItemClick(position: Int, bean: MainCategoryItemsBean) {
                val isEdit = mMainCategoryViewModel?.isEdit() ?: false
                if (isEdit) {
                    Log.w(TAG, "setShortcutEvent.onItemClick -> is edit state, return")
                    return
                }
                if (bean.sideCategoryType == CategoryHelper.CATEGORY_ADD_FOLDER) {
                    getMainCombineFragment()?.showSelectShortcutFolderDialog()
                } else {
                    startShortcutFolder(bean.dbID, bean.name, bean.fileList.get(0), bean.sideCategoryType)
                }
            }

            override fun onDeleteItem(id: Long) {
                getMainCombineFragment()?.showDeleteShortcutFolderDialog(id) {
                    if (it) {
                        mMainCategoryViewModel?.loadShortcutFolders()
                    }
                }
            }

            override fun onItemDragged(list: MutableList<MainCategoryItemsBean>) {
                mMainCategoryViewModel?.launch {
                    withContext(Dispatchers.IO) {
                        val result = list.mapNotNull { it.dbID }
                        val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
                        shortcutFolderApi?.updateShortcutFolderUseTime(result)
                    }
                }
            }
        }
        return event
    }

    /**
     * 初始化存储位置和分类
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun initStorageAndCategoryView(storagePanelView: View) {
        mStorageContentView = storagePanelView
        mMainStorageScrollView =
            storagePanelView.findViewById<HorizontalScrollView?>(R.id.main_storage_scroll_view)
                .apply {
                    mStorageItemHelper.initScrollView(this)
                }
        mMainStorageLayout =
            storagePanelView.findViewById<ConstraintLayout?>(R.id.main_storage_layout)
                .apply {
                    mStorageItemHelper.initStorageLayout(this)
                }
        mLayoutPhoneSpace =
            storagePanelView.findViewById<ConstraintLayout?>(R.id.phone_storage).apply {
                mStorageItemHelper.initPhoneStorageLayout(this)
            }
        mLayoutPhoneSpace?.tag = DropTag(CategoryHelper.CATEGORY_FILE_BROWSER, DropTag.Type.ITEM_VIEW)

        mPhoneSpaceTv = storagePanelView.findViewById(R.id.phone_storage_space_desc)
        mPhoneSpaceProgressBar =
            storagePanelView.findViewById(R.id.phone_storage_space_progress)
        mCategoryRecyclerView =
            storagePanelView.findViewById(R.id.main_category_recycler_view)
        initCloudStorageView()
        initChipEncryptionPrivateSafeView()
        initRemoteDeviceView()

        //首页中插广告判断关闭后是否超过3天
        if (AdvertManager.isAdEnabled() && HomePageAdMgr.isSupportHomeAd()
            && !checkIfExceedDays(false, MAIN_AD_PERIOD)
            && isNetworkAvailable(appContext)//无网络、网络返回超时，不展示广告位
        ) {
            mAdManager = HomePageAdMgr(<EMAIL>).apply {
                setContainer(storagePanelView, R.id.main_ad_vsub)
            }
        }

        (storagePanelView.findViewById<ViewGroup>(R.id.category_content))?.layoutTransition?.setAnimateParentHierarchy(
            false
        )
    }

    /**
     * 初始化最近删除和私密保险箱
     */
    private fun initBrowserItemView(browserItemRootView: View) {
        Log.d(TAG, "initBrowserItemView ")
        mBrowserRootLayout = browserItemRootView
        initPrivateEntryView()
        initRecycleBinContentView()
        setBrowserMenuViewAndHeight()
    }

    override fun initData(savedInstanceState: Bundle?) {
        mSavedInstanceState = savedInstanceState
    }

    @Suppress("TooGenericExceptionCaught")
    private fun initDataInternal() {
        mCategoryRecyclerView?.apply {
            val spanCount = ItemDecorationFactory.getGridItemCount(
                activity,
                KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_MAIN_CATEGORY
            )
            tag = DropTag(CategoryHelper.CATEGORY_MAIN, DropTag.Type.ITEM_VIEW_PROHIBIT)
            mCategoryDecoration = SpaceItemDecoration(spanCount)
            addItemDecoration(mCategoryDecoration!!)
            itemAnimator = MainAnimationUtil.createCategoryItemAnimation()
            layoutManager = object : GridLayoutManager(context, spanCount) {
                override fun onLayoutChildren(
                    recycler: RecyclerView.Recycler,
                    state: RecyclerView.State
                ) {
                    try {
                        super.onLayoutChildren(recycler, state)
                    } catch (e: Exception) {
                        Log.w(TAG, "onLayoutChildren exception: ${e.message}")
                    }
                }
            }
            adapter = mCategoryAdapter
            mCategoryAdapter?.classPanelEvent = object : ExpandItemEvent<MainCategoryItemsBean>() {
                override fun onItemClick(position: Int, bean: MainCategoryItemsBean) {}

                override fun onDeleteItem(id: Long) {}

                override fun onItemDragged(list: MutableList<MainCategoryItemsBean>) {
                    mMainCategoryViewModel?.launch {
                        withContext(Dispatchers.IO) {
                            val orderList = ArrayList<Int>()
                            list.forEach { orderList.add(it.order) }
                            appContext.putValue(KtConstants.CLASS_ITEM_TYPE_NEED_LOAD, true)
                            appContext.putValue(KtConstants.CLASS_ITEM_TYPE, GsonUtil.toJson(orderList))
                        }
                    }
                }
            }
            val editTouchCallback = EditItemTouchCallback(activity, null)
            mCategoryAdapter?.mFiles?.let { files ->
                editTouchCallback.onItemMoveCallback = { viewHolder, target ->
                    (mCategoryAdapter?.classPanelEvent)?.classPanelMoveItem(viewHolder, target, files)
                }
                editTouchCallback.onItemDropCallback = {
                    (mCategoryAdapter?.classPanelEvent)?.onItemDragged(files)
                }
            }
            mCategoryAdapter?.editTouchHelper = ItemTouchHelper(editTouchCallback)
            mCategoryAdapter?.editTouchHelper?.attachToRecyclerView(mCategoryRecyclerView)
            isNestedScrollingEnabled = false
            setOverScrollEnable(false)
        }
        mLayoutPhoneSpace?.setOnClickListener(this)
        mPhoneSpaceProgressBar?.setOnClickListener(this)
        mPhoneSpaceProgressBar?.apply {
            max = KtConstants.MAX_PROGRESS_1000
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        mCategoryAdapter?.apply {
            setOnRecyclerItemClickListener(mCategoryRecyclerViewClickListener)
            setItemWidth(
                KtViewUtils.getGridItemWidth(
                    activity,
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp),
                    ItemDecorationFactory.getGridItemCount(
                        activity,
                        KtConstants.SCAN_MODE_GRID,
                        ItemDecorationFactory.GRID_ITEM_DECORATION_MAIN_CATEGORY
                    ),
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp) * 2
                )
            )
            setMainCategoryHelper(mCategoryHelper)
        }
        mMainCategoryViewModel?.loadSuperAppData()
        refreshCleanGarbageView()
    }

    private fun initQuestionnaire() {
        if (PrivacyPolicyController.hasAgreeUseNet().not()) {
            return
        }
        if (!hasInjectQuestionnaire && (questionnaire?.isSupportQuestionnaire() == true)) {
            val viewStub = mStorageContentView?.findViewById<ViewStub>(R.id.view_stub_cdp_view)
            val view = viewStub?.inflate()
            val container = view?.findViewById<ConstraintLayout>(R.id.cdp_container)
            container?.let {
                cdpView = questionnaire?.getCdpView(baseVMActivity, container)
                hasInjectQuestionnaire = true
                it.addView(cdpView)
            }
        }
        questionnaire?.updateSpace(lifecycleScope, cdpView)
    }

    private fun initToolbar() {
        toolbar = mRootView?.findViewById(R.id.toolbar)
        scrollAppBarLayout = mRootView?.findViewById(R.id.appBarLayout)
        behavior = (scrollAppBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior as? PrimaryTitleBehavior
        behavior?.initBehavior(mRootView, R.id.main_expandable_recyclerview)
        val title = stringResource(com.filemanager.common.R.string.file)
        behavior?.setToolbarTitle(title, false)

        toolbar?.apply {
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            inflateMenu(this, isEdit)
            this.menu?.findItem(R.id.action_search)?.isVisible = true
        }
        mRootView?.apply {
            val paddingTop = COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity)
            setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
        }
    }

    private fun inflateMenu(toolbar: COUIToolbar, isEdit: Boolean) {
        if (!isEdit) {
            toolbar.menu.clear()
            toolbar.inflateMenu(R.menu.main_category_menu)
            toolbar.menu?.findItem(R.id.action_search)?.isVisible = true
            updateOWorkVisible()
            toolbar.findViewById<View>(R.id.no_action)?.apply {
                importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
        } else {
            toolbar.menu.clear()
            toolbar.inflateMenu(R.menu.main_category_edit_menu)
            toolbar.findViewById<View>(R.id.no_action_edit)?.apply {
                importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
        }
        toolbar.findViewById<View>(R.id.no_action)?.apply {
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        toolbar.setOnMenuItemClickListener { menu ->
            onMenuItemSelected(menu)
        }
    }

    override fun onTabSelected() {
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
        showEntranceReport()
        refreshCleanGarbageView()
        refreshEntryBoxView()
        checkJumpToSuperApp()
    }

    private fun refreshEntryBoxView() {
        if (mPrivateSafeLayout == null) {
            initPrivateEntryView()
        }
        setBrowserMenuViewAndHeight()
    }

    private fun refreshChipEncryptPrivateSafeView() {
        Log.d(TAG, "refreshPrivateSafeView")
        if (EncryptViewUtils.isShowEncryptView()) {
            if (mChipEncryptPrivateSafeLayout == null) {
                initChipEncryptionPrivateSafeView()
            }
            mChipEncryptPrivateSafeLayout?.visibility = View.VISIBLE
            mStorageItemHelper.displayPrivate = true
            if (mStorageItemHelper.displayCloud && getStorageCardShow().not()) {
                isShowCloudIcon = false
                isShowPrivateSafeIcon = false
            } else {
                isShowPrivateSafeIcon = true
            }
            setChipEncryptPrivateSafeIconVisible()
            updateCloudDiskAndPrivateSafe(true, isScrollToEnd)
        } else {
            mStorageItemHelper.displayPrivate = false
            if (mStorageItemHelper.displayCloud) {
                isShowCloudIcon = true
            }
            updateCloudDiskAndPrivateSafe(false)
        }
    }

    private fun refreshRemoteDeviceUIState(item: Storage.RemoteDeviceStorage) {
        mStorageItemHelper.relayoutRemoteDeviceLayout(
            !getCloudOrChipEncryptPrivateLayoutStatus(),
            !item.deviceList.isNullOrEmpty(),
            isScrollToEnd
        )
        if (item.deviceList.isNullOrEmpty()) {
            remoteDeviceLayout?.visibility = View.GONE
        } else {
            remoteDeviceLayout?.visibility = View.VISIBLE
            item.deviceList?.let {
                val categoryList = mapRemoteDeviceCategoryBeanList(it)
                remoteDeviceAdapter?.setData(categoryList)
                jumpRemoteDeviceFileIfNeed(categoryList)
            }
        }
    }

    /**
     * 将加载出来的远程设备数据映射为小屏可以渲染的数据
     */
    private fun mapRemoteDeviceCategoryBeanList(deviceList: List<RemoteDeviceInfo>): ArrayList<MainCategoryItemsBean> {
        val categoryList = ArrayList<MainCategoryItemsBean>()
        var categoryType = CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC + 1
        for (device in deviceList) {
            val categoryItem = MainCategoryItemsBean()
            categoryItem.sideCategoryType = categoryType
            categoryItem.name = device.deviceName
            categoryItem.itemsCount = 0
            categoryItem.iconId = com.filemanager.common.R.drawable.color_tool_menu_ic_label_new
            categoryItem.deviceId = device.deviceId
            categoryItem.deviceStatus = device.deviceStatus
            categoryItem.deviceSameAccount = device.sameAccount

            categoryList.add(categoryItem)
            categoryType++
        }
        return categoryList
    }

    private fun jumpRemoteDeviceFileIfNeed(data: ArrayList<MainCategoryItemsBean>) {
        val activity = baseVMActivity ?: return
        val intent = activity.intent
        val fromRemoteControlJumpType = IntentUtils.getInt(intent, Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
        val deviceId = IntentUtils.getString(intent, Constants.KEY_REMOTE_DEVICE_ID)
        val deviceName = IntentUtils.getString(intent, Constants.KEY_DEVICE_NAME)
        Log.d(TAG, "jumpRemoteDeviceFileIfNeed jumpType:$fromRemoteControlJumpType id:$deviceId name:$deviceName")
        if (fromRemoteControlJumpType != Constants.REMOTE_CONTROL_JUMP_TYPE_MAC_FILE || deviceId.isNullOrEmpty()) {
            return
        }
        var deviceBean: MainCategoryItemsBean? = null
        data.forEach {
            if (it.deviceId?.equals(deviceId) == true) {
                deviceBean = it
                return@forEach
            }
        }
        deviceBean?.let { device ->
            baseVMActivity?.let { act ->
                // 跳转到远程设备文件页面
                startRemoteDeviceFile(act, device)
                intent.putExtra(Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
            }
        }
    }

    private fun getStorageCardShow(): Boolean {
        return (mStorageLayoutOne?.isVisible ?: false) || (mStorageLayoutTwo?.isVisible
            ?: false) || (dfmStorageLayout?.isVisible ?: false) || (remoteDeviceLayout?.isVisible ?: false)
    }

    private fun setChipEncryptPrivateSafeIconVisible() {
        mChipEncryptPrivateSafeTitleTv?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                mChipEncryptPrivateSafeTitleTv?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                if (isShowPrivateSafeIcon) {
                    val titleH = mChipEncryptPrivateSafeTitleTv?.height ?: 0
                    val titleMarginTop = mChipEncryptPrivateSafeTitleTv?.marginTop ?: 0
                    val descH = mChipEncryptPrivateSafeDescTv?.height ?: 0
                    val descMarginTop = mChipEncryptPrivateSafeDescTv?.marginTop ?: 0
                    val itemH =
                        appContext.resources.getDimensionPixelSize(R.dimen.main_storage_height)
                    val iconH =
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_36dp)
                    val iconMarginBottom = iconChipEncryptPrivateSafe?.marginBottom ?: 0
                    val remainH = itemH - titleH - titleMarginTop - descH - descMarginTop
                    if (remainH > iconH + iconMarginBottom) {
                        iconChipEncryptPrivateSafe?.visibility = View.VISIBLE
                    } else {
                        iconChipEncryptPrivateSafe?.visibility = View.GONE
                    }
                } else {
                    iconChipEncryptPrivateSafe?.visibility = View.GONE
                }
            }
        })
    }

    private fun updateCloudDiskAndPrivateSafe(isShow: Boolean, isScroll: Boolean = false) {
        mStorageItemHelper.reLayoutPrivateSafeLayout(isShow, isScroll)
        mStorageItemHelper.updateTextSize(mChipEncryptPrivateSafeTitleTv, mChipEncryptPrivateSafeDescTv)
        mStorageItemHelper.updateTextSize(mCloudDiskTitleTv, mCloudDiskDescTv)
        if (isShow) {
            mStorageItemHelper.refreshTextViewDisplayLine(mChipEncryptPrivateSafeTitleTv, mChipEncryptPrivateSafeDescTv)
        }
        mStorageItemHelper.refreshTextViewDisplayLine(mCloudDiskTitleTv, mCloudDiskDescTv)
    }

    private fun showEntranceReport() {
        if (!mIsReportShowEntrance) {
            return
        }
        val oapsAction = Injector.injectFactory<IOapsLib>()
        if (oapsAction?.isShowHomeEntrance() == true) {
            mIsReportShowEntrance = false
            mIsOnResumeRePort = false
            Log.d(TAG, "home entrance show report")
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.OAPS_SHOW_APPSTORE_ENTRANCE_HOME_COUNT
            )
        }
    }

    override fun startObserve() {
        Log.i(TAG, "startObserve")
        // Storage Info Part
        launchAndRepeatWithViewLifecycle {
            Log.i(TAG, "launchAndRepeatWithViewLifecycle callback")
            mMainCategoryViewModel?.storageUiState
                ?.collect { storage ->
                    refreshStorageInfo(storage.storageItems)
                    activity?.let { mStorageItemHelper.reLayoutStorageLayout(storage.storageItems) }
                }
        }

        mMainCategoryViewModel?.mMainCategoryItemList?.observe(
            this
        ) {
            mCategoryAdapter?.setData(it)
        }

        // 最近删除
        mMainCategoryViewModel?.mDeleteState?.observe(this) {
            Log.d(TAG, "start observe mDeleteState $it")
            setRecycleBinText(it)
            // 设置完文字后 重新刷新高度，防止高度出现问题
            setBrowserMenuViewAndHeight()
        }

        // super app
        mMainCategoryViewModel?.mMainSuperAppItemList?.observe(this) { list ->
            Log.d(TAG, "showSuperAppFile listSuperApp is null or empty")
            list?.let {
                val showList = list.filter { it.isShow }.toMutableList()
                expandableAdapter?.updateSuperAppData(showList)
                checkJumpToSuperApp()
            }
        }

        // 重新加载数据
        launchAndRepeatWithViewLifecycle {
            mMainCategoryViewModel?.reloadData?.collect {
                Log.d(TAG, "reloadData -> $it")
                if (it) {
                    onResumeLoadData()
                } else {
                    //父子级切换时，不会执行此加载事件，需要不加载数据时重新初始化。之前每次在onResumeLoadData中执行不需要改变
                    initQuestionnaire()
                }
            }
        }
        // 快捷文件夹
        startShortcutFolderObserve()

        //标签数据
        startLabelDataObserve()

        //编辑状态
        startEditStateObserve()

        // 主页分组数据
        mMainCategoryViewModel?.mMainGroupListData?.observe(this) {
            expandableAdapter?.setData(it)
        }
    }

    private fun updateCloudSubtitle() {
        val cloudDriveKitApi = Injector.injectFactory<ICloudDriveKit>()
        if (PrivacyPolicyController.hasAgreeUseNet().not() || cloudDriveKitApi == null || activity == null) {
            Log.d(TAG, "updateCloudSubtitle->Not agreed use net or Feature not supported or activity is null")
            cloudStatistics()
            isProcessing = false
            return
        }
        lifecycleScope.launch(context = Dispatchers.IO) {
            cloudDriveKitApi.isCloudSpaceFull(requireActivity().application) { isCloudSpaceFull ->
                mCloudDiskDescTv?.post {
                    val currentSubtitle = mCloudDiskDescTv?.text.toString()
                    val newSubtitle = if (isCloudSpaceFull == true) {
                        if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
                            appContext.resources.getString(com.filemanager.common.R.string.cloud_disk_subtitle_option_4)
                        } else if (LanguageUtil.isZHCN()) {
                            appContext.resources.getString(com.filemanager.common.R.string.cloud_disk_subtitle_option_1)
                        } else {
                            appContext.resources.getString(com.filemanager.common.R.string.cloud_disk_subtitle_option_4)
                        }
                    } else {
                        appContext.resources.getString(com.filemanager.common.R.string.save_to_cloud_safely)
                    }
                    if ((newSubtitle != currentSubtitle)) {
                        mCloudDiskDescTv?.text = newSubtitle
                        val currentLanguage = LanguageUtil.getCurrentLocalLanguage()
                        val currentCountry = LanguageUtil.getCurrentLocalCountry()
                        // 存储当前显示的副标题内容和当前本地语言
                        PreferencesUtils.put(
                            key = CommonConstants.CLOUD_DRIVE_SUBTITLE,
                            value = newSubtitle + PARTITION_CHARACTER + currentLanguage + PARTITION_CHARACTER + currentCountry
                        )
                    }
                    cloudStatistics()
                    isProcessing = false
                }
            }
        }
    }

    private fun cloudStatistics() {
        mCloudDiskDescTv?.let {
            val map = arrayMapOf(StatisticsUtils.CLOUD_DRIVER_SUBTITLE to mCloudStatisticsMap[it.text])
            StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPPORT_CLOUDDRIVER, map)
        }
    }

    private fun startShortcutFolderObserve() {
        mMainCategoryViewModel?.shortcutFolderItemList?.observe(this) {
            expandableAdapter?.updateShortcutFolders(it)
        }
    }

    private fun startLabelDataObserve() {
        labelViewModel?.uiState?.observe(this) { uiModel ->
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            Log.d(TAG, "startObserve label list ${uiModel.mAllLabelsWithFilesList.size} isEdit $isEdit")
            val categoryList = mapLabelCategoryBeanList(uiModel)
            expandableAdapter?.updateLabelData(categoryList)
            if (!isEdit && newLabelName != null) {
                val bean = categoryList.find { it.name == newLabelName }
                bean?.let {
                    startLabelPage(bean.dbID, bean.name, bean.sideCategoryType)
                }
                newLabelName = null
            }
        }

        labelViewModel?.errorState?.observe(this) { errorCode ->
            if (errorCode == MainLabelViewModel.ERROR_ADD_FAILED) {
                CustomToast.showShort(com.filemanager.common.R.string.phone_storage_can_not_save)
                labelViewModel?.errorState?.value = 0
            }
        }
    }

    private fun startEditStateObserve() {
        mMainCategoryViewModel?.editState?.observe(this) { editState ->
            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            Log.d(TAG, "startEditStateObserve editState $editState isEdit $isEdit")
            if (editState != MainCategoryViewModel.EDIT_STATE_IDLE) {
                (parentFragment as? MainCombineFragment)?.setEditState(isEdit)
                toolbar?.let {
                    inflateMenu(it, isEdit)
                }
                if (isEdit) {
                    setEditCallback()
                    enterUiEditState()
                } else {
                    exitUiEditState()
                    activity?.lifecycle?.coroutineScope?.launch(Dispatchers.IO) {
                        delay(ItemAnimationHelper.DEFAULT_ENTER_EDIT_ANIMATION_DURATION) {
                            mMainCategoryViewModel?.loadShortcutFolders()
                            labelViewModel?.loadLabels()
                        }
                    }
                }
                expandableAdapter?.startAnimation(isEdit)
                mCategoryAdapter?.startAnimation(isEdit)
                mMainCategoryViewModel?.updateMainCategoryList()
                expandableAdapter?.updateSuperAppData()
                remoteDeviceAdapter?.startAnimation(isEdit)
            }
        }
    }

    private fun setEditCallback() {
        getMainCombineFragment()?.onDeleteLabelCallback = onDeleteLabelCallback
        getMainCombineFragment()?.checkLabelMaxCountCallback = {
            labelViewModel?.isReachLabelMaxCount() ?: false
        }
    }

    private fun enterUiEditState() {
        val titleEdit = stringResource(com.filemanager.common.R.string.menu_recent_file_edit)
        behavior?.setToolbarTitle(titleEdit, false)
        (baseVMActivity as? MainActivity)?.enableViewPagerScroll(false)
        (baseVMActivity as? MainActivity)?.enableNavigationTab(false)
    }

    private fun exitUiEditState() {
        val titleEdit = stringResource(com.filemanager.common.R.string.file)
        behavior?.setToolbarTitle(titleEdit, false)
        (baseVMActivity as? MainActivity)?.enableViewPagerScroll(true)
        (baseVMActivity as? MainActivity)?.enableNavigationTab(true)
    }

    /**
     * 改变侧导的编辑模式
     */
    private fun changeEditState() {
        mMainCategoryViewModel?.changeEditState()
    }

    fun completeEditState(): Boolean {
        val isEdit = mMainCategoryViewModel?.isEdit() ?: false
        if (isEdit) {
            changeEditState()
        }
        return isEdit
    }

    /**
     * 将加载出来的标签数据映射为小屏可以渲染的数据
     */
    private fun mapLabelCategoryBeanList(uiModel: MainLabelViewModel.LabelUiModel): ArrayList<MainCategoryItemsBean> {
        val labelEntryWrapperList = uiModel.mAllLabelsWithFilesList
        val categoryList = ArrayList<MainCategoryItemsBean>()
        var categoryType = CategoryHelper.CATEGORY_LABEL_GROUP + 1
        for (wrapper in labelEntryWrapperList) {
            val categoryItem = MainCategoryItemsBean()
            categoryItem.sideCategoryType = categoryType
            categoryItem.name = wrapper.name
            categoryItem.itemsCount = wrapper.filesCount.toLong()
            categoryItem.iconId = com.filemanager.common.R.drawable.color_tool_menu_ic_label_new
            categoryItem.dbID = wrapper.label.id

            categoryList.add(categoryItem)
            categoryType++
        }
        val addLabelBean = MainCategoryItemsBean()
        addLabelBean.sideCategoryType = CategoryHelper.CATEGORY_ADD_LABEL
        addLabelBean.name = stringResource(com.filemanager.common.R.string.menu_new_folder)
        addLabelBean.iconId = R.drawable.ic_category_add
        categoryList.add(addLabelBean)

        return categoryList
    }

    private fun refreshStorageInfo(storageItems: List<Storage>) {
        Log.d(TAG, "refreshStorageInfo -> storageItems = ${storageItems.size}")
        storageItems.forEach { item ->
            when (item) {
                is Storage.PhoneStorage -> {
                    refreshPhoneUIState(item)
                }

                is Storage.OTGStorage -> {
                    mOtgState = item.state
                    refreshStorageUIState(item)
                }

                is Storage.SDCardStorage -> {
                    mSdCardState = item.state
                    refreshStorageUIState(item)
                }

                is Storage.DFMStorage -> refreshDfmUIState(item)
                is Storage.CloudDiskStorage -> {
                    mCloudDiskState = item.isSingedIn
                    refreshCloudDiskUIState(item)
                }

                is Storage.PrivateSafeStorage ->
                    refreshChipEncryptPrivateSafeView()

                is Storage.RemoteDeviceStorage -> {
                    Log.d(TAG, "refreshStorageInfo -> RemoteDeviceStorage")
                    refreshRemoteDeviceUIState(item)
                }
            }
        }
    }

    private fun refreshStorageUIState(info: Storage) {
        Log.d(
            TAG, "refreshStorageUIState -> state=${getStorageState(info)} " +
                    "kind -> ${if (info is Storage.SDCardStorage) " SD " else " OTG "}"
        )
        when (getStorageState(info)) {
            STATE_MOUNTED -> {
                bindCardTag(info)
                Log.d(TAG, "refreshStorageUIState -> STATE_MOUNTED")
                //给卡片1或卡片2赋值
                if (mStorageLayoutOne == null || mOtgTitleTv?.text?.toString()
                        .equals(getString(com.filemanager.common.R.string.sdcard_checking)) || getStorageOneIsThisInfoKind(
                        info
                    )
                ) {
                    Log.d(TAG, "refreshStorageUIState -> STATE_MOUNTED one")
                    if (!getStorageTwoIsThisInfoKind(info)) {
                        Log.d(TAG, "refreshStorageUIState -> STATE_MOUNTED one handle")
                        handleStorageOne(info)
                    }
                } else {
                    Log.d(TAG, "refreshStorageUIState -> STATE_MOUNTED two")
                    if (getStorageTwoIsThisInfoKind(info)) {
                        handleStorageTwo(info)
                    }
                }
            }

            KtConstants.STATE_CHECKING -> {
                if (getStorageOneLayoutStatus() && mOtgTitleTv?.text.toString() != getString(com.filemanager.common.R.string.sdcard_checking)) {
                    mSdCardSpaceProgressBar?.progress = 0
                    mSdCardSpaceProgressBar?.setTag(com.filemanager.common.R.id.porgress_tag_id, 0)
                    mSdcardDesTv?.text = ""
                    initExternalStorageTwoView()
                    mStorageLayoutTwo?.apply {
                        if (this.visibility != View.VISIBLE) {
                            mStorageItemHelper.reLayoutSdCardLayout(
                                !(getCloudOrChipEncryptPrivateLayoutStatus() || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                                true,
                                isScroll = isScrollToEnd
                            )
                        }
                        mSdcardDesTv?.visibility = View.GONE
                        mSdcardTitleTv?.setText(com.filemanager.common.R.string.sdcard_checking)
                        mIsNeedSdLayoutAnim = false
                    }
                } else {
                    mOtgSpaceProgressBar?.progress = 0
                    mOtgSpaceProgressBar?.setTag(com.filemanager.common.R.id.porgress_tag_id, 0)
                    mOtgDesTv?.text = ""
                    initExternalStorageOneView()
                    mStorageLayoutOne?.isClickable = false
                    mStorageLayoutOne?.apply {
                        if (this.visibility != View.VISIBLE) {
                            mStorageItemHelper.reLayoutOtgLayout(
                                !(getStorageTwoLayoutStatus() || getCloudOrChipEncryptPrivateLayoutStatus()
                                        || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                                isShow = true, isScroll = isScrollToEnd
                            )
                        }
                        mOtgDesTv?.visibility = View.GONE
                        mOtgTitleTv?.setText(com.filemanager.common.R.string.sdcard_checking)
                        mIsNeedSdLayoutAnim = false
                    }
                }
            }

            else -> {
                handleStateUnmounted(info)
            }
        }
    }

    private fun bindCardTag(info: Storage) {
        //检测到MOUNTED事件时，判断之前如果卡一卡二都未安装的话，清除卡一卡二的绑定内容
        if (mCheckBothUnInstall) {
            mStorageOne = null
            mStorageTwo = null
        }
        mCheckBothUnInstall = false
        //绑定卡一或卡二的类型
        if (mStorageOne == null) {
            mStorageOne = info
        } else if (mStorageTwo == null && !getStorageOneIsThisInfoKind(info)) {
            mStorageTwo = info
        }
    }

    private fun getStorageState(info: Storage): Int {
        if (info is Storage.SDCardStorage) {
            return info.state
        } else if (info is Storage.OTGStorage) {
            return info.state
        }
        return 0
    }

    private fun getStorageOneIsThisInfoKind(info: Storage): Boolean {
        if (info is Storage.SDCardStorage) {
            return mStorageOne is Storage.SDCardStorage
        } else if (info is Storage.OTGStorage) {
            return mStorageOne is Storage.OTGStorage
        }
        return false
    }

    private fun getStorageTwoIsThisInfoKind(info: Storage): Boolean {
        if (info is Storage.SDCardStorage) {
            return mStorageTwo is Storage.SDCardStorage
        } else if (info is Storage.OTGStorage) {
            return mStorageTwo is Storage.OTGStorage
        }
        return false
    }

    private fun handleStorageOne(info: Storage) {
        Log.d(TAG, "handleStorageOne start")
        initExternalStorageOneView()
        mStorageLayoutOne?.apply {
            if (visibility != View.VISIBLE) {
                mStorageItemHelper.reLayoutOtgLayout(
                    !(getStorageTwoLayoutStatus() || getCloudOrChipEncryptPrivateLayoutStatus()
                            || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                    isShow = true, isScroll = isScrollToEnd
                )
                nearMeStatistics(info)
            } else {
                visibility = View.VISIBLE
            }
            Log.d(TAG, "handleStorageOne setStorageOne")
            setStorageOne(info)
        }
    }

    private fun handleStorageTwo(info: Storage) {
        initExternalStorageTwoView()
        mStorageLayoutTwo?.apply {
            if ((visibility != View.VISIBLE)) {
                mStorageItemHelper.reLayoutSdCardLayout(
                    !(getCloudOrChipEncryptPrivateLayoutStatus() || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                    true,
                    isScroll = isScrollToEnd
                )
                nearMeStatistics(info)
            }
            setStorageTwo(info)
        }
    }

    private fun nearMeStatistics(info: Storage) {
        if (info is Storage.SDCardStorage) {
            StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.SDCARD)
        } else if (info is Storage.OTGStorage) {
            if (VolumeEnvironment.getExternalOTGState(activity, false)) {
                StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.OTG)
            }
        }
    }

    private fun setStorageOne(info: Storage) {
        Log.d(TAG, "setStorageOne -> start mOtgDesTv $mOtgDesTv")
        mOtgDesTv?.visibility = View.VISIBLE
        mStorageLayoutOne?.isClickable = true
        if (info is Storage.SDCardStorage) {
            Log.d(TAG, "setStorageOne -> SDCardStorage")
            //这个是直接使用sdcard的词条，没有使用sdcard的具体文件或路径
            mOtgTitleTv?.setText(com.filemanager.common.R.string.storage_external)
            mOtgDesTv?.setStorageInfo(
                info.sdCardAvailableSize,
                info.sdCardTotalSize,
                object : FormatTextView.ChangeAllFontSize {
                    override fun callBack(currentTextSize: Int) {
                        if (currentTextSize < mCurrentMinTextSize) {
                            mCurrentMinTextSize = currentTextSize
                            updateOtherTextSize()
                        }
                    }
                })
            if (mOtgSpaceProgressBar?.isAnimating == true) {
                Log.d(TAG, "showSDCardSpaceSize in animating")
                return
            }
            val newProgress =
                Utils.calcSpaceSizeRatio(info.sdCardAvailableSize, info.sdCardTotalSize)
            MainAnimationUtil.startProgressAnimation(mOtgSpaceProgressBar, newProgress)
        } else if (info is Storage.OTGStorage) {
            Log.d(TAG, "setStorageOne -> OTGStorage")
            //这个是直接使用OTG的词条，没有使用OTG的具体文件或路径
            mOtgTitleTv?.setText(com.filemanager.common.R.string.storage_otg)
            Log.d(TAG, "setStorageOne -> otgSize ${info.otgSize}")
            info.otgSize?.let { otgSize ->
                Log.d(TAG, "setStorageOne -> mOtgDesTv $mOtgDesTv")
                mOtgDesTv?.apply {
                    mCurrentTextSize = mCurrentMinTextSize
                    Log.d(
                        TAG,
                        "setStorageOne -> otgSize.first ${otgSize.first} otgSize.second ${otgSize.second}"
                    )
                    setStorageInfo(otgSize.second, otgSize.first, object :
                        FormatTextView.ChangeAllFontSize {
                        override fun callBack(currentTextSize: Int) {
                            if (currentTextSize < mCurrentMinTextSize) {
                                mCurrentMinTextSize = currentTextSize
                                updateOtherTextSize()
                            }
                        }
                    })
                    Log.d(TAG, "setStorageOne -> mOtgDesTv end")
                }
                if (mOtgSpaceProgressBar?.isAnimating == true) {
                    Log.d(TAG, "showOtgSpaceSize in animating")
                    return
                }
                val newProgress = Utils.calcSpaceSizeRatio(otgSize.second, otgSize.first)
                MainAnimationUtil.startProgressAnimation(mOtgSpaceProgressBar, newProgress)
            }
        }
    }

    private fun setStorageTwo(info: Storage) {
        mSdcardDesTv?.visibility = View.VISIBLE
        if (info is Storage.SDCardStorage) {
            mSdcardTitleTv?.setText(com.filemanager.common.R.string.storage_external)
            mSdcardDesTv?.mCurrentTextSize = mCurrentMinTextSize
            mSdcardDesTv?.setStorageInfo(
                info.sdCardAvailableSize,
                info.sdCardTotalSize,
                object : FormatTextView.ChangeAllFontSize {
                    override fun callBack(currentTextSize: Int) {
                        if (currentTextSize < mCurrentMinTextSize) {
                            mCurrentMinTextSize = currentTextSize
                            updateOtherTextSize()
                        }
                    }
                })
            if (mSdCardSpaceProgressBar?.isAnimating == true) {
                Log.d(TAG, "showSDCardSpaceSize in animating")
                return
            }
            val newProgress =
                Utils.calcSpaceSizeRatio(info.sdCardAvailableSize, info.sdCardTotalSize)
            MainAnimationUtil.startProgressAnimation(mSdCardSpaceProgressBar, newProgress)
        } else if (info is Storage.OTGStorage) {
            mSdcardTitleTv?.setText(com.filemanager.common.R.string.storage_otg)
            info.otgSize?.let { otgSize ->
                mSdcardDesTv?.apply {
                    mCurrentTextSize = mCurrentMinTextSize
                    setStorageInfo(otgSize.second, otgSize.first, object :
                        FormatTextView.ChangeAllFontSize {
                        override fun callBack(currentTextSize: Int) {
                            if (currentTextSize < mCurrentMinTextSize) {
                                mCurrentMinTextSize = currentTextSize
                                updateOtherTextSize()
                            }
                        }
                    })
                }
                if (mSdCardSpaceProgressBar?.isAnimating == true) {
                    Log.d(TAG, "showOtgSpaceSize in animating")
                    return
                }
                val newProgress = Utils.calcSpaceSizeRatio(otgSize.second, otgSize.first)
                MainAnimationUtil.startProgressAnimation(mSdCardSpaceProgressBar, newProgress)
            }
        }
    }

    private fun handleStateUnmounted(info: Storage) {
        if (getStorageOneIsThisInfoKind(info)) {
            //卡一是当前info类型且已安装
            hideStorageLayoutOne()
            checkBothUnInstall(
                true,
                mStorageLayoutTwo == null || mStorageLayoutTwo?.visibility == View.GONE
            )
        } else if (getStorageTwoIsThisInfoKind(info)) {
            //卡二是当前info类型且已安装
            hideStorageLayoutTwo()
            checkBothUnInstall(
                mStorageLayoutOne == null || mStorageLayoutOne?.visibility == View.GONE,
                true,
            )
        } else if (mCheckBothUnInstall) {
            //卡一卡二均未安装
            hideStorageLayoutOne()
        } else if (getStorageOneLayoutStatus() && !getStorageTwoIsThisInfoKind(info)
            && mSdcardTitleTv?.text.toString() == getString(com.filemanager.common.R.string.sdcard_checking)
        ) {
            //卡二在识别中
            hideStorageLayoutTwo()
        } else if (getStorageTwoLayoutStatus() && !getStorageTwoIsThisInfoKind(info)
            && mOtgTitleTv?.text.toString() == getString(com.filemanager.common.R.string.sdcard_checking)
        ) {
            //卡一是在识别中
            hideStorageLayoutOne()
        }
    }

    private fun hideStorageLayoutOne() {
        mStorageLayoutOne?.isClickable = false
        mStorageLayoutOne?.let {
            if (it.visibility == View.VISIBLE) {
                mStorageItemHelper.reLayoutOtgLayout(
                    !(getStorageTwoLayoutStatus() || getCloudOrChipEncryptPrivateLayoutStatus()
                            || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                    false
                )
            } else {
                it.visibility = View.GONE
            }
        }
    }

    private fun hideStorageLayoutTwo() {
        mStorageLayoutTwo?.let {
            if (it.visibility == View.VISIBLE) {
                mStorageItemHelper.reLayoutSdCardLayout(
                    !(getCloudOrChipEncryptPrivateLayoutStatus() || getDFMLayoutStatus() || getRemoteDeviceLayoutStatus()),
                    false
                )
            } else {
                it.visibility = View.GONE
                mIsNeedSdLayoutAnim = true
            }
        }
    }

    private fun updateOtherTextSize() {
        updateOtherTextSizeInternal(mSdcardDesTv)
        updateOtherTextSizeInternal(mOtgDesTv)
        updateOtherTextSizeInternal(mPhoneSpaceTv)
    }

    private fun updateOtherTextSizeInternal(desTv: FormatTextView?) {
        if (desTv?.visibility == View.VISIBLE && desTv.mCurrentTextSize != mCurrentMinTextSize) {
            desTv.apply {
                mCurrentTextSize = mCurrentMinTextSize
                if ((!TextUtils.isEmpty(this.mAmplifyString)) && (!TextUtils.isEmpty(this.mTotalVolume)) && (mOriginString != 0)) {
                    amplifyFont(
                        this.mOriginString,
                        this.mAmplifyString,
                        this.mTotalVolume, mCurrentMinTextSize
                    )
                }
            }
        }
    }

    private fun getStorageOneLayoutStatus(): Boolean {
        mStorageLayoutOne?.apply {
            if (visibility == View.VISIBLE) {
                return true
            }
        }
        return false
    }

    private fun getStorageTwoLayoutStatus(): Boolean {
        mStorageLayoutTwo?.apply {
            if (visibility == View.VISIBLE) {
                return true
            }
        }
        return false
    }

    private fun getCloudOrChipEncryptPrivateLayoutStatus(): Boolean {
        return mCloudDiskLayout?.visibility == View.VISIBLE || mChipEncryptPrivateSafeLayout?.visibility == View.VISIBLE
    }

    private fun getChipEncryptPrivateLayoutStatus(): Boolean {
        return if (mChipEncryptPrivateSafeLayout == null) {
            mCloudDiskLayout?.visibility == View.VISIBLE
        } else {
            mCloudDiskLayout?.visibility == View.VISIBLE || mChipEncryptPrivateSafeLayout?.visibility == View.VISIBLE
        }
    }

    private fun getDFMLayoutStatus(): Boolean {
        dfmStorageLayout?.apply {
            if (visibility == View.VISIBLE) {
                return true
            }
        }
        return false
    }

    private fun getRemoteDeviceLayoutStatus(): Boolean {
        return remoteDeviceLayout?.visibility == View.VISIBLE
    }

    private fun checkBothUnInstall(storageOneGone: Boolean, storageTwoGone: Boolean) {
        if (storageOneGone && storageTwoGone) {
            mCheckBothUnInstall = true
        }
    }

    private fun refreshPhoneUIState(info: Storage.PhoneStorage) {
        Log.i(TAG, "refreshPhoneUIState -> info = $info")
        if (info.phoneTotalSize > 0) {
            mPhoneSpaceTv?.mCurrentTextSize = mCurrentMinTextSize
            mPhoneSpaceTv?.setStorageInfo(
                info.phoneAvailableSize,
                info.phoneTotalSize,
                object : FormatTextView.ChangeAllFontSize {
                    override fun callBack(currentTextSize: Int) {
                        if (currentTextSize < mCurrentMinTextSize) {
                            mCurrentMinTextSize = currentTextSize
                            updateOtherTextSize()
                        }
                    }
                })
            if (mPhoneSpaceProgressBar?.isAnimating == true) {
                Log.d(TAG, "showPhoneSpaceSize in animating")
                return
            }
            val newProgress = Utils.calcSpaceSizeRatio(info.phoneAvailableSize, info.phoneTotalSize)
            MainAnimationUtil.startProgressAnimation(mPhoneSpaceProgressBar, newProgress)
        }
    }

    private fun refreshDfmUIState(info: Storage.DFMStorage) {
        Log.i(TAG, "refreshDfmUIState -> info = $info")
        if (info.state == STATE_MOUNTED) {
            initDFMStorageView()
        }
        // 处理otg和sd卡的约束
        mStorageItemHelper.relayoutDFMLayout(
            !(getChipEncryptPrivateLayoutStatus() || getRemoteDeviceLayoutStatus()),
            info.totalSize > 0,
            isScrollToEnd
        )
        dfmStorage = info
        if (info.totalSize > 0) {
            dfmTitleTv?.text = info.deviceName
            dfmSpaceTv?.mCurrentTextSize = mCurrentMinTextSize
            dfmSpaceTv?.setStorageInfo(
                info.availableSize,
                info.totalSize,
                object : FormatTextView.ChangeAllFontSize {
                    override fun callBack(currentTextSize: Int) {
                        if (currentTextSize < mCurrentMinTextSize) {
                            mCurrentMinTextSize = currentTextSize
                            updateOtherTextSize()
                        }
                    }
                })
            if (dfmSpaceProgressBar?.isAnimating == true) {
                Log.d(TAG, "showPhoneSpaceSize in animating")
                return
            }
            val newProgress = Utils.calcSpaceSizeRatio(info.availableSize, info.totalSize)
            MainAnimationUtil.startProgressAnimation(dfmSpaceProgressBar, newProgress)

            StatisticsUtils.showDFMEntry(appContext, info.deviceName)
            directJumpDFM(info.path)
        }
    }

    /**
     * 直接跳转到dfm
     */
    private fun directJumpDFM(mountPath: String) {
        val activity = baseVMActivity ?: return
        val intent = activity.intent
        val isFromDevice = IntentUtils.getBoolean(intent, Constants.KEY_IS_FROM_DEVICE, false)
        val remoteDeviceId = IntentUtils.getString(intent, Constants.KEY_REMOTE_DEVICE_ID) ?: return
        Log.d(TAG, "directJumpDFM -> isFromDevice:$isFromDevice id:$remoteDeviceId")
        if (isFromDevice && mountPath.endsWith(remoteDeviceId)) { // 跳转到DFM页面
            intent.putExtra(Constants.KEY_IS_FROM_DEVICE, false)
            dfmStorageLayout?.performClick()
        }
    }

    private fun refreshCloudDiskUIState(info: Storage.CloudDiskStorage) {
        Log.d(TAG, "refreshCloudDiskUIState -> info = $info")
        if (info.isSingedIn) {
            initCloudStorageView()
        }
        mCloudDiskLayout?.let {
            if (info.isSingedIn) {
                mStorageItemHelper.displayCloud = true
                if (FeatureCompat.sIsExpRom.not() && isProcessing.not()) {
                    isProcessing = true
                    updateCloudSubtitle()
                }
                if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT.not() || getStorageCardShow()) {
                    isShowCloudIcon = true
                }
                setCloudIconVisible()
                if ((it.visibility != View.VISIBLE)) {
                    mStorageItemHelper.reLayoutCloudDiskLayout(
                        isShow = true,
                        isScroll = isScrollToEnd
                    )
                }
            } else {
                mStorageItemHelper.displayCloud = false
                if ((it.visibility == View.VISIBLE)) {
                    mStorageItemHelper.reLayoutCloudDiskLayout(false)
                    it.visibility = View.GONE
                }
            }
        }
    }

    private fun setCloudIconVisible() {
        mCloudDiskTitleTv?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                mCloudDiskTitleTv?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                if (isShowCloudIcon) {
                    val titleH = mCloudDiskTitleTv?.height ?: 0
                    val titleMarginTop = mCloudDiskTitleTv?.marginTop ?: 0
                    val descH = mCloudDiskDescTv?.height ?: 0
                    val descMarginTop = mCloudDiskDescTv?.marginTop ?: 0
                    val itemH =
                        appContext.resources.getDimensionPixelSize(R.dimen.main_storage_height)
                    val iconH =
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_37dp)
                    val iconMarginBottom = iconCloud?.marginBottom ?: 0
                    val remainH = itemH - titleH - titleMarginTop - descH - descMarginTop
                    if (remainH >= iconH + iconMarginBottom) {
                        iconCloud?.visibility = View.VISIBLE
                    } else {
                        iconCloud?.visibility = View.GONE
                    }
                } else {
                    iconCloud?.visibility = View.GONE
                }
            }
        })
    }

    fun setOTGStateChecking() {
        mMainCategoryViewModel?.updateOTGState()
    }

    fun refreshDataWithMount() {
        mMainCategoryViewModel?.apply {
            loadMainCategoryItem(checkParentPermission())
            fetchStorageInfo(false)
        }
    }

    private fun initSuperLayout() {
        val initSuperAppList = Injector.injectFactory<ISuperApp>()?.getMainSuperInitList()
        if (!initSuperAppList.isNullOrEmpty()) {
            expandableAdapter?.updateSuperAppData(initSuperAppList)
        }
    }

    private fun handleLabelItemClick(labelBean: MainCategoryItemsBean) {
        val isEdit = mMainCategoryViewModel?.isEdit() ?: false
        if (isEdit) {
            Log.w(TAG, "handleLabelItemClick -> is edit state, return")
            return
        }
        if (labelBean.sideCategoryType == CategoryHelper.CATEGORY_ADD_LABEL) {
            //新建标签
            getMainCombineFragment()?.setShowAddLabelDialogState(true)
        } else {
            val labelId = labelBean.dbID
            val name = labelBean.name ?: ""
            startLabelPage(labelId, name, labelBean.sideCategoryType)
        }
    }

    private fun startLabelPage(labelId: Long, labelName: String, categoryType: Int) {
        val bundle = Bundle().apply {
            putString(KtConstants.P_TITLE, labelName)
            putLong(Constants.LABEL_ID, labelId)
            putInt(Constants.SIDE_CATEGORY_TYPE, categoryType)
        }
        if (!FeatureCompat.isSmallScreenPhone) {
            baseVMActivity?.let {
                MainApi.startFragment(it, categoryType, bundle)
            }
        } else {
            baseVMActivity?.let {
                MainApi.startSubLabelListActivity(it, labelId, labelName, sideCategoryType = categoryType)
            }
        }
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_LABEL, Constants.PAGE_MAIN)
    }

    private fun startShortcutFolder(dbID: Long, name: String, path: String, categoryType: Int) {
        val bundle = Bundle().apply {
            putString(KtConstants.P_TITLE, name)
            putString(KtConstants.FILE_PATH, path)
            putLong(Constants.DB_ID, dbID)
            putInt(Constants.SIDE_CATEGORY_TYPE, categoryType)
        }
        if (!FeatureCompat.isSmallScreenPhone) {
            baseVMActivity?.let {
                MainApi.startFragment(it, categoryType, bundle)
            }
        } else {
            baseVMActivity?.let {
                val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
                shortcutFolderApi?.startShortcutFolderActivity(it, dbID, name, path, categoryType)
            }
        }
        StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_SHORTCUT_FOLDER, Constants.PAGE_MAIN)
    }

    private fun initExternalStorageTwoView() {
        Log.d(TAG, "initExternalStorageTwoView")
        mStorageLayoutTwo = mStorageContentView?.findViewById(R.id.sd_card_storage)
        mStorageLayoutTwo?.let { mStorageItemHelper.initSdCardLayout(it) }
        mStorageLayoutTwo?.setOnClickListener(this)
        mSdcardTitleTv = mStorageContentView?.findViewById(R.id.sd_card_storage_title)
        mSdcardDesTv = mStorageContentView?.findViewById(R.id.sd_card_storage_space_desc)
        mSdCardSpaceProgressBar = mStorageContentView?.findViewById(R.id.sd_card_storage_space_progress)
        mSdCardSpaceProgressBar?.apply {
            max = KtConstants.MAX_PROGRESS_1000
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        mStorageLayoutTwo?.tag = DropTag(CategoryHelper.CATEGORY_SDCARD_BROWSER, DropTag.Type.ITEM_VIEW)
    }

    private fun initDFMStorageView() {
        Log.d(TAG, "initDFMStorageView")
        dfmStorageLayout = mStorageContentView?.findViewById(R.id.dfm_storage)
        dfmStorageLayout?.let { mStorageItemHelper.initDfmLayout(it) }
        dfmStorageLayout?.setOnClickListener(this)
        dfmTitleTv = mStorageContentView?.findViewById(R.id.dfm_storage_title)
        dfmSpaceTv = mStorageContentView?.findViewById(R.id.dfm_storage_space_desc)
        dfmSpaceProgressBar = mStorageContentView?.findViewById(R.id.dfm_storage_space_progress)
        dfmSpaceProgressBar?.apply {
            max = KtConstants.MAX_PROGRESS_1000
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        dfmStorageLayout?.tag = DropTag(CategoryHelper.CATEGORY_DFM, DropTag.Type.ITEM_VIEW)
    }

    private fun initCloudStorageView() {
        Log.d(TAG, "initCloudStorageView")
        mCloudDiskLayout = mStorageContentView?.findViewById(R.id.cloud_disk_storage)
        val dragTag = DropTag(CategoryHelper.CATEGORY_CLOUD, DropTag.Type.ITEM_VIEW)
        dragTag.canPenetrate = false
        mCloudDiskLayout?.tag = dragTag
        iconCloud = mStorageContentView?.findViewById(R.id.iv_cloud_disk)
        mCloudDiskLayout?.let { mStorageItemHelper.initCloudDiskLayout(it) }
        mCloudDiskLayout?.setOnClickListener(this)
        mCloudDiskTitleTv = mStorageContentView?.findViewById(R.id.cloud_disk_storage_title)
        mCloudDiskDescTv = mStorageContentView?.findViewById(R.id.cloud_disk_storage_space_desc)
        mCloudDiskDescTv?.apply {
            val subtitle = PreferencesUtils.getString(
                key = CommonConstants.CLOUD_DRIVE_SUBTITLE,
                default = appContext.resources.getString(com.filemanager.common.R.string.save_to_cloud_safely)
            )
            val parts = subtitle!!.split(PARTITION_CHARACTER)
            text = if (parts.size > 2) {
                val realSubtitle = parts[0]
                val lastLanguage = parts[1]
                val lastCountry = parts[2]
                val currentLanguage = LanguageUtil.getCurrentLocalLanguage()
                val currentCountry = LanguageUtil.getCurrentLocalCountry()
                if (lastLanguage.equals(currentLanguage, true) && lastCountry.equals(currentCountry, true)) {
                    realSubtitle
                } else {
                    appContext.resources.getString(com.filemanager.common.R.string.save_to_cloud_safely)
                }
            } else {
                subtitle
            }
        }
        if (!NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
            val image = mStorageContentView?.findViewById<ImageView>(R.id.cloud_disk_image)
            val imageResource =
                Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CLOUD_DISK)
            imageResource?.apply {
                image?.setImageResource(this)
            }
            image?.visibility = View.GONE
        }
        setCloudDiskDropTag()
    }

    private fun setCloudDiskDropTag() {
        mCloudDiskLayout?.tag = if (isDraggingFromMac) {
            null
        } else {
            val dropTag = DropTag(CategoryHelper.CATEGORY_CLOUD, DropTag.Type.ITEM_VIEW)
            dropTag.canPenetrate = false
            dropTag
        }
    }

    /**
     * 不支持芯片级加密，初始化处于最近删除上面的私密宝箱箱入口
     */
    private fun initPrivateEntryView() {
        val rootView = mBrowserRootLayout?.findViewById<View>(R.id.encrypt_file_browser)
        if (!NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
            rootView?.visibility = View.VISIBLE
            mPrivateSafeLayout = rootView?.findViewById(R.id.encrypt_file_browser)
            mPrivateSafeLayout?.setOnClickListener(this)
            val imageView = mPrivateSafeLayout?.findViewById<ImageView>(R.id.icon_encrypt)
            imageView?.setImageResource(R.drawable.ic_encrypt_category)
            showEncryptDividerLine(rootView)

            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            val alpha = if (isEdit) BaseOptionHolder.EDIT_VIEW_ALPHA else 1.0f
            mPrivateSafeLayout?.alpha = alpha
            mPrivateSafeLayout?.setBackgroundAnimationEnabled(!isEdit)
            val dragTag = DropTag(CategoryHelper.CATEGORY_PRIVATE_SAVE, DropTag.Type.ITEM_VIEW)
            dragTag.canPenetrate = false
            mPrivateSafeLayout?.tag = dragTag
        } else {
            rootView?.visibility = View.GONE
        }
    }

    /**
     * 支持芯片级加密，初始化处于存储上面的私密宝箱箱入口
     */
    private fun initChipEncryptionPrivateSafeView() {
        if (!EncryptViewUtils.isShowEncryptView()) {
            return
        }
        Log.d(TAG, "initPrivateSafeView")
        if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
            mChipEncryptPrivateSafeLayout = mStorageContentView?.findViewById(R.id.private_safe)
            mChipEncryptPrivateSafeLayout?.findViewById<ImageView>(R.id.private_safe_image)?.apply {
                val icon = Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_PRIVATE_SAFE)
                icon?.let {
                    this.setImageResource(it)
                }
            }
            iconChipEncryptPrivateSafe = mStorageContentView?.findViewById(R.id.iv_private_safe)
            mChipEncryptPrivateSafeLayout?.let {
                if (it is ConstraintLayout) {
                    mStorageItemHelper.initPrivateSafeLayout(it)
                }
            }
            mChipEncryptPrivateSafeLayout?.setOnClickListener(this)
            mChipEncryptPrivateSafeTitleTv = mStorageContentView?.findViewById(R.id.private_safe_title)
            mChipEncryptPrivateSafeDescTv = mStorageContentView?.findViewById(R.id.private_safe_space_desc)
            val dragTag = DropTag(CategoryHelper.CATEGORY_PRIVATE_SAVE, DropTag.Type.ITEM_VIEW)
            dragTag.canPenetrate = false
            mChipEncryptPrivateSafeLayout?.tag = dragTag
        }
    }

    private fun initRemoteDeviceView() {
        Log.d(TAG, "initRemoteDeviceView")
        remoteDeviceLayout = mStorageContentView?.findViewById(R.id.remote_device)
        remoteDeviceLayout?.tag = DropTag(CategoryHelper.CATEGORY_FILE_REMOTE_MAC, DropTag.Type.ITEM_VIEW_PROHIBIT)
        remoteDeviceLayout?.let { mStorageItemHelper.initRemoteDeviceLayout(it) }
        remoteDeviceRecyclerView = mStorageContentView?.findViewById(R.id.recycler_view)
        remoteDeviceRecyclerView?.apply {
            isNestedScrollingEnabled = false
            layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                val isRtl = Utils.isRtl()
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val space = if (parent.getChildLayoutPosition(view) == 0) {
                        0
                    } else {
                        context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
                    }
                    if (isRtl) {
                        outRect.right = space
                    } else {
                        outRect.left = space
                    }
                }
            })
            adapter = remoteDeviceAdapter
        }
    }

    private fun showEncryptDividerLine(rootView: View?) {
        val dividerLine = rootView?.findViewById<View>(R.id.divider_line)
        if (!FeatureCompat.sIsLightVersion) {
            dividerLine?.visibility = View.VISIBLE
        } else {
            dividerLine?.visibility = View.GONE
        }
    }

    private fun initExternalStorageOneView() {
        Log.d(TAG, "initExternalStorageOneView")
        mStorageLayoutOne = mStorageContentView?.findViewById(R.id.otg_storage)
        mStorageLayoutOne?.let { mStorageItemHelper.initOtgLayout(it) }
        mStorageLayoutOne?.setOnClickListener(this)
        mOtgDesTv = mStorageContentView?.findViewById(R.id.otg_external_storage_space_desc)
        mOtgTitleTv = mStorageContentView?.findViewById(R.id.otg_external_storage_title)
        mOtgSpaceProgressBar = mStorageContentView?.findViewById(R.id.otg_storage_space_progress)
        mOtgSpaceProgressBar?.apply {
            max = KtConstants.MAX_PROGRESS_1000
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        mStorageLayoutOne?.tag = DropTag(CategoryHelper.CATEGORY_OTG_BROWSER, DropTag.Type.ITEM_VIEW)
    }

    private fun initRecycleBinContentView() {
        val contentView = mBrowserRootLayout?.findViewById<View>(R.id.recycle_bin_file_browser)
        // if is light Version，RecycleBin will closed
        if (!FeatureCompat.sIsLightVersion) {
            contentView?.visibility = View.VISIBLE
            mLayoutRecycleBin = contentView?.findViewById(R.id.recycle_bin_file_browser)
            mRecycleBinDesTv = contentView?.findViewById(R.id.recycle_bin_desc)
            mPrivacyLockIv = contentView?.findViewById(R.id.privacy_lock)
            val imageView = contentView?.findViewById<ImageView>(R.id.icon_recycle_bin)
            imageView?.setImageResource(R.drawable.ic_recently_deleted_category)
            mLayoutRecycleBin?.setOnClickListener(this)
            val consRoot = contentView?.findViewById<ConstraintLayout>(R.id.main_recycle_bin_cons)
            val titleTv = contentView?.findViewById<TextView>(R.id.recycle_bin_title)
            titleTv?.post {
                val title = titleTv.text.toString()
                val moreOne = ViewUtils.isMoreOneLine(titleTv, title)
                setIconConstraintSet(consRoot, moreOne)
                imageView?.updateLayoutParams<MarginLayoutParams> {
                    val margin = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_4dp)
                    topMargin = if (moreOne) margin else 0
                }
            }
            val deleteState = mMainCategoryViewModel?.mDeleteState?.value
            Log.d(TAG, "initBrowserItemView deleteState $deleteState")
            deleteState?.let {
                setRecycleBinText(it)
            }

            val isEdit = mMainCategoryViewModel?.isEdit() ?: false
            val alpha = if (isEdit || isDraggingFromMac) BaseOptionHolder.EDIT_VIEW_ALPHA else 1.0f
            mLayoutRecycleBin?.alpha = alpha
            mLayoutRecycleBin?.setBackgroundAnimationEnabled(!isEdit)
            val dragTag = DropTag(CategoryHelper.CATEGORY_RECYCLE_BIN, DropTag.Type.ITEM_VIEW)
            dragTag.canPenetrate = false
            mLayoutRecycleBin?.tag = dragTag
            setRecycleBinDropTag()
        } else {
            contentView?.visibility = View.GONE
        }
    }

    private fun setRecycleBinDropTag() {
        mLayoutRecycleBin?.tag = if (isDraggingFromMac) {
            null
        } else {
            val dragTag = DropTag(CategoryHelper.CATEGORY_RECYCLE_BIN, DropTag.Type.ITEM_VIEW)
            dragTag.canPenetrate = false
            dragTag
        }
    }

    /**
     * 外部跳转到最近删除
     */
    private fun checkJumpToRecycleBin() {
        parentFragment?.let {
            if (it is MainCombineFragment && it.needSetTypeRecBin) {
                it.needSetTypeRecBin = false
                val activity = baseVMActivity ?: return
                startRecycleBin(activity)
            }
        }
    }

    private fun checkJumpToSuperApp() {
        val activity = baseVMActivity ?: return
        parentFragment?.let {
            Log.w(TAG, "checkJumpToSuperApp start...")
            if (it is MainCombineFragment && it.needSetTypeSuper) {
                val superApp =
                    mMainCategoryViewModel?.mMainSuperAppItemList?.value?.find { item -> item.itemType == it.setSuperCategoryType } ?: return
                it.needSetTypeSuper = false
                val dir = File(it.externalSuperPath).parent
                val relativePath = PathUtils.getRelativePath(activity, dir) + File.separator
                superApp.fileList = ArrayUtils.add(superApp.fileList, relativePath)
                superApp.externalPath = it.externalSuperPath
                Log.w(TAG, "checkJumpToSuperApp categoryType:${it.setSuperCategoryType} item:$superApp")
                mSupperRecyclerViewClickListener.onSuperAppItemClick(superApp)
            }
        }
    }

    private fun setIconConstraintSet(rootView: ConstraintLayout?, isMoreThanOneLine: Boolean) {
        if (rootView == null) return
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.icon_recycle_bin, ConstraintSet.TOP)
            clear(R.id.icon_recycle_bin, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.icon_recycle_bin,
                    ConstraintSet.TOP,
                    R.id.recycle_bin_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.icon_recycle_bin,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.icon_recycle_bin,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    private fun setRecycleBinText(deleteState: Pair<Long, Long>) {
        lifecycleScope.launch(Dispatchers.IO) {
            if (PrivacyPasswordSettingCompat.checkPrivacySettingPassword(activity) && isRecentlyDeletedSwitchOpen(appContext)) {
                withContext(Dispatchers.Main) {
                    mRecycleBinDesTv?.isVisible = false
                    mPrivacyLockIv?.isVisible = true
                }
            } else {
                withContext(Dispatchers.Main) {
                    mRecycleBinDesTv?.isVisible = true
                    mPrivacyLockIv?.isVisible = false
                }
            }
        }
        val stringBuilder = StringBuilder()
        stringBuilder.append(
            appContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.text_x_items,
                deleteState.second.toInt(),
                deleteState.second
            )
        )
        if (deleteState.second != 0L) {
            stringBuilder.append(SYMBOL).append(VERTICAL_LINE).append(SYMBOL)
            stringBuilder.append(
                Utils.formatMessage(
                    Utils.byteCountToDisplaySize(deleteState.first),
                    Utils.RTL_POSITION_DOUBLE
                )
            )
        }
        val desText = stringBuilder.toString()
        val index = desText.indexOf(VERTICAL_LINE)
        Log.d(TAG, "mDeleteState observer line index: $index $desText $mRecycleBinDesTv $mBrowserRootLayout")
        if (index == -1) { // 没有删除文件的情况
            mRecycleBinDesTv?.text = desText
        } else {
            val spanStr = SpannableString(desText)
            // 给竖线单独设置颜色
            spanStr.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        appContext,
                        R.color.main_line_color
                    )
                ),
                index, index + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            mRecycleBinDesTv?.text = spanStr
        }
    }

    private fun setBrowserMenuViewAndHeight() {
        val showRecycleBin = !FeatureCompat.sIsLightVersion
        val showPrivacySafe = EncryptViewUtils.isShowEncryptView() && !NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT
        Log.d(TAG, "showRecycleBin $showRecycleBin showPrivacySafe $showPrivacySafe isShowEncryptView ${EncryptViewUtils.isShowEncryptView()}")
        if (showRecycleBin && showPrivacySafe) {
            mPrivateSafeLayout?.let {
                it.visibility = View.VISIBLE
                COUICardListHelper.setItemCardBackground(mPrivateSafeLayout, COUICardListHelper.HEAD)
            }
            mLayoutRecycleBin?.let {
                it.visibility = View.VISIBLE
                COUICardListHelper.setItemCardBackground(mLayoutRecycleBin, COUICardListHelper.TAIL)
            }
        }

        if (!showPrivacySafe && showRecycleBin) {
            mPrivateSafeLayout?.visibility = View.GONE
            mLayoutRecycleBin?.let {
                it.visibility = View.VISIBLE
                COUICardListHelper.setItemCardBackground(mLayoutRecycleBin, COUICardListHelper.FULL)
            }
        }

        if (showPrivacySafe && !showRecycleBin) {
            mPrivateSafeLayout?.let {
                it.visibility = View.VISIBLE
                COUICardListHelper.setItemCardBackground(mPrivateSafeLayout, COUICardListHelper.FULL)
            }
            mLayoutRecycleBin?.visibility = View.GONE
        }

        if (!showRecycleBin && !showPrivacySafe) {
            mPrivateSafeLayout?.visibility = View.GONE
            mLayoutRecycleBin?.visibility = View.GONE
        }

        setBrowserMenuLayoutHeight()
    }

    private fun setBrowserMenuLayoutHeight() {
        mBrowserRootLayout?.updateLayoutParams<ViewGroup.LayoutParams> {
            height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        mBrowserRootLayout?.doOnNextLayout {
            val parent = it as ViewGroup
            var totalH = it.paddingTop + it.paddingBottom
            for (i in 0 until parent.childCount) {
                val child = parent.getChildAt(i)
                if (child.visibility != View.VISIBLE) {
                    continue
                }
                val childParam = child.layoutParams as MarginLayoutParams
                totalH += child.height + childParam.topMargin + childParam.bottomMargin
            }
            Log.d(TAG, "setBrowserMenuLayoutHeight height:${it.height}  totalHeight:$totalH")
            it.updateLayoutParams<ViewGroup.LayoutParams> {
                this.height = totalH
            }
        }
    }

    /**
     * 确认重新加载数据
     * onResumeLoadData()方法要在initDataInternal()之后调用，才能生效
     */
    fun ensureReloadData() {
        mMainCategoryViewModel?.updateReloadData()
        // 防止从最近页面切换过来时，删除过文件，但最近删除的数量不更新
        mMainCategoryViewModel?.loadRecycleBinSize()
        labelViewModel?.loadLabels()
    }

    fun refreshDataForShortcutFolders(path: String) {
        mMainCategoryViewModel?.shortcutFolderItemList?.value?.withIndex()?.let {
            for ((index, categoryBean) in it) {
                if (categoryBean.path != null && categoryBean.path.contains(path)) {
                    mMainCategoryViewModel?.loadShortcutFolders()
                    break
                }
            }
        }
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData")
        if (mMainCategoryViewModel != null) {
            mMainCategoryViewModel?.loadMainCategoryItem(PermissionUtils.hasStoragePermission())
            if ((mOtgTitleTv?.text.toString() != getString(com.filemanager.common.R.string.sdcard_checking))
                && (mSdcardDesTv?.text.toString() != getString(com.filemanager.common.R.string.sdcard_checking))
            ) {
                mMainCategoryViewModel?.fetchStorageInfo(false)
            }
            mMainCategoryViewModel?.loadRecycleBinSize()
            mMainCategoryViewModel?.loadSuperAppData()
            mMainCategoryViewModel?.loadShortcutFolders()
            labelViewModel?.loadLabels()
            baseVMActivity?.let { activity ->
                mAdManager?.requestMainAd(activity as Activity)
                initQuestionnaire()
            }
            getMainCombineFragment()?.onCreateLabelCallback = onCreateLabelCallback
        }
        mRootView?.post {
            setDraggingState()
//            //当从二级页面进入拖拽模式，点返回回到首页，这里需要手动触发一下拖拽状态
//            val activity = baseVMActivity ?: return@post
//            if (MainApi.isDragging(activity as Activity)) {
//                handleDragStart()
//            }
        }
    }

    private fun setDraggingState() {
        val isDragging = (baseVMActivity as? MainActivity)?.isDragging ?: false
        Log.w(TAG, "setDraggingState isDragging $isDragging")
        if (isDragging || isDraggingFromMac) {
            mCategoryRecyclerView?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
            mCategoryRecyclerView?.isEnabled = false

            setMainRemoteMacItemViewAlpha(BaseOptionHolder.EDIT_VIEW_ALPHA) // 正常拖拽和mac拖拽都需要设置mac背景alpha
            // 正常拖拽和mac拖拽都需要设置添加快捷文件夹和新增标签按钮的背景alpha
            expandableAdapter?.shortcutFolderVH?.setItemViewAlpha(BaseOptionHolder.EDIT_VIEW_ALPHA)
            expandableAdapter?.labelPanelViewHolder?.setItemViewAlpha(BaseOptionHolder.EDIT_VIEW_ALPHA)
            //mac正在拖拽和mac结束拖拽的时候才执行，正常拖拽不执行
            if (isDraggingFromMac) {
                //云盘
                mCloudDiskLayout?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
                setCloudDiskDropTag()
                //最近删除
                mLayoutRecycleBin?.alpha = BaseOptionHolder.EDIT_VIEW_ALPHA
                setRecycleBinDropTag()
            }
        } else {
            mCategoryRecyclerView?.alpha = 1f
            mCategoryRecyclerView?.isEnabled = true
            setMainRemoteMacItemViewAlpha(1f)
            expandableAdapter?.shortcutFolderVH?.setItemViewAlpha(1f)
            expandableAdapter?.labelPanelViewHolder?.setItemViewAlpha(1f)
            //云盘
            mCloudDiskLayout?.alpha = 1f
            setCloudDiskDropTag()
            //最近删除
            mLayoutRecycleBin?.alpha = 1f
            setRecycleBinDropTag()
        }
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.ACTIVITY_QUICK_CLICK) || baseVMActivity == null) {
            return false
        }
        when (item.itemId) {
            R.id.action_search -> {
                baseVMActivity?.let {
                    StatisticsUtils.onCommon(it, StatisticsUtils.ACTION_SEARCH)
                    StatisticsUtils.statisticsPageExposure(it, "", Constants.PAGE_SEARCH, Constants.PAGE_MAIN)
                    val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                    categoryGlobalSearchApi?.startGlobalSearch(it)
                }
            }

            R.id.action_setting -> {
                StatisticsUtils.onCommon(appContext, StatisticsUtils.ACTION_SETTING)
                StatisticsUtils.statisticsPageExposure(baseVMActivity, "", Constants.PAGE_SETTING, Constants.PAGE_MAIN)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.HOME_PAGE_FILE)
                Injector.injectFactory<ISetting>()?.startSettingActivity(baseVMActivity)
                return true
            }

            R.id.actionbar_owork -> {
                (activity as? MainActivity)?.clickOworkMenu()
                return true
            }

            R.id.action_edit -> {
                //进入编辑模式埋点
                StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPER_APP_ENTER_EDIT_MODE)
                changeEditState()
                return true
            }

            R.id.finish_edit -> {
                completeEditState()
                return true
            }
        }
        return false
    }

    override fun fromActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return false
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        toolbar?.menu?.removeItem(R.id.action_setting)
        toolbar?.inflateMenu(R.menu.main_category_menu)
        toolbar?.findViewById<View>(R.id.no_action)?.apply {
            importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
    }

    private fun cleanupGarbage() {
        StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.FILE_CLEANUP)
        StatisticsUtils.statisticsPageExposure(appContext, "", Constants.PAGE_CLEAR_UP, Constants.PAGE_MAIN)
        if (UIConfigMonitor.isZoomWindowShow()) {
            CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
        }
        val bundle = Bundle().apply {
            putBoolean("same_task_animation", true)
            putString(
                KtConstants.FILEMANAGER_TO_SECURESAFE_STRING,
                getString(com.filemanager.common.R.string.garbage_cleanup)
            )
        }
        baseVMActivity?.let { it1 -> KtAppUtils.startPhoneManager(it1, bundle) }
    }

    override fun onClick(view: View?) {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return
        }
        val isEdit = mMainCategoryViewModel?.isEdit() ?: false
        if (isEdit) {
            Log.d(TAG, "onClick -> is edit state, return!")
            return
        }
        view?.let {
            when (it.id) {
                R.id.action_cleanup_garbage -> {
                    cleanupGarbage()
                }

                R.id.phone_storage -> {
                    baseVMActivity?.let { act ->
                        OptimizeStatisticsUtil.clickAllFile(OptimizeStatisticsUtil.PAGE_PHONE_STORAGE)
                        StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_FILE_BROWSER, Constants.PAGE_MAIN)
                        if (FeatureCompat.isSmallScreenPhone) { // 判断直板机，平板，折叠屏
                            fileBrowser?.startFileBrowserActivity(
                                act,
                                mMainCategoryViewModel?.mInternalPath
                            )
                        } else {
                            fileBrowser?.startFileBrowserFragment(
                                act,
                                mMainCategoryViewModel?.mInternalPath
                            )
                        }
                    }
                }

                R.id.recycle_bin_file_browser -> {
                    if (isDraggingFromMac) {
                        Log.w(TAG, "recycle_bin_file_browser -> is drag state, return")
                        return
                    }
                    Log.d(TAG, "action_recycle_bin is clicked")
                    baseVMActivity?.let { act ->
                        StatisticsUtils.nearMeStatistics(
                            appContext,
                            StatisticsUtils.ACTION_RECYCLE_BIN
                        )
                        lifecycleScope.launch(Dispatchers.IO) {
                            if (PrivacyPasswordSettingCompat.checkPrivacySettingPassword(act) && isRecentlyDeletedSwitchOpen(appContext)) {
                                withContext(Dispatchers.Main) {
                                    StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_PRIVACY_PWD, Constants.PAGE_MAIN)
                                    launcher.launch(PrivacyPasswordSettingCompat.privacySettingPasswordIntent(act))
                                }
                            } else {
                                withContext(Dispatchers.Main) {
                                    startRecycleBin(act)
                                }
                            }
                        }
                    }
                }

                R.id.sd_card_storage -> {
                    if (mStorageTwo is Storage.SDCardStorage) {
                        clickSDCard()
                    } else {
                        clickOTG()
                    }
                }

                R.id.otg_storage -> {
                    if (mStorageOne is Storage.SDCardStorage) {
                        clickSDCard()
                    } else {
                        clickOTG()
                    }
                }

                R.id.dfm_storage -> {
                    baseVMActivity?.let { act ->
                        DialogUtil.showPermissionDialog(
                            act, com.filemanager.common.R.string.use_net_tips_for_dfs
                        ) {
                            startDFM(act)
                        }
                    }
                }

                R.id.cloud_disk_storage -> {
                    if (isDraggingFromMac) {
                        Log.w(TAG, "cloud_disk_storage -> is drag state, return")
                        return
                    }
                    baseVMActivity?.let { act ->
                        mMainCategoryViewModel?.openCloudDisk(act, mCloudStatisticsMap[mCloudDiskDescTv?.text])
                    }
                }

                R.id.private_safe, R.id.encrypt_file_browser -> {
                    baseVMActivity?.let { act ->
                        StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.ACTION_ENCRYPT)
                        FileEncryptFactor.startFileSafeActivity(act)
                    }
                }

                else -> {

                }
            }
        }
    }

    private fun startRecycleBin(act: BaseVMActivity) {
        if (FeatureCompat.isSmallScreenPhone) {
            val recycleBinAction = Injector.injectFactory<IRecycleBin>()
            recycleBinAction?.startRecycleBinActivity(act)
        } else {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.startFragment(act, CategoryHelper.CATEGORY_RECYCLE_BIN, null)
        }
        StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_RECYCLE_BIN, Constants.PAGE_MAIN)
    }

    private fun startDFM(act: BaseVMActivity) {
        val path = dfmStorage?.path ?: return
        val deviceName = dfmStorage?.deviceName ?: ""
        val categoryDFMApi = Injector.injectFactory<ICategoryDFMApi>()
        categoryDFMApi?.startDFM(act, deviceName, path)
        StatisticsUtils.clickDFMEntry(appContext, deviceName, dfmStorage?.connectTime ?: 0L)
        StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_DFM, Constants.PAGE_MAIN)
    }

    private fun clickOTG() {
        baseVMActivity?.let { act ->
            if (mMainCategoryViewModel?.mOtgPaths != null) {
                OptimizeStatisticsUtil.clickAllFile(OptimizeStatisticsUtil.PAGE_OTG)
                StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_OTG, Constants.PAGE_MAIN)
                if (FeatureCompat.isSmallScreenPhone) {
                    fileBrowser?.startOtgBrowserActivity(act, mMainCategoryViewModel!!.mOtgPaths)
                } else {
                    fileBrowser?.startOtgBrowserFragment(act, mMainCategoryViewModel!!.mOtgPaths)
                }
            }
        }
    }

    private fun clickSDCard() {
        baseVMActivity?.let { act ->
            if (mMainCategoryViewModel?.mExternalPath != null) {
                OptimizeStatisticsUtil.clickAllFile(OptimizeStatisticsUtil.PAGE_SD_CARD)
                StatisticsUtils.statisticsPageExposure(act, "", Constants.PAGE_SD_CARD, Constants.PAGE_MAIN)
                if (FeatureCompat.isSmallScreenPhone) {
                    val intent = Intent().apply {
                        putExtra(CURRENT_DIR, mMainCategoryViewModel?.mExternalPath)
                        putExtra(
                            Constants.TITLE_RES_ID,
                            com.filemanager.common.R.string.storage_external
                        )
                        putExtra(
                            Constants.TITLE,
                            getString(com.filemanager.common.R.string.storage_external)
                        )
                        putExtra(Constants.SELECTED_ITEM, -1)
                        setClassName(act.packageName, FILE_BROWSER_ACTIVITY)
                    }
                    act.startActivity(intent)
                } else {
                    fileBrowser?.startSdCardBrowserFragment(
                        act,
                        mMainCategoryViewModel?.mExternalPath
                    )
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            configList.forEach { config ->
                if (config is ScreenSizeConfig) {
                    val spanCount = ItemDecorationFactory.getGridItemCount(
                        activity,
                        KtConstants.SCAN_MODE_GRID,
                        ItemDecorationFactory.GRID_ITEM_DECORATION_MAIN_CATEGORY
                    )
                    mCategoryAdapter?.apply {
                        setItemWidth(
                            KtViewUtils.getGridItemWidth(
                                activity,
                                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp),
                                spanCount,
                                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp) * 2
                            )
                        )
                    }
                    mCategoryRecyclerView?.apply {
                        mCategoryDecoration?.updateSpanCount(spanCount)
                        (layoutManager as? GridLayoutManager)?.spanCount = spanCount
                        adapter?.notifyDataSetChanged()
                    }
                    mRootView?.apply {
                        setPadding(
                            paddingLeft, COUIPanelMultiWindowUtils.getStatusBarHeight(
                                baseVMActivity
                            ), paddingRight, paddingBottom
                        )
                    }
                }
                if (config is DarkThemeLevelConfig) {
                    mCategoryRecyclerView?.apply {
                        adapter?.notifyDataSetChanged()
                    }
                }

                if (config is ScreenFoldConfig) {
                    completeEditState()
                }
            }
            activity?.let {
                mMainCategoryViewModel?.storageUiState?.value?.let { storage ->
                    Log.i(TAG, "reLayoutStorageLayout")
                    mStorageItemHelper.reLayoutStorageLayout(storage.storageItems)
                }
            }
            setScrollBottomView()
        }
    }

    private fun setScrollBottomView() {
        val navigationViewY = (activity as? MainActivity)?.getNavigationTabView()?.y?.toInt() ?: 0
        scrollHelper?.setBottomView(navigationViewY)
    }

    override fun onDestroy() {
        mAppShowAnimatorSet?.apply {
            cancel()
            removeAllListeners()
            mAppShowAnimatorSet = null
        }

        mAppHideAnimatorSet?.apply {
            cancel()
            removeAllListeners()
            mAppHideAnimatorSet = null
        }
        questionnaire?.releaseSpace(cdpView)
        handler.removeCallbacksAndMessages(null)
        expandableAdapter?.removeEditAnimationListener(editAnimationListener)
        super.onDestroy()
    }

    override fun pressBack(): Boolean {
        return completeEditState()
    }

    private fun refreshCleanGarbageView() {
        baseVMActivity?.apply {
            val cleanGarbage: ImageView? = mStorageContentView?.findViewById(R.id.action_cleanup_garbage)
            cleanGarbage?.let {
                mMainCategoryViewModel?.let { viewModel ->
                    val cleanupGarbageVisibility = StorageInfoUtils.getCleanupGarbageVisibility()
                    val fbaClearContainer =
                        mRootView?.findViewById<LinearLayout>(R.id.add_label_fab_container)
                    if (cleanupGarbageVisibility.and(KtAppUtils.isExportSmallScreenPhone())) {
                        //小屏外销 开关开启 可显示fba悬浮按钮
                        if (fbaClearContainer?.visibility == View.GONE) {
                            fbaClearContainer.visibility = View.VISIBLE
                        }
                    } else {
                        fbaClearContainer?.visibility = View.GONE
                    }
                    val hideCleanup =
                        (cleanupGarbageVisibility.not()).or(KtAppUtils.isExportSmallScreenPhone())
                    it.visibility = if (hideCleanup) View.GONE else View.VISIBLE
                }
                if (it.visibility == View.VISIBLE) {
                    it.setOnClickListener(this@MainCategoryFragment)
                }
            }
        }
    }

    fun setEditState() {
        Log.d(TAG, "setEditState")
        mMainCategoryViewModel?.editState?.postValue(EDIT_STATE_ON)
    }

    fun getEditState(): Boolean {
        return mMainCategoryViewModel?.isEdit() ?: false
    }

    fun handleDragStart() {
        Log.d(TAG, "handleDragStart")
        val isEdit = mMainCategoryViewModel?.isEdit() ?: false
        itemDisableDragAnimator.onDragStart(isEdit, getAlphaChangeCallBack())
        if (isDraggingFromMac) {
            animationHelper.startDragAnimation()
        }
    }

    fun handleDragEnd() {
        Log.d(TAG, "handleDragEnd")
        val isEdit = mMainCategoryViewModel?.isEdit() ?: false
        itemDisableDragAnimator.onDragEnd(isEdit, getAlphaChangeCallBack())
        animationHelper.endDragAnimation()
    }

    fun handleDragScroll(event: DragEvent?): Boolean {
        Log.d("TAGAS", "cactegory event action is ${event?.action}")
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    fun resetDragStatus() {
        scrollHelper?.resetDragStatus()
    }

    private fun getAlphaChangeCallBack(): (alpha: Float, enterDrag: Boolean) -> Unit {
        val alphaChangedCallback: (alpha: Float, enterDrag: Boolean) -> Unit = { alpha, enterDrag ->
            mCategoryRecyclerView?.alpha = alpha
            this.enterDrag = enterDrag
            mCategoryRecyclerView?.isEnabled = !enterDrag
            setMainRemoteMacItemViewAlpha(alpha) // 正常拖拽和mac拖拽都需要设置mac背景alpha
            // 正常拖拽和mac拖拽都需要设置添加快捷文件夹和新增标签按钮的背景alpha
            expandableAdapter?.shortcutFolderVH?.setItemViewAlpha(alpha)
            expandableAdapter?.labelPanelViewHolder?.setItemViewAlpha(alpha)
            //mac正在拖拽和mac结束拖拽的时候才执行，正常拖拽不执行
            if (isDraggingFromMac || (!isDraggingFromMac && mCloudDiskLayout?.alpha != 1f)) {
                mCloudDiskLayout?.alpha = alpha
                setCloudDiskDropTag()
            }
            if (isDraggingFromMac || (!isDraggingFromMac && mLayoutRecycleBin?.alpha != 1f)) {
                //最近删除
                mLayoutRecycleBin?.alpha = alpha
                setRecycleBinDropTag()
            }
        }
        return alphaChangedCallback
    }

    private fun setMainRemoteMacItemViewAlpha(alpha: Float) {
        val layoutManager = remoteDeviceRecyclerView?.layoutManager
        if (layoutManager != null) {
            for (i in 0 until layoutManager.childCount) {
                val childView = layoutManager.getChildAt(i)
                childView?.let {
                    val viewHolder = remoteDeviceRecyclerView?.getChildViewHolder(it)
                    // 在这里对ViewHolder执行操作
                    viewHolder?.itemView?.alpha = alpha
                }
            }
        }
    }

    fun getMainCombineFragment(): MainCombineFragment? {
        return (parentFragment as? MainCombineFragment)
    }

    fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { fileOperateController.onSelectPathReturn(it, requestCode, paths) }
    }

    fun updateLabels() {
        labelViewModel?.loadLabels()
    }

    fun getLabelIdByCategoryType(categoryType: Int): Long? {
        val labelBean = expandableAdapter?.getLabelDataList()?.find { it.sideCategoryType == categoryType }
        return labelBean?.dbID
    }

    fun getSuperAppFilePathsByCategoryType(categoryType: Int): Array<String>? {
        val superAppItem = mMainCategoryViewModel?.mMainSuperAppItemList?.value?.find { it.sideCategoryType == categoryType }
        return superAppItem?.fileList
    }

    fun getShortcutFilePathByCategoryType(categoryType: Int): String? {
        val itemBean = mMainCategoryViewModel?.shortcutFolderItemList?.value?.find { it.sideCategoryType == categoryType }
        return itemBean?.fileList?.getOrNull(0)
    }

    companion object {
        private const val TAG = "MainCategoryFragment"
        private const val SYMBOL = "\u0020\u0020" // 2 SPACE
        private const val VERTICAL_LINE = "|" // 竖线
        private const val IS_FOLD = "isFold"
        const val FLING_SPEED_THRESHOLD = 1500
        const val MAIN_AD_PERIOD =  3
        private const val FILE_BROWSER_ACTIVITY = "com.oplus.filebrowser.FileBrowserActivity"
        private const val PARTITION_CHARACTER = ","
    }

    class SpaceItemDecoration(private var spanCount: Int) : RecyclerView.ItemDecoration() {
        private var horizontalSpace =
            appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
        private var verticalSpace =
            appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_4dp)

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            val position = parent.getChildAdapterPosition(view)
            if ((position < 0) || (spanCount <= 1)) {
                Log.w(TAG, "SpaceItemDecoration has error spanCount or position")
                return
            }

            val layoutManager = parent.layoutManager as GridLayoutManager
            val column =
                layoutManager.spanSizeLookup.getSpanIndex(position, layoutManager.spanCount)
            val row =
                layoutManager.spanSizeLookup.getSpanGroupIndex(position, layoutManager.spanCount)

            val eachSpace = ((spanCount - 1) * horizontalSpace) / (spanCount * 1.0f)
            val diff = eachSpace / (spanCount - 1)
            val left = column * diff
            val right = eachSpace - left

            if (Utils.isRtl()) {
                when (row) {
                    0 -> outRect.set(right.toInt(), 0, left.toInt(), verticalSpace)
                    else -> outRect.set(right.toInt(), verticalSpace, left.toInt(), 0)
                }
            } else {
                when (row) {
                    0 -> outRect.set(left.toInt(), 0, right.toInt(), verticalSpace)
                    else -> outRect.set(left.toInt(), verticalSpace, right.toInt(), 0)
                }
            }
        }

        fun updateSpanCount(spanCount: Int) {
            this.spanCount = spanCount
        }
    }
}