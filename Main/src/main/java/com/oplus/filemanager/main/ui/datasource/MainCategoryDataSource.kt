/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MainCategoryDataSource
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/2/22 17:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/2/22       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.datasource

import com.oplus.filemanager.main.ui.category.MainCategoryHelper
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class MainCategoryDataSource(
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    suspend fun fetchCategoryItemCount(category: Int): Long {
        val mainCategoryHelper = MainCategoryHelper()
        return withContext(ioDispatcher) {
            mainCategoryHelper.getCountByCategory(category)
        }
    }
}