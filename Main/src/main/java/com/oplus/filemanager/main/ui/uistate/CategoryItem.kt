/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryItem
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/2/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/2/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.uistate

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_APK
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_ARCHIVE
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_AUDIO
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_DOC
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_PIC
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_VIDEO

/**
 * Home Page Category includes Picture, Video, Audio, Document, Apk, Archive and Oaps.
 * Among category items, Oaps category item, only display on exp-rom.
 */
sealed class CategoryItem(
    @DrawableRes val categoryIconId: Int,
    @StringRes var categoryName: Int,
    val categoryType: Int,
    var categoryCount: Long
) {


    class CategoryPic : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_PIC) ?: -1,
        com.filemanager.common.R.string.string_photos,
        CategoryHelper.CATEGORY_IMAGE,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryVideo : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_VIDEO) ?: -1,
        com.filemanager.common.R.string.string_videos,
        CategoryHelper.CATEGORY_VIDEO,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryAudio : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_AUDIO) ?: -1,
        com.filemanager.common.R.string.string_audio,
        CategoryHelper.CATEGORY_AUDIO,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryDoc : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_DOC) ?: -1,
        com.filemanager.common.R.string.string_documents,
        CategoryHelper.CATEGORY_DOC,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryApk : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_APK) ?: -1,
        com.filemanager.common.R.string.string_apk,
        CategoryHelper.CATEGORY_APK,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryArchive : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_ARCHIVE) ?: -1,
        com.filemanager.common.R.string.string_compress,
        CategoryHelper.CATEGORY_COMPRESS,
        DEFAULT_ITEM_COUNT.toLong()
    )

    class CategoryOAPS : CategoryItem(
        Injector.injectFactory<ISuperApp>()?.getCategoryIconRes(IC_CATEGORY_APK) ?: -1,
        com.filemanager.common.R.string.recommended_apps,
        CategoryHelper.CATEGORY_OAPS,
        DEFAULT_ITEM_COUNT.toLong()
    )

    companion object {
        private const val DEFAULT_ITEM_COUNT = 0
    }
}
