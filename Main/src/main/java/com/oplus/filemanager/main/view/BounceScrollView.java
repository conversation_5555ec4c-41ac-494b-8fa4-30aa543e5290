/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BounceScrollView.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/26      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.main.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Interpolator;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.core.widget.NestedScrollView;
import com.oplus.filemanager.main.R;

public class BounceScrollView extends NestedScrollView {

    private static final float DEFAULT_DAMPING_COEFFICIENT = 4.0f;
    private static final int DEFAULT_SCROLL_THRESHOLD = 20;
    private static final long DEFAULT_BOUNCE_DELAY = 400;
    private static final float RATIO_STEP = 0.2f;
    private static final float FLOAT_0 = 0.0f;
    private static final float FLOAT_1 = 1.0f;
    private static final float NUM_0 = 0;
    private static final float NUM_1 = 1;
    private static final float NUM_2 = 2;
    private static final float NUM_4 = 4;
    private static final float NUM_100 = 100;

    private boolean mIsHorizontal;
    private float mDamping;
    private boolean mIncrementalDamping;
    private long mBounceDelay;
    private boolean mDisableBounce;

    private Interpolator mInterpolator;
    private View mChildView;
    private float mStart;
    private int mPreDelta;
    private int mOverScrolledDistance;
    private ObjectAnimator mAnimator;

    public BounceScrollView(@NonNull Context context) {
        this(context, null);
    }

    public BounceScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BounceScrollView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @VisibleForTesting
    public void init(@NonNull Context context, @Nullable AttributeSet attrs) {
        setVerticalScrollBarEnabled(false);
        setHorizontalScrollBarEnabled(false);
        setFillViewport(true);
        setOverScrollMode(View.OVER_SCROLL_NEVER);
        initDataFromTypedArray(context, attrs);
        mInterpolator = new DefaultQuartOutInterpolator();
    }

    @VisibleForTesting
    public void initDataFromTypedArray(@NonNull Context context, @Nullable AttributeSet attrs) {
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.BounceScrollView, 0, 0);
        mDamping = a.getFloat(R.styleable.BounceScrollView_damping, DEFAULT_DAMPING_COEFFICIENT);
        int orientation = a.getInt(R.styleable.BounceScrollView_scrollOrientation, 0);
        mIsHorizontal = orientation == 1;
        mIncrementalDamping = a.getBoolean(R.styleable.BounceScrollView_incrementalDamping, true);
        mBounceDelay = a.getInt(R.styleable.BounceScrollView_bounceDelay, (int) DEFAULT_BOUNCE_DELAY);
        mDisableBounce = a.getBoolean(R.styleable.BounceScrollView_disableBounce, false);
        boolean enable = a.getBoolean(R.styleable.BounceScrollView_nestedScrollingEnabled, true);
        setNestedScrollingEnabled(enable);
        a.recycle();
    }

    @Override
    public boolean canScrollVertically(int direction) {
        return !mIsHorizontal;
    }

    @Override
    public boolean canScrollHorizontally(int direction) {
        return mIsHorizontal;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (needUpdateChildView(mChildView, getChildCount(), getChildAt(0))) {
            mChildView = getChildAt(0);
        }
        return super.onInterceptTouchEvent(ev);
    }

    @VisibleForTesting
    public boolean needUpdateChildView(View childView, int childCount, View childAtFirst) {
        return (childView == null) && (childCount > 0) || (childView != childAtFirst);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if ((mChildView == null) || mDisableBounce) {
            return super.onTouchEvent(ev);
        }

        switch (ev.getActionMasked()) {
            case MotionEvent.ACTION_DOWN:
                mStart = mIsHorizontal ? ev.getX() : ev.getY();

                break;
            case MotionEvent.ACTION_MOVE:
                float now = mIsHorizontal ? ev.getX() : ev.getY();
                float delta = mStart - now;
                int dampingDelta = (int) (delta / calculateDamping());
                mStart = now;

                boolean onePointerTouch = true;
                if ((mPreDelta <= 0) && (dampingDelta > 0)) {
                    onePointerTouch = false;
                } else if ((mPreDelta >= 0) && (dampingDelta < 0)) {
                    onePointerTouch = false;
                }
                mPreDelta = dampingDelta;

                if (onePointerTouch && canMove(dampingDelta)) {
                    mOverScrolledDistance += dampingDelta;
                    if (mIsHorizontal) {
                        mChildView.setTranslationX(-mOverScrolledDistance);
                    } else {
                        mChildView.setTranslationY(-mOverScrolledDistance);
                    }
                }

                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mPreDelta = 0;
                mOverScrolledDistance = 0;

                cancelAnimator();
                if (mIsHorizontal) {
                    mAnimator = ObjectAnimator.ofFloat(mChildView, View.TRANSLATION_X, 0);
                } else {
                    mAnimator = ObjectAnimator.ofFloat(mChildView, View.TRANSLATION_Y, 0);
                }
                mAnimator.setDuration(mBounceDelay).setInterpolator(mInterpolator);
                mAnimator.start();

                break;
            default:
                break;
        }

        return super.onTouchEvent(ev);
    }

    @VisibleForTesting
    public float calculateDamping() {
        float ratio = FLOAT_0;
        if (mIsHorizontal) {
            ratio = Math.abs(mChildView.getTranslationX()) / mChildView.getMeasuredWidth();
        } else {
            ratio = Math.abs(mChildView.getTranslationY()) / mChildView.getMeasuredHeight();
        }
        ratio += RATIO_STEP;
        if (mIncrementalDamping) {
            return mDamping / (FLOAT_1 - (float) Math.pow(ratio, NUM_2));
        } else {
            return mDamping;
        }
    }

    private boolean canMove(int delta) {
        return (delta < 0) ? canMoveFromStart() : canMoveFromEnd();
    }

    private boolean canMoveFromStart() {
        return mIsHorizontal ? getScrollX() == 0 : getScrollY() == 0;
    }

    private boolean canMoveFromEnd() {
        return true;
    }

    private void cancelAnimator() {
        if ((mAnimator != null) && (mAnimator.isRunning())) {
            mAnimator.cancel();
        }
    }

    @VisibleForTesting
    public void setIncrementalDamping(boolean incrementalDamping) {
        mIncrementalDamping = incrementalDamping;
    }

    @VisibleForTesting
    public void setChildView(View childView) {
        this.mChildView = childView;
    }

    public boolean isIncrementalDamping() {
        return mIncrementalDamping;
    }

    public float getDamping() {
        return mDamping;
    }

    public long getBounceDelay() {
        return mBounceDelay;
    }

    public boolean isDisableBounce() {
        return mDisableBounce;
    }

    @Override
    protected void onScrollChanged(int scrollX, int scrollY, int oldl, int oldt) {
        super.onScrollChanged(scrollX, scrollY, oldl, oldt);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelAnimator();
    }

    public void setScrollHorizontally(boolean horizontal) {
        this.mIsHorizontal = horizontal;
    }

    public boolean isScrollHorizontally() {
        return mIsHorizontal;
    }


    ////////////////////////////////////////////////////////////////////////////////////////////////
    private static class DefaultQuartOutInterpolator implements Interpolator {

        @Override
        public float getInterpolation(float input) {
            return (float) (FLOAT_1 - Math.pow(NUM_1 - input, NUM_4));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    public interface OnScrollListener {
        void onScrolling(int scrollX, int scrollY);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    public interface OnOverScrollListener {
        /**
         * @param fromStart LTR, the left is start; RTL, the right is start.
         */
        void onOverScrolling(boolean fromStart, int overScrolledDistance);
    }
}