/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/6/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.adapter

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewGroup.VISIBLE
import android.view.ViewStub
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.reddot.COUIHintRedDot
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.view.SmoothRoundedCornersConstraintLayout
import com.filemanager.common.viewholder.PressAnimViewHolder
import com.oplus.filemanager.interfaze.categoryappamanger.ICategoryAppManagerApi
import com.oplus.filemanager.interfaze.categoryappmarketamanager.ICategoryAppMarketManagerApi
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.category.MainCategoryHelper
import com.oplus.filemanager.main.ui.usecase.GetApkCountInfoUseCase
import com.oplus.filemanager.main.utils.ClassBeanUtil
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Locale

class MainCategoryAdapter(content: Context, lifecycle: Lifecycle) :
    BaseSelectionRecycleAdapter<MainCategoryAdapter.ViewHolder, MainCategoryItemsBean>(content), LifecycleObserver {
    companion object {
        private const val TAG = "MainCategoryAdapter"
        private const val BURMESE_LANGUAGE = "my"
    }

    private val mUiHandler: Handler = Handler(Looper.getMainLooper())
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private var mMainCategoryHelper: MainCategoryHelper? = null
    private var mInitState = false
    private var mThreadManager: ThreadManager = ThreadManager(lifecycle)
    private var mLifecycle: Lifecycle? = lifecycle
    private var context: Context = content
    @VisibleForTesting var mWidth = 0

    private val animationHelper: ItemAnimationHelper = ItemAnimationHelper()
    private val categoryAppMarketManagerApi = Injector.injectFactory<ICategoryAppMarketManagerApi>()
    private val categoryAppManagerApi = Injector.injectFactory<ICategoryAppManagerApi>()
    var editTouchHelper: ItemTouchHelper? = null
    var isEdit = false
    var onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit = { viewHolder ->
        editTouchHelper?.startDrag(viewHolder)
    }
    var classPanelEvent: ExpandItemEvent<MainCategoryItemsBean>? = null

    private val superApp: ISuperApp? by lazy {
        Injector.injectFactory<ISuperApp>()
    }

    init {
        mFiles = ClassBeanUtil.mainCategoryClassPanelInitView()
        lifecycle.addObserver(this)
    }


    fun setMainCategoryHelper(mainCategoryHelper: MainCategoryHelper) {
        mMainCategoryHelper = mainCategoryHelper
    }

    fun startAnimation(isEdit: Boolean) {
        animationHelper.isEdit = isEdit
        animationHelper.startAnimation()
    }

    fun setData(data: MutableList<MainCategoryItemsBean>) {
        mInitState = true
        val previousSize = mFiles.size
        mFiles.clear()
        notifyItemRangeRemoved(0, previousSize)
        notifyItemRangeChanged(0, previousSize)
        mFiles.addAll(data)
        notifyItemRangeInserted(0, data.size)
        notifyItemRangeChanged(0, data.size)
    }

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = onRecyclerItemClickListener
    }

    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        animationHelper.addListener(holder, animationHelper.isEdit)
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        animationHelper.removeListener(holder)
    }

    override fun getItem(position: Int): MainCategoryItemsBean? {
        return if (0 <= position && position < mFiles.size) {
            mFiles[position]
        } else null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        if (viewType == CategoryHelper.CATEGORY_APPMANAGER_VIEWTYPE) {
            val appmanagerView  = categoryAppManagerApi?.getAppManagerView(parent)
            if (appmanagerView != null) {
                return ViewHolder(appmanagerView)
            }
        }
        val v = LayoutInflater.from(parent.context).inflate(R.layout.main_category_list_item, parent, false)
        return ViewHolder(v)
    }

    override fun getItemCount(): Int {
        return mFiles.size
    }

    override fun getItemViewType(position: Int): Int {
        val item = getItem(position)
        item?.let {
            if (it.itemType == CategoryHelper.CATEGORY_APPMANAGER) {
                return CategoryHelper.CATEGORY_APPMANAGER_VIEWTYPE
            }
        }
        return 0
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data: MainCategoryItemsBean = mFiles[position]
        holder.mContainer.apply {
            requestLayout()
            refreshBg()
        }
        holder.mContainer.layoutParams?.apply {
            width = mWidth
        }
        holder.mTitle?.apply {
            text = data.name
        }
        holder.onItemDragCallback = onItemDragCallback
        holder.setImageViewAttachStateListener()
        holder.mIcon?.apply {
            setImageResource(data.iconId)
            setOnClickListener {
                mOnRecyclerItemClickListener?.onItemClick(holder.itemView, position)
            }
            setOnLongClickListener {
                true
            }
        }
        holder.mSubDesc?.apply {
            Log.d(TAG, "onBindViewHolder -> initState = $mInitState")
            if (mInitState) {
                if ((data.itemsCount < 0) || (data.itemType == CategoryHelper.CATEGORY_CLOUD) || (data.itemType == CategoryHelper.CATEGORY_OAPS)) {
                    visibility = View.GONE
                } else {
                    visibility = View.VISIBLE
                    text = String.format(Locale.getDefault(), "%d", data.itemsCount)
                    asyncItemsCount(this, data)
                }
            }
            if (BURMESE_LANGUAGE == Locale.getDefault().language) {
                val s: String = text.toString()
                if (!TextUtils.isEmpty(s)) {
                    text = s.replace(" ", "\n")
                }
            }
        }

        if (data.itemType == CategoryHelper.CATEGORY_APK && mInitState) {
            holder.initRedHot()
            val redDot = holder.redHot
            val marketManagerEnabled =
                categoryAppMarketManagerApi?.isAppMarketManagerEnabled(appContext) ?: false
            Log.d(TAG, "onBindViewHolder -> marketManagerEnabled = $marketManagerEnabled")
            if (marketManagerEnabled && !ModelUtils.isTablet()) {
                asyncAppMarketManagerUpdateCount(redDot, data, true) {
                    val unInstallApkCount = GetApkCountInfoUseCase().invoke().first
                    val count = (categoryAppMarketManagerApi?.getUpdateNumber(appContext) ?: 0) +
                            unInstallApkCount.toLong()
                    mUiHandler.post {
                        data.itemsCount = count
                        val countStr = String.format(Locale.getDefault(), "%d", data.itemsCount)
                        holder.mSubDesc?.text = countStr
                    }
                    CategoryHelper.saveCategoryCountData(data.itemType, count, 0)
                    count.toInt()
                }
            }
        }

        if (data.itemType == CategoryHelper.CATEGORY_APPMANAGER) {
            val mAppUpdate: COUIHintRedDot? = categoryAppManagerApi?.getAppUpdateTextView(holder.itemView)
            mAppUpdate?.apply {
                asyncAppMarketManagerUpdateCount(this, data, false) {
                    categoryAppManagerApi?.getUpdateAppNumber() ?: 0
                }
            }
        }

        holder.itemView.setOnClickListener {
            mOnRecyclerItemClickListener?.onItemClick(holder.itemView, position)
        }

        holder.itemView.setOnLongClickListener {
            true
        }
        holder.mContainer.isEdit = animationHelper.isEdit
        holder.isEdit = animationHelper.isEdit
        holder.itemView.isEnabled = !animationHelper.isEdit
        holder.mDragView?.contentDescription = data.name
    }

    @Suppress("TooGenericExceptionCaught")
    private fun asyncItemsCount(mSubDesc: TextView?, data: MainCategoryItemsBean) {
        mSubDesc?.tag = data.name
        if (data.itemType == CategoryHelper.CATEGORY_APK &&
            categoryAppMarketManagerApi?.isAppMarketManagerEnabled(appContext) == true &&
            !ModelUtils.isTablet()
        ) {
            Log.d(TAG, "asyncItemsCount")
            return
        }
        Log.d(TAG, "asyncItemsCount type=${data.itemType}")
        (context as? BaseVMActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
            var mCount: Long = 0
            if (data.order >= 0) {
                try {
                    mMainCategoryHelper?.let {
                        mCount = it.getCountByCategory(data.itemType)
                        CategoryHelper.saveCategoryCountData(data.itemType, mCount, 0)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "asyncItemsCount err : " + e.message)
                    return@launch
                }
            }
            CollectPrivacyUtils.collectMedia(data.itemType, mCount)
            mUiHandler.post {
                val currentPath = mSubDesc?.tag as? String
                if (data.name == currentPath) {
                    data.itemsCount = mCount
                    val countStr = String.format(Locale.getDefault(), "%d", data.itemsCount)
                    mSubDesc?.text = countStr
                }
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun asyncAppMarketManagerUpdateCount(
        redHot: COUIHintRedDot?,
        data: MainCategoryItemsBean,
        onlyRedDot: Boolean,
        updateCount: () -> Int
    ) {
        redHot?.tag = data.name
        mThreadManager.execute(FileRunnable(Runnable {
            var count = 0
            if (data.order >= 0) {
                try {
                    count = updateCount()
                    Log.d(TAG, "asyncAppMarketManagerUpdateCount -> count = $count")
                } catch (e: Exception) {
                    Log.e(TAG, "asyncAppMarketManagerUpdateCount has error, ", e)
                    return@Runnable
                }
            }
            mUiHandler.post {
                updateRedDot(redHot, count, data, onlyRedDot)
            }
        }, TAG))
    }

    private fun updateRedDot(
        redHot: COUIHintRedDot?,
        count: Int,
        data: MainCategoryItemsBean,
        onlyRedDot: Boolean
    ) {
        val currentPath = redHot?.tag as? String
        if (count > 0) {
            if (data.name == currentPath && redHot != null) {
                if (onlyRedDot) {
                    redHot.pointMode = COUIHintRedDot.POINT_ONLY_MODE
                } else {
                    redHot.pointMode = COUIHintRedDot.POINT_WITH_NUM_MODE
                    redHot.pointNumber = count
                }
                redHot.visibility = View.VISIBLE
            }
        } else {
            redHot?.visibility = View.GONE
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mUiHandler.removeCallbacksAndMessages(null)
        mLifecycle?.removeObserver(this)
        mLifecycle = null
    }

    class ViewHolder(convertView: View) : PressAnimViewHolder(convertView), ItemAnimationHelper.AnimationListener {
        var mContainer: SmoothRoundedCornersConstraintLayout = itemView.findViewById(R.id.main_category_item)
        var mDragView: ImageView? = itemView.findViewById(R.id.drag_view)
        var mTitle: TextView? = itemView.findViewById(R.id.list_item_title)
        var mSubDesc: TextView? = itemView.findViewById(R.id.list_item_sub_desc)
        var redHot: COUIHintRedDot? = null
        var onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit = { viewHolder -> }
        private val dragViewNormalMarginStart = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_drag_view_normal_margin_start)
        private val dragViewEditMarginStart = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_drag_view_edit_margin_top)
        init {
            mIcon = itemView.findViewById(R.id.list_item_icon)
            mDragView?.setOnLongClickListener {
                if (this.isEdit) {
                    onItemDragCallback.invoke(this)
                    return@setOnLongClickListener true
                }
                return@setOnLongClickListener false
            }
            if (mContainer.isEdit) {
                mDragView?.visibility = View.VISIBLE
            } else {
                mDragView?.visibility = View.GONE
            }
            setImageViewTouchListener()
        }

        fun initRedHot() {
            if (redHot != null) {
                Log.d(TAG, "initRedHot -> redHot no need to re-inflate.")
                return
            }
            val viewStub = itemView.findViewById<ViewStub>(R.id.vs_red_dot)
            val viewRoot = viewStub.inflate()
            redHot = viewRoot.findViewById(R.id.red_dot)
        }

        override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
            Log.d(TAG, "MainCategoryAdapter.ViewHolder.onAnimationUpdate -> fraction = $fraction, enterEdit = $enterEdit")
            if (enterEdit) {
                val dragViewMargin = dragViewNormalMarginStart + (dragViewEditMarginStart - dragViewNormalMarginStart) * fraction
                mDragView?.updateLayoutParams<MarginLayoutParams> {
                    this.topMargin = dragViewMargin.toInt()
                }
                Log.d(TAG, "onAnimationUpdate is ${dragViewMargin.toInt()}")
                mDragView?.visibility = VISIBLE
                mDragView?.alpha = fraction
            } else {
                val dragViewMargin = dragViewEditMarginStart + (dragViewNormalMarginStart - dragViewEditMarginStart) * (1 - fraction)
                mDragView?.updateLayoutParams<MarginLayoutParams> {
                    this.topMargin = dragViewMargin.toInt()
                }
                mDragView?.visibility = VISIBLE
                mDragView?.alpha = fraction
            }
        }

        override fun onEditStateChange(isEdit: Boolean) {
            mContainer.isEdit = isEdit
            <EMAIL> = isEdit
        }

        override fun itemEnableStateChange(isEdit: Boolean) {
            itemView.isEnabled = !isEdit
        }
    }

    override fun getItemKey(item: MainCategoryItemsBean, position: Int): Int {
        return 0
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }

    fun setItemWidth(width: Int) {
        mWidth = width
    }
}