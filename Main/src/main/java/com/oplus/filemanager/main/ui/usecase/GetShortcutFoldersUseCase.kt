/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: GetQuickFoldersUseCase
 * * Description: GetQuickFoldersUseCase
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.main.ui.usecase

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.parentchild.bean.CategoryListBeanFactory
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class GetShortcutFoldersUseCase(
    private val defaultDispatcher: CoroutineDispatcher = Dispatchers.IO
) {

    companion object {
        private const val TAG = "GetQuickFoldersUseCase"
    }

    private val shortcutFolderApi: IShortcutFolderApi? by lazy {
        Injector.injectFactory<IShortcutFolderApi>()
    }

    suspend fun invoke(): List<MainCategoryItemsBean> = withContext(defaultDispatcher) {
        var folders = shortcutFolderApi?.loadFolders() ?: mutableListOf<MainCategoryItemsBean>()
        Log.d(TAG, "invoke folders: ${folders.size}")
        var categoryType = CategoryHelper.CATEGORY_FOLDER_GROUP + 1
        for (folder in folders) {
            folder.sideCategoryType = categoryType
            categoryType++
        }
        folders = folders.filter {
            !it.path.isNullOrEmpty() && File(it.path).exists()
        }.toMutableList()
        val addFolderBean = CategoryListBeanFactory.createAddShortcutFolderCategoryBean()
        folders.add(addFolderBean)
        folders
    }
}