/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/6/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.adapter

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.INVISIBLE
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewGroup.VISIBLE
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.view.animation.PathInterpolatorCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.couiswitch.COUISwitch
import com.coui.appcompat.rippleutil.COUIRippleDrawableUtil
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.viewholder.PressAnimViewHolder
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.view.CropImageView
import com.oplus.filemanager.main.view.SideEditText
import com.oplus.filemanager.parentchild.bean.CategoryListBean.Companion.TYPE_SOURCE_PANEL
import com.oplus.filemanager.parentchild.util.BaseDiffUtilCallback
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder.Companion.EDIT_VIEW_ALPHA
import java.io.File

class MainListItemAdapter(content: Context, resId: Int, private val animationHelper: ItemAnimationHelper) :
    BaseSelectionRecycleAdapter<MainListItemAdapter.ViewHolder, MainCategoryItemsBean>(content), LifecycleObserver {

    companion object {
        const val TAG = "MainListItemAdapter"
        private const val MAX_ELEVATION = 16f
        private const val DURATION_300 = 300L
        private const val DURATION_400 = 400L

        const val ELEVATION_CONTROL_X1 = 0.4f
        const val ELEVATION_CONTROL_Y1 = 0f
        const val ELEVATION_CONTROL_X2 = 0.2f
        const val ELEVATION_CONTROL_Y2 = 1f

        const val SCALE_CONTROL_X1 = 0.15f
        const val SCALE_CONTROL_Y1 = 0f
        const val SCALE_CONTROL_X2 = 0f
        const val SCALE_CONTROL_Y2 = 1f

        const val ONE_DAY = 1000 * 60 * 60 * 24
        const val SOURCE_SWITCH_UPLOAD_INFO = "source_switch_upload_info"
        const val SOURCE_SWITCH_FIRST_IN_TIMESTAMP = "source_switch_first_in_timestamp"
    }

    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private var mIconVisible = true
    private val resLayoutId: Int
    var onItemDeleteCallback: (labelId: Long) -> Unit = { }
    var onItemDragCallback: (viewHolder: RecyclerView.ViewHolder) -> Unit = { }
    private val rippleRadius = COUIMaskRippleDrawable.getMaskRippleRadiusByType(mContext, COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS)
    private var oldFiles = mutableListOf<MainCategoryItemsBean>()

    var isEdit: Boolean = false

    init {
        resLayoutId = resId
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<MainCategoryItemsBean>, listType: Int = -1) {
        if (listType == TYPE_SOURCE_PANEL) {
            oldFiles = mFiles.toMutableList()
            mFiles = data
            notifySuperAppItemListUpdated(mFiles, oldFiles)
        } else {
            mFiles = data
            notifyDataSetChanged()
        }
    }

    private fun notifySuperAppItemListUpdated(newList: MutableList<MainCategoryItemsBean>, oldList: MutableList<MainCategoryItemsBean>) {
        newList.apply {
            mFiles = newList
            DiffUtil.calculateDiff(object : BaseDiffUtilCallback<MainCategoryItemsBean>(oldList, this, { oldItem, newItem ->
                oldItem.packageName == newItem.packageName
            }, { oldItem, newItem ->
                oldItem.superAppSwitch == newItem.superAppSwitch && oldItem.name == newItem.name
            }) {}).dispatchUpdatesTo(this@MainListItemAdapter)
        }
        // 单独刷新背景和下划线
        if (oldList.isEmpty() || newList.isEmpty()) {
            return
        }
        // 用于进行局部刷新
        val diff = Bundle().apply { putBoolean("change_style", true) }
        // 找到旧列表中的第一个和最后一个 Item
        val oldFirstItem = oldList.getOrNull(0)
        val oldLastItem = oldList.getOrNull(oldList.size - 1)
        // 找到旧列表中第一个Item在新列表中的位置,并更新它
        val newFirstPosition = newList.indexOf(newList.find { it.packageName == oldFirstItem?.packageName })
        if (newFirstPosition != -1) {
            notifyItemChanged(newFirstPosition, diff)
        }
        // 更新新列表的第0个项
        notifyItemChanged(0, diff)
        // 找到旧列表中最后一个Item在新列表中的位置，并更新它
        val newLastPosition = newList.indexOf(newList.find { it.packageName == oldLastItem?.packageName })
        if (newLastPosition != -1) {
            notifyItemChanged(newLastPosition, diff)
        }
        // 更新新列表的最后一个项
        notifyItemChanged(newList.size - 1, diff)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        //do nothing
    }

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener?) {
        mOnRecyclerItemClickListener = onRecyclerItemClickListener
    }

    override fun getItem(position: Int): MainCategoryItemsBean? {
        return if (0 <= position && position < mFiles.size) {
            mFiles[position]
        } else {
            null
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(resLayoutId, parent, false)
        return ViewHolder(parent.context, v)
    }

    override fun getItemCount(): Int {
        return mFiles.size
    }

    override fun getItemId(position: Int): Long {
        if (!hasStableIds()) {
            return RecyclerView.NO_ID
        }
        return getItem(position)?.name?.hashCode()?.toLong() ?: RecyclerView.NO_ID
    }

    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        animationHelper.addListener(holder, isEdit)
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        animationHelper.removeListener(holder)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = mFiles[position]
        holder.bindData(data, isEdit)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            // 更改圆角和分割线
            COUICardListHelper.setItemCardBackground(holder.itemView, COUICardListHelper.getPositionInGroup(mFiles.size, position))
            if (position == mFiles.size - 1) {
                holder.divider?.visibility = View.GONE
            } else {
                holder.divider?.visibility = View.VISIBLE
            }
        }
    }

    fun setImageIconVisible(visible: Boolean) {
        mIconVisible = visible
    }

    inner class ViewHolder(val content: Context, convertView: View) : PressAnimViewHolder(convertView), ItemAnimationHelper.AnimationListener {

        private var title: TextView? = convertView.findViewById(R.id.item_title)
        private var arrowIcon: View? = convertView.findViewById(R.id.arrow_icon)
        var divider: View? = convertView.findViewById(R.id.divider_line)
        private var desc: TextView? = convertView.findViewById(R.id.item_desc)
        private var itemNum: TextView? = convertView.findViewById(R.id.list_item_num)
        private var dragView: ImageView? = convertView.findViewById(R.id.list_item_drag)
        private var deleteView: ImageView? = convertView.findViewById(R.id.list_item_delete)
        private var switchView: COUISwitch? = convertView.findViewById(R.id.list_item_switch)
        private var isSuperAppType: Boolean = false
        private var isDocType: Boolean = false
        private var categoryType = -1
        var editable: Boolean = true
        private var mSelectedAnimator: AnimatorSet? = null
        private var mDropAnimator: AnimatorSet? = null
        private val mScale: Float
        private val fileServiceInterface = Injector.injectFactory<IFileService>()

        private val dragViewNormalMarginStart = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_drag_view_normal_margin_start)
        private val dragViewEditMarginStart = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_drag_view_edit_margin_start)
        private val deleteViewNormalMarginEnd = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_delete_view_normal_margin_end)
        private val deleteViewEditMarginEnd = appContext.resources.getDimensionPixelOffset(R.dimen.main_list_delete_view_edit_margin_end)

        init {
            mIcon = itemView.findViewById(R.id.list_item_icon)
            title?.apply {
                setTextColor(ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_primary_neutral))
                setHintTextColor(ContextCompat.getColor(context, com.support.appcompat.R.color.coui_color_primary_neutral))
            }
            val outValue = TypedValue()
            itemView.resources.getValue(R.dimen.holder_view_item_scale, outValue, true)
            mScale = outValue.float
            initAnimators()
        }

        fun bindData(data: MainCategoryItemsBean, isEdit: Boolean) {
            val editState = (content as? MainActivity)?.getCategoryFragment()?.getEditState()
            this.isEdit = isEdit || editState ?: false
            setEditTextOperate(data, this@ViewHolder)
            isSuperAppType = CategoryHelper.isSuperAppType(data.sideCategoryType ?: -1)
            isDocType = data.sideCategoryType == CategoryHelper.CATEGORY_K_DOCS || data.sideCategoryType == CategoryHelper.CATEGORY_TENCENT_DOCS
            categoryType = data.sideCategoryType
            setNormalData(data)
            setEditState(data)
        }

        private fun setEditTextData(data: MainCategoryItemsBean) {
            val editText = (title as? SideEditText)
            editText?.activity = content as? ComponentActivity
            editText?.apply {
                if (CategoryHelper.isShortcutFolderType(data.sideCategoryType)) {
                    name = data.name.ifEmpty {
                        File(data.path).name.toString()
                    }
                    fileType = data.sideCategoryType
                    filePath = data.path
                } else if (CategoryHelper.isLabelType(data.sideCategoryType)) {
                    name = data.name
                    fileType = data.sideCategoryType
                }
            }
        }

        private fun setEditTextOperate(
            data: MainCategoryItemsBean,
            viewHolder: ViewHolder,
        ) {
            if (data.itemType != null && data.itemType == CategoryHelper.CATEGORY_ADD_FOLDER) {
                (viewHolder.title as? SideEditText)?.fileType = -1
            }
            if (CategoryHelper.isShortcutFolderType(data.sideCategoryType)
                || CategoryHelper.isLabelType(data.sideCategoryType)) {
                (viewHolder.title as? SideEditText)?.apply {
                    fileServiceAction = fileServiceInterface
                    setEditTextData(data)
                    setOnEditorActionListener { editText, actionId, _ ->
                        if (exceededUpperLimit && !isFocused) {
                            return@setOnEditorActionListener true
                        }
                        Log.d(TAG, "shortcutFolder rename operation $actionId")
                        runCatching {
                            return@setOnEditorActionListener renameTo()
                        }.onFailure {
                            Log.d(TAG, "shortcutFolder rename failed")
                        }
                        true
                    }
                }
            }
            if (CategoryHelper.CATEGORY_ADD_LABEL == data.sideCategoryType) {
                (viewHolder.title as? SideEditText)?.apply {
                    name = stringResource(com.filemanager.common.R.string.menu_new_folder)
                }
            }
        }

        private fun setNormalData(data: MainCategoryItemsBean) {
            itemView.setOnClickListener {
                if (isEdit) {
                    Log.d(TAG, "setOnClickListener -> edit state,return!")
                    return@setOnClickListener
                }
                if (isSuperAppType || isDocType) {
                    mOnRecyclerItemClickListener?.onSuperAppItemClick(data)
                } else {
                    mOnRecyclerItemClickListener?.onItemClick(itemView, adapterPosition)
                }
            }
            val categoryType = data.sideCategoryType
            categoryType?.let {
                itemView.tag = DropTag(categoryType, DropTag.Type.ITEM_VIEW)
            }
            (itemView as? COUICardListSelectedItemLayout)?.setIsSelected(false, false)
            var bitmap: Drawable? = null
            val resId: Int = data.iconId
            if (resId != 0) {
                (mIcon as? CropImageView)?.enableCropping = false
                bitmap = AppCompatResources.getDrawable(mContext, resId)
            } else if (data.drawable != null) {
                (mIcon as? CropImageView)?.enableCropping = true
                bitmap = data.drawable
            } else {
                (mIcon as? CropImageView)?.enableCropping = false
                bitmap = AppCompatResources.getDrawable(mContext, R.drawable.ic_download)
            }
            mIcon?.apply {
                setImageDrawable(bitmap)
            }
            title?.text = data.name
            val showDesc = (data.itemType == CategoryHelper.CATEGORY_K_DOCS || data.itemType == CategoryHelper.CATEGORY_TENCENT_DOCS)
            if (showDesc) {
                title?.updateLayoutParams<MarginLayoutParams> {
                    bottomMargin = 0
                }
                desc?.updateLayoutParams<MarginLayoutParams> {
                    bottomMargin =
                        appContext.resources.getDimensionPixelOffset(com.support.preference.R.dimen.support_preference_text_content_padding_bottom)
                }
            } else {
                title?.updateLayoutParams<MarginLayoutParams> {
                    bottomMargin =
                        appContext.resources.getDimensionPixelOffset(com.support.preference.R.dimen.support_preference_text_content_padding_bottom)
                }
                desc?.updateLayoutParams<MarginLayoutParams> {
                    bottomMargin = 0
                }
            }
            desc?.isVisible = showDesc
            COUICardListHelper.setItemCardBackground(itemView, COUICardListHelper.getPositionInGroup(itemCount, position))
            if (mIconVisible) {
                arrowIcon?.visibility = VISIBLE
            } else {
                arrowIcon?.visibility = View.GONE
            }
            if (position == mFiles.size - 1) {
                divider?.visibility = View.GONE
            } else {
                divider?.visibility = VISIBLE
            }
            changeTitleMode(isEdit)
            if (categoryType != null && CategoryHelper.isLabelType(categoryType)) {
                itemNum?.visibility = View.VISIBLE
                itemNum?.text = data.itemsCount.toString()
            } else {
                itemNum?.visibility = View.INVISIBLE
            }
        }

        private fun setEditState(data: MainCategoryItemsBean) {
            val categoryType = data.sideCategoryType ?: -1
            val isAddType = categoryType == CategoryHelper.CATEGORY_ADD_LABEL || categoryType == CategoryHelper.CATEGORY_ADD_FOLDER
            if (isAddType) {
                editable = false
                title?.isEnabled = false
                dragView?.isEnabled = false
                dragView?.visibility = INVISIBLE
                dragView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginStart = dragViewNormalMarginStart
                }
                deleteView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginEnd = deleteViewNormalMarginEnd
                }
                dragView?.visibility = INVISIBLE
                deleteView?.isEnabled = false

                itemView.tag = null //添加标签，添加快捷文件夹 dragTag都是null
                if (isEdit || MainApi.isDragging(content as Activity)) {
                    itemView.alpha = EDIT_VIEW_ALPHA
                } else {
                    itemView.alpha = 1.0f
                }
                (itemView as? COUICardListSelectedItemLayout)?.setBackgroundAnimationEnabled(!isEdit)
                return
            }

            if (isSuperAppType || isDocType) {
                switchView?.isChecked = data.superAppSwitch
            }

            editable = true
            itemView.alpha = 1.0f
            deleteView?.let {
                COUIRippleDrawableUtil.setIconPressRippleDrawable(it, rippleRadius)
            }
            dragView?.isEnabled = isEdit
            deleteView?.isEnabled = isEdit
            switchView?.isEnabled = isEdit
            if (isEdit) {
                onAnimationUpdate(1f, true)
            } else {
                onAnimationUpdate(0f, false)
            }

            dragView?.setOnLongClickListener {
                if (isEdit) {
                    onItemDragCallback.invoke(this)
                    return@setOnLongClickListener true
                }
                return@setOnLongClickListener false
            }
            dragView?.contentDescription = data.name
            deleteView?.setOnClickListener {
                if (isEdit) {
                    data.dbID?.let {
                        onItemDeleteCallback.invoke(it)
                    }
                }
            }

            switchView?.setOnClickListener {
                uploadSourceSwitch(data)
                data.superAppSwitch = switchView!!.isChecked
                mFiles.find { it.packageName == data.packageName }?.superAppSwitch = data.superAppSwitch
                mOnRecyclerItemClickListener?.onSuperAppItemSwitchClick(mFiles)
            }

            (itemView as? COUICardListSelectedItemLayout)?.setBackgroundAnimationEnabled(true)
        }

        private fun uploadSourceSwitch(data: MainCategoryItemsBean) {
            val map = hashMapOf<String, String>()
            map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_LAST_STATUS] =
                if (switchView!!.isChecked) "1" else "0"
            map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_NAME] = data.packageName
            StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPER_APP_SOURCE_SWITCH, map)
        }

        override fun onMacDragStateChange(isMacDragging: Boolean, fraction: Float) {
            setItemViewAlpha(isMacDragging, fraction)
            super.onMacDragStateChange(isMacDragging, fraction)
        }
        /**
         * 进入编辑，enterEdit 为true，fraction 0 -> 1变化
         * 退出编辑，enterEdit 为false，fraction 1 -> 0变化
         */
        override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {
            //只要是正在拖动的，添加标签和添加快捷文件都置灰
            if (MainApi.isDragging(content as Activity)) {
                if (itemView.tag == null) {
                    val alpha = EDIT_VIEW_ALPHA + fraction * (1 - EDIT_VIEW_ALPHA)
                    itemView.alpha = alpha
                    itemView.setOnClickListener(null)
                }
                return
            }
            if (!editable) {
                val disableAlpha = EDIT_VIEW_ALPHA
                val alpha = (1 - disableAlpha) * (1 - fraction) + disableAlpha
                itemView.alpha = alpha
                return
            }
            val rightOperateView = if (isSuperAppType || isDocType) switchView else deleteView
            if (enterEdit) {
                val dragViewMargin = dragViewNormalMarginStart + (dragViewEditMarginStart - dragViewNormalMarginStart) * fraction
                dragView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginStart = dragViewMargin.toInt()
                }
                dragView?.visibility = VISIBLE
                dragView?.alpha = fraction
                val deleteViewMargin = deleteViewNormalMarginEnd + (deleteViewEditMarginEnd - deleteViewNormalMarginEnd) * fraction
                rightOperateView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginEnd = deleteViewMargin.toInt()
                }
                rightOperateView?.visibility = VISIBLE
                rightOperateView?.alpha = fraction
                arrowIcon?.alpha = (1 - fraction)
                itemNum?.alpha = (1 - fraction)
            } else {
                val dragViewMargin = dragViewEditMarginStart + (dragViewNormalMarginStart - dragViewEditMarginStart) * (1 - fraction)
                dragView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginStart = dragViewMargin.toInt()
                }
                dragView?.alpha = fraction
                val deleteViewMargin = deleteViewEditMarginEnd + (deleteViewNormalMarginEnd - deleteViewEditMarginEnd) * (1 - fraction)
                rightOperateView?.updateLayoutParams<MarginLayoutParams> {
                    this.marginEnd = deleteViewMargin.toInt()
                }
                rightOperateView?.alpha = fraction
                arrowIcon?.alpha = (1 - fraction)
                itemNum?.alpha = (1 - fraction)
                if (fraction == 0f) {
                    dragView?.visibility = View.INVISIBLE
                    rightOperateView?.visibility = View.INVISIBLE
                }
            }
        }

        override fun onEditStateChange(isEdit: Boolean) {
            this.isEdit = isEdit
            <EMAIL> = isEdit
            if (editable) {
                changeTitleMode(this.isEdit)
                dragView?.isEnabled = isEdit
                deleteView?.isEnabled = isEdit
                switchView?.isEnabled = isEdit
            } else {
                (itemView as? COUICardListSelectedItemLayout)?.setBackgroundAnimationEnabled(!isEdit)
            }
        }

        private fun changeTitleMode(isEdit: Boolean) {
            (title as? SideEditText)?.apply {
                if (!((CategoryHelper.isShortcutFolderType(fileType))
                            || CategoryHelper.isLabelType(fileType))) return
                if (!isEdit && position != mFiles.size - 1) {
                    divider?.visibility = VISIBLE
                } else {
                    divider?.visibility = View.GONE
                }
                changeEditTextMode(isEdit)
            }
        }

        private fun initAnimators() {
            initSelectAnimator()
            initDropAnimator()
        }

        private fun initSelectAnimator() {
            val elevationAnimation = ObjectAnimator.ofFloat(
                itemView,
                "elevation",
                0f,
                MAX_ELEVATION
            ).apply {
                duration = DURATION_300
                interpolator = PathInterpolatorCompat.create(
                    ELEVATION_CONTROL_X1, ELEVATION_CONTROL_Y1, ELEVATION_CONTROL_X2, ELEVATION_CONTROL_Y2
                )
            }

            val scaleAnimator = ValueAnimator.ofFloat(1f, mScale).apply {
                duration = DURATION_300
                interpolator = PathInterpolatorCompat.create(
                    SCALE_CONTROL_X1, SCALE_CONTROL_Y1, SCALE_CONTROL_X2, SCALE_CONTROL_Y2
                )
                addUpdateListener { animation ->
                    val value = animation.animatedValue as Float
                    itemView.scaleX = value
                    itemView.scaleY = value
                }
            }

            mSelectedAnimator = AnimatorSet().apply { play(scaleAnimator)?.with(elevationAnimation) }
        }

        private fun initDropAnimator() {
            val scaleXAnimator = ObjectAnimator.ofFloat(itemView, "scaleX", mScale, 1f)
            val scaleYAnimator = ObjectAnimator.ofFloat(itemView, "scaleY", mScale, 1f)
            val elevationAnimation = ObjectAnimator.ofFloat(itemView, "elevation", MAX_ELEVATION, 0f)
            mDropAnimator = AnimatorSet().apply {
                duration = DURATION_400
                interpolator = PathInterpolatorCompat.create(
                    SCALE_CONTROL_X1, SCALE_CONTROL_Y1, SCALE_CONTROL_X2, SCALE_CONTROL_Y2
                )
                play(scaleXAnimator)?.with(scaleYAnimator)?.with(elevationAnimation)
            }
        }

        fun onItemSelected() {
            if (mDropAnimator != null && mDropAnimator?.isRunning!!) {
                mDropAnimator?.end()
            }
            mSelectedAnimator?.start()
        }

        fun onItemDrop() {
            if (mSelectedAnimator != null && mSelectedAnimator?.isRunning!!) {
                mSelectedAnimator?.end()
            }
            mDropAnimator?.start()
        }

        private fun setItemViewAlpha(isMacDragging: Boolean, fraction: Float) {
            val isAddType = categoryType == CategoryHelper.CATEGORY_ADD_LABEL || categoryType == CategoryHelper.CATEGORY_ADD_FOLDER
            if (isAddType) {
                itemView.alpha = if (isMacDragging) {
                    1f - (1f - EDIT_VIEW_ALPHA) * fraction
                } else {
                    EDIT_VIEW_ALPHA + (1f - EDIT_VIEW_ALPHA) * fraction
                }
            }
        }
    }

    override fun getItemKey(item: MainCategoryItemsBean, position: Int): Int {
        return 0
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }

    fun getAllList(): MutableList<MainCategoryItemsBean> {
        return mFiles
    }
}