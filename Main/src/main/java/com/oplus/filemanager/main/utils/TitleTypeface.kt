/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/12/01, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.main.utils

import android.graphics.Typeface
import android.widget.TextView

object TitleTypeface {
    private const val SANS_SERIF_MEDIUM = "sans-serif-medium"
    private const val WEIGHT_SEMI_BOLD = 600
    private const val LIGHT_WEIGHT = 350
    private const val MEDIUM_WEIGHT = 750
    private var lightTypeface: Typeface? = null
    private var mediumTypeface: Typeface? = null
    private var semiBoldTypeface: Typeface? = null

    fun creator(textView: TextView) {
        if (lightTypeface == null) {
            lightTypeface = textView.typeface
        }

        if (mediumTypeface == null) {
            runCatching {
                mediumTypeface = Typeface.create(SANS_SERIF_MEDIUM, Typeface.NORMAL)
            }.onFailure {
                mediumTypeface = Typeface.DEFAULT
            }
        }

        if (semiBoldTypeface == null) {
            runCatching {
                semiBoldTypeface = Typeface.create(Typeface.DEFAULT_BOLD, WEIGHT_SEMI_BOLD, false)
            }.onFailure {
                semiBoldTypeface = Typeface.DEFAULT_BOLD
            }
        }
    }

    fun getTypeface(weight: Int, isEditMode: Boolean): Typeface? {
        return if (isEditMode) {
            when (weight) {
                LIGHT_WEIGHT -> lightTypeface
                MEDIUM_WEIGHT -> mediumTypeface
                else -> lightTypeface
            }
        } else {
            semiBoldTypeface
        }
    }
}