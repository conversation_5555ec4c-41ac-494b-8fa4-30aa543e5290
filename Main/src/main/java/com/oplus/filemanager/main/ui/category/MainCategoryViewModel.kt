/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.oplus.filemanager.main.ui.category
 * * Version     : 1.0
 * * Date        : 2020/6/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.ui.category

import android.app.Activity
import androidx.annotation.VisibleForTesting
import androidx.collection.arrayMapOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.SUPER_APP_STATUS_LIST_KEY
import com.filemanager.common.constants.CommonConstants.SUPER_APP_STATUS_SP_NAME
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.STATE_CHECKING
import com.filemanager.common.constants.KtConstants.STATE_MOUNTED
import com.filemanager.common.constants.KtConstants.STATE_UNMOUNTED
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.google.gson.JsonSyntaxException
import com.oplus.filemanager.bean.SuperAppBean
import com.oplus.filemanager.interfaze.categoryapk.ICategoryApkApi
import com.oplus.filemanager.interfaze.categoryappamanger.ICategoryAppManagerApi
import com.oplus.filemanager.interfaze.categoryappmarketamanager.ICategoryAppMarketManagerApi
import com.oplus.filemanager.interfaze.categoryaudiovideo.ICategoryAudioVideoApi
import com.oplus.filemanager.interfaze.categorycompress.ICategoryCompressApi
import com.oplus.filemanager.interfaze.categorydoc.ICategoryDocumentApi
import com.oplus.filemanager.interfaze.categroyalbumset.ICategoryAlbumSetApi
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.recyclebin.RecycleBinTotalSizeCallback
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.adapter.MainListItemAdapter.Companion.ONE_DAY
import com.oplus.filemanager.main.adapter.MainListItemAdapter.Companion.SOURCE_SWITCH_FIRST_IN_TIMESTAMP
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.ui.uistate.StorageUiState
import com.oplus.filemanager.main.ui.usecase.GetApkCountInfoUseCase
import com.oplus.filemanager.main.ui.usecase.GetCloudDiskStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetDFMStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetOTGStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetPhoneStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetPrivateSafeStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetRemoteDeviceStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetSDCardStorageUseCase
import com.oplus.filemanager.main.ui.usecase.GetShortcutFoldersUseCase
import com.oplus.filemanager.main.ui.usecase.GetSuperAppItemsUseCase
import com.oplus.filemanager.main.utils.ClassBeanUtil
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.GroupCollapseUtil
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.Collator
import java.util.Locale
import kotlin.math.abs

open class MainCategoryViewModel : BaseViewModel() {

    companion object {
        private const val TAG = "MainCategoryViewModel"

        private const val KEY_REPORT_APK_COUNT_TIME_STAMP = "report_apk_count_time_stamp"
        private const val ONE_DAY_MILLIS =  24 * 60 * 60 * 1000

        const val EDIT_STATE_IDLE = -1
        const val EDIT_STATE_OFF = 0
        const val EDIT_STATE_ON = 1

        fun getOWorkVisibility(): Boolean {
            return if (AppUtils.isAppInstalledByPkgName(appContext, CommonConstants.OWORK)) {
                if (PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true)) {
                    true
                } else {
                    Log.i(TAG, "OWork switch is close!!")
                    false
                }
            } else {
                Log.i(TAG, "Not support OWork!!")
                false
            }
        }
    }

    var mDeleteState = MutableLiveData<Pair<Long, Long>>()
    var mMainCategoryItemList = MutableLiveData<MutableList<MainCategoryItemsBean>>()
    var mMainClassPanelItemList = MutableLiveData<MutableList<CategoryListBean>>()
    var mMainSuperAppItemList = MutableLiveData<MutableList<MainCategoryItemsBean>>()
    val mMainGroupListData = MutableLiveData<MutableList<CategoryListBean>>()
    val shortcutFolderItemList = MutableLiveData<List<MainCategoryItemsBean>>()
    private var mRecycleDataSizeThreadKey: String? = null
    private var hasReqSignInAccount = false

    protected val storageUiStateFlow = MutableStateFlow(StorageUiState())
    val storageUiState: StateFlow<StorageUiState> = storageUiStateFlow.asStateFlow()

    private val _reloadData = MutableStateFlow(false)
    val reloadData: StateFlow<Boolean> = _reloadData.asStateFlow()

    var editState = MutableLiveData(EDIT_STATE_IDLE)

    private val cloudDrive: ICloudDrive? by lazy {
        Injector.injectFactory<ICloudDrive>()
    }

    @Volatile
    var mInternalPath: String? = null
        get() {
            if (field == null) {
                field = VolumeEnvironment.getInternalSdPath(appContext)
            }
            return field
        }

    @Volatile
    var mExternalPath: String? = null
        get() {
            if (field == null) {
                field = VolumeEnvironment.getExternalSdPath(appContext)
            }
            return field
        }

    @Volatile
    var mOtgPaths: List<String>? = null

    fun changeEditState() {
        val state = editState.value ?: EDIT_STATE_IDLE
        if (state == EDIT_STATE_IDLE || state == EDIT_STATE_OFF) {
            editState.postValue(EDIT_STATE_ON)
        } else {
            editState.postValue(EDIT_STATE_OFF)
        }
    }

    fun isEdit(): Boolean {
        val state = editState.value ?: EDIT_STATE_IDLE
        return state == EDIT_STATE_ON
    }

    fun loadSuperAppData(defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        launch {
            withContext(defaultDispatcher) {
                val items = GetSuperAppItemsUseCase(appContext).invoke()
                appendSuperAppCategoryType(items)
                sortAndInitSwitchStatus(items)
                mMainSuperAppItemList.postValue(items)
            }
        }
    }

    fun saveMainSuperAppData(superAppList: MutableList<MainCategoryItemsBean>, isUpdateMainCategory: Boolean = false) {
        Log.d(TAG, "saveMainEditSourceData start")
        viewModelScope.launch(context = Dispatchers.IO) {
            val saveStatusList = mutableListOf<SuperAppBean>()
            superAppList.withIndex().forEach { (index, it) ->
                saveStatusList.add(SuperAppBean(it.packageName, index, it.superAppSwitch))
            }
            val jsonString = GsonUtil.toJsonSafeCall(saveStatusList)
            PreferencesUtils.put(SUPER_APP_STATUS_SP_NAME, SUPER_APP_STATUS_LIST_KEY, jsonString)

            if (isUpdateMainCategory) {
                updateCategory()
            }
        }
    }

    open fun updateCategory() {
        updateMainCategoryList()
    }

    private fun sortAndInitSwitchStatus(superAppList: MutableList<MainCategoryItemsBean>) {
        val jsonString = PreferencesUtils.getString(SUPER_APP_STATUS_SP_NAME, SUPER_APP_STATUS_LIST_KEY)
        val beanList = GsonUtil.toListSafeCall<SuperAppBean>(jsonString, SuperAppBean::class.java)
        superAppList.forEach { superAppItem ->
            val bean = beanList.firstOrNull { it.packageName == superAppItem.packageName }
            superAppItem.order = bean?.order ?: Int.MAX_VALUE
            superAppItem.superAppSwitch = bean?.switchStatus ?: true
        }
        val collator = Collator.getInstance(Locale.getDefault())
        superAppList.sortWith(compareBy<MainCategoryItemsBean> { it.order }.thenBy { collator.getCollationKey(it.name) })
        // 保存的时候过滤掉不需要保存的item
        saveMainSuperAppData(superAppList.filter { it.isShow }.toMutableList(), true)
        uploadSourceSwitch(superAppList)//上传埋点
    }

    /**
     * 来源开关埋点，24小时内，每个开关上传1次
     * */
    private fun uploadSourceSwitch(superAppList: MutableList<MainCategoryItemsBean>) {
        try {
            //调起进程SP中存的时间戳，默认0
            val lastTimeMillis = PreferencesUtils.getLong(key = SOURCE_SWITCH_FIRST_IN_TIMESTAMP, default = 0L)
            //当前时间戳
            val currentTimeMillis = System.currentTimeMillis()
            //差
            val timeDifference = abs(currentTimeMillis - lastTimeMillis)
            //相差不足1天，返回
            if (timeDifference <= ONE_DAY) {
                return
            }
            //相差大于1天，存入当前时间戳，开始上传埋点
            PreferencesUtils.put(key = SOURCE_SWITCH_FIRST_IN_TIMESTAMP, value = currentTimeMillis)
            viewModelScope.launch(context = Dispatchers.IO) {
                superAppList.forEach { superAppItem ->
                    //开关埋点
                    Log.i(TAG, "do source switch status upload, $timeDifference")
                    val map = hashMapOf<String, String>()
                    //遍历来源集合，获取每个来源的开关状态，1是开，0是关
                    map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_FIRST_STATUS] =
                        if (superAppItem.superAppSwitch) "1" else "0"
                    //来源的包名
                    map[StatisticsUtils.SUPER_APP_SOURCE_SWITCH_NAME] = superAppItem.packageName
                    StatisticsUtils.onCommon(appContext, StatisticsUtils.SUPER_APP_SOURCE_SWITCH, map)
                }
            }
        } catch (e: JsonSyntaxException) {
            Log.w(TAG, "JSON parsing error: ${e.message}")
        } catch (e: NumberFormatException) {
            Log.w(TAG, "Error converting string to Long: ${e.message}")
        }
    }

    /**
     * 由于拿到的三方应用的MainCategoryItemsBean的CategoryType统一赋值成CategoryType.Category_QQ，参见SuperAppHelper的checkAndAddApkInfo
     * 并考虑到后续添加的第三方应用的可扩展性，来源下的categoryType重新赋值，以CATEGORY_SOURCE_GROUP为基础，逐个增加，标记不同的类别
     * CATEGORY_SOURCE_GROUP 相比于其他类别，数值大10倍，避免重复
     */
    private fun appendSuperAppCategoryType(superAppList: MutableList<MainCategoryItemsBean>): ArrayList<CategoryListBean> {
        val categoryList = ArrayList<CategoryListBean>()
        var categoryType = CategoryHelper.CATEGORY_SOURCE_GROUP + 1
        for (superApp in superAppList) {
            if (superApp.itemType == CategoryHelper.CATEGORY_K_DOCS || superApp.itemType == CategoryHelper.CATEGORY_TENCENT_DOCS) {
                superApp.sideCategoryType = superApp.itemType
            } else {
                superApp.sideCategoryType = categoryType
                categoryType++
            }
        }
        return categoryList
    }

    fun loadMainCategoryItem(hasPermission: Boolean, ioDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        if (PrivacyPolicyController.hasAgreePrivacy().not()) {
            Log.d(TAG, "loadMainCategoryItem not hasAgreePrivacy")
            return
        }
        viewModelScope.launch(context = ioDispatcher) {
            val categoryItems = ClassBeanUtil.mainCategoryClassPanelInitView(mMainCategoryItemList.value)
            categoryItems.forEach { bean ->
                CollectPrivacyUtils.collectMedia(bean.itemType, bean.itemsCount)
            }
            mMainCategoryItemList.postValue(categoryItems)
            reportApkCountInfo(hasPermission)
        }
    }

    fun updateReloadData() {
        _reloadData.value = true
    }

    fun reportApkCountInfo(hasPermission: Boolean) {
        if (!hasPermission) {
            Log.d(TAG, "reportApkCountInfo no storage permission.")
            return
        }
        val currTimeStamp = System.currentTimeMillis()
        val recordTimeStamp = PreferencesUtils.getLong(key = KEY_REPORT_APK_COUNT_TIME_STAMP, default = 0L)
        if (currTimeStamp - recordTimeStamp < ONE_DAY_MILLIS) {
            Log.d(TAG, "reportApkCountInfo interval less than one day.")
            return
        }
        val apkCountInfo = GetApkCountInfoUseCase().invoke()
        val unInstalledApkCount = apkCountInfo.first
        val installedApkCount = apkCountInfo.second
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_APK_COUNT_INFO, mapOf(
                StatisticsUtils.UN_INSTALLED_APK_COUNT to unInstalledApkCount.toString(),
                StatisticsUtils.INSTALLED_APK_COUNT to installedApkCount.toString()
            ))
        PreferencesUtils.put(key = KEY_REPORT_APK_COUNT_TIME_STAMP, value = currTimeStamp)
    }

    /**
     * Fetch Storage Infos.
     */
    open fun fetchStorageInfo(invokePrivateSafe: Boolean, defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        viewModelScope.launch {
            fetchStorages(invokePrivateSafe, defaultDispatcher)
        }
    }

    @VisibleForTesting
    suspend fun fetchStorages(invokePrivateSafe: Boolean, defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        withContext(defaultDispatcher) {
            val storageItems = mutableListOf<Storage>()
            var privateSafeState = true
            GetPhoneStorageUseCase().invoke(storageItems)
            GetSDCardStorageUseCase().invoke(storageItems)
            GetOTGStorageUseCase().invoke(storageItems)
            GetRemoteDeviceStorageUseCase().invoke(storageItems)
            GetDFMStorageUseCase({ storage -> updateDFMItem(storage) }).invoke(storageItems)
            GetCloudDiskStorageUseCase().invoke(storageItems)
            if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
                GetPrivateSafeStorageUseCase().invoke(storageItems)
                privateSafeState = false
            }
            if (invokePrivateSafe && privateSafeState) {
                GetPrivateSafeStorageUseCase().invoke(storageItems)
            }
            Log.i(TAG, "fetchStorageInfo -> storageItems = $storageItems")
            setStoragePath(storageItems)
            storageUiStateFlow.update {
                it.copy(storageItems = storageItems)
            }
            SortRecordModeFactory.resetAllPath()
        }
    }

    suspend fun reqSignInAccount(defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        viewModelScope.launch {
            withContext(defaultDispatcher) {
                if (MyApplication.buildFlavorRegion != KtConstants.REGION_GDPR) {
                    for (storageItem in storageUiStateFlow.value.storageItems) {
                        if (storageItem is Storage.CloudDiskStorage && !storageItem.isSingedIn && !hasReqSignInAccount) {
                            Log.d(TAG, "reqSignInAccount CloudDiskStorage, isSingedIn = false")
                            if (cloudDrive?.isSupportCloudDisk() == true) {
                                val heytapAccountAction = Injector.injectFactory<IHeytapAccount>()
                                heytapAccountAction?.login(appContext, false) { login ->
                                    Log.d(TAG, "reqSignInAccount onReqFinish isLogin = $login")
                                    if (login) {
                                        fetchStorageInfo(false)
                                    }
                                    hasReqSignInAccount = true
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    fun updateOTGState(defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        viewModelScope.launch {
            updateOtgItem(defaultDispatcher)
        }
    }

    @VisibleForTesting
    suspend fun updateOtgItem(defaultDispatcher: CoroutineDispatcher = Dispatchers.IO) {
        withContext(defaultDispatcher) {
            val storageItems = storageUiStateFlow.value.storageItems
            storageItems.forEach {
                if (it is Storage.OTGStorage) {
                    it.state = STATE_CHECKING
                }
            }
            storageUiStateFlow.update {
                it.copy(storageItems = storageItems)
            }
        }
    }

    private fun updateDFMItem(storage: Storage.DFMStorage) {
        Log.d(TAG, "updateDFMItem $storage")
        val storageItems = mutableListOf<Storage>()
        storageItems.addAll(storageUiStateFlow.value.storageItems)
        storageItems.forEach {
            if (it is Storage.DFMStorage) {
                // 要更新的值是旧的
                if (storage.refreshTime < it.refreshTime) {
                    Log.d(TAG, "updateDFMItem item is old")
                    return@forEach
                }
                it.set(storage)
            }
        }
        storageUiStateFlow.update {
            it.copy(storageItems = storageItems)
        }
    }

    fun updateDeviceStatus(deviceId: String, status: Int) {
        Log.d(TAG, "updateDeviceStatus deviceId=$deviceId status=$status")
        val storageItems = mutableListOf<Storage>()
        storageItems.addAll(storageUiStateFlow.value.storageItems)
        storageItems.forEach {
            if (it is Storage.RemoteDeviceStorage) {
                it.deviceList?.forEach { device ->
                    if ((device.deviceId == deviceId)) {
                        device.deviceStatus = status
                    }
                }
            }
        }
        storageUiStateFlow.update {
            it.copy(storageItems = storageItems)
        }
    }

    private fun setStoragePath(storageItems: List<Storage>) {
        storageItems.forEach { item ->
            if (item is Storage.OTGStorage) {
                mOtgPaths = item.otgPath
            } else if (item is Storage.SDCardStorage) {
                if (item.state == STATE_MOUNTED) {
                    mExternalPath = VolumeEnvironment.getExternalSdPath(appContext)
                } else if (item.state == STATE_UNMOUNTED) {
                    mExternalPath = null
                }
            }
        }
    }

    fun loadRecycleBinSize() {
        Log.d(TAG, "loadRecycleBinSize ")
        // <EMAIL> For ROM.App.FileManager, BugID.13455 RecycleBin, 2020/3/24, Modify
        // if is light Version，RecycleBin will closed
        if (!FeatureCompat.sIsLightVersion) {
            val recycleBinAction = Injector.injectFactory<IRecycleBin>()
            mRecycleDataSizeThreadKey = recycleBinAction?.loadRecycleBinSize(TAG,
                mRecycleDataSizeThreadKey, object : RecycleBinTotalSizeCallback {
                    override fun onTotalSizeReturn(size: Pair<Long, Long>) {
                        mDeleteState.postValue(size)
                    }
                })
        }
    }

    fun onCategoryItemClick(activity: Activity, position: Int) {
        val data = mMainCategoryItemList.value?.get(position)
        when (data?.itemType) {
            CategoryHelper.CATEGORY_CLOUD -> openCloudDisk(activity)
            CategoryHelper.CATEGORY_OAPS -> Injector.injectFactory<IOapsLib>()?.startOaps(activity)
            CategoryHelper.CATEGORY_APPMANAGER -> {
                val categoryAppManagerApi = Injector.injectFactory<ICategoryAppManagerApi>()
                categoryAppManagerApi?.entryAppManager(activity)
            }
            CategoryHelper.CATEGORY_APK -> openApkPage(activity, data)
            else -> {
                if ((activity is MainActivity)) {
                    data?.let {
                        Log.d(TAG, "data.getItemType()  =  " + it.itemType)
                        when (it.itemType) {
                            CategoryHelper.CATEGORY_DOC -> {
                                val categoryDocumentApi = Injector.injectFactory<ICategoryDocumentApi>()
                                if (FeatureCompat.isSmallScreenPhone) {
                                    categoryDocumentApi?.startCategoryDocumentActivity(
                                        activity,
                                        it.name
                                    )
                                } else {
                                    categoryDocumentApi?.startCategoryDocumentFragment(activity, it.name)
                                }
                                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_DOC, Constants.PAGE_MAIN)
                            }
                            CategoryHelper.CATEGORY_IMAGE -> {
                                val categoryAlbumSetApi = Injector.injectFactory<ICategoryAlbumSetApi>()
                                if (FeatureCompat.isSmallScreenPhone) {
                                    categoryAlbumSetApi?.startCategoryAlbumSetActivity(activity)
                                } else {
                                    categoryAlbumSetApi?.startCategoryAlbumSetFragment(activity)
                                }
                                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_IMAGE_SET, Constants.PAGE_MAIN)
                            }
                            CategoryHelper.CATEGORY_COMPRESS -> {
                                // The title has been hard-coded in the CategoryCompressActivity
                                val categoryCompressApi = Injector.injectFactory<ICategoryCompressApi>()
                                if (FeatureCompat.isSmallScreenPhone) {
                                    categoryCompressApi?.startCategoryCompressActivity(
                                        activity,
                                        it.name
                                    )
                                } else {
                                    categoryCompressApi?.startCategoryCompressFragment(activity, it.name)
                                }
                                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_COMPRESS, Constants.PAGE_MAIN)
                            }
                            CategoryHelper.CATEGORY_AUDIO -> {
                                val categoryAudioVideoApi = Injector.injectFactory<ICategoryAudioVideoApi>()
                                if (FeatureCompat.isSmallScreenPhone) {
                                    categoryAudioVideoApi?.startCategoryAudioActivity(activity)
                                } else {
                                    categoryAudioVideoApi?.startCategoryAudioFragment(activity)
                                }
                                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_AUDIO, Constants.PAGE_MAIN)
                            }
                            CategoryHelper.CATEGORY_VIDEO -> {
                                val categoryAudioVideoApi = Injector.injectFactory<ICategoryAudioVideoApi>()
                                if (FeatureCompat.isSmallScreenPhone) {
                                    categoryAudioVideoApi?.startCategoryVideoActivity(activity)
                                } else {
                                    categoryAudioVideoApi?.startCategoryVideoFragment(activity)
                                }
                                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_VIDEO, Constants.PAGE_MAIN)
                            }
                            else -> {
                            }
                        }
                        StatisticsUtils.nearMeStatistics(activity, it.itemType)
                    }
                }
            }
        }
    }

    private fun openApkPage(
        activity: Activity,
        data: MainCategoryItemsBean
    ) {
        val categoryAppMarketManagerApi = Injector.injectFactory<ICategoryAppMarketManagerApi>()
        if (categoryAppMarketManagerApi?.isAppMarketManagerEnabled(appContext) == true && !ModelUtils.isTablet()) {
            Log.d(TAG, "openApkPage -> jump to app market manager.")
            categoryAppMarketManagerApi.entryAppMarketManager(activity)
            val hasRedDot = data.itemsCount > 0
            StatisticsUtils.onCommon(
                activity, StatisticsUtils.EVENT_OPEN_APP_MARKET_MANAGER,
                mapOf(StatisticsUtils.HAS_RED_DOT to hasRedDot.toString())
            )
            StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_APK_MARKET, Constants.PAGE_MAIN)
        } else {
            if ((activity is MainActivity)) {
                val categoryApkApi = Injector.injectFactory<ICategoryApkApi>()
                if (FeatureCompat.isSmallScreenPhone) {
                    categoryApkApi?.startCategoryApkActivity(activity, data.name)
                } else {
                    categoryApkApi?.startCategoryApkFragment(activity, data.name)
                }
            }
            StatisticsUtils.nearMeStatistics(activity, data.itemType)
            StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_APK, Constants.PAGE_MAIN)
        }
    }

    fun onSupperItemClick(activity: Activity, data: MainCategoryItemsBean) {
        if ((activity is MainActivity)) {
            data.let {
                Log.d(TAG, "onSupperItemClick -> data = ${it.itemType} ; isSmallPhone = ${FeatureCompat.isSmallScreenPhone}")
                if (it.itemType == CategoryHelper.CATEGORY_TENCENT_DOCS || it.itemType == CategoryHelper.CATEGORY_K_DOCS) {
                    val fileCloudBrowserAction = Injector.injectFactory<IFileCloudBrowser>()
                    fileCloudBrowserAction?.startCloudDriveFragment(activity, it.itemType)
                } else {
                    val superApp = Injector.injectFactory<ISuperApp>()
                    if (FeatureCompat.isSmallScreenPhone) {
                        superApp?.startSuperApp(activity, data)
                    } else {
                        superApp?.startSuperAppFragment(activity, data)
                    }
                }
                if (data.itemType == CategoryHelper.CATEGORY_PCCONNECT) {
                    StatisticsUtils.onCommon(activity, StatisticsUtils.HEY_PC_CLICK)
                }
                StatisticsUtils.statisticsPageExposure(activity, "", data.name, Constants.PAGE_MAIN)
                StatisticsUtils.nearMeStatistics(activity, it.itemType)
            }
        }
    }

    /**
     * 打开云盘
     * @param activity activity
     * @param cloudDriverSubtitle 云盘副标题的内容，埋点使用，默认为 null
     */
    fun openCloudDisk(activity: Activity, cloudDriverSubtitle: String? = null) {
        if (KtAppUtils.checkAppEnabledWithDialog(
                activity,
                cloudDrive?.getCloudPackageName(activity) ?: "",
                com.filemanager.common.R.string.cloud_service_disable_message
            ).not()
        ) return
        if (UIConfigMonitor.isZoomWindowShow()) {
            CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
        }
        try {
            cloudDrive?.startCloudDiskActivity(activity)
            if (cloudDriverSubtitle == null) {
                StatisticsUtils.onCommon(activity, StatisticsUtils.OPEN_CLOUDDRIVER)
            } else {
                val map = arrayMapOf(StatisticsUtils.CLOUD_DRIVER_SUBTITLE to cloudDriverSubtitle)
                StatisticsUtils.onCommon(activity, StatisticsUtils.OPEN_CLOUDDRIVER, map)
            }
            StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_CLOUD_DISK, Constants.PAGE_MAIN)
        } catch (e: Exception) {
            Log.e(TAG, "startCategoryActivity start CloudDisk Error ->" + e.message)
        }
    }

    /**
     * 加载默认的类别，用于MainCategoryFragment
     */
    fun loadMainCategoryList() {
        // 先加载不耗时的数据，避免数据量过大的时候等待时间过长
        val categoryList = ArrayList<CategoryListBean>()
        //存储文件
        loadStoragePanel(categoryList)
        //快捷文件夹
        loadFolderList(categoryList)
        //标签
        loadLabelList(categoryList)
        //最近删除和私密保险箱
        loadMenuPanelList(categoryList)
        mMainGroupListData.value = categoryList

        // IO中加载所有数据
        updateMainCategoryList()
    }

    /**
     * 加载全量的默认类别，包含耗时的操作
     */
    fun updateMainCategoryList() {
        viewModelScope.launch(context = Dispatchers.IO) {
            val categoryList = ArrayList<CategoryListBean>()
            //存储文件
            loadStoragePanel(categoryList)
            //来源
            loadSuperAppList(categoryList)
            //快捷文件夹
            loadFolderList(categoryList)
            //标签
            loadLabelList(categoryList)
            //最近删除和私密保险箱
            loadMenuPanelList(categoryList)
            mMainGroupListData.postValue(categoryList)
        }
    }

    private fun loadStoragePanel(categoryList: ArrayList<CategoryListBean>) {
        val panelBean = CategoryListBean(CategoryListBean.TYPE_STORAGE_PANEL, CategoryListBean.TYPE_STORAGE_PANEL)
        categoryList.add(panelBean)
    }

    fun loadMenuPanelList(categoryList: ArrayList<CategoryListBean>) {
        val panelBean = CategoryListBean(CategoryListBean.TYPE_MENU_PANEL, CategoryListBean.TYPE_MENU_PANEL)
        categoryList.add(panelBean)
    }

    /**
     * 加载文件夹列表
     */
    protected fun loadFolderList(categoryList: ArrayList<CategoryListBean>) {
        val groupBean = CategoryListBean(CategoryHelper.CATEGORY_FOLDER_GROUP, CategoryListBean.TYPE_EXPANDABLE)
        groupBean.name = stringResource(R.string.string_fast_folder)
        groupBean.expanded = GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_FOLDER_GROUP)
        categoryList.add(groupBean)

        //分类组内的面板
        val groupPanelBean = CategoryListBean(-1, CategoryListBean.TYPE_FOLDER_PANEL)
        groupBean.childCategoryList.add(groupPanelBean)
        if (groupBean.expanded) {
            categoryList.add(groupPanelBean)
        }
    }

    /**
     * 加载标签列表
     */
    protected fun loadLabelList(categoryList: ArrayList<CategoryListBean>) {
        val groupBean = CategoryListBean(CategoryHelper.CATEGORY_LABEL_GROUP, CategoryListBean.TYPE_EXPANDABLE)
        groupBean.name = stringResource(R.string.menu_file_list_label)
        groupBean.expanded = GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_LABEL_GROUP)
        categoryList.add(groupBean)

        //分类组内的面板
        val groupPanelBean = CategoryListBean(CategoryListBean.TYPE_LABEL_PANEL, CategoryListBean.TYPE_LABEL_PANEL)
        groupBean.childCategoryList.add(groupPanelBean)
        if (groupBean.expanded) {
            categoryList.add(groupPanelBean)
        }
    }

    /**
     * 加载来源列表
     */
    protected fun loadSuperAppList(categoryList: ArrayList<CategoryListBean>) {
        val jsonString = PreferencesUtils.getString(SUPER_APP_STATUS_SP_NAME, SUPER_APP_STATUS_LIST_KEY)
        val beanList = GsonUtil.toListSafeCall<SuperAppBean>(jsonString, SuperAppBean::class.java)
        val isHide = beanList.all { !it.switchStatus }
        if (isEdit().not() && isHide && beanList.isNotEmpty()) {
            Log.d(TAG, "SuperApp items need to be hidden")
            return
        }

        val groupBean = CategoryListBean(CategoryHelper.CATEGORY_SOURCE_GROUP, CategoryListBean.TYPE_EXPANDABLE)
        groupBean.name = stringResource(R.string.text_file_source)
        groupBean.expanded = GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_SOURCE_GROUP)
        categoryList.add(groupBean)

        //分类组内的面板
        val groupPanelBean = CategoryListBean(CategoryListBean.TYPE_SOURCE_PANEL, CategoryListBean.TYPE_SOURCE_PANEL)
        groupBean.childCategoryList.add(groupPanelBean)
        if (groupBean.expanded) {
            categoryList.add(groupPanelBean)
        }
    }

    fun loadShortcutFolders() {
        Log.d(TAG, "loadShortcutFolders start ...")
        viewModelScope.launch(context = Dispatchers.IO) {
            val categoryList = GetShortcutFoldersUseCase().invoke()
            shortcutFolderItemList.postValue(categoryList)
        }
    }
}