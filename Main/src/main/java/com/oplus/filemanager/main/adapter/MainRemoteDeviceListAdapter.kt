/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: MainRemoteDeviceListAdapter
 ** Description: Remote device list adapter
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.main.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.marginBottom
import androidx.core.view.marginTop
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.SmoothRoundedCornersConstraintLayout
import com.filemanager.common.viewholder.PressAnimViewHolder
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.R
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper

class MainRemoteDeviceListAdapter : RecyclerView.Adapter<MainRemoteDeviceListAdapter.ViewHolder>() {

    companion object {
        const val TAG = "MainRemoteDeviceListAdapter"
    }

    var devices: MutableList<MainCategoryItemsBean> = mutableListOf()
    private val animationHelper: ItemAnimationHelper = ItemAnimationHelper()
    private var recyclerItemClickListener: OnRecyclerItemClickListener? = null

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        recyclerItemClickListener = onRecyclerItemClickListener
    }

    override fun getItemId(position: Int): Long {
        val item = getItem(position) ?: return SelectionTracker.NO_LONG_ITEM_ID
        return getItemKey(item, position).toLong()
    }

    override fun getItemCount(): Int {
        return devices.size
    }

    fun getItem(position: Int): MainCategoryItemsBean? {
        return if (0 <= position && position < devices.size) {
            devices[position]
        } else null
    }

    fun getItemKey(item: MainCategoryItemsBean, position: Int): Int {
        if (item.deviceId.isNullOrEmpty()) {
            Log.d(TAG, "getItemKey isNullOrEmpty: ${position.hashCode()}")
            return position.hashCode()
        }
        Log.d(TAG, "getItemKey deviceID: ${item.deviceId.hashCode()}")
        return item.deviceId.hashCode()
    }

    fun setData(data: MutableList<MainCategoryItemsBean>) {
        devices.clear()
        devices.addAll(data)
        notifyDataSetChanged()
    }

    fun startAnimation(isEdit: Boolean) {
        animationHelper.isEdit = isEdit
        animationHelper.startAnimation()
    }

    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        animationHelper.addListener(holder, animationHelper.isEdit)
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        animationHelper.removeListener(holder)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.main_remote_device_list_item, parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = devices[position]
        holder.bindData(data, animationHelper.isEdit)
    }

    inner class ViewHolder(convertView: View) : PressAnimViewHolder(convertView), ItemAnimationHelper.AnimationListener {
        var container: SmoothRoundedCornersConstraintLayout = itemView.findViewById(R.id.main_remote_device_item)
        var deviceName: TextView = itemView.findViewById(R.id.remote_device_name)
        var deviceStatus: TextView = itemView.findViewById(R.id.remote_device_status)
        var deviceTitle: TextView = itemView.findViewById(R.id.remote_device_title)
        var deviceIcon: ImageView = itemView.findViewById(R.id.remote_mac_icon)

        fun bindData(data: MainCategoryItemsBean, isEdit: Boolean) {
            container.isEdit = isEdit
            this.isEdit = isEdit
            updateDeviceName(data)
            updateDeviceStatus(data)
            setRemoteMacIconVisible()
            itemView.setOnClickListener {
                recyclerItemClickListener?.onItemClick(itemView, layoutPosition)
            }
        }

        private fun updateDeviceName(data: MainCategoryItemsBean) {
            deviceName.text = data.name
            /*deviceName.setTextColor(
                COUIContextUtil.getAttrColor(
                    deviceName.context,
                    if (data.deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
                        com.support.appcompat.R.attr.couiColorLabelSecondary
                    } else {
                        com.support.appcompat.R.attr.couiColorLabelPrimary
                    }
                )
            )*/
        }

        private fun updateDeviceStatus(data: MainCategoryItemsBean) {
            deviceStatus.text = getDeviceStatusDes(data.deviceStatus)
            deviceStatus.setTextColor(
                COUIContextUtil.getAttrColor(
                    deviceStatus.context,
                    if (data.deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
                        com.support.appcompat.R.attr.couiColorLabelTertiary
                    } else {
                        com.support.appcompat.R.attr.couiColorLabelSecondary
                    }
                )
            )
            val drawable = if (data.deviceStatus == RemoteDeviceConstants.CONNECTED) {
                ContextCompat.getDrawable(
                    deviceStatus.context,
                    com.filemanager.common.R.drawable.ic_remote_device_online_status
                )
            } else null
            if (Utils.isRtl()) {
                deviceStatus.setCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null)
            } else {
                deviceStatus.setCompoundDrawablesWithIntrinsicBounds(drawable, null, null, null)
            }
            deviceStatus.compoundDrawablePadding = deviceStatus.context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_6dp)
        }

        private fun getDeviceStatusDes(deviceStatus: Int): String {
            return when (deviceStatus) {
                RemoteDeviceConstants.UNDISCOVERED -> MyApplication.appContext.resources.getString(com.filemanager.common.R.string.offline)
                RemoteDeviceConstants.DISCOVERED -> MyApplication.appContext.resources.getString(com.filemanager.common.R.string.connectable)
                RemoteDeviceConstants.CONNECTING -> MyApplication.appContext.resources.getString(com.filemanager.common.R.string.connecting)
                else -> MyApplication.appContext.resources.getString(com.filemanager.common.R.string.connected)
            }
        }

        private fun setRemoteMacIconVisible() {
            deviceName.viewTreeObserver?.addOnGlobalLayoutListener(object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    deviceName.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                    val titleH = deviceTitle.height ?: 0
                    val titleMarginTop = deviceTitle.marginTop ?: 0
                    val nameH = deviceName.height ?: 0
                    val deviceStatusH = deviceStatus.height ?: 0
                    val itemH =
                        MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.main_storage_height)
                    val iconH =
                        MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_42dp)
                    val iconMarginBottom = deviceIcon.marginBottom ?: 0
                    val remainH = itemH - titleH - titleMarginTop - nameH - deviceStatusH
                    Log.d(TAG, "setRemoteMacIconVisible remainH $remainH, height ${(iconH + iconMarginBottom)}")
                    if (remainH >= iconH + iconMarginBottom) {
                        deviceIcon.visibility = View.VISIBLE
                    } else {
                        deviceIcon.visibility = View.GONE
                    }
                }
            })
        }


        override fun onAnimationUpdate(fraction: Float, enterEdit: Boolean) {}

        override fun onEditStateChange(isEdit: Boolean) {
            container.isEdit = isEdit
            <EMAIL> = isEdit
        }

        override fun itemEnableStateChange(isEdit: Boolean) {}
    }
}