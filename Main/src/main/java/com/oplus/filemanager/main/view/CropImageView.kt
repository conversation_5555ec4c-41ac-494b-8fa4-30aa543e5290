/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : CropImageView
 * * Version     : 1.0
 * * Date        : 2024/12/25
 * * Author      : W9085798
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.filemanager.common.utils.dp2px

class CropImageView(context: Context, attrs: AttributeSet?) : AppCompatImageView(context, attrs) {
    private val paint = Paint().apply {
        isAntiAlias = true
    }
    private val transparentSizeDp = 2
    private var lastDrawable: Drawable? = null
    private var circularBitmap: Bitmap? = null
    var enableCropping: Boolean = false

    override fun onDraw(canvas: Canvas) {
        if (enableCropping.not()) {
            super.onDraw(canvas)
            return
        }
        if (lastDrawable != drawable) {
            val mBitmap = drawableToBitmap(drawable)
            circularBitmap = getCircularBitmap(mBitmap)
            lastDrawable = drawable
        }
        if (null != circularBitmap) {
            canvas.drawBitmap(circularBitmap!!, 0f, 0f, paint)
        }
    }

    /**
     * 三方应用程序的图标都是有约2dp的透明边的，因此转换时需要去掉
     */
    private fun drawableToBitmap(drawable: Drawable): Bitmap {
        // 转换为Bitmap
        var bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, canvas.width, canvas.height)
        drawable.draw(canvas)
        // 去掉透明边
        val left = dp2px(context, transparentSizeDp)
        val top = dp2px(context, transparentSizeDp)
        val mWidth = width - left * 2
        val mHeight = height - top * 2
        bitmap = Bitmap.createBitmap(bitmap, left, top, mWidth, mHeight)
        // 缩放到控件大小
        bitmap = Bitmap.createScaledBitmap(bitmap, width, height, true)
        return bitmap
    }

    /**
     * 内切圆
     */
    private fun getCircularBitmap(bitmap: Bitmap): Bitmap {
        val size = Math.min(bitmap.width, bitmap.height)
        val output = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)

        val canvas = Canvas(output)
        val paint = Paint().apply {
            isAntiAlias = true
            shader = BitmapShader(bitmap, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        }

        val radius = size / 2f
        val centerX = bitmap.width / 2f
        val centerY = bitmap.height / 2f
        canvas.drawCircle(centerX, centerY, radius, paint)

        return output
    }
}