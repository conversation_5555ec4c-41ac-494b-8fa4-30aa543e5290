/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : UsbAttachedActivity.kt
 * * Description : Started by android.hardware.usb.action.USB_DEVICE_ATTACHED
 * * Version     : 1.0
 * * Date        : 2021/2/23
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.oplus.filemanager.main.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log

class UsbAttachedActivity: Activity() {
    companion object {
        private const val TAG = "UsbAttachedActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            //If MainActivity is started, UsbAttachedActivity doesn't need to start again
            //When SetupWizard is completed, the DEVICE_PROVISIONED is 1
            if (MainActivity.sIsMainActivityStarted.not() &&
                (Settings.Global.getInt(contentResolver, Settings.Global.DEVICE_PROVISIONED, 0) == 1)
            ) {
                val intent = if (KtUtils.isExportMainActivity()) {
                    Intent(this, ExportMainActivity::class.java).apply {
                        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }
                } else {
                    Intent(this, MainActivity::class.java).apply {
                        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }
                }
                startActivity(intent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "onCreate has error: ${e.message}")
        } finally {
            finish()
        }
    }
}