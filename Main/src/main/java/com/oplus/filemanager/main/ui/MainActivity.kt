/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main
 * * Version     : 1.0
 * * Date        : 2020/6/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.ui

import android.animation.ObjectAnimator
import android.app.ActionBar.LayoutParams
import android.app.Activity
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.os.Bundle
import android.os.UserManager
import android.view.DragEvent
import android.view.KeyEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.addListener
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.Constants.FROM_NEW_FILES_SEEDLING
import com.filemanager.common.constants.Constants.KEY_IS_FROM_LABEL_CARD
import com.filemanager.common.constants.Constants.KEY_IS_FROM_LABEL_FILE_LIST
import com.filemanager.common.constants.Constants.KEY_IS_FROM_NOTIFICATION
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.PersonalizedServiceController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NavigationBarHelper
import com.filemanager.common.utils.OnceAction
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PermissionUtils.hasStoragePermission
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.registerExportedReceiver
import com.filemanager.common.view.NavigationView
import com.oplus.encrypt.EncryptActivity
import com.oplus.encrypt.FileEncryptFactor
import com.oplus.filemanager.MainApplication
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.compat.brand.realme.RealmeInterstitialAdMgr
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.cardwidget.ICardWidgetApi
import com.oplus.filemanager.interfaze.categorydfm.ICategoryDFMApi
import com.oplus.filemanager.interfaze.categorydoc.ICategoryDocumentApi
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDeviceStatusListener
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.category.MainCategoryFragment
import com.oplus.filemanager.main.utils.MainAnimationUtil
import com.oplus.filemanager.main.view.BottomNavigationAnimController
import com.oplus.filemanager.main.view.ViewPager2Container
import com.oplus.filemanager.module.appinfo.AsyncApplicationInfo
import com.oplus.filemanager.parentchild.drag.ItemDragDropHelper
import com.oplus.filemanager.parentchild.ui.MainCombineFragment
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.ui.MainRecentFragment
import com.oplus.filemanager.recent.ui.MainRecentFragment.Companion.AUTO_LOAD_MIN_TIME_INTERVAL
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.utils.AdConstants.AD_TAG
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import com.oplusos.sauaar.client.SauSelfUpdateAgent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject
import java.util.Objects
import kotlin.math.abs


open class MainActivity : EncryptActivity(), NavigationInterfaceForMain, NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, InstalledPermissionCallback, IRefreshActivityDataForCreateDir,
    BaseVMActivity.PermissonCallBack, DragDropInterface, PerformClickDir, IDraggingActionOperate {

    companion object {
        private const val TAG = "MainActivityTAG"
        var sIsMainActivityStarted = false
        private var sIsSauUpdateChecked = false
        const val INDEX_RECENT = 0
        const val INDEX_CATEGORY = 1
        private const val KEY_POSITION = "SAVE_POSITION"
        private const val REFRESH_DELAY_TIME = 300L
        private const val MIN_REFRESH_TIME_INTERVAL = 60 * 1000L // one minute
        private const val FROM_KEY = "from"
        const val KEY = "isFromRecentCardWidget"
        // 第一次滑动到最近页延迟Load
        var isFirsToRecent = false
        private const val JUMP_RECYCLE_BIN: String = "JUMP_RECYCLE_BIN"
        private const val JUMP_REMOTE_MAC: String = "JUMP_REMOTE_MAC"
        private const val DURATION_500 = 500L
    }

    private var hasInitSomeConfig = false
    private var isFromLabelCardNeedConsume: Boolean = false
    var mainActivityViewModel: MainActivityViewModel? = null
    var systemBarInsetsBottom: Int = 0
    var showTaskBar: Boolean = false
    var isEdit: Boolean = false
    private var mContent: FrameLayout? = null
    private var dfmExit: Boolean = true
    private var isScrolling = false

    @VisibleForTesting
    var mRootLayout: ConstraintLayout? = null

    @VisibleForTesting
    var mViewPager: ViewPager2? = null

    private var mPosition: Int = INDEX_CATEGORY
    private val mTabPositionArray = intArrayOf(INDEX_RECENT, INDEX_CATEGORY)
    private val mTabIdArray = intArrayOf(
        com.filemanager.common.R.string.main_tab_recently,
        com.filemanager.common.R.string.file
    )
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mUserManager: UserManager? = null
    private var mNoRequest = true
    private var mIsFirstCheckPermission = true
    private var mRefreshTimeMap = mutableMapOf(0 to 0L, 1 to 0L, 2 to 0L)
    private val mNavigationControllerForRecent by lazy { NavigationController(lifecycle, id = R.id.navigation_tool_for_recent) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    private var mIsFromRecentCardWidget = false
    private var isFromNewFilesSeedling = false
    private var mRecentCardWidgetFile: RecentFileEntity? = null
    private var mIsFromLabelFileList = false
    private var categoryType = CategoryHelper.CATEGORY_RECENT
    private var isOpenParentDocumentActivity = false
    private val mPersonalizedServiceController: PersonalizedServiceController by lazy { PersonalizedServiceController() }
    private var navAnimController: BottomNavigationAnimController? = null
    private var navigationTab: NavigationView? = null
    private var navigationTabForRecent: NavigationView? = null
    private var navigationTabForLabel: NavigationView? = null
    private var viewPagerContainer: ViewPager2Container? = null
    private var rmInterstitialAdMgr: RealmeInterstitialAdMgr? = null
    private var mainRecentTab: View? = null
    private var mainCategoryTab: View? = null

    private var defaultNavigationTabTextColor: ColorStateList? = null
    private val disableNavigationTabTextColor: ColorStateList by lazy {
        val states = arrayOf(
            intArrayOf(android.R.attr.state_checked),
            intArrayOf()
        )
        val colors = intArrayOf(
            getColor(com.support.appcompat.R.color.coui_color_label_tertiary),
            getColor(com.support.appcompat.R.color.coui_color_label_tertiary)
        )
        ColorStateList(states, colors)
    }

    // 是否跳转到最近删除界面
    private var isJumpRecycleBin: Boolean = false
    // 跳转到dfm 界面
    private var isFromDevice: Boolean = false
    //从 远程控制电脑 跳过来的类型
    private var fromRemoteControlJumpType: Int = 0
    // 跳转到来源中的文件列表（远程电脑、互传）
    private var isJumpSuperApp: Boolean = false
    // 是否外部跳转到来源中的远程电脑文件
    var isOutJumpSuperRemotePC: Boolean = false
    private var jumpSuperCategoryType: Int = 0
    private var externalSuperPath: String = ""

    // 从标签卡跳转过来
    private var isFromLabelCard: Boolean = false

    private var itemDragDropHelper: ItemDragDropHelper? = null
    var isDragging = false

    private var remoteDeviceNeedExit: Boolean = true
    private var saveInstanceValue = -1

    private val mMainMountReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val action = intent.action
                Log.d(TAG, "onReceive action = $action")
                when (action) {
                    Intent.ACTION_MEDIA_SCANNER_FINISHED, Intent.ACTION_MEDIA_MOUNTED,
                    Intent.ACTION_MEDIA_REMOVED, Intent.ACTION_MEDIA_BAD_REMOVAL,
                    Intent.ACTION_MEDIA_UNMOUNTED -> {
                        //收到广播之后，开始出发otg的信息的获取
                        if (PrivacyPolicyController.hasAgreePrivacy()) {
                            getCategoryFragment()?.refreshDataWithMount()
                        }
                    }
                    Intent.ACTION_MEDIA_CHECKING -> {
                        getCategoryFragment()?.setOTGStateChecking()
                    }
                }
            }
        }
    }
    private val mOnLongClickListener: (view: View) -> Boolean = {
        if (FeatureCompat.sIsSupportEncryption) {
            if (mUserManager == null) {
                mUserManager = getSystemService(Context.USER_SERVICE) as UserManager
            }
            if (mUserManager!!.isSystemUser) {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.LONG_CLICK_ENCRYPT)
                FileEncryptFactor.startFileSafeActivity(this)
            }
        }

        true
    }
    private val mOnPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {

        override fun onPageSelected(position: Int) {
            Log.d(TAG, "onPageSelected $position isFirsToRecent $isFirsToRecent")
            mPosition = position
            resetLabelAddFileForRecent()
            updateSelectedTab()
            if (PrivacyPolicyController.hasAgreePrivacy()) {
                refreshCurrentFragment(isFirsToRecent, isFromPageChange = true)
            }
        }
    }
    private val navigationBarHelper: NavigationBarHelper by lazy { NavigationBarHelper() }
    private var mainCoverAnimate: ConstraintLayout? = null

    private var remoteDeviceListChangeListener = object : IRemoteDeviceStatusListener {
        override fun onDeviceAdd(list: List<RemoteDeviceInfo>) {
            Log.d(TAG, "onDeviceAdd size: ${list.size}")
            getCategoryFragment()?.refreshDataWithMount()
        }

        override fun onDeviceRemove(list: List<RemoteDeviceInfo>) {
            Log.d(TAG, "onDeviceRemove size: ${list.size}")
            getCategoryFragment()?.refreshDataWithMount()
        }

        override fun onDeviceUpdate(list: List<RemoteDeviceInfo>) {
            Log.d(TAG, "onDeviceUpdate size: ${list.size}")
            getCategoryFragment()?.refreshDataWithMount()
        }
    }

    private val cancelRemoteDeviceDownloadAction: OnceAction by lazy {
        OnceAction {
            val api = Injector.injectFactory<ICategoryRemoteDeviceApi>()
            api?.cancelAllDownload(this)
            Log.d(TAG, "cancelRemoteDeviceDownloadAction do")
        }
    }

    private fun resetLabelAddFileForRecent() {
        if (mIsFromLabelFileList) {
            getRecentFragment()?.resetIsChangeToSelectedMode()
        }
        mIsFromLabelFileList = false
    }

    override fun getLayoutResId(): Int {
        return R.layout.main_activity
    }

    override fun onResume() {
        super.onResume()
        mainActivityViewModel?.updateOWorkVisibilityParam()
        val isStartFileCloudDrive = IntentUtils.getBoolean(intent, CommonConstants.KEY_START_FILE_CLOUD_DRIVE, false)
        Log.d(TAG, "onResume -> isStartFileCloudDrive -> $isStartFileCloudDrive")
        if (isStartFileCloudDrive) {
            val bundle = Bundle()
            val itemType = IntentUtils.getInt(intent, Constants.KEY_FILE_DRIVE_TYPE, 0)
            val isAuthorizing = IntentUtils.getBoolean(intent, CommonConstants.KEY_IS_AUTHORIZING, false)
            Log.d(TAG, "onResume -> itemType = $itemType ; isAuthorizing = $isAuthorizing")
            bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, isAuthorizing)
            bundle.putInt(Constants.KEY_FILE_DRIVE_TYPE, itemType)
            setCurrentChildFragment(itemType, bundle)
            intent.putExtra(CommonConstants.KEY_START_FILE_CLOUD_DRIVE, false)
        }
        ShortCutUtils.checkShortCuts(this@MainActivity)
        checkDFMDevice()
    }

    private fun checkDFMDevice() {
        val categoryDFMApi = Injector.injectFactory<ICategoryDFMApi>()
        categoryDFMApi?.let {
            val dfsMountPath = it.getDFSMountPath()
            Log.i(TAG, "checkDFMDevice mountPath:$dfsMountPath")
            if (dfsMountPath.isNullOrEmpty()) {
                Log.w(TAG, "checkDFMDevice dfs device not found, reinit")
                it.init(this, true)
            } else {
                it.init(this, false)
            }
        }
    }

    /**
     * Check for personalized service
     * */
    private fun checkPersonalService() {
        mPersonalizedServiceController.checkShouldShowDialog(this, object :
            PersonalizedServiceController.OnPersonalServiceListener {
            override fun onAgreeResult(agree: Boolean) {
                mPersonalizedServiceController.agreedToPersonalService = agree
                val iAdvertApi = Injector.injectFactory<IAdvertApi>()
                iAdvertApi?.setAdSwitchStatus(agree)//同意时打开广告开关
            }
        })
    }
    private fun updateRedDot() {
        getCategoryFragment()?.updateRedDot()
        getRecentFragment()?.updateOWorkVisible()
    }

    override fun initNavigationBar() {
        StatusBarUtil.setNavigationBarColor(this)
    }

    override fun isAdaptNavigationBar() = true

    private fun initFragmentPosition(): Int {
        if (WindowUtils.isMiddleAndLargeScreen(this)) {
            Log.d(TAG, "initFragmentPosition [category] -> parent child screen")
            return INDEX_CATEGORY
        }
        /**
         * 在小屏时，由于第一个界面是最近页面，当外部需要跳转到文件界面时，需要切换position
         * 比如：最近删除，文档界面，dfm界面
         * 从标签卡点击添加标签按钮时需要切换到文件 position
         */
        if (mIsFromRecentCardWidget || isFromNewFilesSeedling || mIsFromLabelFileList) {
            return INDEX_RECENT
        }
        val isNeedShowLabelAddDialog = isFromLabelCard && hasStoragePermission()
        val needShowIndexCategory =
            isJumpRecycleBin || isOpenParentDocumentActivity || isFromDevice || fromRemoteControlJumpType > 0 || isNeedShowLabelAddDialog
        if (needShowIndexCategory) {
            Log.d(TAG, "initFragmentPosition [category] -> outside jump")
            return INDEX_CATEGORY
        }
        Log.d(TAG, "initFragmentPosition [recent] -> small screen")
        return INDEX_CATEGORY
    }


    override fun initView() {
        mPosition = if (saveInstanceValue != -1) {
            saveInstanceValue
        } else {
            initFragmentPosition()
        }
        saveInstanceValue = -1
        registerVmChangedReceiver(null)
        mContent = findViewById(android.R.id.content)
        mViewPager = findViewById(R.id.viewPager)
        mRootLayout = findViewById(R.id.root_layout)
        navigationTab = findViewById(R.id.navigation_tab)
        navigationTabForRecent = findViewById(R.id.navigation_tool_for_recent)
        navigationTabForLabel = findViewById(R.id.navigation_tool_for_label)
        viewPagerContainer = findViewById(R.id.view_pager_container)
        MainAnimationUtil.initialize()
        initNavTab()
        initViewPager()
        updateWindowInsets()
    }

    private fun refreshOpenParentDocumentActivityParam(intent: Intent) {
        // 判断是否需要打开文档页面父子级
        isOpenParentDocumentActivity = intent.action == "oplus.intent.action.filemanager.QUICK_DOCUMENT"
        Log.d(TAG, "isOpenParentDocumentActivity $isOpenParentDocumentActivity")
        if (isOpenParentDocumentActivity) {
            if (WindowUtils.isMiddleAndLargeScreen(this@MainActivity).not()) {
                Log.d(TAG, "isSmallScreenPhone")
                // 跳转到DocumentActivity
                val categoryDocumentApi = Injector.injectFactory<ICategoryDocumentApi>()
                categoryDocumentApi?.startCategoryDocumentActivity(
                    this,
                    getString(com.filemanager.common.R.string.string_documents)
                )
                finish()
            } else {
                Log.d(TAG, "isMiddleAndLargeScreen")
                categoryType = CategoryHelper.CATEGORY_DOC
                Log.d(TAG, "refreshOpenParentDocumentActivityParam > categoryType $categoryType")
            }
        }
    }

    private fun refreshOpenSuperApp(intent: Intent?) {
        isJumpSuperApp = Constants.ACTION_MAIN_CATEGORY_SUPER == intent?.action
        if (isJumpSuperApp) {
            jumpSuperCategoryType = IntentUtils.getInt(intent, KtConstants.P_CATEGORY_TYPE, 0)
            externalSuperPath = IntentUtils.getString(intent, KtConstants.FILE_PATH) ?: ""
            intent?.setAction(null)
        }
        val fragment = getCategoryFragment()
        Log.d(TAG, "refreshOpenSuperApp jump:$isJumpSuperApp category:$jumpSuperCategoryType fragment:$fragment")
        fragment?.let {
            it.needSetTypeSuper = isJumpSuperApp
            it.setSuperCategoryType = jumpSuperCategoryType
            it.externalSuperPath = externalSuperPath
        }
    }

    private fun refreshOutJumpSuperRemotePC(intent: Intent) {
        isOutJumpSuperRemotePC = intent.extras?.getBoolean(JUMP_REMOTE_MAC) ?: false
    }

    override fun initData() {
        PrivacyPolicyController.reset()
        super.checkStoragePermission()
        checkCanAutoCleanRecycleBin()
        registerMediaReceiver()
        if (FeatureCompat.sIsExpRom) {
            val iAdvertApi = Injector.injectFactory<IAdvertApi>() ?: return
            Log.d(TAG, "sIsExpRom needs to pop")
            checkPersonalService()
            //满足条件时初始化广告：运营商、欧盟、美国、广告开关、云控开关、新机保护、支持机型
            val isRealme = Utils.isRealmePhone()
            val isAdEnabled = iAdvertApi.isSwitchAndCloudConfigEnabled(false)
            val isAdInitialized = iAdvertApi.isAdInit(this)
            //如果是realme，无论闪屏页是否初始化sdk，这里必须再走一次初始化逻辑
            if ((isRealme || !isAdInitialized) && isAdEnabled) {
                Log.i(AD_TAG, "ad is supported, init")
                AdvertManager.initAdLoader(MainApplication.sAppContext)
            }
        }
    }
    private fun updateSelectedTab(): MenuItem? {
        val menuItem: MenuItem? = when (mPosition) {
            INDEX_CATEGORY -> navigationTab?.menu?.findItem(R.id.main_tab_category)
            INDEX_RECENT -> navigationTab?.menu?.findItem(R.id.main_tab_recently)
            else -> navigationTab?.menu?.findItem(R.id.main_tab_category)
        }
        menuItem?.let {
            it.isChecked = true
        }
        return menuItem
    }

    private fun initNavTab() {
        navigationTab?.apply {
            defaultNavigationTabTextColor = itemTextColor
            setOnItemSelectedListener(NavigationBarView.OnItemSelectedListener { menu ->
                Log.d(TAG, "onNavigationItemSelected " + menu.title)
                onNavTabSelected(menu)
                return@OnItemSelectedListener true
            })
            setOnItemReselectedListener { menu -> Log.d(TAG, "onNavigationItemReselected " + menu.title) }
            setNavTabLongClickListener()
            mainRecentTab?.let {
                it.tag = DropTag(CategoryHelper.CATEGORY_MAIN, DropTag.Type.RECENT_TAB)
            }
            mainCategoryTab?.let {
                it.tag = DropTag(CategoryHelper.CATEGORY_MAIN, DropTag.Type.MAIN_TAB)
            }
            navAnimController = BottomNavigationAnimController(this) { needShowTab() }
        }
        updateSelectedTab()
        setNavigationTabVisible()
    }

    fun updateWindowInsets() {
        updateDecorFitsSystemWindows()
        findViewById<View>(android.R.id.content).let {
            navigationBarHelper.addInsetsCallback(it, window) { _, insets, showNavigationBar ->
                Log.d(TAG, "onApplyInsets showNavigationBar: $showNavigationBar")
                systemBarInsetsBottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
                paddingBottom = if (StatusBarUtil.checkIsGestureNavMode()) {
                    0
                } else {
                    systemBarInsetsBottom
                }
                showTaskBar = showNavigationBar
                navigationTabForRecent?.updatePaddingBottom(systemBarInsetsBottom)
                navigationTabForLabel?.updatePaddingBottom(systemBarInsetsBottom)
                navigationTab?.let { navigationTab ->
                    Log.d(TAG, "onApplyInsets navigation_tab: ${navigationTab.measuredHeight}," +
                            "${navigationTab.bottomPadding}," +
                            "${navigationTab.marginBottom}")
                    //切换导航栏高度时更新navigation_tab的bottomMargin
                    if (navigationTab.visibility == View.VISIBLE && navigationTab.marginBottom < 0
                        && navigationTab.bottomPadding != systemBarInsetsBottom
                    ) {
                        navigationTab.updateLayoutParams<ConstraintLayout.LayoutParams> {
                            bottomMargin = -(navigationTab.measuredHeight - navigationTab.bottomPadding + systemBarInsetsBottom)
                        }
                    }
                    navigationTab.updatePaddingBottom(systemBarInsetsBottom)
                    updateChildFragmentPadding(getMainNavShow())
                }
                updateChildNavigationPadding(getMainNavShow())
            }
        }
    }

    private fun updateChildNavigationPadding(mainNavShow: Boolean) {
        Log.d(TAG, "updateChildNavigationPadding  isMainNavShow $mainNavShow")
        getCategoryFragment()?.updateNavPaddingBottom(mainNavShow)
        //处理问题6960537 设置bottomMargin为nav的高度
        updateRecentFragmentNav(mainNavShow)
    }

    private fun updateRecentFragmentNav(mainNavShow: Boolean) {
        if (mainNavShow) {
            navigationTabForRecent?.let { nav ->
                if (nav.marginBottom < 0) {
                    nav.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = -nav.measuredHeight
                    }
                }
            }
        }
    }

    private fun updateChildFragmentPadding(isMainNavShow: Boolean) {
        Log.d(TAG, "updateChildFragmentPadding  isMainNavShow $isMainNavShow")
        getCategoryFragment()?.updateChildFragmentPadding(isMainNavShow, showTaskBar)
    }

    private fun setNavTabLongClickListener() {
        mainRecentTab = findViewById<View>(R.id.main_tab_recently)?.apply {
            setOnLongClickListener(mOnLongClickListener)
        }
        mainCategoryTab = findViewById<View>(R.id.main_tab_category)?.apply {
            setOnLongClickListener(mOnLongClickListener)
        }
    }

    private fun registerMediaReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_MEDIA_CHECKING)
        filter.addAction(Intent.ACTION_MEDIA_MOUNTED)
        filter.addAction(Intent.ACTION_MEDIA_SCANNER_FINISHED)
        filter.addAction(Intent.ACTION_MEDIA_BAD_REMOVAL)
        filter.addAction(Intent.ACTION_MEDIA_REMOVED)
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED)
        filter.addDataScheme("file")
        try {
            appContext.registerExportedReceiver(mMainMountReceiver, filter)
        } catch (e: Exception) {
            Log.e(TAG, "registerMainMediaReceiver " + e.message)
        }
    }

    private fun checkCanAutoCleanRecycleBin() {
        val recycleBinAction = Injector.injectFactory<IRecycleBin>()
        ThreadManager.sThreadManager.execute(FileRunnable({
            if (hasStoragePermission() && PrivacyPolicyController.hasAgreePrivacy()) {
                Log.d(TAG, "doAutoClean")
                recycleBinAction?.doAutoClean(this@MainActivity)
            }
        }, TAG))
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(TAG, "onCreate")
        UIConfigMonitor.instance.callback = {
            doScreenSizeChangeAnimate()
        }
        isJumpRecycleBin = intent.extras?.getBoolean(JUMP_RECYCLE_BIN) ?: false
        mainActivityViewModel = ViewModelProvider(this)[MainActivityViewModel::class.java]
        mainActivityViewModel?.updateOWorkAlreadyShow()
        UIConfigMonitor.instance.onMultiWindowModeChanged(isInMultiWindowMode)
        mIsFromRecentCardWidget = intent.extras?.getBoolean(KEY, false) == true
        if (mIsFromRecentCardWidget) {
            mPosition = INDEX_RECENT
        }
        mIsFromLabelFileList = intent.extras?.getBoolean(KEY_IS_FROM_LABEL_FILE_LIST, false) == true
        isFromDevice = IntentUtils.getBoolean(intent, Constants.KEY_IS_FROM_DEVICE, false)
        fromRemoteControlJumpType = IntentUtils.getInt(intent, Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
        val isFromNotification = intent.extras?.getBoolean(KEY_IS_FROM_NOTIFICATION, false) == true
        isFromNewFilesSeedling = FROM_NEW_FILES_SEEDLING == IntentUtils.getString(intent, FROM_KEY)
        if (mIsFromLabelFileList || isFromNotification || isFromNewFilesSeedling) {
            mPosition = INDEX_RECENT
        }
        isFromLabelCard = getLabelCardValueByKey(KEY_IS_FROM_LABEL_CARD)
        if (isFromLabelCard) {
            isFromLabelCardNeedConsume = true
        }
        savedInstanceState?.getInt(KEY_POSITION, INDEX_CATEGORY)?.let {
            saveInstanceValue = it
            mPosition = it
        }
        parseRecentCardExtras(intent)
        super.onCreate(savedInstanceState)
        if (Utils.isRealmePhone() &&  AdvertManager.isAdEnabled()) {
            lifecycleScope.launch(Dispatchers.Default) {
                rmInterstitialAdMgr = RealmeInterstitialAdMgr()
                rmInterstitialAdMgr?.requestInterstitialAd(INDEX_RECENT, true)
            }
        }

        sIsMainActivityStarted = true
        refreshOpenParentDocumentActivityParam(intent)
        refreshOpenSuperApp(intent)
        refreshOutJumpSuperRemotePC(intent)
        val notificationId = intent.extras?.getInt(KtConstants.EXTRA_NOTIFICATION_ID) ?: -1
        if (notificationId > 0) {
            val nm = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            nm.cancel(notificationId)
        }
        putFromLabelCardValue(isFromLabelCard && getLabelCardValueByKey(Constants.FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG))
        addPrivacyPolicyListener()
        initDragHelper()
        statisticsEntryLaunch(isFromNewFilesSeedling)
    }

    private fun statisticsEntryLaunch(isFromNewFilesSeedling: Boolean) {
        if (isFromDevice) {
            StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_LINKER, "", Constants.PAGE_MAIN)
        } else if (fromRemoteControlJumpType > 0) {
            StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_REMOTE_CONTROL, "", Constants.PAGE_MAIN)
        } else if (isFromNewFilesSeedling) {
            StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_LAUNCHER, Constants.CARD_FILE_SEEDLING, Constants.PAGE_MAIN)
        } else if (isFromLabelCard) {
            StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_LAUNCHER, Constants.CARD_LABEL, Constants.PAGE_MAIN)
        }
        var launcherPkg = IntentUtils.getString(intent, Constants.KEY_LAUNCHER_PKG)
        if (launcherPkg.isNullOrEmpty()) {
            launcherPkg = Utils.getLauncherPkg(this)
        }
        if (!Objects.equals(this.packageName, launcherPkg)) {
            StatisticsUtils.statisticsEntryLaunch(this, launcherPkg, "", Constants.PAGE_MAIN)
        }
    }

    private fun initDragHelper() {
        itemDragDropHelper = ItemDragDropHelper(this)
        itemDragDropHelper?.getSelectedCategoryType = {
            val fragment = getCategoryFragment()
            fragment?.getSideSelectedCategoryType() ?: -1
        }
        itemDragDropHelper?.getEditState = {
            val fragment = getCategoryFragment()
            fragment?.getEditState() ?: false
        }
        itemDragDropHelper?.dragStartCallback = { dragEvent ->
            isDragging = true
            val fragment = getCategoryFragment()
            fragment?.handleDragStart()
        }
        itemDragDropHelper?.dragEndCallback = { dragEvent ->
            isDragging = false
            val fragment = getCategoryFragment()
            fragment?.handleDragEnd()
        }
    }

    private fun parseRecentCardExtras(intent: Intent) {
        val cardType = IntentUtils.getString(intent, Constants.KEY_CARD_TYPE)
        if (cardType.isNullOrEmpty().not()) {
            val isRecent = IntentUtils.getString(intent, KEY)
            if (isRecent?.toBooleanStrict() == true) {
                mIsFromRecentCardWidget = true
            }
        }
        val sourceAppName = IntentUtils.getString(intent, Constants.KEY_SOURCE_APP_NAME)
        Log.d(TAG, "parseExtras -> cardType = $cardType; isRecent = $mIsFromRecentCardWidget; sourceAppName = $sourceAppName")
        if (mIsFromRecentCardWidget) {
            mPosition = INDEX_RECENT
            isFromLabelCardNeedConsume = true
        }
        val openPermission = IntentUtils.getString(intent, Constants.KEY_OPEN_PERMISSION)
        Log.d(TAG, "parseExtras -> openPermission = $openPermission")
        if (openPermission.isNullOrEmpty().not()) {
            mPosition = INDEX_RECENT
            mIsFromRecentCardWidget = true
            isFromLabelCardNeedConsume = true
        }
    }

    private fun putFromLabelCardValue(isFromLabelCardAndShowRename: Boolean) {
        PreferencesUtils.put(key = Constants.FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG, value = isFromLabelCardAndShowRename)
    }
    /**
     * 从标签卡片传来的打开数据，使用Json解析
     * @return 得到是否是来自标签卡或需要弹出新建标签输入框
     */
    private fun getLabelCardValueByKey(key: String): Boolean {
        var value = false
        val paramText = intent.extras?.getString(KEY_IS_FROM_LABEL_CARD)
        paramText?.let {
            try {
                val json = JSONObject(paramText)
                value = json.getBoolean(key)
            } catch (e: JSONException) {
                Log.d(TAG, "getLabelCardValueByKey json $e")
            }
        }
        Log.d(TAG, "getLabelCardValueByKey key:$value")
        return value
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        //当viewpager处于拖拽状态时，不去创建当前fragment的optionmenu，不然会重复创建
        mViewPager?.let {
            if (it.scrollState != ViewPager2.SCROLL_STATE_DRAGGING) {
                getCurrentFragment()?.onCreateOptionsMenu(menu, menuInflater)
            }
        }
        return true
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        refreshOpenSuperApp(intent)
        refreshOutJumpSuperRemotePC(intent)
        val isFromDevice = IntentUtils.getBoolean(intent, Constants.KEY_IS_FROM_DEVICE, false)
        val isFromNewFilesSeedling = (FROM_NEW_FILES_SEEDLING == IntentUtils.getString(intent, FROM_KEY))
        val isFromRecentCard = IntentUtils.getString(intent, KEY)?.toBooleanStrict() ?: false
        Log.d(TAG, "onNewIntent -> isFromDevice $isFromDevice isFromNewFilesSeedling $isFromNewFilesSeedling isFromRecentCard $isFromRecentCard")
        if (isFromDevice) {
            setCurrentCompainFragment()
            // 重新加载数据
            if (PrivacyPolicyController.hasAgreePrivacy()) {
                getCategoryFragment()?.refreshDataWithMount()
            }
        }
        val openPermission = IntentUtils.getString(intent, Constants.KEY_OPEN_PERMISSION)
        Log.d(TAG, "onNewIntent -> openPermission = $openPermission")
        if (openPermission.isNullOrEmpty().not()) {
            PermissionUtils.requestStoragePermission(this@MainActivity, Intent.FLAG_ACTIVITY_NO_HISTORY)
            finish()
            return
        }
        if (isFromNewFilesSeedling || isFromRecentCard) {
            getRecentFragment()?.getViewModel()?.apply {
                this.mLastAutoLoadTime -= AUTO_LOAD_MIN_TIME_INTERVAL
            }
            if (UIConfigMonitor.isCurrentSmallScreen()) {
                setCurrentRecentFragment()
            } else {
                getCategoryFragment()?.switchRecentItem()
            }
        }
        setIntent(intent)
    }

    private fun initViewPager() {
        mViewPager?.apply {
            adapter = ViewPagerFragmentStateAdapter(this@MainActivity)
            registerOnPageChangeCallback(mOnPageChangeCallback)
            setCurrentItem(mPosition, false)
        }
    }

    fun notifySelectModel(selectModel: Boolean) {
        val showByScreen = UIConfigMonitor.isShowBottomTabByScreenSize(this)
        navigationTab?.apply {
            if (selectModel) {
                isEnabled = false
            } else {
                if (showByScreen) {
                    isEnabled = true
                } else {
                    isEnabled = false
                }
            }
        }
    }

    inner class ViewPagerFragmentStateAdapter(fragmentActivity: FragmentActivity) : FragmentStateAdapter(fragmentActivity) {
        override fun createFragment(position: Int): Fragment {
            Log.d(TAG, "createFragment position $position mPosition $mPosition currentItem ${mViewPager?.currentItem}")
            return when (position) {
                INDEX_CATEGORY -> {
                    val mainCombineFragment = MainCombineFragment()
                    if (isOpenParentDocumentActivity) {
                        mainCombineFragment.categoryType = categoryType
                        mainCombineFragment.needSetTypeDoc = true
                    } else if (isJumpRecycleBin) {
                        mainCombineFragment.needSetTypeRecBin = true
                    } else if (isJumpSuperApp) {
                        mainCombineFragment.needSetTypeSuper = true
                        mainCombineFragment.setSuperCategoryType = jumpSuperCategoryType
                        mainCombineFragment.externalSuperPath = externalSuperPath
                    } else if (isOutJumpSuperRemotePC) {
                        mainCombineFragment.isOutToRemoteMac = true
                    }
                    mainCombineFragment
                }
                INDEX_RECENT -> {
                    isFirsToRecent = true
                    MainRecentFragment()
                }
                else -> {
                    MainCombineFragment()//MainCategoryFragment()
                }
            }
        }

        override fun getItemCount(): Int {
            return mTabPositionArray.size
        }

        override fun getItemId(position: Int): Long {
            return mTabIdArray[position].toLong()
        }

        override fun containsItem(itemId: Long): Boolean {
            return mTabIdArray.any {
                itemId == it.toLong()
            }
        }
    }

    private fun onNavTabSelected(menuItem: MenuItem) {
        Log.w(TAG, "onNavTabSelected " + menuItem.title)
        when (menuItem.itemId) {
            R.id.main_tab_category -> {
                mPosition = INDEX_CATEGORY
                StatisticsUtils.onCommon(appContext, StatisticsUtils.CATEGORY_TAB_CLICKED)
            }
            R.id.main_tab_recently -> {
                rmInterstitialAdMgr?.requestInterstitialAd(INDEX_RECENT, false)
                rmInterstitialAdMgr?.showAd(this, INDEX_RECENT)
                mPosition = INDEX_RECENT
                StatisticsUtils.onCommon(appContext, StatisticsUtils.RECENT_TAB_CLICKED)
            }
        }
        mViewPager?.setCurrentItem(mPosition, false)
        updateSelectedTab()
        if (PrivacyPolicyController.hasAgreePrivacy()) {
            refreshCurrentFragment()
        }
    }

    override fun startObserve() {
        mRootLayout?.post {
            performClickFromRecentCardWidgetIfNeed()
        }
        mainActivityViewModel?.oWorkState?.observe(this) {
            Log.d(TAG, "oWorkState:$it")
            updateRedDot()
        }
    }

    override fun checkStoragePermission() {
        if (mIsFirstCheckPermission) {
            mIsFirstCheckPermission = false
        } else {
            super.checkStoragePermission()
        }
    }

    private fun addPrivacyPolicyListener() {
        PrivacyPolicyController.bindActivityPrivacyPolicyListener(object : PrivacyPolicyController.OnPrivacyPolicyListener {
            override fun onAgreeResult(agree: Boolean, noLongerRemind: Boolean) {
                Log.d(TAG, "onAgreeResult agree $agree")
                refreshCurrentFragment(!mIsFirstCheckPermission)
            }
        })
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        Log.d(TAG, "onPermissionSuccess")
        if (!hasInitSomeConfig) {
            //onPermissionSuccess会多次调用，而以下初始化操作只需执行一次，所以判断未初始化时才执行
            hasInitSomeConfig = true
            Log.d(TAG, "onPermissionSuccess initSomeConfig")
            if (PrivacyPolicyController.hasAgreeUseNet()) {
                val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
                cloudConfigApi?.initCloudConfig()
            }
            checkSauUpdate()
            checkDFMDevice()
            val categoryDFMApi = Injector.injectFactory<ICategoryDFMApi>()
            categoryDFMApi?.handleP2PConflict(this)

            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.init(this)
            remoteDeviceApi?.addDeviceStatusListener(remoteDeviceListChangeListener)
        }
        refreshCurrentFragment(!mIsFirstCheckPermission)
        getCategoryFragment()?.apply {
            if ((categoryType == CategoryHelper.CATEGORY_APK) || (categoryType == CategoryHelper.CATEGORY_FILE_BROWSER)) {
                permissionSuccess()
            }
        }
        val categoryType = getCategoryFragment()?.categoryType
        Log.d(TAG, "categoryType $categoryType")
        val isIndexFragment = if (getCategoryFragment() == null) {
            true
        } else {
            categoryType == CategoryHelper.CATEGORY_MAIN
        }

        if ((getCurrentPosition() == INDEX_CATEGORY && isIndexFragment)
            || (WindowUtils.isMiddleAndLargeScreen(this) && categoryType == CategoryHelper.CATEGORY_RECENT)
        ) {
            mainActivityViewModel?.let {
                if (it.firstGetAppListPermission) {
                    super.checkGetInstalledAppsPermission(false, true, object : CheckCallBack {
                        override fun handleAfterCheck() {
                            mainActivityViewModel?.firstGetAppListPermission = false
                        }
                    })
                }
            }
        }
    }

    override fun getRootView(): ViewGroup? {
        return getCategoryFragment()?.getRootViewNew()
    }

    private fun performClickFromRecentCardWidgetIfNeed() {
        if (mIsFromRecentCardWidget) {
            val recentCardWidgetAction = Injector.injectFactory<ICardWidgetApi>()
            recentCardWidgetAction?.getRecentCardClickedFile()?.let { baseFileBean ->
                if (baseFileBean is RecentFileEntity) {
                    mRecentCardWidgetFile = baseFileBean
                    mRecentCardWidgetFile?.let { getRecentFragment()?.getViewModel()?.onItemClick(this, it, null) }
                }
            }
        }
    }

    private fun checkSauUpdate() {
        if (sIsSauUpdateChecked.not()) {
            sIsSauUpdateChecked = true
            ThreadManager.sThreadManager.execute(FileRunnable({
                val sauSelfUpdateAgent = SauSelfUpdateAgent.SauSelfUpdateBuilder(
                    this@MainActivity,
                    com.support.dialog.R.style.COUIAlertDialog_Center
                )
                    .setTextColorId(
                        resources.getColor(
                            com.support.dialog.R.color.coui_alert_dialog_content_text_color,
                            null
                        )
                    )
                    .build()
                sauSelfUpdateAgent.sauCheckSelfUpdate()
            }, TAG))
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        refreshCurrentFragment()
    }

    /**
     * for fix bug 2890640
     * Adjust only for the viewpager change caused data refresh, reduce the logic of data reloading and reading,
     * set the sliding time to the current page and the data refresh time of the current page to be
     * less than 60s. Binder query of the media library. The data refresh logic triggered by non-viewpager
     * sliding remains as it is
     * **/
    private fun refreshCurrentFragment(delay: Boolean = false, isFromPageChange: Boolean = false) {
        Log.d(TAG, "refreshCurrentFragment: delay=$delay, isFromPageChange = $isFromPageChange")
        mViewPager?.apply {
            val runnable = getTag(id).let {
                if (it is RefreshRunnable) {
                    it
                } else {
                    RefreshRunnable { isFrom ->
                        getCurrentFragment()?.run {
                            Log.d(TAG, "refreshCurrentFragment isFromPageChange = $isFrom, mPosition = $mPosition")
                            onTabSelected()
                            if (isFrom) {
                                val lastTime = mRefreshTimeMap[mPosition]
                                if ((lastTime != null && abs(System.currentTimeMillis() - lastTime) > MIN_REFRESH_TIME_INTERVAL)) {
                                    onResumeLoadData()
                                    mRefreshTimeMap[mPosition] = System.currentTimeMillis()
                                }
                            } else {
                                onResumeLoadData()
                                mRefreshTimeMap[mPosition] = System.currentTimeMillis()
                            }
                        }
                    }.also { r ->
                        setTag(id, r)
                    }
                }
            }

            removeCallbacks(runnable)
            runnable.fromPageChange = isFromPageChange
            postDelayed(runnable, if (delay) REFRESH_DELAY_TIME else 0L)
            isFirsToRecent = false
        }
    }

    fun resetRefreshTime() {
        mRefreshTimeMap = mutableMapOf(0 to 0L, 1 to 0L, 2 to 0L)
    }

    class RefreshRunnable(private var function: (isFromPageChange: Boolean) -> Unit?) : Runnable {
        var fromPageChange = false

        override fun run() {
            function.invoke(fromPageChange)
        }
    }

    private fun getCurrentFragment(): BaseMainFragment? {
        return supportFragmentManager.findFragmentByTag("f${mTabIdArray[mPosition]}") as? BaseMainFragment
    }

    fun getCategoryFragment(): MainCombineFragment? {
        return supportFragmentManager.findFragmentByTag("f${mTabIdArray[INDEX_CATEGORY]}") as? MainCombineFragment
    }

    private fun getRecentFragment(): MainRecentFragment? {
        return supportFragmentManager.findFragmentByTag("f${mTabIdArray[INDEX_RECENT]}") as? MainRecentFragment
    }

    fun getCurrentPosition(): Int {
        return mPosition
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        Log.d(TAG, "onPermissionReject")
        if (mainActivityViewModel?.firstGetAppListPermission == true) {
            checkGetInstalledAppsPermission(true, true)
        }
    }

    private fun changeConstraintSetByNavigation(applyNavigationTab: Boolean, position: Int, updateChildPadding: Boolean = true) {
        mRootLayout?.run {
            val navigationTab = findViewById<View>(R.id.navigation_tab)
            val navigationToolForRecent = findViewById<View>(R.id.navigation_tool_for_recent)
            val navigationToolForChild= findViewById<View>(R.id.navigation_tool_for_child)
            val applyId = if (applyNavigationTab) {
                R.id.navigation_tab
            } else {
                when (position) {
                    INDEX_RECENT -> R.id.navigation_tool_for_recent
                    else -> R.id.navigation_tool_for_child
                }
            }
            val constraintSet = ConstraintSet()
            constraintSet.clone(this)
            constraintSet.clear(id)
            viewPagerContainer?.let {
                constraintSet.connect(it.id, ConstraintSet.BOTTOM, applyId, ConstraintSet.TOP)
                constraintSet.connect(it.id, ConstraintSet.TOP, id, ConstraintSet.TOP)
            }
            navigationTab?.let {
                constraintSet.connect(it.id, ConstraintSet.BOTTOM, id, ConstraintSet.BOTTOM)
            }
            navigationToolForRecent?.let {
                constraintSet.connect(it.id, ConstraintSet.BOTTOM, id, ConstraintSet.BOTTOM)
            }
            navigationToolForChild?.let {
                constraintSet.connect(it.id, ConstraintSet.BOTTOM, id, ConstraintSet.BOTTOM)
            }
            constraintSet.applyTo(this)
            if (updateChildPadding) {
                navigationTab?.let {
                    //showNavigation()方法执行时会走到这里来让navigationTab隐藏，此处更新ChildFragmentPadding
                    if (it.marginBottom < 0) {
                        Log.d(TAG, "updateChildFragmentPadding marginBottom ${it.marginBottom}")
                        updateChildFragmentPadding(false)
                    }
                }
            }
        }
    }

    override fun showNavigation() {
        mViewPager?.isUserInputEnabled = false
        mRootLayout?.run {
            viewPagerContainer?.let {
                it.isUserInputEnabled = false
            }
        }
        if (mPosition == INDEX_RECENT) {
            navAnimController?.setAnimEndConsumer {
                changeConstraintSetByNavigation(it, INDEX_RECENT)
            }
            navAnimController?.setSubNavigationTool(mNavigationControllerForRecent)
            navAnimController?.showToolNav(this, 0)
        } else if (INDEX_CATEGORY == mPosition) {
            navAnimController?.setAnimEndConsumer {
                changeConstraintSetByNavigation(false, INDEX_CATEGORY)
            }
            getCategoryFragment()?.showNavigation(navAnimController)
        }
    }

    /**
     * 显示隐藏底部导航栏
     * 当隐藏导航栏时，禁止左右滑动
     */
    private fun showNavigationTab(show: Boolean) {
        Log.e(TAG, "showNavigationTab $show")
        navigationTab?.isVisible = show
        if (show) {
            navAnimController?.showMainTabNav()
        }
        enableViewPagerScroll(show)
        updateChildFragmentPadding(show)
        updateChildNavigationPadding(getMainNavShow())
    }

    fun enableViewPagerScroll(enabled: Boolean) {
        mViewPager?.isUserInputEnabled = enabled
        viewPagerContainer?.isUserInputEnabled = enabled
    }

    fun enableNavigationTab(enabled: Boolean) {
        navigationTab?.isEnabled = enabled
        navigationTab?.menu?.findItem(R.id.main_tab_category)?.isEnabled = enabled
        navigationTab?.menu?.findItem(R.id.main_tab_recently)?.isEnabled = enabled
        if (enabled) {
            navigationTab?.itemTextColor = defaultNavigationTabTextColor
        } else {
            navigationTab?.itemTextColor = disableNavigationTabTextColor
        }
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        setNavigateItemAble(isEnable, mHasDrm, mHasSelectedMultiLabels = false, mHasSelectedLabelsAllPin = false, mHasSelectedFileEmpty = false)
    }

    override fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean,
        mHasSelectedLabelsAllPin: Boolean,
        mHasSelectedFileEmpty: Boolean
    ) {
        when (mPosition) {
            INDEX_RECENT -> mNavigationControllerForRecent.setNavigateItemAble(isEnable, mHasDrm)

            INDEX_CATEGORY -> {
                getCategoryFragment()?.setNavigateItemAble(
                    isEnable,
                    mHasDrm,
                    mHasSelectedMultiLabels,
                    mHasSelectedLabelsAllPin,
                    mHasSelectedFileEmpty
                )
            }
        }
    }

    override fun showNavigation(type: NavigationType, showTab: Boolean) {
        if (INDEX_CATEGORY == mPosition) {
            val categoryFragment = getCategoryFragment() ?: return
            if (categoryFragment.isScreenHalf()) {
                enableViewPagerScroll(showTab)
            }
            navAnimController?.setAnimEndConsumer {
                changeConstraintSetByNavigation(true, mPosition)
            }
            categoryFragment.showNavigation(type, navAnimController, !showTab)
        }
    }

    override fun hideNavigation() {
        hideNavigation(null)
    }

    override fun hideNavigation(after: Runnable?) {
        val isSmallScreen = WindowUtils.isSmallScreen(this)
        mViewPager?.isUserInputEnabled = isSmallScreen
        mRootLayout?.run {
            viewPagerContainer?.let {
                it.isUserInputEnabled = isSmallScreen
            }
        }
        var hasSetTab = true
        when (mPosition) {
            INDEX_RECENT -> {
                val showByScreen = UIConfigMonitor.isShowBottomTabByScreenSize(this)
                if (showByScreen) {
                    hasSetTab = false
                    navAnimController?.setAnimEndConsumer {
                        enableViewPagerScroll(true)
                        changeConstraintSetByNavigation(applyNavigationTab = true, mPosition)
                        updateChildFragmentPadding(true)
                        after?.run()
                        //小屏幕退出编辑模式后工具栏会收在下面，切换到大屏后会出现在平板虚拟按键区域
                        navAnimController?.mSubNavController?.setVisibility(View.INVISIBLE)
                    }
                    navAnimController?.setSubNavigationTool(mNavigationControllerForRecent)
                    navAnimController?.hideToolNav(this)
                } else {
                    showNavigationTab(false)
                }
            }
            INDEX_CATEGORY -> {
                if (getCategoryFragment()?.isScreenHalf() == false && (getCategoryFragment()?.secondFragment is MainCategoryFragment).not()) {
                    enableViewPagerScroll(false)
                }
                val showByScreen = UIConfigMonitor.isShowBottomTabByScreenSize(this)
                if (showByScreen) {
                    if (getCategoryFragment()?.isScreenHalf() == true) {
                        hasSetTab = false
                        navAnimController?.setAnimEndConsumer {
                            enableViewPagerScroll(true)
                            changeConstraintSetByNavigation(applyNavigationTab = true, mPosition)
                            updateChildFragmentPadding(true)
                            updateChildNavigationPadding(true)
                            after?.run()
                        }
                    }
                } else {
                    showNavigationTab(false)
                }
                getCategoryFragment()?.hideNavigation(navAnimController)
            }
        }
        Log.d(TAG, "hideNavigation hasSetTab:$hasSetTab")
        if (hasSetTab) {
            changeConstraintSetByNavigation(applyNavigationTab = true, mPosition)
            after?.run()
        }
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.ACTIVITY_QUICK_CLICK)) {
            return false
        }
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNavigationItemSelected(p0: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(p0) ?: false
    }

    override fun onBackPressed() {
        if ((getCurrentFragment() as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
            //Task cannot be finished if it is the root task in android S. It may be deleted in a stable version
            val categoryDFMApi = Injector.injectFactory<ICategoryDFMApi>()
            categoryDFMApi?.exit()
            dfmExit = false
            cancelRemoteDownload()
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.removeDeviceStatusListener(remoteDeviceListChangeListener)
            remoteDeviceApi?.destroy()
            remoteDeviceNeedExit = false
            finish()
        }
    }

    override fun isNotMainActivity(): Boolean {
        return false
    }

    fun actionCheckPermission(checkStorage: Boolean = true): Boolean {
        return if (hasStoragePermission()) {
            true
        } else {
            if (checkStorage) {
                checkStoragePermission()
            }
            false
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(KEY_POSITION, mPosition)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        FileImageVHUtils.changedListMargin(
            this,
            (this as? BaseVMActivity)?.sideNavigationContainer?.drawerViewWidth ?: 0,
            (this as? BaseVMActivity)?.sideNavigationStatus?.value ?: KtConstants.SIDE_NAVIGATION_OPEN
        )
        setMainPageOnUIConfigChange(configList)
        setCategoryFragmentOnOrientationChange(configList)
        setNavTabLongClickListener()
        getCategoryFragment()?.onUIConfigChanged(configList)
        getRecentFragment()?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
        setNavigationTabVisible()
        updateDecorFitsSystemWindows()
    }

    /**
     * 小屏转大屏时，需要设置viewpager到INDEX_CATEGORY页面，显示大屏的页面
     */
    private fun setMainPageOnUIConfigChange(configList: MutableCollection<IUIConfig>) {
        val screenSizeConfig = configList.find { it is ScreenSizeConfig }
        if (screenSizeConfig != null && WindowUtils.isMiddleAndLargeScreen(this)) {
            mViewPager?.setCurrentItem(INDEX_CATEGORY, false)
            mPosition = INDEX_CATEGORY
        }
    }

    /**
     * 从设置->连接与共享->远程控制电脑->可连接mac设备->远程文件管理 跳转过来时，viewPager 设置的 currentItem 是 INDEX_CATEGORY，
     * 如果设备屏幕发生旋转，viewPager的itemView切换低概率会被中断，无法切换到 INDEX_CATEGORY 对应的 Fragment，默认显示 position 为 0 的 最近页面（MainRecentFragment）
     * 需要对 viewPager 重新设置 adapter 和 currentItem
     */
    private fun setCategoryFragmentOnOrientationChange(configList: MutableCollection<IUIConfig>) {
        val screenOrientationConfig = configList.find { it is ScreenOrientationConfig }
        if (screenOrientationConfig == null) {
            return
        }
        val remoteControlJumpType = IntentUtils.getInt(intent, Constants.FROM_REMOTE_CONTROL_JUMP_TYPE, 0)
        val categoryFragment = getCategoryFragment()
        Log.d(TAG, "setCategoryFragmentOnOrientationChange remoteControlJumpType $remoteControlJumpType categoryFragment $categoryFragment")
        if (remoteControlJumpType == Constants.REMOTE_CONTROL_JUMP_TYPE_MAC_FILE && categoryFragment == null) {
            mPosition = INDEX_CATEGORY
            mViewPager?.apply {
                adapter = ViewPagerFragmentStateAdapter(this@MainActivity)
                setCurrentItem(mPosition, false)
                Log.d(TAG, "setCategoryFragmentOnOrientationChange set viewPager adapter, currentItem")
            }
        }
    }

    private fun updateDecorFitsSystemWindows() {
        //小屏、分屏、虚拟按键导航且导航栏和界面在一边的时候，设置为非沉浸式
        this.let {
            val isMiddleAndLargeScreen = WindowUtils.isMiddleAndLargeScreen(it)
            val checkIsGestureNavMode = StatusBarUtil.checkIsGestureNavMode(it)
            val isDisplayInPrimaryScreen = COUIPanelMultiWindowUtils.isDisplayInPrimaryScreen(it)
            val deviceRotation = UIConfigMonitor.instance.getDeviceRotation(it)
            val isNavigationBarTogether =
                (deviceRotation == UIConfigMonitor.ROTATION_ONE && isDisplayInPrimaryScreen.not()) ||
                        (deviceRotation == UIConfigMonitor.ROTATION_THREE && isDisplayInPrimaryScreen)
            Log.d(TAG, "updateDecorFitsSystemWindows " +
                    "isInMultiWindowMode ${it.isInMultiWindowMode}, " +
                    "isMiddleAndLargeScreen $isMiddleAndLargeScreen, " +
                    "checkIsGestureNavMode $checkIsGestureNavMode, " +
                    "isDisplayInPrimaryScreen $isDisplayInPrimaryScreen, " +
                    "isNavigationBarTogether $isNavigationBarTogether"
            )
            if (it.isInMultiWindowMode && isMiddleAndLargeScreen.not() && checkIsGestureNavMode.not() && isNavigationBarTogether) {
                WindowCompat.setDecorFitsSystemWindows(window, true)
                return
            }
        }
        WindowCompat.setDecorFitsSystemWindows(window, false)
    }

    override fun onPause() {
        super.onPause()
        resetRefreshTime()
    }

    override fun onDestroy() {
        if (dfmExit) {
            val categoryDFMApi = Injector.injectFactory<ICategoryDFMApi>()
            categoryDFMApi?.exit()
        }
        cancelRemoteDownload()
        rmInterstitialAdMgr?.destroy()
        navAnimController?.release()
        sIsMainActivityStarted = false
        mViewPager?.apply {
            getTag(id)?.also {
                if (it is Runnable) {
                    removeCallbacks(it)
                }
            }
            adapter = null
        }
        try {
            appContext.unregisterReceiver(mMainMountReceiver)
            AsyncApplicationInfo.clearTitleAndDetailCache()
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        ThreadManager.sThreadManager.cancelAllThread()
        unregisterVmChangedReceiver()
        RecentDataHelper.instance.clearRecentData()
        mViewPager?.unregisterOnPageChangeCallback(mOnPageChangeCallback)
        mSelectPathController.onDestroy()
        mViewPager = null
        mContent?.let {
            StatusBarUtil.adaptTaskbarByDestroy(it)
        }
        PrivacyPolicyController.activityPrivacyPolicyListener = null
        UIConfigMonitor.instance.callback = {}
        if (remoteDeviceNeedExit) {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.removeDeviceStatusListener(remoteDeviceListChangeListener)
            remoteDeviceApi?.destroy()
        }
        super.onDestroy()
    }

    private fun cancelRemoteDownload() {
        Log.d(TAG, "cancelRemoteDownload")
        cancelRemoteDeviceDownloadAction.run()
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun showSelectPathFragmentDialog(code: Int, path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        getRecentFragment()?.updateLabels()
        getRecentFragment()?.exitSelectionMode()
        getCategoryFragment()?.updateLabels()
        getCategoryFragment()?.fragmentExitSelectionMode()
        resetRefreshTime()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        when (mPosition) {
            INDEX_RECENT -> getRecentFragment()?.fromSelectPathResult(code, paths?.getOrNull(0))
            INDEX_CATEGORY -> getCategoryFragment()?.fromSelectPathResult(code, paths)
        }
    }

    override fun onRefreshData() {
        getRecentFragment()?.onResumeLoadData()
        getCategoryFragment()?.refreshParentData()
    }

    override fun backtoTop() {
        super.backtoTop()
        getRecentFragment()?.backToTop()
        getCategoryFragment()?.backToTop()
    }

    fun setViewPagerScrollEnabled(enabled: Boolean) {
        //当前是大屏，此时页面不可滑动
        if (enabled && WindowUtils.isMiddleAndLargeScreen(this)) {
            Log.d(TAG, "setViewPagerScrollEnabled -> large screen, do not scroll! return")
            return
        }
        if (enabled && (mPosition == INDEX_CATEGORY) && (navigationTab?.let { navAnimController?.isHide(it) } == true)) {
            //当前是INDEX_CATEGORY且属于编辑状态，此时页面不可以滑动
            return
        }
        mViewPager?.isUserInputEnabled = enabled
        mRootLayout?.run {
            viewPagerContainer?.let {
                it.isUserInputEnabled = enabled
                Log.i(TAG, "setViewPagerScrollEnabled:$enabled ")
            }
        }
    }

    fun enterNextFragment(nextCategory: Int, bundle: Bundle) {
        if (mPosition == INDEX_CATEGORY) {
            getCategoryFragment()?.enterNextFragment(nextCategory, bundle)
        }
    }

    fun backPreviousFragment(previousCategory: Int) {
        if (mPosition == INDEX_CATEGORY) {
            getCategoryFragment()?.backPreviousFragment(previousCategory)
        }
    }

    fun setCurrentChildFragment(categoryType: Int, bundle: Bundle?): Boolean {
        setCurrentCompainFragment()
        val fragment = getCategoryFragment() ?: return false
        if (UIConfigMonitor.isCurrentSmallScreen()) {
            Log.d(TAG,"setCurrentChildFragment 当前是小屏")
            fragment.setContainerVisible(showMaster = false, showDetail = true)
        } else {
            Log.d(TAG, "setCurrentChildFragment 当前是大屏")
            fragment.setContainerVisible(showMaster = true, showDetail = true)
        }
        //fragment.selectCategoryTypeItem(categoryType, bundle)
        fragment.setCurrentChildFragment(categoryType, bundle)
        return true
    }

    private fun setCurrentCompainFragment() {
        if (mPosition != INDEX_CATEGORY) {
            getCurrentFragment()?.pressBack()
            mPosition = INDEX_CATEGORY
            mViewPager?.setCurrentItem(mPosition, false)
            updateSelectedTab()
            if (PrivacyPolicyController.hasAgreePrivacy()) {
                refreshCurrentFragment()
            }
        }
    }

    override fun getWindowWidth(category: Int): Int {
        return getCategoryFragment()?.getWindowWidth(this, category) ?: 0
    }

    override fun getActivityType(): Int {
        if (FeatureCompat.isSmallScreenPhone) {
            return InstalledPermissionCallback.DEFAULT_TYPE
        }
        return when (getCategoryFragment()?.categoryType) {
            CategoryHelper.CATEGORY_APK -> InstalledPermissionCallback.APK_ACTIVITY
            CategoryHelper.CATEGORY_FILE_BROWSER -> InstalledPermissionCallback.FILE_BROWSER_ACTIVITY
            else -> InstalledPermissionCallback.DEFAULT_TYPE
        }
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.keyCode == KeyEvent.KEYCODE_MENU) {
            return false
        }
        return super.dispatchKeyEvent(event)
    }

    fun getNavigationView(): View? {
        return findViewById(R.id.navigation_tool_for_child)
    }

    fun getNavigationTabView(): View {
        return findViewById(R.id.navigation_tab)
    }

    fun isParentChild(): Boolean {
        return !FeatureCompat.isSmallScreenPhone
    }

    fun isStorageFragment(): Boolean {
        return if (mPosition == INDEX_CATEGORY) {
            getCategoryFragment()?.isStorageFragment() ?: false
        } else {
            false
        }
    }

    fun getPreviewListFragment(): Fragment? {
        if (mPosition == INDEX_CATEGORY) {
            val secondFragment = getCategoryFragment()?.secondFragment
            if (secondFragment is PreviewCombineFragment) {
                return secondFragment.getPreviewFragment()?.getFragmentInstance()
            }
        }
        return null
    }

    /**
     * 判断当前是否是categoryFragment
     */
    fun isMainCategoryFragment(): Boolean {
        val categoryFragment = getCategoryFragment() ?: return false
        return categoryFragment.isShowMainCategoryFragment()
    }

    fun isRecentFragment(): Boolean {
        return mPosition == INDEX_RECENT
    }

    fun checkPermission() {
        actionCheckPermission()
    }

    fun setNavigationTabVisible() {
        val isShow = needShowTab()
        showNavigationTab(isShow)
        changeConstraintSetByNavigation(isShow, mPosition)
    }

    private fun needShowTab(): Boolean {
        var isShow = true
        when (mPosition) {
            INDEX_CATEGORY -> isShow = getCategoryFragment()?.isNavigationTabShow() ?: true
            INDEX_RECENT -> isShow = getRecentFragment()?.isNavigationTabShow() ?: true
        }

        //中大屏下不显示主菜单栏
        if (WindowUtils.isMiddleAndLargeScreen(appContext)) {
            Log.w(TAG, "needShowTab -> is large screen ,need not show tab!")
            isShow = false
        }
        return isShow
    }

    /** 检测是否从标签文件列表打开MainActivity，用于RecentFragment判断是否弹起提示 */
    fun checkIsFromLabelFileList() = mIsFromLabelFileList

    fun getCategoryType(): Int {
        return getCategoryFragment()?.categoryType ?: -1
    }

    fun getCurrentPath(): String {
        if (mPosition == INDEX_CATEGORY) {
            return getCategoryFragment()?.getCurrentPath() ?: ""
        }
        return ""
    }

    private fun getMainNavShow(): Boolean {
        val mainNavShow = navigationTab?.visibility == View.VISIBLE && navigationTab?.marginBottom == 0
        Log.d(TAG, "mainNavShow : $mainNavShow")
        return mainNavShow
    }

    fun clickOworkMenu() {
        StatisticsUtils.onCommon(appContext, StatisticsUtils.OWORK_ENTRANCE)
        StatisticsUtils.statisticsPageExposure(this, "", Constants.PAGE_OWORK, Constants.PAGE_MAIN)
        KtAppUtils.startOWork(this, KtAppUtils.ENTRANCE_FILE_MANAGER_MAIN)
        PreferencesUtils.put(key = CommonConstants.OWORK_ALREDY_SHOW, value = true)
        mainActivityViewModel?.updateOWorkAlreadyShow()
        updateRedDot()
    }

    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission  getCurrentPosition ${getCurrentPosition()} ${getCategoryFragment()?.categoryType}")
        mainActivityViewModel?.apply {
            //无所有文件访问权限时，首次进入需弹窗，打开应用的场景下只允许弹一次所有文件访问权限弹窗
            if (getCurrentPosition() == INDEX_CATEGORY && this.firstGetAllFilePermission) {
                val hasShow = PreferencesUtils.getBoolean(
                    PreferencesUtils.SHARED_PREFS_NAME, CommonConstants.MAIN_SHOW_MANAGER_FILES_PERMISSION_DIALOG, false
                )
                if (!hasShow) {
                    if (getCategoryFragment() == null) {
                        showSettingGuildDialog()
                        firstGetAllFilePermission = false
                        PreferencesUtils.put(PreferencesUtils.SHARED_PREFS_NAME, CommonConstants.MAIN_SHOW_MANAGER_FILES_PERMISSION_DIALOG, true)
                    } else if (WindowUtils.isMiddleAndLargeScreen(context = this@MainActivity)
                        || getCategoryFragment()?.categoryType == CategoryHelper.CATEGORY_MAIN
                    ) {
                        showSettingGuildDialog()
                        firstGetAllFilePermission = false
                        PreferencesUtils.put(PreferencesUtils.SHARED_PREFS_NAME, CommonConstants.MAIN_SHOW_MANAGER_FILES_PERMISSION_DIALOG, true)
                    }
                }
            }
        }
        if (isFromLabelCardNeedConsume) {
            //标签卡无权限时 跳转过来直接进入权限设置页
            val flags = if (mIsFromRecentCardWidget) Intent.FLAG_ACTIVITY_NO_HISTORY else null
            PermissionUtils.requestStoragePermission(this@MainActivity, flags)
            isFromLabelCardNeedConsume = false
            finish()
        }
    }

    override fun checkGetInstalledAppsPermission(forceShow: Boolean, isMainShow: Boolean, checkCallBack: CheckCallBack?) {
        Log.d(TAG, "checkGetInstalledAppsPermission")
        if (getCurrentPosition() != INDEX_CATEGORY) {
            return
        } else {
            val categoryType = getCategoryFragment()?.categoryType
            Log.d(TAG, "checkGetInstalledAppsPermission categoryType $categoryType isMainShow $isMainShow")
            if (categoryType == CategoryHelper.CATEGORY_APK || categoryType == CategoryHelper.CATEGORY_FILE_BROWSER) {
                super.checkGetInstalledAppsPermission(forceShow, false, object : CheckCallBack {
                    override fun handleAfterCheck() {
                        mainActivityViewModel?.firstGetAppListPermission = false
                    }
                })
                return
            }
            mainActivityViewModel?.let {
                if ((categoryType == CategoryHelper.CATEGORY_MAIN || categoryType == null) && it.firstGetAppListPermission) {
                    super.checkGetInstalledAppsPermission(forceShow, true, object : CheckCallBack {
                        override fun handleAfterCheck() {
                            mainActivityViewModel?.firstGetAppListPermission = false
                        }
                    })
                }
            }
        }
    }

    fun hideRecentNavigationView() {
        mNavigationControllerForRecent?.hideNavigation(this)
    }

    /**
     * 设置当前页面为最近页面，大屏显示最近页面，切换到小屏，需要显示最近页面的tab
     */
    fun setCurrentRecentFragment() {
        mViewPager?.setCurrentItem(INDEX_RECENT, false)
        mPosition = INDEX_RECENT
    }

    fun cancelRecentFragmentLoading() {
        getRecentFragment()?.cancelLoad()
    }

    fun getLabelIdByCategoryType(categoryType: Int): Long? {
        return getCategoryFragment()?.getLabelIdByCategoryType(categoryType)
    }

    fun getShowingLabelId(): Long? {
        return getCategoryFragment()?.getShowingLabelId()
    }

    fun getSuperAppFilePathByCategoryType(categoryType: Int): Array<String>? {
        return getCategoryFragment()?.getSuperAppFilePathsByCategoryType(categoryType)
    }

    fun getShowingSuperAppFilePath(): Array<String>? {
        return getCategoryFragment()?.getShowingSuperAppFilePaths()
    }

    fun getShortcutFilePathByCategoryType(categoryType: Int): String? {
        return getCategoryFragment()?.getShortcutFilePathByCategoryType(categoryType)
    }

    fun getShowingShortCutFilePath(): String? {
        return getCategoryFragment()?.getShowingShortCutFilePath()
    }

    fun getOtgPath(): String? {
        return getCategoryFragment()?.getOTGPath()
    }

    fun getSdCardPath(): String? {
        return getCategoryFragment()?.getSdCardPath()
    }

    /**
     * 获取当前显示的最近页面，包括小屏和大屏
     */
    fun getCurrentRecentFragment(): MainRecentFragment? {
        val currentFragment = getCurrentFragment()
        Log.d(TAG, "getCurrentRecentFragment currentFragment $currentFragment")
        if (currentFragment is MainRecentFragment) {
            return currentFragment
        }
        if (currentFragment is MainCombineFragment) {
            return currentFragment.getCurrentRecentFragment()
        }
        return null
    }

    fun handleDragEvent(rootView: View?, dragEvent: DragEvent?, activity: Activity): Boolean? {
        val fragment = getCurrentFragment()
        var detectView: View? = null
        if (navigationTab != null && navigationTab!!.isVisible && WindowUtils.isSmallScreen(this@MainActivity) && ViewUtils
                .isPointInsideView(
                    navigationTab!!,
                    dragEvent?.x ?: 0f, dragEvent?.y ?: 0f
                )
        ) {
            detectView = navigationTab
            (fragment as? MainRecentFragment)?.scrollHelper?.resetDragStatus()
            (fragment as? MainCombineFragment)?.resetDragStatus()
            isScrolling = false
        } else if (fragment is MainRecentFragment) {
            getCategoryFragment()?.resetDragStatus()
            detectView = fragment.view
            isScrolling = dragEvent?.let { fragment.handleDragScroll(it) } == true
        } else if (fragment is MainCombineFragment) {
            getRecentFragment()?.resetScrollStatus()
            detectView = fragment.getDropDetectionView(rootView, dragEvent)
            // 处理侧导滑动
            isScrolling = fragment.handleDragEvent(detectView, dragEvent) == true
        }
        return itemDragDropHelper?.handleDragEvent(rootView, detectView, dragEvent, activity, isScrolling)
    }

    fun exitEditMode() {
        val fragment = getCurrentFragment()

        if (fragment is MainRecentFragment) {
            fragment.exitSelectionMode()
        }

        if (fragment is MainCombineFragment) {
            fragment.exitEditMode()
        }
    }

    private val screenSizeChangeAnimate: ObjectAnimator by lazy {
        mainCoverAnimate = findViewById(R.id.main_cover_anima_view)
        ObjectAnimator.ofFloat(mainCoverAnimate, "alpha", 1f, 0f).apply {
            duration = DURATION_500
            interpolator = COUIEaseInterpolator()
            addListener({   // 动画结束后
                mainCoverAnimate?.layoutParams?.apply {
                    width = 0
                    height = 0
                }
                mainCoverAnimate?.visibility = View.INVISIBLE
            }, {             // 动画开始前
                mainCoverAnimate?.layoutParams?.apply {
                    width = LayoutParams.MATCH_PARENT
                    height = LayoutParams.MATCH_PARENT
                }
                mainCoverAnimate?.visibility = View.VISIBLE
            })
        }
    }

    private fun doScreenSizeChangeAnimate() {
        Log.d(TAG, "doScreenSizeChangeAnimate")
        screenSizeChangeAnimate.apply {
            if (this.isRunning) end()
            start()
        }
    }

    override fun onRefreshDataForDir(path: String) {
        val categoryFragment = getCategoryFragment()
        categoryFragment?.apply {
            refreshDataForDir(path, categoryType)
        }
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean) {
        refreshSideGuide()
        val categoryFragment = getCategoryFragment()
        categoryFragment?.apply {
            renameToShortCutFolder(newName, file)
        }
    }

    override fun renameToLabel(newName: String, labelId: Long) {
        refreshSideGuide()
        val categoryFragment = getCategoryFragment()
        categoryFragment?.apply {
            renameToLabel(newName, labelId)
        }
    }

    private fun refreshSideGuide() {
        val currentFragment = getCurrentFragment()
        if (currentFragment is MainCombineFragment) {
            currentFragment.onResumeLoadData()
        }
    }

    override fun handleDragFileChange(intent: Intent?) {
        val currentFragment = getCurrentFragment()
        if (currentFragment is MainRecentFragment) {
            val operate = intent?.extras?.getString(KtConstants.DRAG_OPERATE)
            currentFragment.refreshAfterDragOut(operate)
        } else {
            onRefreshData()
        }
    }

    fun refreshSideNavigationData() {
        val currentFragment = getCurrentFragment()
        if (currentFragment is MainCombineFragment) {
            currentFragment.refreshSideNavigationData()
        }
    }

    override fun onClickDir(path: String) {
        val categoryFragment = getCategoryFragment()
        categoryFragment?.apply {
            onClickDir(path)
        }
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        val categoryFragment = getCategoryFragment()
        return categoryFragment?.handleDragEvent(null, event)
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val fragment = getCurrentFragment()
        if (fragment is MainRecentFragment) {
            return fragment.getSelectedItemView()
        }
        if (fragment is MainCombineFragment) {
            return fragment.getSelectedItemView()
        }
        return null
    }

    override fun setNavigateItemAble() {
        val fragment = getCurrentFragment()
        if (fragment is MainRecentFragment) {
            fragment.setNavigateItemAble()
        }
        if (fragment is MainCombineFragment) {
            fragment.setNavigateItemAble()
        }
    }

    override fun getDragCurrentPath(): String? {
        return getCurrentPath()
    }
}