/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main
 * * Version     : 1.0
 * * Date        : 2024/4/47
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.ui

import android.content.Intent
import android.os.Bundle
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.Constants.IS_OPEN_PARENT_DOCUMENT_ACTIVITY
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.WindowUtils
import com.oplus.filemanager.interfaze.categorydoc.ICategoryDocumentApi
import com.oplus.filemanager.main.R

class QuickDocumentActivity : BaseVMActivity() {
    companion object {
        private const val TAG = "QuickDocumentActivity"
        private const val DOCUMENT_ACTIVITY_ACTION = "oppo.intent.action.FILE_DOCUMENT_VIEW"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
    }

    override fun getLayoutResId(): Int {
        return R.layout.quick_document_activity
    }

    override fun initView() {
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
        // 如果是桌面pad or折叠屏&展开时，跳转MainActivity，然后以父子级方式展示文档页面
        if (WindowUtils.isMiddleAndLargeScreen(this)) {
            Log.d(TAG, "Tablet or Foldable && !isScreenFold")
            val intent = Intent()
            intent.putExtra(IS_OPEN_PARENT_DOCUMENT_ACTIVITY, true)
            if (KtUtils.isExportMainActivity()) {
                intent.setClass(this, ExportMainActivity::class.java)
            } else {
                intent.setClass(this, MainActivity::class.java)
            }
            startActivity(intent)
            staticsLaunchEntry(Constants.PAGE_MAIN)
            finish()
        } else {
            Log.d(TAG, "SmallScreenPhone")
            // 跳转到DocumentActivity
            val categoryDocumentApi = Injector.injectFactory<ICategoryDocumentApi>()
            categoryDocumentApi?.startCategoryDocumentActivity(this, getString(com.filemanager.common.R.string.string_documents))
            staticsLaunchEntry(Constants.PAGE_DOC)
            finish()
        }
    }

    private fun staticsLaunchEntry(landingPage: String) {
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_SPEECH_ASSIST, "", landingPage)
    }
}