/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HorizontalProgressBar2
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/2/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/2/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.*
import android.util.AttributeSet
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.MyApplication
import com.oplus.filemanager.main.R
import kotlin.math.roundToInt

@SuppressLint("CustomViewStyleable", "WrongConstant")
class HorizontalProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = com.support.progressbar.R.attr.couiHorizontalProgressBarStyle
) : COUIHorizontalProgressBar(context, attrs, defStyleAttr) {

    private val mPaint: Paint = Paint()
    private val mBackgroundRect: RectF = RectF()
    private val mProgressRect: RectF = RectF()
    private val mPath: Path = Path()
    private val mProgressPath: Path = Path()
    private var mStyle = 0
    private var mRadius = 0f
    private var mBackgroundColor: ColorStateList? = null
    private var mProgressColor: ColorStateList? = null
    private val mDefProgressColor = Color.parseColor("#FFFFFF")
    private val mDefBackgroundColor = Color.argb(OPACITY_HEX, 0, 0, 0)

    init {
        mStyle = if ((attrs != null) && (attrs.styleAttribute != 0)) {
            attrs.styleAttribute
        } else {
            defStyleAttr
        }
        mRadius =
            MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.main_category_phone_space_progress_radius)
                .toFloat()
        val typedArray = context.obtainStyledAttributes(attrs, com.support.progressbar.R.styleable.COUIHorizontalProgressBar, defStyleAttr, 0)
        mBackgroundColor =
            typedArray.getColorStateList(com.support.progressbar.R.styleable.COUIHorizontalProgressBar_couiHorizontalProgressBarBackgroundColor)
        mProgressColor =
            typedArray.getColorStateList(com.support.progressbar.R.styleable.COUIHorizontalProgressBar_couiHorizontalProgressBarProgressColor)
        typedArray.recycle()
        mPaint.isDither = true
        mPaint.isAntiAlias = true
        setLayerType(1, mPaint)
    }

    override fun onDraw(canvas: Canvas) {
        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
        mProgressPath.reset()
        mPath.reset()
        val width = width - paddingLeft - paddingRight
        mPaint.color = getStateColor(mBackgroundColor, mDefBackgroundColor)
        mBackgroundRect[paddingLeft.toFloat(), paddingTop.toFloat(), (width - paddingRight).toFloat()] =
            (height - paddingBottom).toFloat()
        canvas.drawRoundRect(mBackgroundRect, mRadius, mRadius, mPaint)
        mPath.addRoundRect(mBackgroundRect, mRadius, mRadius, Path.Direction.CCW)
        val ratio = progress.toFloat() / max.toFloat()
        val progressLeft: Int
        if (isLayoutRtl) {
            progressLeft = ((width - paddingRight).toFloat() - ratio * width.toFloat()).roundToInt()
            mProgressRect[progressLeft.toFloat(), paddingTop.toFloat(), (progressLeft + width).toFloat()] =
                (height - paddingBottom).toFloat()
        } else {
            progressLeft = (paddingLeft.toFloat() - (1.0f - ratio) * width.toFloat()).roundToInt()
            mProgressRect[progressLeft.toFloat(), paddingTop.toFloat(), (progressLeft + width).toFloat()] =
                (height - paddingBottom).toFloat()
        }
        mPaint.color = getStateColor(mProgressColor, mDefProgressColor)
        mProgressPath.addRoundRect(mProgressRect, 0f, 0f, Path.Direction.CCW)
        mProgressPath.op(mPath, Path.Op.INTERSECT)
        canvas.drawPath(mProgressPath, mPaint)
    }

    private fun getStateColor(colorStateList: ColorStateList?, defaultValue: Int): Int {
        return colorStateList?.getColorForState(drawableState, defaultValue) ?: defaultValue
    }

    companion object {
        private const val OPACITY_HEX = 76
    }
}