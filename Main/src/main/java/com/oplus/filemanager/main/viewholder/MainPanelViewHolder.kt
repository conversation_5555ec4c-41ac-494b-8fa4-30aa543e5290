/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/18, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.main.viewholder

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.utils.Log

class MainPanelViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    companion object {
        const val TAG = "MainPanelViewHolder"
    }

    fun bindData(onItemInitViewCallback: (itemView: View) -> Unit) {
        Log.d(TAG, "bindData")
        onItemInitViewCallback.invoke(itemView)
    }
}