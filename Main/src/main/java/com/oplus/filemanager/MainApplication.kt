/***********************************************************
 * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * File:  - MainApplication.java
 * Description: custom application class
 * Version: 1.0
 * Date : 2020/10/19
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/10/19    1.0     create
 ****************************************************************/
package com.oplus.filemanager

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants.AD_SWITCH_STATUS
import com.filemanager.common.constants.CommonConstants.AD_SWITCH_UPLOAD_TIME
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager.Companion.sThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.CategoryAppConfig
import com.filemanager.common.utils.FileTraceUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.WhiteListParser
import com.oplus.compat.utils.util.AdapterHelper
import com.oplus.filemanager.category.remotedevice.disconect.SafeWorkManagerInitializer
import com.oplus.filemanager.glide.GlideApp
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import com.oplus.filemanager.interfaze.questionnaire.IQuestionnaire
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.main.BuildConfig
import com.oplus.filemanager.main.ui.ExportMainActivity
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.labelmanager.recommend.aiunit.AiUnitUtils
import com.oplus.statistics.OplusTrack
import kotlin.properties.Delegates
import com.oplusx.sysapi.utils.util.AdapterHelper as SysApiAdapterHelper

open class MainApplication : Application(), PrivacyPolicyController.OnPrivacyPolicyListener {

    companion object {
        private const val TAG = "MainApplication"
        private const val ONE_DAY = 24 * 60 * 60 * 1000L
        private var instance: Context by Delegates.notNull()

        @JvmStatic
        val sAppContext: Context
            get() = instance
    }

    init {
        Log.i(TAG, "MainApplication constructor")
        MyApplication.configureBuildInfo(BuildConfig.FLAVOR_B, BuildConfig.FLAVOR_region)
    }

    override fun attachBaseContext(base: Context?) {
        Log.i(TAG, "MainApplication attachBaseContext base $base")
        super.attachBaseContext(base)
        instance = this
        MyApplication.init(this, BuildConfig.FLAVOR_region)
        try {
            CompatUtils.compactApi({
                SysApiAdapterHelper.init(base)
            }, {
                AdapterHelper.init(base)
            })
        } catch (e: Throwable) {
            Log.e(TAG, "Failed to init AdapterHelper, ${e.message}")
        }
    }

    override fun onCreate() {
        super.onCreate()
        MyApplication.onCreate(this)
        initApp()
        initActivityCallback()
        ProcessLifecycleOwner.get().lifecycle.addObserver(ApplicationLifecycleObserver())
    }

    private fun adSwitchStatusUpload() {
        //外销时触发开关埋点上报
        if (FeatureCompat.sIsExpRom) {
            //当前时间减去上一次上传的时间大于一天时，再次触发
            val currentTimeMillis = System.currentTimeMillis()
            val savedTime = PreferencesUtils.getLong(key = AD_SWITCH_UPLOAD_TIME, default = 0L)
            val timeDifference = currentTimeMillis - savedTime
            Log.i(TAG, "currentTime, $currentTimeMillis,savedCurrentTime, $savedTime")
            if (timeDifference > ONE_DAY) {
                //开关埋点
                Log.i(TAG, "do switch status upload, $timeDifference")
                val status = PreferencesUtils.getBoolean(key = AD_SWITCH_STATUS, default = false)
                OptimizeStatisticsUtil.adSwitch(status)
                PreferencesUtils.put(
                    key = AD_SWITCH_UPLOAD_TIME, value = System.currentTimeMillis()
                )
            }
        }
    }
    private fun initActivityCallback() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, p1: Bundle?) {
                addNewActivityToList(activity, MyApplication.activities)
            }

            override fun onActivityStarted(activity: Activity) {}

            override fun onActivityResumed(activity: Activity) {
                MyApplication.activityResumedCounter++
            }

            override fun onActivityPaused(activity: Activity) {
                MyApplication.activityResumedCounter--
            }

            override fun onActivityStopped(activity: Activity) {}

            override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {}

            override fun onActivityDestroyed(activity: Activity) {
                MyApplication.activities.remove(activity)
            }
        })
    }

    private fun addNewActivityToList(activity: Activity, activityList: MutableList<Activity>) {
        var isContain = false
        for (it in activityList) {
            if (it.componentName.className == activity.componentName.className) {
                isContain = true
                break
            }
        }
        if (!isContain) activityList.add(activity)
    }

    private fun initQuestionnaire() {
        // 用户同意隐私权限，则直接初始化。如果没有授权，用户同意之后才授权
        if (PrivacyPolicyController.hasAgreeUseNet()) {
            Log.d(TAG, "hasAgreePrivacy true, init QuestionnaireAction")
            val questionnaire = Injector.injectFactory<IQuestionnaire>()
            questionnaire?.initSDK(this)
        } else {
            Log.d(TAG, "hasAgreePrivacy false, init QuestionnaireAction after agree")
            PrivacyPolicyController.bindPrivacyPolicyListener(this)
        }
    }

    private fun initApp() {
        sThreadManager.execute(FileRunnable({
            Log.i(TAG, "initApp start")
            adSwitchStatusUpload()//开关埋点上报
            if (FeatureCompat.sIsExpRom) {
                val advertApi = Injector.injectFactory<IAdvertApi>()
                advertApi?.updateAdCloud()
            }
            initGlideApp()
            preloadSharePreferences()
            initSafeWorkManager()
            initAppSwitchAndDmp()
            FileTraceUtil.getInstance().deleteTraceFileIfExpRom()
            VolumeEnvironment.isOTGVersion(sAppContext)
            BlacklistParser.initializeBlacklist(sAppContext)
            WhiteListParser.initialize(sAppContext)
            CategoryAppConfig.initialize(sAppContext)
            OplusTrack.init(sAppContext)
            AiUnitUtils.getInstance().init(sAppContext)
            Log.i(TAG, "initAccountClient")
            val heytapAccountAction = Injector.injectFactory<IHeytapAccount>()
            heytapAccountAction?.initAccountClient(sAppContext)
            initQuestionnaire()
            val fileCloudBrowserAction = Injector.injectFactory<IFileCloudBrowser>()
            fileCloudBrowserAction?.initStd(sAppContext)
            fileCloudBrowserAction?.initDocsEnvironment()
            Log.i(TAG, "initApp fileCloudBrowserAction $fileCloudBrowserAction")
            initNewFilesSeedling()
        }, TAG + "_initApp"), ThreadType.NORMAL_THREAD, ThreadPriority.HIGH)
    }

    private fun initNewFilesSeedling() {
        val value = PreferencesUtils.getLong(key = Constants.KEY_NEW_FILES_REFRESH_LAST_TIME, default = 0L)
        if (value == 0L) {
            PreferencesUtils.put(key = Constants.KEY_NEW_FILES_REFRESH_LAST_TIME, value = System.currentTimeMillis())
        }
    }

    private fun initAppSwitchAndDmp() {
        val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
        if (appSwitchApi == null) {
            Log.i(TAG, "initAppSwitchAndDmp appSwitchApi NULL, return")
            return
        }
        appSwitchApi.initDmpAndAppSwitch(true)
        val dmpApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpApi == null) {
            Log.i(TAG, "initAppSwitchAndDmp dmpApi NULL, return")
            return
        }
        dmpApi.checkAndUpdateSwitchSpToDmp()
    }

    private fun initGlideApp() {
        GlideApp.get(applicationContext)
    }

    /**
     * 安全地初始化 WorkManager
     */
    private fun initSafeWorkManager() {
        Log.i(TAG, "initSafeWorkManager start")
        runCatching {
            val success = SafeWorkManagerInitializer
                .safeInitializeWorkManager(this)
            Log.i(TAG, "initSafeWorkManager result: $success")
        }.onFailure { exception ->
            Log.e(TAG, "initSafeWorkManager failed: ${exception.message}", exception)
        }
    }

    /**
     * preload the specific share preferences
     * here do a preload if getString or get Float involved in main thread
     */
    private fun preloadSharePreferences() {
        Injector.injectFactory<ISuperApp>()?.preloadSuperSharePreferences(this)
        PrivacyPolicyController.preloadPrivacyPolicySharePreference(this)
    }

    override fun onAgreeResult(agree: Boolean, noLongerRemind: Boolean) {
        Log.d(TAG, "onAgreeResult agree $agree")
        if (agree && PrivacyPolicyController.hasAgreeAdditionalFunctions()) {
            val questionnaire = Injector.injectFactory<IQuestionnaire>()
            questionnaire?.initSDK(this)
        }
    }


    class ApplicationLifecycleObserver : LifecycleObserver {

        companion object {
            const val TAG = "ApplicationLifecycleObserver"
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
        fun onAppStop() {
            val activityList = MyApplication.activities
            val homeFromMainActivity = activityList.size == 1 &&
                    (activityList.get(0)::class.java == MainActivity::class.java || activityList.get(0)::class.java == ExportMainActivity::class.java)
            Log.d(TAG, "onAppStop, activityList ${MyApplication.activities}, homeFromMainActivity $homeFromMainActivity, " +
                        "openFileCauseBg ${MyApplication.openFileCauseBg}")
            MyApplication.appInBackground = true
            if (homeFromMainActivity && !MyApplication.openFileCauseBg) {
                Injector.injectFactory<ICategoryRemoteDeviceApi>()?.checkAndTrigDelayDisconnect()
                MyApplication.isColdLaunch = false
            }
            MyApplication.openFileCauseBg = false
        }

        @OnLifecycleEvent(Lifecycle.Event.ON_START)
        fun onAppStart() {
            Log.d(TAG, "onAppStart")
            MyApplication.appInBackground = false
            Injector.injectFactory<ICategoryRemoteDeviceApi>()?.cancelDelayDisconnect()
        }
    }
}