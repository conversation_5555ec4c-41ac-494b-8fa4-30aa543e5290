/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main.recent
 * * Version     : 1.0
 * * Date        : 2020/6/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.recent.ui

import android.content.Context
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.constants.KtConstants
import com.oplus.filemanager.recent.entity.recent.*

abstract class RecentRecyclerExpandBaseAdapter<G, C, VH : RecyclerView.ViewHolder>(context: Context) :
    BaseSelectionRecycleAdapter<VH, BaseRecentEntity>(context) {
    companion object {
        var LIST_EXPAND_ITEM_COUNT = 6
        const val ERROR_INDEX = -99
    }

    protected var mDataList: List<ExpandGroupItemEntity> = ArrayList()
    var mScanMode = KtConstants.SCAN_MODE_GRID


    fun getData(): List<ExpandGroupItemEntity> {
        return mDataList
    }

    fun getNextGroupBean(curBean: RecentGroupEntity?, position: Int): BaseRecentEntity? {
        var isNext = false
        for (i in mDataList.indices) {
            if (curBean != null && curBean.mGroupDate == mDataList[i].mParent?.mGroupDate) {
                isNext = true
                continue
            }
            if (isNext) {
                return mDataList[i].mParent
            }
        }
        return null
    }

    /**
     * Get the index of the group title of the next group of the current group in the list
     *
     * @param curBean
     * @param position
     * @return
     */
    fun getNextGroupIndex(curBean: RecentGroupEntity?, position: Int): Int {
        if (position >= mFiles.size) {
            return ERROR_INDEX
        }
        for (i in position + 1 until mFiles.size) {
            if (mFiles[i].getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_GROUP) {
                return i
            }
            if (i == mFiles.size - 1) {
                return mFiles.size
            }
        }
        return 0
    }

    /**
     * Get the index of the group title of the Prev group of the current group in the list
     *
     * @param curBean
     * @param position
     * @return
     */
    fun getPrevGroupIndex(curBean: RecentGroupEntity?, position: Int): Int {
        if (position > mFiles.size) {
            return ERROR_INDEX
        }
        if (position <= 0) {
            return 0
        }
        for (i in (position - 1) downTo 0) {
            if (mFiles[i].getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_GROUP) {
                return i
            }
        }
        return 0
    }

    /**
     * Get the index of the folder to which the file belongs
     *
     * @param bean
     * @param position
     * @return
     */
    fun getFolderIndex(bean: BaseRecentEntity?, position: Int): Int {
        if (position >= mFiles.size) {
            return ERROR_INDEX
        }
        var index = position
        if (bean != null) {
            if (bean.getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_FOLDER) {
                return index
            } else if (bean.getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_GROUP || bean.getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_FOOTER) {
                return -1
            }
        }
        for (i in position downTo 0) {
            if (mFiles[i].getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_FOLDER) {
                index = i
                break
            }
        }
        return index
    }

    /**
     * Get the index of the group title of the current item
     *
     * @param position
     * @return
     */
    fun getPositionGroupIndex(position: Int): Int {
        if ((position >= mFiles.size) || (position < 0)) {
            return ERROR_INDEX
        }
        var index = position
        for (i in position downTo 0) {
            if (mFiles[i].getmViewType() == BaseRecentEntity.VIEW_TYPE_ITEM_GROUP) {
                index = i
                break
            }
        }
        return index
    }

    fun setRecentData(dataList: List<ExpandGroupItemEntity>, recentList: MutableList<BaseRecentEntity>, selectKeyList: MutableList<Int>) {
        mDataList = dataList
        mFiles = recentList
        mSelectionArray = selectKeyList
        notifyDataSetChanged()
    }

    /**
     * Expand a group，calculate data
     *
     * @param item
     * @param groupItem
     * @param count
     * @return
     */
    protected fun getExpandOneGroup(item: ExpandGroupItemEntity, groupItem: RecentGroupEntity, count: Int): List<BaseRecentEntity> {
        var list: MutableList<BaseRecentEntity> = ArrayList()
        if (groupItem.mIsExpand) {
            //sub folder and files
            for (folder in item.getChildList().indices) {
                val folderItem = item.getChildList()[folder]
                folderItem?.apply {
                    list.add(this)
                }
            }
        }
        return list
    }

    override fun getItemCount(): Int {
        return if (mFiles.isEmpty()) {
            0
        } else mFiles.size + 1
    }


    override fun getItemId(position: Int): Long {
        if (position < mFiles.size) {
            return when (val item = mFiles[position]) {
                is RecentFileEntity -> {
                    item.mAbsolutePath.hashCode().toLong()
                }
                is RecentGroupEntity -> {
                    ("${item.mGroupDate}_${item.groupShowDateAndSize}").hashCode().toLong()
                }
                else -> {
                    position.toLong()
                }
            }
        }
        // footer id
        if (getItemViewType(position) == BaseRecentEntity.VIEW_TYPE_ITEM_FOOTER) {
            return "FooterItem".hashCode().toLong()
        }
        return position.toLong()
    }

    abstract fun refreshEditMode(isInEdit: EditMode)

    abstract fun isSelected(file: BaseRecentEntity): Boolean
}