/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:RecentDataStorage.java
 * * Description:
 * * Version:1.0
 * * Date :2019/7/21
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.oplus.filemanager.recent.utils;


import com.filemanager.common.base.BaseFileBean;

import java.util.Observable;

public class RecentDataStorage extends Observable {
    private static final String TAG = "RecentDataStorage";
    private BaseFileBean mCurrentOpenFile;

    private RecentDataStorage() {
    }

    public static RecentDataStorage getInstance() {
        return RecentDataStorage.RecentDataHolder.sInstance;
    }

    public void setOpenFile(BaseFileBean file) {
        mCurrentOpenFile = file;
    }

    public BaseFileBean getOpenFile() {
        return mCurrentOpenFile;
    }

    private static class RecentDataHolder {
        public static RecentDataStorage sInstance = new RecentDataStorage();
    }
}
