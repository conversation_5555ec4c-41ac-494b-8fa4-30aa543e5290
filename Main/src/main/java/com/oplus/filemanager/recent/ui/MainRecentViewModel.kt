/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main.recent
 * * Version     : 1.0
 * * Date        : 2020/6/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.recent.ui

import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Handler
import android.os.SystemClock
import android.view.MotionEvent
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEncryptController
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.HiddenFileHelper.isHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper.CATEGORY_RECENT
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isInvalid
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.clouddriver.FileActionCloudDriver
import com.filemanager.fileoperate.clouddriver.FileCloudDriverObserver
import com.filemanager.fileoperate.compress.CompressConfirmDialog
import com.filemanager.fileoperate.compress.CompressConfirmDialog.Companion.DEFAULT_SAVE_PATH
import com.filemanager.fileoperate.compress.CompressConfirmType
import com.filemanager.fileoperate.compress.FileActionCompress
import com.filemanager.fileoperate.compress.FileCompressObserver
import com.filemanager.fileoperate.copy.FileActionCopy
import com.filemanager.fileoperate.copy.FileCopyObserver
import com.filemanager.fileoperate.cut.FileActionCut
import com.filemanager.fileoperate.cut.FileCutObserver
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.FileActionDecompress
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.detail.FileActionDetail
import com.filemanager.fileoperate.detail.FileDetailObserver
import com.filemanager.fileoperate.encrypt.FileActionEncrypt
import com.filemanager.fileoperate.encrypt.FileSecurityEncryptObserver
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.filemanager.fileoperate.previewcompress.FileActionPreviewCompress
import com.filemanager.fileoperate.previewcompress.FilePreviewCompressObserver
import com.filemanager.fileoperate.rename.FileActionRename
import com.filemanager.fileoperate.rename.FileRenameObserver
import com.filemanager.fileoperate.share.FileActionShare
import com.filemanager.fileoperate.share.FileShareObserver
import com.oplus.encrypt.EncryptActivity
import com.oplus.encrypt.EncryptUtils
import com.oplus.filemanager.interfaze.compresspreview.ICompressPreview
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.recent.entity.recent.BaseRecentEntity
import com.oplus.filemanager.recent.entity.recent.ExpandGroupItemEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.entity.recent.RecentGroupEntity
import com.oplus.filemanager.recent.task.RecentLoadCallback
import com.oplus.filemanager.recent.ui.MainRecentViewModel.RecentUiModel
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.recent.utils.RecentDataStorage
import com.oplus.filemanager.recent.utils.RecentFileObserver
import com.oplus.ota.upgrade.OtaUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.lang.ref.WeakReference
import java.util.Locale

class MainRecentViewModel : SelectionViewModel<BaseRecentEntity, RecentUiModel>() {
    companion object {
        const val MSG_PULL_REFRESH_TYPE = 1
        const val MSG_END_REFRESH = 3
        const val MSG_DELAY_SHOW_CACHE_LOAD_TIP = 4
        const val MSG_UPDATE_TOOLBAR_BEHAVIOR = 12
        const val MSG_UPDATE_FOLDER_SELECT = 13
        private const val TAG = "MainRecentViewModel"
        private const val SHOW_PULLREFRESH_MIN_TIME = 800
        private const val RECENT_SCAN_MODE_SP_KEY = "recent_scan_mode_sp_key"
        private const val DELAYED_TIME = 200L
    }

    val mModeState = BaseStateModel(MutableLiveData(KtConstants.LIST_NORMAL_MODE))
    private val operateActionImpl = RecentOperateActionImpl()
    var mLoadType = RecentDataHelper.TYPE_CACHE
    var mNormalLoad = false
    var mCurrentVisiblePosition = 0
    var mLastAutoLoadTime = -MainRecentFragment.AUTO_LOAD_MIN_TIME_INTERVAL
    private var mRecentFileObserver = RecentFileObserver()
    private val mMainRecentLoadCallback = MainRecentLoadCallback(this)
    private var mStartRefreshTime = 0L
    private var mUiHandler: Handler? = null
    private var mDeCompressDialog: CompressConfirmDialog? = null
    private var mAction: BaseFileAction<*>? = null
    private var mCompressFile: BaseFileBean? = null
    private var mOperateAction: IFileOperateAction<IFileActionObserver>? = null
    private var fileDetailObserver: FileDetailObserver? = null
    private var isChangeToSelectMode = false
    var isPreviewOpen = false
    val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(RECENT_SCAN_MODE_SP_KEY, 0)
        MutableLiveData(
            if (lastScanMode == 0) {
                KtConstants.SCAN_MODE_GRID
            } else {
                lastScanMode
            }
        )
    }

    /**
     * only  the RecentUiModel.mSourceList is not change and the content inside has changed will be true;
     * other time is false;
     * warning: this value only get true once; it will be changed to false; so you need to  care about the
     * timing of your acquisition
     */
    var mLocalDataHasChanged = false
        get() {
            val res = field
            Log.d(TAG, "get mDataHasChanged: $res")
            field = false
            return res
        }

    fun obtainOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.RecentOperatorListener =
        RecentOperatorListener(recentOperateBridge, operateActionImpl)

    fun setUiHandler(handler: Handler) {
        mUiHandler = handler
    }

    fun pullRefreshLoadData() {
        Log.d(TAG, "pullRefreshLoadData")
        mStartRefreshTime = SystemClock.elapsedRealtime()
        mRecentFileObserver.loadRecentData(RecentDataHelper.TYPE_MEDIA, uiState.value?.mSourceList, mMainRecentLoadCallback)
        mLoadType = RecentDataHelper.TYPE_MEDIA
    }

    fun loadRecentData(type: Int) {
        Log.d(TAG, "loadRecentData: type= $type")
        mRecentFileObserver.loadRecentData(type, uiState.value?.mSourceList, mMainRecentLoadCallback)
        mLoadType = type
        if (type == RecentDataHelper.TYPE_CACHE) {
            mNormalLoad = true
        }
    }

    fun isLoading(): Boolean {
        return mRecentFileObserver.isLoading()
    }

    fun cancelLoad() {
        mRecentFileObserver.loadCancel()
    }

    fun onItemClick(activity: BaseVMActivity?, baseFile: RecentFileEntity, event: MotionEvent?) {
        if (isInSelectMode()) {
            toggleSelectItem(baseFile)
        } else {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return
            }
            launch {
                val isExits = baseFile.checkExist()
                if (!isExits) {
                    CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                    return@launch
                } else {
                    if (PCConnectAction.openFileOnRemote(baseFile, event)) {
                        return@launch
                    }
                    if (activity != null) {
                        OptimizeStatisticsUtil.clickRecentFile(baseFile)
                        if (baseFile.mLocalType == MimeTypeHelper.COMPRESSED_TYPE) {
                            if (!OtaUtils.checkOzipFile(activity, baseFile)) {
                                val decompressObserver = object : FileDecompressObserver(activity) {
                                    override fun onActionDone(result: Boolean, data: Any?) {
                                        Log.d(TAG, "FileActionOpen onActionDone $result")
                                        if (result) {
                                            RecentDataStorage.getInstance().openFile = baseFile
                                        }
                                    }

                                    override fun onActionCancelled() {
                                    }

                                    override fun onActionReloadData() {
                                    }
                                }
                                FileActionPreviewCompress(activity, baseFile, decompressObserver).execute(object : FilePreviewCompressObserver(activity) {
                                    override fun onActionPreviewOverCount(msg: String?) {
                                        mCompressFile = baseFile
                                        showDeCompressDialog(activity)
                                    }

                                    override fun onActionDone(result: Boolean, data: Any?) {
                                        Log.d(TAG, "FileActionPreviewCompress onActionDone $result")
                                        if (result && (data is Pair<*, *>) && (data.second is Map<*, *>?)) {
                                            CompressPreviewCacheHelper.storeTransferPreviewFile(data.second as Map<String, MutableList<out BaseDecompressFile>?>?)
                                            Injector.injectFactory<ICompressPreview>()?.startCompressPreviewActivity(activity, baseFile.mData)
                                        }
                                        Log.d(TAG, "FileActionOpen onActionDone $result")
                                        if (result) {
                                            RecentDataStorage.getInstance().openFile = baseFile
                                        } else {
                                            CustomToast.showShort(com.filemanager.common.R.string.compress_preview_failed)
                                        }
                                    }

                                    override fun onActionCancelled() {
                                    }

                                    override fun onActionReloadData() {
                                    }
                                })
                            }
                        } else {
                            val mediaImgIds = getRecentMediaImgIds(baseFile)
                            FileActionOpen.Companion.Builder(activity, baseFile)
                                    .setIsFromRecent(true)
                                    .setOrderMediaIdList(mediaImgIds)
                                    .build()
                                    .execute(object : FileOpenObserver(activity) {
                                        override fun onActionDone(result: Boolean, data: Any?) {
                                            Log.d(TAG, "FileActionOpen onActionDone $result")
                                            if (result) {
                                                RecentDataStorage.getInstance().openFile = baseFile
                                            }
                                        }
                                    })
                        }
                    }
                }
            }
        }
    }

    fun setSavePath(path: String) {
        mDeCompressDialog?.setSavePath(path)
    }

    fun releaseDecompressDialog() {
        mDeCompressDialog?.dismiss()
        mDeCompressDialog = null
    }

    fun showDeCompressDialog(baseVMActivity: ComponentActivity, operateCallback: ((String?, String?) -> Unit)? = null) {
        val selectFiles = getSelectItems()
        mDeCompressDialog?.dismiss()
        mDeCompressDialog =
            CompressConfirmDialog(baseVMActivity, com.support.panel.R.style.COUIFitContentBottomSheetDialog, CompressConfirmType.DECOMPRESS).apply {
                if ((selectFiles.size == 1) && (selectFiles[0].mLocalType == MimeTypeHelper.COMPRESSED_TYPE)) {
                    val dir = selectFiles[0].mData?.let { File(it).parent }
                        ?: (VolumeEnvironment.getInternalSdPath(appContext) + DEFAULT_SAVE_PATH)
                    setSavePath(dir)
                } else if (mCompressFile != null) {
                    val parentPath = KtUtils.getParentFilePath(mCompressFile!!.mData ?: "")
                    setSavePath(parentPath)
                } else {
                    setSavePath(VolumeEnvironment.getInternalSdPath(appContext) + DEFAULT_SAVE_PATH)
                }

                setModifySavePathListener {
                    if (baseVMActivity is TransformNextFragmentListener) {
                        (baseVMActivity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_DECOMPRESS)
                    }
                }

                if (selectFiles.size == 1) {
                    setFileName(selectFiles[0].mDisplayName?.substringBeforeLast(".") ?: "")
                } else if (mCompressFile != null) {
                    setFileName(mCompressFile?.mDisplayName?.substringBeforeLast(".") ?: "")
                } else {
                    Log.e(TAG, "file name is exception")
                }

                setClickButtonListener { name, path ->
                    operateCallback?.invoke(path, name)
                    if (doDecompressFile(baseVMActivity, path, name)) {
                        mDeCompressDialog?.dismiss()
                    }
                }

                setOnDismissListener {
                    mCompressFile = null
                }
                show()
            }
    }

    private fun getRecentMediaImgIds(baseFile: BaseFileBean): ArrayList<String>? {
        // 获取最近tab所有文件
        var mediaIdList: ArrayList<String>? = null
        if (baseFile.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
            val allEntityList = uiState.value?.mAllEntityList
            Log.d(TAG, "mAllEntityList size=${allEntityList?.size}")
            mediaIdList = FileMediaHelper.getMediaImgIds(baseFile, allEntityList)
        }
        return mediaIdList
    }

    fun onItemLongClick(activity: BaseVMActivity?, file: RecentFileEntity) {
        if (!isInSelectMode()) {
            changeListMode(LIST_SELECTED_MODE)
        }
        selectItems(file)
    }

    private fun selectItems(file: RecentFileEntity) {
        toggleSelectItem(file, false)
    }

    private fun toggleSelectItem(file: RecentFileEntity, canReverse: Boolean = true) {
        uiState.value?.apply {
            if (isInSelectMode()) {
                val key = getItemKey(file)
                if (selectedList.contains(key).not()) {
                    selectedList.add(key)
                } else if (canReverse) {
                    selectedList.remove(key)
                }
                uiState.value = uiState.value
            }
        }
    }

    private fun RecentUiModel.checkIfSelectAllFilesInRecentContentEntity(
        file: RecentFileEntity
    ) {
        if (mSourceList.size <= file.mGroupPosition) {
            return
        }
        mSourceList[file.mGroupPosition].getChildList().forEach { recentContentEntity ->
            if (recentContentEntity?.mAbsolutePath == file.mAbsolutePath) {
                checkIfContentSelectedAll(recentContentEntity, selectedList)
                return@forEach
            }
        }
    }

    @VisibleForTesting
    fun checkAllEntitySelected(entries: List<BaseRecentEntity>, selectedList: ArrayList<Int>) {
        entries.forEach { entity ->
            if (entity is RecentFileEntity) {
                checkIfContentSelectedAll(entity, selectedList)
            }
        }
    }

    private fun checkIfContentSelectedAll(recentContentEntity: RecentFileEntity, selectedList: ArrayList<Int>): Boolean {
        if (!selectedList.contains(getItemKey(recentContentEntity))) {
            return false
        }
        return true
    }

    fun clickToolbarSelectAll() {
        if (uiState.value?.selectedList?.size == uiState.value?.mAllEntityList?.size) {
            uiState.value?.selectedList?.clear()
        } else {
            uiState.value?.selectedList?.clear()
            uiState.value?.mAllEntityList?.let {
                for (baseFileBean in it) {
                    uiState.value?.selectedList?.add(getItemKey(baseFileBean))
                }
            }
        }
        uiState.value = uiState.value
    }

    fun pressBack(): Boolean {
        return if (isInSelectMode()) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
            true
        } else {
            false
        }
    }

    private fun setUiData(data: MutableList<ExpandGroupItemEntity>?, coroutine: CoroutineDispatcher = Dispatchers.IO) {
        Log.d(TAG, "setUiData : data size=${data?.size}")
        launch {
            withContext(coroutine) {
                if ((data != null) && (data.size > 0)) {
                    data.apply {
                        val recentList: MutableList<BaseRecentEntity> = mutableListOf()
                        var count = 0
                        for (group in data.indices) {
                            val item = data[group]
                            val groupItem = item.mParent
                            if (groupItem != null) {
                                item.setmViewType(BaseRecentEntity.VIEW_TYPE_ITEM_GROUP)
                                groupItem.setmViewType(BaseRecentEntity.VIEW_TYPE_ITEM_GROUP)
                                groupItem.mGroupPosition = group
                                recentList.add(groupItem)
                                count = traversingOneGroup(recentList, item, groupItem, group, count)
                            }
                        }
                        val fileEntityList: MutableList<RecentFileEntity> = mutableListOf()
                        val keyMap = HashMap<Int, BaseRecentEntity>()
                        for (groupItemEntity in this) {
                            for (fileEntity in groupItemEntity.getChildList()) {
                                if (fileEntity == null) { continue }
                                if (fileEntity.mSize == 0L) {
                                    try { //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
                                        val file = File(fileEntity.mAbsolutePath)
                                        fileEntity.mSize = file.length()
                                        fileEntity.mLastModified = file.lastModified() / SECONDS_TO_MILLISECONDS
                                    } catch (e: Exception) {
                                        Log.e(TAG, e.message)
                                    }
                                }
                                fileEntityList.add(fileEntity)
                                keyMap[getItemKey(fileEntity)] = fileEntity
                            }
                        }
                        val selectedKeyList = ArrayList<Int>()
                        if ((uiState.value?.selectedList?.size ?: 0) > 0) {
                            withContext(Dispatchers.IO) {
                                for (selectedKey in uiState.value!!.selectedList) {
                                    if (keyMap.containsKey(selectedKey)) {
                                        selectedKeyList.add(selectedKey)
                                    }
                                }
                                checkAllEntitySelected(recentList, selectedKeyList)
                            }
                        }
                        refreshUiState(this, recentList, fileEntityList, mModeState, selectedKeyList, keyMap)
                    }
                } else {
                    refreshUiState(mutableListOf(), mutableListOf(), mutableListOf(), mModeState)
                }
            }
        }
    }

    private fun refreshUiState(
        expandGroupItemEntities: MutableList<ExpandGroupItemEntity>,
        recentList: MutableList<BaseRecentEntity>,
        fileEntityList: MutableList<RecentFileEntity>,
        mModeState: BaseStateModel,
        selectedKeyList: ArrayList<Int> = arrayListOf(),
        keyMap: HashMap<Int, BaseRecentEntity> = hashMapOf()
    ) {
        val recentUiModel = RecentUiModel(
            expandGroupItemEntities,
            recentList,
            fileEntityList,
            mModeState,
            selectedKeyList,
            keyMap
        )
        recentUiModel.mIsFromSetUiData = true
        uiState.postValue(recentUiModel)
    }

    private fun getItemKey(item: BaseFileBean): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return "null".hashCode()
        }
        return path.lowercase(Locale.getDefault()).hashCode()
    }

    private fun checkNeedShowDelay(data: MutableList<ExpandGroupItemEntity>?) {
        val cur = SystemClock.elapsedRealtime()
        val coast = cur - mStartRefreshTime
        val delay = if (coast > SHOW_PULLREFRESH_MIN_TIME) 0L else SHOW_PULLREFRESH_MIN_TIME - coast
        Log.d(TAG, "checkNeedShowDelay : data size=${data?.size}  data = $data")
        mUiHandler?.postDelayed({
            setUiData(data)
            if (data != null && data.size != 0) {
                checkChangeToSelectMode()
            } else {
                resetIsChangeToSelectedMode()
            }
            mUiHandler?.sendEmptyMessage(MSG_END_REFRESH)
        }, delay)
    }

    /**
     * 检测是否转换成编辑模式，从标签文件列表页面跳转到此页面时需要切换
     */
    private fun checkChangeToSelectMode() {
        Log.d(TAG, "checkChangeToSelectMode")
        if (isChangeToSelectMode) {
            resetIsChangeToSelectedMode()
            //变更成编辑状态，在加载完数据并显示数据后
            changeListMode(LIST_SELECTED_MODE)
        }
    }

    /**
     * 跳转到编辑模式后，复位
     */
    fun resetIsChangeToSelectedMode() {
        isChangeToSelectMode = false
        PreferencesUtils.put(key = Constants.CHANGE_TO_SELECT_MODE, value = false)
    }

    private fun traversingOneGroup(
        recentList: MutableList<BaseRecentEntity>,
        item: ExpandGroupItemEntity?,
        groupItem: RecentGroupEntity?,
        group: Int,
        inputCount: Int
    ): Int {
        var count = inputCount
        if ((item != null) && (groupItem != null) && groupItem.mIsExpand) {
            //sub folder and files
            for (posi in item.getChildList().indices) {
                val file = item.getChildList()[posi]
                file?.apply {
                    file.setmViewType(BaseRecentEntity.VIEW_TYPE_ITEM_FILE)
                    file.mGroupPosition = group
                    recentList.add(file)
                    count++
                }
            }
        }
        return count
    }

    fun shareFile(fragment: Fragment, rect: Rect?) {
        fragment.activity?.let { activity ->
            val shareBean =
                FileActionShare.FileActionShareBean(fragment, getSelectItems(), activity, rect)
            FileActionShare(shareBean).execute(object : FileShareObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    Log.d(TAG, "shareFile onActionDone $result $data ")
                }
            })
        }
    }

    fun copyFile(fragment: Fragment, path: String) {
        val activity = fragment.activity ?: return
        uiState.value?.selectedList?.let {
            val selectBaseFileBean = getSelectItems()
            FileActionCopy(fragment, selectBaseFileBean, PathFileWrapper(path)).execute(object : FileCopyObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    Log.d(TAG, "copyFile onActionDone ${result};${data} ")
                }

                override fun onActionCancelled() {
                    operateActionImpl.onCopyCancelled(path, selectBaseFileBean)
                }
            })
        }
    }

    fun cutFile(fragment: Fragment, path: String, dragToCut: Boolean) {
        val activity = fragment.activity ?: return
        uiState.value?.selectedList?.let {
            val selectBaseFileBean = getSelectItems()
            val cutAction = FileActionCut(fragment, selectBaseFileBean, PathFileWrapper(path), CATEGORY_RECENT)
            cutAction.dragToCut = dragToCut
            cutAction.execute(object : FileCutObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    operateActionImpl.onCutDone(result, data, selectBaseFileBean, dragToCut)
                }

                override fun onActionCancelled() {
                    operateActionImpl.onCutCancelled(path, selectBaseFileBean)
                }
            })
        }
    }

    @Suppress("UnsafeCast")
    fun deleteFile(activity: BaseVMActivity) {
        uiState.value?.selectedList?.let {
            val selectBaseFileBean = getSelectItems()
            val recycleBinAction = Injector.injectFactory<IRecycleBin>()
            mOperateAction = recycleBinAction?.getFileActionDelete(
                activity,
                selectBaseFileBean,
                selectBaseFileBean.size == uiState.value?.mAllEntityList?.size,
                CATEGORY_RECENT
            )

            mOperateAction?.execute(object : IFileActionObserver {
                override fun onActionDone(result: Boolean, data: Any?) {
                    (activity as? MainActivity)?.refreshSideNavigationData()
                    operateActionImpl.onDeleteDone(result, data, selectBaseFileBean)
                }

                override fun onActionCancelled() {
                }

                override fun onActionReloadData() {
                    operateActionImpl.onDeleteReload(selectBaseFileBean)
                }

                override fun onActionReShowDialog() {
                }

                override fun isShowDialog(): Boolean {
                    return false
                }
            }) ?: Log.w(TAG, "onDelete failed: action get null")
        }
    }

    fun doEncryption(mActivity: ComponentActivity) {
        fun doFileActionEncrypt(activity: EncryptActivity, service: FileEncryptController.FileManagerEncryptionInterface) {
            uiState.value?.selectedList?.also {
                Log.d(TAG, "onClickEncryption selectedList.size=${it.size}")
                val selectBaseFileBean = <EMAIL>()
                Log.d(TAG, "onClickEncryption selectBaseFileBean.size=${selectBaseFileBean.size} ")
                FileActionEncrypt(activity, service, selectBaseFileBean).execute(object : FileSecurityEncryptObserver(mActivity) {
                    override fun onActionDone(result: Boolean, data: Any?) {
                        Log.d(TAG, "recent page onEncrypt result $result")
                        if (result) {
                            operateActionImpl
                                .onEncryptDone(mActivity, result, data, selectBaseFileBean)
                        } else {
                            Log.d(TAG, "onActionDone result=false refresh data")
                            /**
                             * 折叠屏小屏，最近页连续操作加密（一个20GB以上文件，一个20GB以下文件）部分成功部分失败场景下
                             * 调用这里刷新时，偶尔判断文件还存在，刷新不成功，所以延迟200毫秒（参考分批加密时也是延时200毫秒）
                             */
                            mUiHandler?.postDelayed({
                                moveDeleteCompleteRefreshUI(selectBaseFileBean, true)
                            }, DELAYED_TIME)
                        }
                    }

                    override fun onActionCancelled() {
                        operateActionImpl.onEncryptCancelled(selectBaseFileBean)
                    }
                })
            }
        }
        if (EncryptUtils.checkPrivateSafeEnabled(mActivity).not()) return
        if (mActivity is EncryptActivity) {
            mActivity.getFileEncryptController()?.runEncryptTask(object : FileEncryptController.ServiceConnectedCallback {
                override fun onServiceConnected(service: FileEncryptController.FileManagerEncryptionInterface) {
                    if (mActivity.isInvalid().not()) {
                        doFileActionEncrypt(mActivity, service)
                    }
                }
            })
        } else {
            Log.d(TAG, "onClickEncryption not EncryptActivity")
        }
    }

    fun doRename(mActivity: ComponentActivity) {
        getSelectItems().let {
            if (it.size == 1) {
                mAction = FileActionRename(mActivity, it[0]).execute(object :
                    FileRenameObserver(mActivity) {
                    override fun onActionDone(result: Boolean, data: Any?) {
                        operateActionImpl.onRenameDone(mActivity, result, data, it)
                    }

                    override fun onActionCancelled() {
                    }
                })
            }
        }
    }

    fun doDecompressFile(activity: ComponentActivity, destPath: String?, fileName: String?): Boolean {
        getSelectItems().let {
            if ((it.size == 1) && (it[0].mLocalType == MimeTypeHelper.COMPRESSED_TYPE)) {
                var dir = destPath
                if (destPath.isNullOrEmpty()) {
                    dir = it[0].mData?.let { it1 -> File(it1).parent }
                }
                if (dir.isNullOrEmpty()) {
                    Log.d(TAG, "onDecompress failed: dir is null or empty.")
                    return false
                }
                mAction = FileActionDecompress(
                    lifecycle = activity,
                    sourceFile = it[0],
                    destParentFile = PathFileWrapper(dir),
                    needShowCheckButton = true,
                    decompressFileName = fileName
                ).execute(
                    object : FileDecompressObserver(activity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            operateActionImpl.onDecompressDone(result)
                        }

                        override fun onActionCancelled() {
                            Log.d(TAG, "onDecompress onActionCancelled ")
                        }

                        override fun onActionReloadData() {
                            Log.d(TAG, "onDecompress onActionCancelled ")
                        }
                    })
                mCompressFile = null
            }
        }

        if (mCompressFile != null) {
            mCompressFile!!.let {
                var dir = destPath
                if (destPath.isNullOrEmpty()) {
                    dir = it.mData?.let { it1 -> File(it1).parent }
                }
                if (dir.isNullOrEmpty()) {
                    Log.d(TAG, "onDecompress failed: dir is null or empty.")
                    return false
                }
                FileActionDecompress(
                    lifecycle = activity,
                    sourceFile = it,
                    destParentFile = PathFileWrapper(dir!!),
                    needShowCheckButton = true,
                    decompressFileName = fileName
                ).execute(
                    object : FileDecompressObserver(activity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            operateActionImpl.onDecompressDone(result)
                        }

                        override fun onActionCancelled() {
                            Log.d(TAG, "onDecompress onActionCancelled ")
                        }

                        override fun onActionReloadData() {
                            Log.d(TAG, "onDecompress onActionCancelled ")
                        }
                    })
                mCompressFile = null
            }
        }
        return true
    }

    fun doCompress(activity: ComponentActivity, destPath: String, fileName: String): Boolean {
        val selectFiles = getSelectItems()
        if (selectFiles.isEmpty()) {
            return true
        }
        var dir = destPath
        if (destPath.isNullOrEmpty() && selectFiles[0].mData.isNullOrEmpty().not()) {
            dir = selectFiles[0].mData.let { File(it).parent }
        }
        if (dir.isNullOrEmpty()) {
            Log.d(TAG, "onCompress failed: dir is null or empty.")
            return false
        }
        mAction = FileActionCompress(activity, selectFiles, PathFileWrapper(dir), fileName).execute(object :
            FileCompressObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }

            override fun onActionCancelled() {
            }

            override fun onActionReloadData() {
            }
        })
        return true
    }

    fun doShowDetail(mActivity: ComponentActivity) {
        fileDetailObserver = FileDetailObserver(mActivity)
        uiState.value?.selectedList?.let {
            FileActionDetail(mActivity, getSelectItems()).execute(fileDetailObserver!!)
        }
        StatisticsUtils.nearMeStatistics(mActivity, StatisticsUtils.DETAIL_ACTION)
    }

    fun onConfigurationChanged(newConfig: Configuration?) {
        fileDetailObserver?.onConfigurationChanged(newConfig)
    }


    override fun onCleared() {
        super.onCleared()
        Log.e(TAG, "onCleared")
        mRecentFileObserver.destroy()
    }

    fun releaseObserver() {
        fileDetailObserver?.release()
    }

    fun doOtherOpen(mActivity: ComponentActivity) {
        uiState.value?.selectedList?.let {
            if (it.size == 1) {
                val baseFile = getSelectItems()[0]
                val mediaImgIds = getRecentMediaImgIds(baseFile)
                FileActionOpen.Companion.Builder(mActivity, baseFile)
                    .setIsOpenByOtherWay(true)
                    .setIsFromRecent(true)
                    .setOrderMediaIdList(mediaImgIds)
                    .build()
                    .execute(object : FileOpenObserver(mActivity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            operateActionImpl
                                .onOpenByOtherDone(mActivity, result, baseFile, getSelectItems())
                        }
                    })
            }
        }
    }

    fun doClickCloudDisk(mActivity: ComponentActivity) {
        uiState.value?.selectedList?.let {
            val selectItems = getSelectItems()
            FileActionCloudDriver(mActivity, selectItems, mActivity).execute(object : FileCloudDriverObserver(mActivity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    operateActionImpl.onUploadCloudDiskDone(mActivity, selectItems)
                }
            })
        }
    }

    fun createShortCut(mActivity: ComponentActivity) {
        uiState.value?.selectedList?.let {
            val selectItems = getSelectItems()
            if (selectItems.isNotEmpty()) {
                mActivity.lifecycleScope.launch(Dispatchers.IO) {
                    ShortCutUtils.createShortCut(mActivity, selectItems[0], object : ShortCutUtils.ShortCutCallback {
                            override fun result(success: Boolean) {
                                Log.d(TAG, "createShortCut success $success")
                                launch(Dispatchers.Main) {
                                    if (success) {
                                        CustomToast.showShort(R.string.toast_create_shortcut_success)
                                        changeListMode(KtConstants.LIST_NORMAL_MODE)
                                    } else {
                                        CustomToast.showShort(R.string.toast_create_shortcut_repeat)
                                    }
                                }
                                OptimizeStatisticsUtil.shortcutEvent(
                                    StatisticsUtils.SHORTCUT_OPE_ADD,
                                    selectItems[0].mLocalType.toString(),
                                    OptimizeStatisticsUtil.getOptionPage(mActivity, "")
                                )
                            }
                        })
                }
            }
        }
    }

    private fun moveDeleteCompleteRefreshUI(selectList: List<BaseFileBean>) {
        moveDeleteCompleteRefreshUI(selectList, false)
    }

    private fun moveDeleteCompleteRefreshUIImmediately(selectList: List<BaseFileBean>) {
        moveDeleteCompleteRefreshUI(selectList, false, Dispatchers.Main)
    }

    private fun moveDeleteCompleteRefreshUI(
        selectList: List<BaseFileBean>,
        isKeepSelectionMode: Boolean = false,
        refreshUICoroutine: CoroutineDispatcher = Dispatchers.IO
    ) {
        launch {
            withContext(Dispatchers.IO) {
                Log.d(TAG, "moveDeleteCompleteRefreshUI start")
                for (fileBean in selectList) {
                    if (!JavaFileHelper.exists(fileBean)) {
                        removeAllSelectDataIfNotExist(fileBean)
                    } else {
                        Log.d(TAG, "deleteFile file is null or not update")
                    }
                }
                setUiData(uiState.value?.mSourceList, refreshUICoroutine)
                Log.d(TAG, "moveDeleteCompleteRefreshUI end")
            }

            if (!isKeepSelectionMode) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
        }
    }

    private fun renameSelectData(it: MutableList<ExpandGroupItemEntity>, path: String, newPath: String): Boolean {
        for (groupItemEntity in it) {
            val iterator = groupItemEntity.getChildList().iterator()
            while (iterator.hasNext()) {
                val fileEntity = iterator.next() ?: continue
                if (fileEntity.mAbsolutePath == path) {
                    fileEntity.mAbsolutePath = newPath
                    fileEntity.mDisplayName = FilenameUtils.getName(newPath)
                    Log.i(TAG, "renameSelectData ")
                    if (isHiddenFile(fileEntity.mDisplayName)) {
                        iterator.remove()
                        // remove file in mSourceList, should mark data has changed
                        mLocalDataHasChanged = true
                    }
                    return true
                }
            }
        }
        it.removeIf { item -> item.getChildList().isEmpty() }
        return false
    }

    private fun removeAllSelectDataIfNotExist(baseFileBean: BaseFileBean) {
        uiState.value?.mSourceList?.let {
            for (groupItemEntity in it) {
                val iterator = groupItemEntity.getChildList().iterator()
                while (iterator.hasNext()) {
                    val item = iterator.next() ?: continue
                    if (item.mAbsolutePath == baseFileBean.mData) {
                        iterator.remove()
                        mLocalDataHasChanged = true
                    }
                }
                groupItemEntity.mParent?.mChildSize = groupItemEntity.getChildList().size
            }
            it.removeIf { item -> item.getChildList().isEmpty() }
        }
    }

    override fun getRealFileSize(): Int {
        return uiState.value?.mAllEntityList?.size ?: 0
    }

    override fun loadData() {
        Log.d(TAG, "Selection View Model loaddata")
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mBrowseModeState.value == KtConstants.SCAN_MODE_GRID) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        }
    }

    fun refresh() {
        setUiData(data = uiState.value?.mSourceList)
    }

    fun updateRecentContentEntitySelectStatus(selected: Boolean, keys: ArrayList<Int>?) {
        uiState.value?.apply {
            keys?.let {
                if (isInSelectMode()) {
                    if (selected) {
                        for (key in it) {
                            if (!selectedList.contains(key)) {
                                selectedList.add(key)
                            }
                        }
                    } else {
                        for (key in it) {
                            if (selectedList.contains(key)) {
                                selectedList.remove(key)
                            }
                        }
                    }
                    uiState.value = uiState.value
                }
            }
        }
    }

    fun onConfigurationChanged() {
        mAction?.reShowDialog()
        mOperateAction?.hideDialog()
    }

    fun clickScanModeItem() {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            setScanMode(KtConstants.SCAN_MODE_GRID)
        } else {
            setScanMode(KtConstants.SCAN_MODE_LIST)
        }
    }

    fun setScanMode(mode: Int) {
        mBrowseModeState.value = mode
        ConfigSharedPreferenceUtils.putInt(RECENT_SCAN_MODE_SP_KEY, mode)
    }

    class MainRecentLoadCallback(viewModel: MainRecentViewModel) : RecentLoadCallback {
        private var mViewModel = WeakReference(viewModel)

        override fun loadSucc(type: Int, data: MutableList<ExpandGroupItemEntity>?) {
            val viewModel = mViewModel.get()
            if (viewModel != null) {
                viewModel.mLoadType = type
                viewModel.mLocalDataHasChanged = false
                when (type) {
                    RecentDataHelper.TYPE_CACHE -> {
                        viewModel.mUiHandler?.removeMessages(MSG_DELAY_SHOW_CACHE_LOAD_TIP)
                        viewModel.mCurrentVisiblePosition = 0
                        viewModel.setUiData(data)
                        viewModel.mUiHandler?.sendEmptyMessage(MSG_PULL_REFRESH_TYPE)
                    }
                    RecentDataHelper.TYPE_MEDIA -> {
                        Log.d(TAG, "loadSuccess put new files refresh last time")
                        PreferencesUtils.put(key = Constants.KEY_NEW_FILES_REFRESH_LAST_TIME, value = System.currentTimeMillis())
                        viewModel.mCurrentVisiblePosition = 0
                        viewModel.checkNeedShowDelay(data)
                    }
                    RecentDataHelper.TYPE_LOCAL -> {
                        viewModel.setUiData(data)
                    }
                }
            } else {
                Log.d(TAG, "loadSucc viewModel is null")
            }
        }

        override fun loadFail(type: Int, msgObj: Any?) {
            val viewModel = mViewModel.get()
            if (viewModel != null) {
                viewModel.mLoadType = type
                viewModel.mLocalDataHasChanged = false
                when (type) {
                    RecentDataHelper.TYPE_CACHE -> {
                        viewModel.mUiHandler?.removeMessages(MSG_DELAY_SHOW_CACHE_LOAD_TIP)
                        viewModel.mUiHandler?.sendEmptyMessage(MSG_PULL_REFRESH_TYPE)
                    }
                    RecentDataHelper.TYPE_MEDIA -> {
                        viewModel.mCurrentVisiblePosition = 0
                        viewModel.checkNeedShowDelay(null)
                    }
                    RecentDataHelper.TYPE_LOCAL -> {
                    }
                }
            }
        }

        override fun loadInvalid() {
            Log.i(TAG, "loadInvalid ${mViewModel.get()?.mLoadType}")
        }
    }

    class RecentUiModel(sourceList: MutableList<ExpandGroupItemEntity>, fileList: List<BaseRecentEntity>,
                        allEntityList: MutableList<RecentFileEntity>, stateModel: BaseStateModel,
                        selectedList: ArrayList<Int> = arrayListOf(), keyMap: HashMap<Int, BaseRecentEntity> = hashMapOf()) :
            BaseUiModel<BaseRecentEntity>(fileList, stateModel, selectedList, keyMap) {
        val mSourceList: MutableList<ExpandGroupItemEntity> = sourceList
        val mAllEntityList: MutableList<RecentFileEntity> = allEntityList
        var mIsFromSetUiData: Boolean = false
    }

    fun changeToSelectedMode() {
        isChangeToSelectMode = PreferencesUtils.getBoolean(key = Constants.CHANGE_TO_SELECT_MODE)
    }

    private inner class RecentOperateActionImpl {

        fun onDeleteDone(result: Boolean, data: Any?, selectBaseFileBean: List<BaseFileBean>) {
            Log.d(TAG, "deleteFile onActionDone $result;$data ")
            moveDeleteCompleteRefreshUIImmediately(selectBaseFileBean)
        }

        fun onCutDone(result: Boolean, data: Any?, selectBaseFileBean: List<BaseFileBean>, dragToCut: Boolean = false) {
            Log.d(TAG, "cutFile onActionDone $result;$data")
            moveDeleteCompleteRefreshUI(selectBaseFileBean, isKeepSelectionMode = dragToCut)
        }

        fun onDecompressDone(result: Boolean) {
            Log.d(TAG, "onDecompress onActionDone $result")
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }

        fun onOpenByOtherDone(
            context: Context,
            result: Boolean,
            baseFile: BaseFileBean,
            selectBaseFileBean: List<BaseFileBean>
        ) {
            if (result) {
                RecentDataStorage.getInstance().openFile = baseFile
            }
            val info = OptimizeStatisticsUtil.OperationInfo(
                selectBaseFileBean.size.toString(),
                OptimizeStatisticsUtil.OP_OPEN_WITH,
                Utils.getDateAndTimeFormat(context, System.currentTimeMillis()),
                "",
                selectBaseFileBean
            )
            OptimizeStatisticsUtil.allOperation(info)
        }

        fun onUploadCloudDiskDone(context: Context, selectBaseFileBean: List<BaseFileBean>) {
            val info = OptimizeStatisticsUtil.OperationInfo(
                selectBaseFileBean.size.toString(),
                OptimizeStatisticsUtil.OP_UPLOAD_CLOUD,
                Utils.getDateAndTimeFormat(context, System.currentTimeMillis()),
                "",
                selectBaseFileBean
            )
            OptimizeStatisticsUtil.allOperation(info)
        }

        fun onRenameDone(
            context: Context,
            result: Boolean,
            data: Any?,
            selectBaseFileBean: List<BaseFileBean>
        ) {
            Log.d(TAG, "onClickRename onActionDone $result;$data")
            if (result && (data is Pair<*, *>)) {
                uiState.value?.mSourceList?.run {
                    launch {
                        withContext(Dispatchers.IO) {
                            mLocalDataHasChanged = renameSelectData(
                                uiState.value?.mSourceList ?: mutableListOf(),
                                selectBaseFileBean[0].mData ?: "",
                                (data.first as? String) ?: ""
                            )
                            setUiData(uiState.value?.mSourceList)
                        }
                        changeListMode(KtConstants.LIST_NORMAL_MODE)
                    }
                }
            }
            val info = OptimizeStatisticsUtil.OperationInfo(
                selectBaseFileBean.size.toString(),
                OptimizeStatisticsUtil.OP_RENAME,
                Utils.getDateAndTimeFormat(context, System.currentTimeMillis()),
                "",
                selectBaseFileBean
            )
            OptimizeStatisticsUtil.allOperation(info)
        }

        fun onEncryptDone(
            context: Context,
            result: Boolean,
            data: Any?,
            selectBaseFileBean: List<BaseFileBean>
        ) {
            Log.d(TAG, "onClickEncryption onActionDone $result;$data")
            moveDeleteCompleteRefreshUI(selectBaseFileBean)
            val info = OptimizeStatisticsUtil.OperationInfo(
                selectBaseFileBean.size.toString(),
                OptimizeStatisticsUtil.OP_ENCRYPT,
                Utils.getDateAndTimeFormat(context, System.currentTimeMillis()),
                "",
                selectBaseFileBean
            )
            OptimizeStatisticsUtil.allOperation(info)
        }

        fun onDeleteReload(selectBaseFileBean: List<BaseFileBean>) {
            moveDeleteCompleteRefreshUI(selectBaseFileBean, true)
        }

        fun onCutCancelled(path: String, selectBaseFileBean: List<BaseFileBean>) {
            val list = mutableListOf<BaseFileBean>()
            list.addAll(selectBaseFileBean)
            selectBaseFileBean.forEach {
                val displayName = it.mDisplayName
                if (!displayName.isNullOrEmpty()) {
                    list.add(PathFileWrapper(File(path, displayName).absolutePath))
                }
            }
            moveDeleteCompleteRefreshUI(list)
        }

        fun onCopyCancelled(path: String, selectBaseFileBean: List<BaseFileBean>) {
            val list = mutableListOf<BaseFileBean>()
            selectBaseFileBean.forEach {
                val displayName = it.mDisplayName
                if (!displayName.isNullOrEmpty()) {
                    list.add(PathFileWrapper(File(path, displayName).absolutePath))
                }
            }
            moveDeleteCompleteRefreshUI(list)
        }

        fun onRenameCancelled() {
            if (!isPreviewOpen) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
        }

        fun onEncryptCancelled(selectBaseFileBean: List<BaseFileBean>) {
            Log.d(TAG, "onClickEncryption onActionCancelled")
            moveDeleteCompleteRefreshUI(selectBaseFileBean, true)
        }

        fun onCreateShortCutEnd() {
            if (!isPreviewOpen) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
        }
    }

    private class RecentOperatorListener(
        private val recentBridge: IFileOperate.IRecentOperateBridge,
        private val actionImpl: RecentOperateActionImpl
    ) : IFileOperate.RecentOperatorListener {

        private val selectItems: List<BaseFileBean>
            get() = recentBridge.getSelectFileItems()

        private val context: Context
            get() = recentBridge.getOperateContext()

        override fun onActionDone(opType: Int, result: Boolean, data: Any?): Unit = actionImpl.run {
            when (opType) {
                IFileOperate.OP_DELETE_TO_RECYCLE -> onDeleteDone(result, data, selectItems)
                IFileOperate.OP_CUT -> onCutDone(result, data, selectItems)
                IFileOperate.OP_DECOMPRESS -> onDecompressDone(result)
                IFileOperate.OP_OPEN_BY_OTHER -> onOpenByOtherDone(context, result, selectItems[0], selectItems)
                IFileOperate.OP_UPLOAD_CLOUD_DISK -> onUploadCloudDiskDone(context, selectItems)
                IFileOperate.OP_RENAME -> onRenameDone(context, result, data, selectItems)
                IFileOperate.OP_ENCRYPT -> onEncryptDone(context, result, data, selectItems)
            }
        }

        override fun onActionReloadData(opType: Int): Unit = actionImpl.run {
            when (opType) {
                IFileOperate.OP_DELETE_TO_RECYCLE -> onDeleteReload(selectItems)
            }
        }

        override fun onActionCancelled(opType: Int): Unit = actionImpl.run {
            when (opType) {
                IFileOperate.OP_CUT -> onCutCancelled(recentBridge.getCutDestPath(), selectItems)
                IFileOperate.OP_COPY -> onCopyCancelled(recentBridge.getCopyDestPath(), selectItems)
                IFileOperate.OP_RENAME -> onRenameCancelled()
                IFileOperate.OP_ENCRYPT -> onEncryptCancelled(selectItems)
                IFileOperate.OP_CREATE_SHORTCUT -> onCreateShortCutEnd()
            }
        }
    }

    fun reset() {
        Log.d(TAG, "reset ViewModel")
        mNormalLoad = false
        mCurrentVisiblePosition = 0
        mLastAutoLoadTime = -MainRecentFragment.AUTO_LOAD_MIN_TIME_INTERVAL
        mModeState.listModel.value = KtConstants.LIST_NORMAL_MODE
        uiState.value = RecentUiModel(
            mutableListOf(),
            mutableListOf(),
            mutableListOf(),
            mModeState,
            arrayListOf(),
            hashMapOf()
        )
    }

    fun refreshDataAfterDragOut(context: Context?, dragOperate: String?) {
        Log.d(TAG, "refreshAfterDragOut dragOperate $dragOperate")
        if (dragOperate == KtConstants.DRAG_OP_DELETE_TO_RECYCLE_BIN) {
            uiState.value?.selectedList?.let {
                val selectBaseFileBean = getSelectItems()
                operateActionImpl.onDeleteDone(true, null, selectBaseFileBean)
            }
            return
        }

        if (dragOperate == KtConstants.DRAG_OP_CUT) {
            uiState.value?.selectedList?.let {
                val selectBaseFileBean = getSelectItems()
                operateActionImpl.onCutDone(true, null, selectBaseFileBean, true)
            }
            return
        }

        if (dragOperate == KtConstants.DRAG_OP_ENCRYPT) {
            uiState.value?.selectedList?.let {
                val selectBaseFileBean = getSelectItems()
                context?.let {
                    operateActionImpl.onEncryptDone(context, true, null, selectBaseFileBean)
                }
            }
        }
    }
}