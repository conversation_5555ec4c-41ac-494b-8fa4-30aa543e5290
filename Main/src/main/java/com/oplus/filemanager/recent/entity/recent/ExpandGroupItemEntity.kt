/***********************************************************
 * * Copyright (C), 2008- Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:com.coloros.filemanager.view.main.recent.entity/ExpandGroupItemEntity.java
 * * Description:Group's Title And Group's Folder entity
 * * Version:
 * * Date :2019/7/22
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version>    <desc>
 * *
</desc></version></data></author> */
package com.oplus.filemanager.recent.entity.recent

import java.util.*

class ExpandGroupItemEntity : BaseRecentEntity() {
    /**
     * Group Title
     */
    var mParent: RecentGroupEntity? = null

    /**
     * Group Folder List
     */
    private val mChildList: MutableList<RecentFileEntity> = ArrayList()
    val isEmpty: Boolean
        get() = mChildList.isEmpty()

    fun getChildList(): MutableList<RecentFileEntity> {
        return mChildList
    }

    fun setChildList(childList: List<RecentFileEntity>) {
        mChildList.clear()
        mChildList.addAll(childList)
    }

    override fun equals(other: Any?): Boolean {
        if (super.equals(other).not()) {
            return false
        }
        if (javaClass != other?.javaClass) return false
        other as ExpandGroupItemEntity
        return mParent == other.mParent
    }
}