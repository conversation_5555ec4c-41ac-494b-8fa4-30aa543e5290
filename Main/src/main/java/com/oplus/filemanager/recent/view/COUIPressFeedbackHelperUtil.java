/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : COUIPressFeedbackHelperUtil
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/12       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.recent.view;

import android.animation.PropertyValuesHolder;
import android.animation.ValueAnimator;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.PathInterpolator;

import com.coui.appcompat.animation.COUIInEaseInterpolator;
import com.coui.appcompat.animation.COUIMoveEaseInterpolator;
import com.support.appcompat.R;

public class COUIPressFeedbackHelperUtil {
    public static final int CARD_PRESS_FEEDBACK = 0;
    public static final int BORDERLESS_BUTTON_PRESS_FEEDBACK = 1;
    public static final int FILL_BUTTON_PRESS_FEEDBACK = 2;
    public static final int UNJUMPABLE_CARD_PRESS_FEEDBACK = 3;

    public static final long DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION = 200L;
    public static final long DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION = 340L;

    public static final float DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE = 0.92f;
    public static final float DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE = 1f;
    public static final float DEFAULT_BRIGHTNESS_MAX_VALUE = 0.8f;
    public static final float DEFAULT_ALPHA = 1f;
    public static final float PRESSED_ALPHA = 0.5f;
    public static final float DEFAULT_FILL_ALPHA = 0f;

    public static final PathInterpolator PRESS_FEEDBACK_INTERPOLATOR = new COUIMoveEaseInterpolator();
    public static final PathInterpolator RELEASE_FEEDBACK_INTERPOLATOR = new COUIInEaseInterpolator();

    private ValueAnimator mScaleAnimator;
    private float mCurrentScale = 1;
    private float mCurrentBrightness = 1.0f;
    private float mCurrentBlackAlpha = 0.0f;
    private boolean mIsNeedToDelayCancelScaleAnim = false;
    private int mFeedbackType;
    private View mView;
    private float mScaleEndValue = DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE;
    private float mCurrentAlpha = 0f;
    private float mPressedFillAlpha;
    private float mMaxEndValue = 0.98f;
    private float mMinEndValue = 0.94f;
    private float mMaxEndValueSize;
    private float mMinEndValueSize;

    public COUIPressFeedbackHelperUtil(View v, int type) {
        mFeedbackType = type;
        mView = v;
        TypedValue outValue = new TypedValue();
        mView.getContext().getResources().getValue(R.dimen.button_fill_alpha, outValue, true);
        mPressedFillAlpha = outValue.getFloat();
        int maxWidth = mView.getContext().getResources().getDimensionPixelOffset(R.dimen.coui_max_end_value_width);
        int maxHeight = mView.getContext().getResources().getDimensionPixelOffset(R.dimen.coui_max_end_value_height);
        int minSize = mView.getContext().getResources().getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_10dp);
        mMaxEndValueSize = maxWidth * maxHeight;
        mMinEndValueSize = minSize * minSize;
    }

    public void executeFeedbackAnimator(final boolean isPressed) {
        mIsNeedToDelayCancelScaleAnim = false;
        long duration = 0;
        float brightValue = 1f;
        float alphaValue = 1f;
        float startAlpha = 1f;
        float startBlackAlpha = 0f;
        TypedValue outValue = new TypedValue();
        mView.getContext().getResources().getValue(R.dimen.coui_button_press_black_alpha, outValue, true);
        float blackAlphaValue = outValue.getFloat();
        switch (mFeedbackType) {
            case CARD_PRESS_FEEDBACK:
                duration = isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION : DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION;
                mScaleEndValue = calculateScaleEndValue(mView.getWidth(), mView.getHeight());
                break;
            case BORDERLESS_BUTTON_PRESS_FEEDBACK:
                duration = isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION : DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION;
                alphaValue = mPressedFillAlpha;
                startAlpha = DEFAULT_FILL_ALPHA;
                if (isPressed) {
                    mCurrentAlpha = 0f;
                }
                break;
            case FILL_BUTTON_PRESS_FEEDBACK:
                duration = isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION : DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION;
                brightValue = DEFAULT_BRIGHTNESS_MAX_VALUE;
                break;
            case UNJUMPABLE_CARD_PRESS_FEEDBACK:
                duration = isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION : DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION;
                alphaValue = PRESSED_ALPHA;
                startAlpha = DEFAULT_ALPHA;
                if (isPressed) {
                    mCurrentAlpha = 1f;
                }
                mScaleEndValue = calculateScaleEndValue(mView.getWidth(), mView.getHeight());
                break;
            default:
                break;
        }

        cancelAnimator();
        if (mIsNeedToDelayCancelScaleAnim) {
            return;
        }
        setScaleAnimator(isPressed, brightValue, startAlpha, alphaValue, blackAlphaValue, startBlackAlpha);
        handleAnimator(isPressed, duration);
    }

    private void setScaleAnimator(boolean isPressed,
                                  float brightValue,
                                  float startAlpha,
                                  float alphaValue,
                                  float blackAlphaValue,
                                  float startBlackAlpha) {
        PropertyValuesHolder narrowHolderFont = PropertyValuesHolder.ofFloat(
                "scaleHolder",
                isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE : mCurrentScale,
                isPressed ? mScaleEndValue : DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE);
        PropertyValuesHolder brightnessHolder = PropertyValuesHolder.ofFloat(
                "brightnessHolder",
                isPressed ? DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE : mCurrentBrightness,
                isPressed ? brightValue : DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE);
        PropertyValuesHolder alphaHolder = PropertyValuesHolder.ofFloat(
                "alphaHolder",
                isPressed ? startAlpha : mCurrentAlpha,
                isPressed ? alphaValue : startAlpha);
        PropertyValuesHolder blackAlphaHolder = PropertyValuesHolder.ofFloat(
                "blackAlphaHolder",
                isPressed ? startBlackAlpha : mCurrentBlackAlpha,
                isPressed ? blackAlphaValue : startBlackAlpha);
        mScaleAnimator = ValueAnimator.ofPropertyValuesHolder(narrowHolderFont, brightnessHolder, alphaHolder, blackAlphaHolder);
    }

    private void handleAnimator(Boolean isPressed, long duration) {
        mScaleAnimator.setInterpolator(isPressed ? PRESS_FEEDBACK_INTERPOLATOR : RELEASE_FEEDBACK_INTERPOLATOR);
        mScaleAnimator.setDuration(duration);
        mScaleAnimator.addUpdateListener(
                animator -> {
                    mCurrentScale = (Float) animator.getAnimatedValue("scaleHolder");
                    mCurrentBrightness = (Float) animator.getAnimatedValue("brightnessHolder");
                    mCurrentAlpha = (Float) animator.getAnimatedValue("alphaHolder");
                    mCurrentBlackAlpha = (Float) animator.getAnimatedValue("blackAlphaHolder");
                    setScale(mCurrentScale, mView, mScaleEndValue);
                    setAlpha(mCurrentAlpha, mView);
                });
        mScaleAnimator.start();
    }

    public void refresh() {
        TypedValue outValue = new TypedValue();
        mView.getContext().getResources().getValue(R.dimen.button_fill_alpha, outValue, true);
        mPressedFillAlpha = outValue.getFloat();
    }

    private void setScale(float scaleValue, View view, float endValue) {
        float scaleValueResult = Math.max(endValue, Math.min(DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE, scaleValue));
        view.setScaleX(scaleValueResult);
        view.setScaleY(scaleValueResult);
        view.invalidate();
    }

    private void setAlpha(float alpha, View view) {
        if (alpha != view.getAlpha() && mFeedbackType != BORDERLESS_BUTTON_PRESS_FEEDBACK) {
            view.setAlpha(alpha);
        }
    }

    private void cancelAnimator() {
        if (mScaleAnimator != null && mScaleAnimator.isRunning()) {
            mScaleAnimator.cancel();
        }
    }

    private float calculateScaleEndValue(int width, int height) {
        float endValue = mMaxEndValue + (width * height - mMaxEndValueSize)
                * (mMaxEndValue - mMinEndValue) / (mMaxEndValueSize - mMinEndValueSize);
        if (width * height < mMinEndValueSize) {
            return 1;
        }
        if (width * height > mMaxEndValueSize) {
            return mMaxEndValue;
        }
        return endValue;
    }
}
