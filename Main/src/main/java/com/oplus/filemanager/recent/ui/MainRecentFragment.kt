/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main.recent
 * * Version     : 1.0
 * * Date        : 2020/6/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.recent.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import android.util.Size
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewStub
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.rotateview.COUIRotateView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.MoreItemClickListener
import com.filemanager.common.controller.MoreItemController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DefaultSelectDelegate
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.DarkThemeLevelConfig
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.CoroutineUtil
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.OptimizeStatisticsUtil.RECENT
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StaticHandler
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.view.MiddleMultilineTextView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.fileoperate.compress.CompressConfirmDialog
import com.filemanager.fileoperate.compress.CompressConfirmDialog.Companion.DEFAULT_SAVE_PATH
import com.google.android.material.appbar.COUICollapsableAppBarLayout
import com.google.android.material.appbar.COUICollapsableAppBarLayout.MODE_COLLAPSABLE
import com.google.android.material.appbar.COUICollapsableAppBarLayout.MODE_FIXED_EXPANDED
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.RecycleSelectionBuilder
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareFragmentSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.behavior.PrimaryTitleBehavior
import com.oplus.filemanager.main.ui.BaseMainFragment
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.MainActivity.Companion.INDEX_RECENT
import com.oplus.filemanager.recent.adapter.MainRecentAdapter
import com.oplus.filemanager.recent.adapter.viewholder.BaseRecentFileVH
import com.oplus.filemanager.recent.decoration.RecentSpacesItemDecoration
import com.oplus.filemanager.recent.entity.recent.BaseRecentEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.entity.recent.RecentGroupEntity
import com.oplus.filemanager.recent.ui.MainRecentViewModel.Companion.MSG_DELAY_SHOW_CACHE_LOAD_TIP
import com.oplus.filemanager.recent.ui.MainRecentViewModel.Companion.MSG_END_REFRESH
import com.oplus.filemanager.recent.ui.MainRecentViewModel.Companion.MSG_PULL_REFRESH_TYPE
import com.oplus.filemanager.recent.ui.MainRecentViewModel.Companion.MSG_UPDATE_FOLDER_SELECT
import com.oplus.filemanager.recent.ui.MainRecentViewModel.Companion.MSG_UPDATE_TOOLBAR_BEHAVIOR
import com.oplus.filemanager.recent.ui.RecentRecyclerExpandBaseAdapter.Companion.LIST_EXPAND_ITEM_COUNT
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.recent.utils.RecentDataStorage
import com.oplus.filemanager.recent.utils.RecentUtils.hasDrmFileRecentEntity
import com.oplus.filemanager.recent.view.MainRecentEmptyLayout
import com.oplus.filemanager.recent.view.refresh.BounceCallBack
import com.oplus.filemanager.recent.view.refresh.BounceHandler
import com.oplus.filemanager.recent.view.refresh.BounceLayout
import com.oplus.filemanager.recent.view.refresh.EventForwardingHelper
import com.oplus.filemanager.recent.view.refresh.header.DefaultHeader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MainRecentFragment : BaseMainFragment(), OnDragStartListener, RefreshLayoutInterface,
    MoreItemClickListener, IPreviewListFragment {
    companion object {
        const val TAG = "MainRecentFragment"
        const val AUTO_LOAD_MIN_TIME_INTERVAL = 2 * 60 * 1000L
        private const val POSITION_FIRST = 0
        private const val CLICK_UPDATE_DELAY = 400L
        private const val IS_FROM_CONFIG_CHANGE = "isFromConfigChange"
        private const val PADDING_RATIO = 0.8
        private const val NEWLINE = "\n"
        private const val DELAY_100 = 100L
        private const val DELAY_UPDATE = 100L
    }
    private val operateInterceptor = RecentOperateInterceptor()

    private var mIsInitLoadData = false
    private var mIsFromConfigChange = false
    private var scrollAppBarLayout: RelativeLayout? = null
    private var behavior: PrimaryTitleBehavior? = null
    private var mScrollFraction = 0f
    private var mBounceLayout: BounceLayout? = null
    private var mHandler = Handler(Looper.getMainLooper())
    private var mEmptyLayout: MainRecentEmptyLayout? = null
    private var mMainRecentViewModel: MainRecentViewModel? = null
    private var mRecyclerView: FileManagerRecyclerView? = null
    private var mAdapter: MainRecentAdapter? = null
    private var mLayoutManager: GridLayoutManager? = null
    private var mRefreshMode = RefreshMode.Auto
    private var mLastAutoLoadTime = -AUTO_LOAD_MIN_TIME_INTERVAL
    private var mIsLoadingData = false
    private var mIsReloaded = false
    private var mIsShowEmptyView = false
    private var mDefaultHeader: DefaultHeader? = null
    private val mMoreItemController by lazy { MoreItemController(lifecycle) }
    private val mMainRecentHandler by lazy { MainRecentHandler(this) }
    private var mDeCompressDialog: CompressConfirmDialog? = null
    private var mDragScanner: FileDragDropScanner? = null
    private var mStickyHeaderContainer: View? = null
    private var mStickyHeaderDateTv: TextView? = null
    private var mStickyHeaderArrow: COUIRotateView? = null
    private var mStickyHeaderData: RecentGroupEntity? = null
    private var mStickyHeaderPosition: Int = 0
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var mLastListExpandItemCount = 0
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var previewOperate: IPreviewOperate? = null
    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null
    private var sortEntryView: SortEntryView? = null
    private var bounceHeaderInitMarginTop = 0
    private var bounceHeaderFinialMarginTop = 0
    private var stickyViewInitMarginTop = 0
    private var stickyViewFinalMarginTop = 0
    private var mCompressDialog: CompressConfirmDialog? = null
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private val gridItemVerticalSpace = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.recent_grid_item_space_vertical)
    private val mSpacesItemDecoration by lazy {
        activity?.let {
            RecentSpacesItemDecoration(
                ItemDecorationFactory.getGridItemCount(
                    activity, mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST,
                    ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT
                ),
                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.recent_grid_item_space_horizontal),
                false,
                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.recent_grid_margin_horizontal),
                gridItemVerticalSpace,
                mAdapter,
                it,
                this@MainRecentFragment
            )
        }
    }

    var scrollHelper: DragScrollHelper? = null

    class MainRecentHandler(fragment: MainRecentFragment) :
        StaticHandler<MainRecentFragment>(fragment) {
        override fun handleMessage(msg: Message, fragment: MainRecentFragment) {
            when (msg.what) {
                MSG_PULL_REFRESH_TYPE -> {
                    //cache load completed, start load media data
                    fragment.autoPullRefresh()
                }
                MSG_END_REFRESH -> {
                    fragment.endRefreshing()
                }
                MSG_DELAY_SHOW_CACHE_LOAD_TIP -> {
                    if (fragment.mMainRecentViewModel?.mLoadType == RecentDataHelper.TYPE_CACHE) {
                        fragment.setEmptyVisible(View.VISIBLE, com.filemanager.common.R.string.netdisk_refreshing)
                    }
                }
                MSG_UPDATE_TOOLBAR_BEHAVIOR -> {
                    Log.d(TAG, "MSG_UPDATE_TOOLBAR_BEHAVIOR")
                }
                MSG_UPDATE_FOLDER_SELECT -> {
                    val bundle = msg.data
                    val isSelected = bundle.getBoolean(KtConstants.IS_SELECTED)
                    val keys = bundle.getIntegerArrayList(KtConstants.SELECTED_KEYS)
                    fragment.mMainRecentViewModel?.updateRecentContentEntitySelectStatus(isSelected, keys)
                }
            }
        }
    }

    private enum class RefreshMode {
        Auto, User
    }

    private val mOnMainRecentItemClickListener = object : BaseRecentFileVH.OnRecentItemClickListener {
        override fun onItemClick(view: View, file: RecentFileEntity, event: MotionEvent?) {
            if (isRefresh()) {
                return
            }
            val previewResult = if (mMainRecentViewModel?.isInSelectMode() == false) {
                previewClickedFile(file, mMainRecentViewModel?.previewClickedFileLiveData)
            } else {
                false
            }
            if (!previewResult) {
                mMainRecentViewModel?.onItemClick(baseVMActivity, file, event)
            }
        }

        override fun onItemLongClick(view: View?, file: RecentFileEntity) {
            if (isRefresh()) {
                return
            }
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.RECENT_TO_EDITMODE_BY_LONG_CLICKED
            )
            // Fix bug 1616522: In zoom window and pc connect, long press to select files is not supported,
            // unless long press to enter edit mode
            if (PCConnectAction.isScreenCast()) {
                val selectMode = mMainRecentViewModel?.isInSelectMode() ?: false
                if ((context != null) && PCConnectAction.checkViewCanLongPress(selectMode)) {
                    mMainRecentViewModel?.onItemLongClick(baseVMActivity, file)
                }
            } else {
                mMainRecentViewModel?.onItemLongClick(baseVMActivity, file)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        }
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_RECENT
    }

    override fun getViewModel() = mMainRecentViewModel

    override fun getRecyclerView() = mRecyclerView

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData")
        if (PermissionUtils.hasStoragePermission().not() || !PrivacyPolicyController.hasAgreePrivacy()) {
            setPermissionEmptyVisible(View.VISIBLE)
            sortEntryView?.setFileCount(0)
            return
        }
        setPermissionEmptyVisible(View.GONE)

        activity?.let {
            updateListExpandItemCount()
            mLastListExpandItemCount = LIST_EXPAND_ITEM_COUNT
            mMainRecentViewModel?.refresh()
        }
        mIsInitLoadData = true
        mRecyclerView?.visibility = View.VISIBLE
        if (PermissionUtils.hasStoragePermission()) {
            if (mMainRecentViewModel?.mNormalLoad == true) {
                val curTime = SystemClock.elapsedRealtime()
                mMainRecentViewModel?.let {
                    mLastAutoLoadTime = it.mLastAutoLoadTime
                }
                if (HiddenFileHelper.forceReloadRecentDataOnce || (curTime - mLastAutoLoadTime > AUTO_LOAD_MIN_TIME_INTERVAL)) {
                    Log.d(TAG, "onResumeLoadData TYPE_MEDIA")
                    autoPullRefresh()
                } else {
                    Log.d(TAG, "onResumeLoadData TYPE_LOCAL")
                    compareLocalData()
                }
            } else {
                Log.d(TAG, "onResumeLoadData TYPE_CACHE $mMainRecentViewModel")
                mMainRecentHandler.sendEmptyMessageDelayed(
                    MSG_DELAY_SHOW_CACHE_LOAD_TIP,
                    CLICK_UPDATE_DELAY
                )
                mMainRecentViewModel?.loadRecentData(RecentDataHelper.TYPE_CACHE)
            }
            mLastAutoLoadTime = SystemClock.elapsedRealtime()
            mMainRecentViewModel?.let {
                it.mLastAutoLoadTime = mLastAutoLoadTime
                if (checkIsSelectedMode()) {
                    it.changeToSelectedMode()
                }
            }
        } else {
            setEmptyVisible(View.VISIBLE)
        }
    }

    private fun updateListExpandItemCount() {
        LIST_EXPAND_ITEM_COUNT = ItemDecorationFactory.getGridItemCount(
            activity, mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST,
            ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT
        )
    }

    private fun compareLocalData() {
        if (isRefreshing()) {
            Log.w(TAG, "compareLocalData -- ReLoading... give up compare --")
            return
        }
        if ((RecentDataStorage.getInstance().openFile != null)) {
            forceLoadFromMedia()
            RecentDataStorage.getInstance().openFile = null
            return
        }
        mMainRecentViewModel?.loadRecentData(RecentDataHelper.TYPE_LOCAL)
    }

    private fun isPreRefreshing(): Boolean {
        return mBounceLayout?.isDragging() ?: false
    }

    private fun isRefreshLayoutDragY(): Boolean {
        return mBounceLayout?.isDragY() ?: false
    }

    private fun isScrollDrag(): Boolean {
        return mBounceLayout?.isScrollDrag() ?: false
    }

    private fun isRefreshing(): Boolean {
        return mBounceLayout?.isRefreshing() ?: false
    }

    fun autoPullRefresh() {
        Log.d(TAG, "autoPullRefresh")
        mRefreshMode = RefreshMode.Auto
        cancelLoad()
        doPullRefreshAnim()
    }

    private fun doPullRefreshAnim() {
        if (mIsLoadingData && isRefreshing()) {
            Log.w(TAG, "doPullRefreshAnim on loading, return")
            return
        }
        mBounceLayout?.apply {
            mRecyclerView?.scrollToPosition(POSITION_FIRST)
            autoRefresh()
            mIsLoadingData = true
            Log.d(TAG, "doPullRefreshAnim")
        }
    }

    fun cancelLoad() {
        if (isRefreshing()) {
            cancelRefreshing()
        }
        mMainRecentViewModel?.cancelLoad()
        mIsLoadingData = false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (savedInstanceState != null && savedInstanceState.containsKey(IS_FROM_CONFIG_CHANGE)) {
            mIsFromConfigChange = savedInstanceState.get(IS_FROM_CONFIG_CHANGE) as Boolean
            Log.d(TAG, "onViewCreated mIsFromConfigChange:$mIsFromConfigChange")
        }
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.d(TAG, "onSaveInstanceState")
        activity?.apply {
            if (WindowUtils.isSmallScreen(this)) {
                outState.putBoolean(IS_FROM_CONFIG_CHANGE, true)
            }
        }
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> mMainRecentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)

            R.id.action_search -> {
                baseVMActivity?.let {
                    StatisticsUtils.onCommon(it, StatisticsUtils.ACTION_SEARCH)
                    StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_RECENT)
                    val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                    categoryGlobalSearchApi?.startGlobalSearch(it)
                }
            }

            R.id.actionbar_recent_edit -> {
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                StatisticsUtils.onCommon(appContext, StatisticsUtils.RECENT_MENU_EDIT_CLICKED)
                CoroutineUtil.launchDelay(lifecycleScope, DELAY_100) {
                    mMainRecentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
            }

            com.filemanager.common.R.id.action_select_cancel -> mMainRecentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            R.id.actionbar_scan_mode -> mMainRecentViewModel?.clickScanModeItem()

            R.id.action_setting -> {
                StatisticsUtils.onCommon(appContext, StatisticsUtils.ACTION_SETTING)
                OptimizeStatisticsUtil.pageSetting(RECENT)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_RECENT)
                Injector.injectFactory<ISetting>()?.startSettingActivity(baseVMActivity)
            }

            R.id.actionbar_owork -> (activity as? MainActivity)?.clickOworkMenu()

            com.filemanager.common.R.id.action_select_all -> mMainRecentViewModel?.clickToolbarSelectAll()

            R.id.navigation_sort -> {
                if (mMainRecentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            0,
                            anchorView,
                            SortRecordModeFactory.getRecentFileKey(),
                            object : SelectItemListener {
                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }
                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag && isRefreshing().not()) {
                                        Log.d(TAG, "onPopUpItemClick")
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        autoPullRefresh()
                                        OptimizeStatisticsUtil.recentSort(sortMode)
                                    }
                                    mSortPopupController.hideSortPopUp()
                                }
                            })
                    }
                }
            }

            com.filemanager.common.R.id.action_select_cancel -> mMainRecentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        return true
    }

    override fun fromActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
        return false
    }

    fun fromSelectPathResult(requestCode: Int, path: String?) {
        baseVMActivity?.let {
            if (path != null) {
                when (requestCode) {
                    MessageConstant.MSG_EDITOR_CUT -> {
                        operateInterceptor.onCut(it, path)
                        mMainRecentViewModel?.cutFile(this, path, false)
                    }
                    MessageConstant.MSG_EDITOR_COPY -> {
                        operateInterceptor.onCopy(it, path)
                        mMainRecentViewModel?.copyFile(this, path)
                    }
                    MessageConstant.MSG_EDITOR_DECOMPRESS -> mMainRecentViewModel?.setSavePath(path)
                    MessageConstant.MSG_EDITOR_COMPRESS -> mCompressDialog?.setSavePath(path)
                }
            }
        }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            mMainRecentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    /**
     * 在最近页面进行拖拽
     */
    fun dragFileOperate(requestCode: Int, path: String?) {
        Log.d(TAG, "dragFileOperate requestCode $requestCode path ${path != null}")
        baseVMActivity?.let {
            when (requestCode) {
                IFileOperate.OP_CUT -> {
                    if (path == null) return
                    operateInterceptor.onCut(it, path)
                    mMainRecentViewModel?.cutFile(this, path, true)
                }

                IFileOperate.OP_DELETE_TO_RECYCLE -> {
                    operateInterceptor.onDelete(it)
                    mMainRecentViewModel?.deleteFile(it)
                }

                else -> {}
            }
        }
    }

    fun refreshAfterDragOut(dragOperate: String?) {
        mMainRecentViewModel?.refreshDataAfterDragOut(baseVMActivity, dragOperate)
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }

        when (item.itemId) {
            com.filemanager.common.R.id.navigation_delete -> {
                baseVMActivity?.let {
                    StatisticsUtils.onCommon(appContext, StatisticsUtils.RECENT_DELETE_RECORD_AND_FILE)
                    OptimizeStatisticsUtil.onDelete(RECENT)
                    operateInterceptor.onDelete(it)
                    mMainRecentViewModel?.deleteFile(it)
                }
            }
            com.filemanager.common.R.id.navigation_send -> {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                StatisticsUtils.onCommon(appContext, StatisticsUtils.SEND_MENU_PRESSED)
                OptimizeStatisticsUtil.onSend(RECENT)
                val share: View? = baseVMActivity?.findViewById(com.filemanager.common.R.id.navigation_send)
                mMainRecentViewModel?.shareFile(this,
                    share?.let { ViewHelper.getViewRect(share) })
            }
            com.filemanager.common.R.id.navigation_label -> {
                if (baseVMActivity is TransformNextFragmentListener) {
                    mMainRecentViewModel?.let {
                        (baseVMActivity as TransformNextFragmentListener).showEditLabelFragmentDialog(it.getSelectItems())
                        OptimizeStatisticsUtil.onLabel(RECENT)
                    }
                }
            }
            com.filemanager.common.R.id.navigation_cut -> {
                if (baseVMActivity is TransformNextFragmentListener) {
                    (baseVMActivity as TransformNextFragmentListener).showSelectPathFragmentDialog(
                        MessageConstant.MSG_EDITOR_CUT
                    )
                    OptimizeStatisticsUtil.onCut(RECENT)
                }
            }
            com.filemanager.common.R.id.navigation_copy -> {
                if (baseVMActivity is TransformNextFragmentListener) {
                    (baseVMActivity as TransformNextFragmentListener).showSelectPathFragmentDialog(
                        MessageConstant.MSG_EDITOR_COPY
                    )
                    OptimizeStatisticsUtil.onCopy(RECENT)
                }
            }
            com.filemanager.common.R.id.navigation_detail -> {
                baseVMActivity?.let {
                    onClickDetail(it)
                }
            }
            com.filemanager.common.R.id.navigation_more -> {
                baseVMActivity?.let { activity ->
                    lifecycleScope.launch(Dispatchers.IO) {
                        val items = mMainRecentViewModel?.getSelectItems() ?: emptyList()
                        if (items.isNotEmpty()) {
                            val supportConfig = MoreItemController.SupportConfig(
                                true,
                                Injector.injectFactory<ICloudDrive>()?.supportCloudDisk() ?: false,
                                true,
                                true
                            )
                            withContext(Dispatchers.Main) {
                                mMoreItemController.showMoreItemPopupWindow(
                                    activity,
                                    items,
                                    supportConfig,
                                    this@MainRecentFragment
                                )
                            }
                        }
                    }
                }
            }
        }
        return false
    }

    override fun getLayoutResId(): Int {
        return if (isChildDisplay) {
            R.layout.main_parent_child_recent
        } else {
            R.layout.main_recent_fragment
        }
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        Log.d(TAG, "initView")
        rootView = view.findViewById(R.id.coordinator_layout)
        sortEntryView = view.findViewById<SortEntryView?>(com.filemanager.common.R.id.sort_entry_view)?.apply {
            this.visibility = View.VISIBLE
            this.disableSortOrderView()
        }
        mBounceLayout = view.findViewById(R.id.recent_bl)
        mRecyclerView = view.findViewById<FileManagerRecyclerView>(R.id.recent_recycle_view)?.apply {
            PCConnectAction.getItemTouchInterceptor()?.let { addOnItemTouchListener(it) }
            if (!isChildDisplay) {
                val defaultPadding = baseVMActivity?.resources?.getDimensionPixelOffset(R.dimen.recent_recyclerview_padding_top) ?: 0
                val sortEntryHeight = baseVMActivity?.resources?.getDimensionPixelOffset(com.filemanager.common.R.dimen.sort_entry_height) ?: 0
                val paddingTop = defaultPadding + sortEntryHeight
                updatePadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
            }
        }
        if (previewOperate?.isSupportPreview() != true) {
            toolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        initToolbar()
        initBounceLayout()
        kotlin.runCatching {
            initStickyHeader()
        }.onFailure {
            Log.e(TAG, "initStickyHeader ${it.message}")
        }
        scrollHelper = DragScrollHelper(getRecyclerView())
        setScrollBottomView()
    }

    private fun setScrollBottomView() {
        val navigationViewY = (activity as MainActivity).getNavigationTabView().y.toInt()
        scrollHelper?.setBottomView(navigationViewY)
    }

    @SuppressLint("InflateParams")
    private fun initStickyHeader() {
        activity?.let {
            val view = it.layoutInflater.inflate(R.layout.recent_parent_item, null)
            mStickyHeaderContainer = view.findViewById(R.id.pinned_header_container)
            val bgColor = COUIContextUtil.getAttrColor(it, com.support.appcompat.R.attr.couiColorBackgroundWithCard)
            mStickyHeaderContainer?.setBackgroundColor(bgColor)
            mStickyHeaderDateTv = view.findViewById(R.id.parent_left_text)
            mStickyHeaderArrow = view.findViewById(R.id.parent_expend)
            mStickyHeaderContainer?.setOnClickListener {
                mStickyHeaderData?.let { data ->
                    mAdapter?.expandOrCollapseGroup(data, mStickyHeaderPosition)
                    mStickyHeaderArrow?.startRotateAnimation()
                }
            }
            if (mStickyHeaderDateTv != null && mStickyHeaderArrow != null) {
                MainRecentAdapter.updateGroupTitleMarginLeft(mStickyHeaderDateTv!!, mStickyHeaderArrow!!)
            }

            val lp = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            if (isChildDisplay) {
                rootView?.findViewById<FrameLayout>(R.id.recent_parent_layout)?.addView(view, lp)
            } else {
                mBounceLayout?.addView(view, lp)
            }
            setStickyHeaderVisibility(View.GONE)
        }
    }

    fun updateStickyHeaderUi(data: RecentGroupEntity?) {
        if (data != null && mAdapter?.mExpanding != true) {
            mStickyHeaderData = data
            mStickyHeaderPosition = mAdapter?.mFiles?.indexOf(data as BaseRecentEntity) ?: 0
            mStickyHeaderDateTv?.let {
                it.text = data.groupShowDateAndSize
            }
            mStickyHeaderArrow?.let {
                it.isExpanded = data.mIsExpand
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun initData(savedInstanceState: Bundle?) {
        mMainRecentViewModel = ViewModelProvider(requireActivity())[MainRecentViewModel::class.java]
        mMainRecentViewModel?.setUiHandler(mMainRecentHandler)
        mAdapter = MainRecentAdapter(
            baseVMActivity!!,
            mMainRecentHandler,
            this@MainRecentFragment,
            <EMAIL>
        ).apply {
            setOnRecyclerItemClickListener(mOnMainRecentItemClickListener)
            setHasStableIds(true)
            mScanMode = mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        }
        mLayoutManager = object : GridLayoutManager(
            context, ItemDecorationFactory.getGridItemCount(
                activity,
                mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST,
                ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT
            )
        ) {
            override fun onLayoutChildren(
                recycler: RecyclerView.Recycler?,
                state: RecyclerView.State?
            ) {
                try {
                    super.onLayoutChildren(recycler, state)
                } catch (e: IndexOutOfBoundsException) {
                    Log.e(TAG, "onLayoutChildren exception: ${e.message}")
                }
            }
        }.apply {
            spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val viewType = mAdapter?.getItemViewType(position) ?: 0
                    val isSingleLine = (viewType >= BaseRecentEntity.VIEW_TYPE_ITEM_GROUP)
                    return if (isSingleLine) {
                        spanCount
                    } else {
                        1
                    }
                }
            }
        }
        mRecyclerView?.apply {
            layoutManager = mLayoutManager
            adapter = mAdapter
            isForceDarkAllowed = false
            mSpacesItemDecoration?.let { addItemDecoration(it) }
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        FileImageLoader.sInstance.resumeRequests(this@MainRecentFragment)
                    } else {
                        FileImageLoader.sInstance.pauseRequests(this@MainRecentFragment)
                    }
                }
            })
            itemAnimator = RecentGroupItemAnim()
            (itemAnimator as RecentGroupItemAnim).supportsChangeAnimations = false
            setOverScrollEnable(false)
            initSelectionTracker(this)
            if (isChildDisplay) {
                toolbar?.title = ""
            }
            mGridSpanAnimationHelper = GridSpanAnimationHelper(this)
        }
        setOffsetChangedListener()
        Log.d(TAG, "initData loadData:$mNeedLoadData")
        if (mNeedLoadData) {
            Log.d(TAG, "initData onResumeLoadData")
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        val category = getFragmentCategoryType()
        TouchShareSupplier.attach(this, TouchShareFragmentSupplier(category, baseVMActivity, mMainRecentViewModel, operateInterceptor))
    }

    private fun setOffsetChangedListener() {
        if (isChildDisplay) {
            mRecyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    mScrollFraction =
                        if (mLayoutManager?.findViewByPosition(0)?.top == 0 || mLayoutManager?.findViewByPosition(0)?.top == -1) {
                            0.0f
                        } else {
                            2f
                        }
                    if (mScrollFraction == 0.0f) {
                        setStickyHeaderVisibility(View.GONE)
                    } else {
                        setStickyHeaderVisibility(if (mIsShowEmptyView) View.GONE else View.VISIBLE)
                    }
                    if (mScrollFraction == 0.0f && mIsShowEmptyView) {
                        updateEmptyLayoutMarginTop()
                    }
                }
            })
            (rootView?.findViewById(R.id.appbar_layout) as? COUIDividerAppBarLayout)?.setHasDivider(false)
        } else {
            mRecyclerView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    mScrollFraction = if (mLayoutManager?.findViewByPosition(0)?.top == stickyViewInitMarginTop) {
                        0.0f
                    } else {
                        2f
                    }
                }
            })
        }
    }

    private fun initBounceLayout() {
        mBounceLayout?.apply {
            mDefaultHeader = DefaultHeader(context)
            val lp = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            if (!isChildDisplay) {
                lp.topMargin = bounceHeaderInitMarginTop
            } else {
                lp.topMargin = -appContext.resources.getDimensionPixelSize(R.dimen.default_height) * DefaultHeader.MARGIN_TIMES
            }
            mDefaultHeader?.isClickable = false
            setHeaderView(mDefaultHeader, this, lp)
            setBounceHandler(BounceHandler(), mRecyclerView)
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(
                    downX: Float,
                    downY: Float,
                    moveX: Float,
                    moveY: Float
                ): Boolean {
                    return true
                }
            })
            setBounceCallBack(object : BounceCallBack {
                override fun startRefresh() {
                    Log.d(TAG, "mBounceLayout startRefresh")
                    if (checkParentPermission()) {
                        // 隐藏空页面
                        setEmptyVisible(View.GONE)
                        //pull refresh by user
                        if (mRefreshMode == RefreshMode.User) {
                            StatisticsUtils.onCommon(
                                appContext,
                                StatisticsUtils.RECENT_PULL_REFRESH_BY_USER
                            )
                        }
                        mIsReloaded = true
                        mMainRecentViewModel?.pullRefreshLoadData()
                    } else {
                        mHandler.post {
                            if (isAdded) {
                                mRefreshMode = RefreshMode.User
                                endRefreshing()
                                setPermissionEmptyVisible(View.VISIBLE)
                            }
                        }
                    }
                }

                override fun startLoadingMore() {}
            })
            //26dp
            val headerViewHeight =
                appContext.resources.getDimensionPixelSize(R.dimen.default_height)
            //14dp
            val headerTopPadding =
                appContext.resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_top_padding)
            //24dp
            val headerBottomPadding =
                appContext.resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_bottom_padding)
            //64dp
            mDragDistanceThreshold = ((headerViewHeight + headerTopPadding + headerBottomPadding) * PADDING_RATIO).toInt()
            //86dp
            mMaxDragDistance = appContext.resources.getDimensionPixelOffset(R.dimen.pull_refresh_max_drag_distance)
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_recent_edit)?.let {
            if (isChildDisplay) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        if (mMainRecentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_NORMAL_MODE) {
            inflater.inflate(R.menu.main_recent_menu, menu)
            toolbar?.apply {
                refreshScanModeItemIcon(this)
                setToolbarMenuVisible(this, !isChildDisplay)
                setToolbarEditIcon(this, isChildDisplay)
                updateOWorkVisible()
            }
        } else {
            toolbar?.apply {
                initToolbarWithEditMode(this)
            }
        }
    }

    private fun initToolbar() {
        if (!isChildDisplay) {
            rootView?.apply {
                val paddingTop = COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity)
                setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
            }
            scrollAppBarLayout = rootView?.findViewById(R.id.appBarLayout)
            behavior = (scrollAppBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior as? PrimaryTitleBehavior
            behavior?.initBehavior(rootView, R.id.recent_recycle_view)
            behavior?.showSortEntryView(true)
            behavior?.showDividerLine(false)
            behavior?.onListScrolling = { fraction ->
                mDefaultHeader?.isClickable = false
                mDefaultHeader?.updateLayoutParams<MarginLayoutParams> {
                    this.topMargin = (bounceHeaderInitMarginTop - (bounceHeaderInitMarginTop - bounceHeaderFinialMarginTop) * fraction).toInt()
                }
                mStickyHeaderContainer?.updateLayoutParams<MarginLayoutParams> {
                    this.topMargin = (stickyViewInitMarginTop - (stickyViewInitMarginTop - stickyViewFinalMarginTop) * fraction).toInt()
                }
                if (fraction == 1f) {
                    mStickyHeaderContainer?.visibility = View.VISIBLE
                } else {
                    mStickyHeaderContainer?.visibility = View.GONE
                }
            }
            val title = stringResource(com.filemanager.common.R.string.main_tab_recently)
            behavior?.setToolbarTitle(title, false)

            val defaultPadding = baseVMActivity?.resources?.getDimensionPixelOffset(R.dimen.recent_recyclerview_padding_top) ?: 0
            val sortEntryHeight = baseVMActivity?.resources?.getDimensionPixelOffset(com.filemanager.common.R.dimen.sort_entry_height) ?: 0
            val toolbarHeight = baseVMActivity?.resources?.getDimensionPixelOffset(com.support.toolbar.R.dimen.toolbar_min_height) ?: 0
            stickyViewInitMarginTop = defaultPadding + sortEntryHeight
            stickyViewFinalMarginTop = toolbarHeight + sortEntryHeight

            val headerDefaultMargin = -appContext.resources.getDimensionPixelSize(R.dimen.default_height) * DefaultHeader.MARGIN_TIMES
            bounceHeaderInitMarginTop = defaultPadding + sortEntryHeight + headerDefaultMargin
            bounceHeaderFinialMarginTop = toolbarHeight + sortEntryHeight + headerDefaultMargin
        } else {
            appBarLayout = rootView?.findViewById(R.id.appBarLayout)
            (appBarLayout as? COUICollapsableAppBarLayout)?.enableAutoExpand(false)
            (appBarLayout as? COUIDividerAppBarLayout)?.setHasDivider(false)
        }
    }

    /**
     * 对于小屏，RecycleView加了padding，上滑时，需要找到RecycleView中位置上下范围在stickyViewFinalMarginTop之间的view，
     * 将其对应的group信息更新在stickyHeader上
     */
    @Suppress("LoopWithTooManyJumpStatements")
    fun findStickyHeaderData() {
        var lastView: View? = null
        val viewCount = mRecyclerView?.childCount ?: 0
        var position = -1
        for (index in 0 until viewCount) {
            val view = mRecyclerView?.getChildAt(index) ?: continue
            val top = view.top
            val bottom = view.bottom
            if (stickyViewFinalMarginTop in top..bottom) {
                position = mRecyclerView?.getChildAdapterPosition(view) ?: -1
                break
            }
            if (bottom > stickyViewFinalMarginTop) {
                lastView?.let {
                    position = mRecyclerView?.getChildAdapterPosition(it) ?: -1
                }
                break
            }
            lastView = view
        }
        if (position >= 0) {
            updateStickyHeaderUi(mAdapter?.getGroupItem(position))
        }
    }

    override fun onResume() {
        super.onResume()
        updateOWorkVisible()
    }

    override fun updateOWorkVisible() {
        if (isChildDisplay) {
            toolbar?.apply {
                menu.findItem(R.id.actionbar_owork)?.setVisible(false)
            }
        } else if (mMainRecentViewModel?.isInSelectMode()?.not() != false) {
            super.updateOWorkVisible()
        }
    }

    override fun onTabSelected() {
    }

    private fun changeToolbarToNormalMode() {
        toolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            inflateMenu(R.menu.main_recent_menu)
            setOnMenuItemClickListener { menu ->
                if (Utils.isQuickClick()) {
                    Log.d(TAG, "menu quick click. return")
                    return@setOnMenuItemClickListener true
                }
                if (parentFragment is PreviewCombineFragment) {
                    (parentFragment as PreviewCombineFragment).onMenuItemSelected(menu)
                } else {
                    onMenuItemSelected(menu)
                }
            }
            updateOWorkVisible()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            val titleString = appContext.getString(com.filemanager.common.R.string.main_tab_recently)
            if (isChildDisplay) {
                title = titleString
            } else {
                behavior?.setToolbarTitle(titleString, false)
            }
            previewOperate?.onToolbarMenuUpdated(menu)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
        setEditMenuItemEnable(mEmptyLayout?.visibility != View.VISIBLE)
        updateNavigationBarColor()
    }

    private fun changeToolbarToSelectedMode(titleString: String) {
        if (isChildDisplay) {
            toolbar?.title = titleString
        } else {
            behavior?.setToolbarTitle(titleString, true)
        }
    }

    private fun updateNavigationBarColor() {
        baseVMActivity?.apply { StatusBarUtil.setNavigationBarColor(this) }
    }

    private fun initSelectionTracker(recyclerView: RecyclerView) {
        val keyProvider = DefaultKeyProvider(recyclerView)
        val detailsLookup = DefaultDetailsLookup(recyclerView)
        RecycleSelectionBuilder(TAG, recyclerView, DefaultSelectDelegate(mMainRecentViewModel), keyProvider, detailsLookup).apply {
            withSlideSelection(false)
            withOnDragStartListener(this@MainRecentFragment)
            build()
        }
    }

    private fun initEmptyView() {
        if (mEmptyLayout != null || rootView == null) {
            return
        }
        Log.d(TAG, "initEmptyView")
        val viewStub = rootView!!.findViewById<ViewStub>(R.id.main_recent_empty_ll)
        val viewRoot = viewStub.inflate()
        mEmptyLayout = viewRoot.findViewById(R.id.empty_view_layout)
        mEmptyLayout?.initView()
        updateEmptyLayoutMarginTop()
    }

    private fun updateEmptyLayoutMarginTop() {
        if (!mIsShowEmptyView) {
            return
        }
        mEmptyLayout?.let { emptyLayout ->
            val emptyMarginTop = getEmptyMarginTop()
            val layoutParams = emptyLayout.layoutParams
            if (layoutParams is CoordinatorLayout.LayoutParams) {
                layoutParams.topMargin = emptyMarginTop
                emptyLayout.layoutParams = layoutParams
            }
        }
    }

    private fun getEmptyMarginTop(): Int {
        return if (isChildDisplay) {
            appBarLayout?.measuredHeight ?: 0
        } else {
            stickyViewInitMarginTop
        }
    }

    private fun setEditMenuItemEnable(enable: Boolean) {
        toolbar?.apply {
            val menuEnabled = menu.findItem(R.id.actionbar_recent_edit)
            if (menuEnabled?.isEnabled != enable) {
                menuEnabled?.isEnabled = enable
            }
        }
    }

    private fun setEmptyVisible(visible: Int, tipResId: Int = com.filemanager.common.R.string.empty_file) {
        if ((View.VISIBLE == visible) && PermissionUtils.hasStoragePermission().not()) {
            return
        }
        behavior?.setScrollEnable(visible != View.VISIBLE)
        if (View.VISIBLE == visible) {
            toolbar?.menu?.findItem(R.id.actionbar_recent_edit)?.isVisible = false
            mIsShowEmptyView = true
            (appBarLayout as? COUICollapsableAppBarLayout)?.mode = MODE_FIXED_EXPANDED
            initEmptyView()
            setEditMenuItemEnable(false)
            listEmptyFile()
        } else {
            mIsShowEmptyView = false
            (appBarLayout as? COUICollapsableAppBarLayout)?.mode = MODE_COLLAPSABLE
            if (isRefreshing().not()) {
                toolbar?.menu?.findItem(R.id.actionbar_recent_edit)?.isVisible = true
                setEditMenuItemEnable(true)
            }
        }
        mEmptyLayout?.let { layout ->
            layout.visibility = visible
            if (View.VISIBLE == visible) {
                layout.bringToFront()
            }
            layout.setTipResId(tipResId)
            if (SystemClock.elapsedRealtime() - mLastPlayAnimationTime > PLAY_EMPTY_VIEW_ANIMATION_GAP) {
                mLastPlayAnimationTime = SystemClock.elapsedRealtime()
                layout.playAnimation()
            }
        }
    }

    private fun endRefreshing() {
        mIsLoadingData = false
        mRefreshMode = RefreshMode.User
        mLastAutoLoadTime = SystemClock.elapsedRealtime()
        mMainRecentViewModel?.let {
            it.mLastAutoLoadTime = mLastAutoLoadTime
        }
        mBounceLayout?.setRefreshCompleted()
    }

    private fun cancelRefreshing() {
        mIsLoadingData = false
        mRefreshMode = RefreshMode.User
        mBounceLayout?.setRefreshCompleted()
    }

    private fun isCurrentFragment(): Boolean {
        (baseVMActivity as? MainActivity)?.let {
            if (parentFragment is PreviewCombineFragment) { // 父子级
                return true
            }
            if (it.getCurrentPosition() == INDEX_RECENT) {
                return true
            }
            if (!it.isMainCategoryFragment()) {
                return true
            }
        }
        return false
    }

    override fun startObserve() {
        rootView?.post {
            if (isAdded) {
                mMainRecentViewModel?.mModeState?.listModel?.observe(this, Observer {
                    Log.d(TAG, "listModel=$it")
                    if (!isCurrentFragment()) {
                        Log.e(TAG, "listModel observer is not current Fragment")
                        return@Observer
                    }
                    mMainRecentViewModel?.isPreviewOpen = isPreviewOpen()
                    if (it == KtConstants.LIST_SELECTED_MODE) {
                        if ((baseVMActivity is NavigationInterfaceForMain)) {
                            (baseVMActivity as NavigationInterfaceForMain).showNavigation()
                        }
                        mBounceLayout?.apply {
                            cancelRefreshing()
                            setDisallowBounce(true)
                        }
                        (baseVMActivity as? MainActivity)?.notifySelectModel(true)
                        mAdapter?.setSelectEnabled(true)
                        setChildDisplayPaddingBottom(true)
                        toolbar?.let { toolbar ->
                            initToolbarWithEditMode(toolbar)
                            changeToolbarToSelectedMode(refreshSelectToolbar(toolbar))
                        }
                        previewEditedFiles(mMainRecentViewModel?.getSelectItems())
                    } else {
                        previewClickedFile(
                            mMainRecentViewModel?.previewClickedFileLiveData?.value,
                            mMainRecentViewModel?.previewClickedFileLiveData
                        )
                        (baseVMActivity as? MainActivity)?.notifySelectModel(false)
                        mBounceLayout?.setDisallowBounce(false)
                        if ((baseVMActivity is NavigationInterfaceForMain)) {
                            (baseVMActivity as NavigationInterfaceForMain).hideNavigation()
                        }
                        mAdapter?.setSelectEnabled(false)
                        setChildDisplayPaddingBottom(false)
                        toolbar?.let {
                            changeToolbarToNormalMode()
                            refreshScanModeItemIcon(it)
                        }
                    }
                })
                mMainRecentViewModel?.uiState?.observe(this, Observer {
                    Log.d(TAG, "uiState =${it.mAllEntityList.size},${it.selectedList.size},mIsFromConfigChange:$mIsFromConfigChange")
                    sortEntryView?.setFileCount(mMainRecentViewModel?.getRealFileSize() ?: 0)
                    if (mIsFromConfigChange) {
                        //当时配置改变时从viewmodel恢复数据(fileList数据是全部的，折叠时会有异常)，而不是从setUIData中获取数据时，不执行数据刷新。
                        mIsFromConfigChange = false
                        return@Observer
                    }
                    if (it.mIsFromSetUiData) {
                        it.mIsFromSetUiData = false
                    }
                    if (mMainRecentViewModel!!.mModeState.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                        toolbar?.let { toolbar ->
                            changeToolbarToSelectedMode(refreshSelectToolbar(toolbar))
                        }
                        // #ifdef For BugID.269804, 2020/8/20, Modify
                        //if data has changed, refresh list, else only refresh visible view's select status
                        mAdapter?.let { adapter ->
                            //If the memory address of the SourceList object changes or the number changes,
                            // it is considered that the data has changed
                            Log.d(TAG, "mLastListExpandItemCount: $mLastListExpandItemCount,$LIST_EXPAND_ITEM_COUNT")
                            if ((it.mSourceList !== adapter.getData())
                                || mMainRecentViewModel?.mLocalDataHasChanged == true
                                || mLastListExpandItemCount != LIST_EXPAND_ITEM_COUNT
                            ) {
                                if (mLastListExpandItemCount != LIST_EXPAND_ITEM_COUNT) {
                                    mLastListExpandItemCount = LIST_EXPAND_ITEM_COUNT
                                }
                                adapter.checkComputingAndExecute {
                                    adapter.setRecentData(
                                        it.mSourceList,
                                        it.fileList as MutableList<BaseRecentEntity>,
                                        it.selectedList
                                    )
                                }
                            } else {
                                adapter.upSelectData(it.selectedList)
                            }
                        }
                        previewEditedFiles(mMainRecentViewModel?.getSelectItems())
                        // #endif BugID.269804
                    } else {
                        previewClickedFile(
                            mMainRecentViewModel?.previewClickedFileLiveData?.value,
                            mMainRecentViewModel?.previewClickedFileLiveData
                        )
                        mAdapter?.checkComputingAndExecute {
                            mAdapter?.setRecentData(it.mSourceList, it.fileList as MutableList<BaseRecentEntity>, it.selectedList)
                        }
                    }
                    if (it.mAllEntityList.isEmpty()) {
                        if (mMainRecentViewModel?.isLoading() == true || mMainRecentViewModel?.mLoadType == RecentDataHelper.TYPE_CACHE) {
                            setEmptyVisible(View.VISIBLE, com.filemanager.common.R.string.netdisk_refreshing)
                            setStickyHeaderVisibility(View.GONE)
                        } else {
                            setEmptyVisible(View.VISIBLE)
                            setStickyHeaderVisibility(View.GONE)
                            toolbar?.let { refreshScanModeItemIcon(it) }
                        }
                    } else {
                        setEmptyVisible(View.GONE)
                        setStickyHeaderVisibility(
                            if (mScrollFraction == 0.0F) {
                                View.GONE
                            } else {
                                View.VISIBLE
                            }
                        )
                    }
                })
                startScanModeObserver()
                startSideNavigationStatusObserver()
                mMainRecentViewModel?.previewClickedFileLiveData?.observe(this) {
                    mAdapter?.setPreviewClickedFile(it)
                }
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            baseVMActivity?.let {
                FileImageVHUtils.changedListMargin(
                    it,
                    it.sideNavigationContainer?.drawerViewWidth ?: 0,
                    it.sideNavigationStatus.value ?: KtConstants.SIDE_NAVIGATION_OPEN
                )
            }
            toolbar?.let { refreshScanModeItemIcon(it) }
            setEditMenuStatus(status)
            mRecyclerView?.postDelayed({
                updateLeftRightMargin()
            }, DELAY_UPDATE)
        }
    }

    private fun setEditMenuStatus(status: Int?) {
        toolbar?.menu?.findItem(R.id.actionbar_recent_edit)?.apply {
            if (status == KtConstants?.LIST_SELECTED_MODE
                || !isChildDisplay) {
                icon = null
                setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        }
    }

    private fun setChildDisplayPaddingBottom(isSelectedMode: Boolean) {
        if (isChildDisplay) {
            mRecyclerView?.let {
                val paddingBottom: Int
                if (isSelectedMode) {
                    val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                    paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                } else {
                    paddingBottom = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                }
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
            }
        }
    }

    private fun startScanModeObserver() {
        mMainRecentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            run {
                toolbar?.let {
                    val needSkipAnimation = mNeedSkipAnimation
                    if (needSkipAnimation) {
                        refreshScanModeAdapter(scanMode)
                    } else {
                        mRecyclerView?.let { recyclerView ->
                            recyclerView.mTouchable = false
                            recyclerView.stopScroll()
                        }
                        mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                            override fun onSpanChangeCallback() {
                                mLayoutManager?.scrollToPosition(0)
                                refreshScanModeAdapter(scanMode)
                            }
                        }, object : OnAnimatorEndListener {
                            override fun onAnimatorEnd() {
                                mRecyclerView?.mTouchable = true
                            }
                        })
                    }
                    delay { refreshScanModeItemIcon(it, needSkipAnimation) }
                }
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (mMainRecentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setEditMenuStatus(baseVMActivity?.sideNavigationStatus?.value)
            if (baseVMActivity?.sideNavigationStatus?.value == KtConstants?.LIST_SELECTED_MODE
                && mMainRecentViewModel!!.uiState.value?.mAllEntityList?.isNotEmpty() == true
                && isChildDisplay) {
                it.icon = null
                it.title = desc
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                it.title = null
                if (needSkipAnimation) {
                    it.setIcon(resId)
                } else {
                    KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
                }
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        }
    }

    private fun setStickyHeaderVisibility(visibility: Int) {
        mStickyHeaderContainer?.let {
            it.visibility = visibility
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            Log.d(TAG, ">>>onUIConfigChanged")
            //bug5722632
            mLastListExpandItemCount = LIST_EXPAND_ITEM_COUNT
            if (!isChildDisplay) {
                rootView?.apply {
                    setPadding(paddingLeft, COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
                }
            }
            activity?.let {
                updateListExpandItemCount()
                mMainRecentViewModel?.setUiHandler(mMainRecentHandler)
                mMainRecentViewModel?.mLocalDataHasChanged = true
                mMainRecentViewModel?.refresh()
                mMainRecentViewModel?.onConfigurationChanged()
            }
            configList.forEach { iuiConfig ->
                if (iuiConfig is DarkThemeLevelConfig) {
                    mSpacesItemDecoration?.refreshCardBg()
                }
            }
            refreshScanModeAdapter(mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST)
            mMainRecentViewModel?.onConfigurationChanged(context?.resources?.configuration)
            if (mStickyHeaderDateTv != null && mStickyHeaderArrow != null) {
                MainRecentAdapter.updateGroupTitleMarginLeft(mStickyHeaderDateTv!!, mStickyHeaderArrow!!)
            }
            updatePermissionEmptyHeight()
            val screenState = UIConfigMonitor.getCurrentScreenState()
            //大屏切小屏时编辑状态下的底部工作栏依附在不同的Fragment，需要重新显示出来
            if (screenState == UIConfigMonitor.SCREEN_LARGE_TO_SMALL
                && mMainRecentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE
            ) {
                Log.d(TAG, "Large to small showNavigation")
                (baseVMActivity as? NavigationInterfaceForMain)?.showNavigation()
            } else if (screenState == UIConfigMonitor.SCREEN_SMALL_TO_LARGE
                && mMainRecentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE
            ) {
                //小屏切大屏切时，小屏最近页的底部工具栏会遮挡大屏下的工具栏，需要先隐藏
                Log.d(TAG, "small to Large hideNavigation")
                (baseVMActivity as? MainActivity)?.hideRecentNavigationView()
            }
            updateLeftRightMargin()
            setScrollBottomView()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.checkComputingAndExecute {
                mAdapter?.notifyDataSetChanged()
            }
        }
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return mIsShowEmptyView
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        Log.d("TAGAS", "recent event action is ${event?.action}")
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        ItemDecorationFactory.setRecentGridCount(activity)
        ItemDecorationFactory.setRecentItemImageWidth(activity)
        val spanCount = ItemDecorationFactory.getGridItemCount(
            activity, scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT
        )
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration?.spanCount = spanCount
        mAdapter?.apply {
            checkComputingAndExecute {
                mScanMode = scanMode
                notifyDataSetChanged()
            }
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
            setOnMenuItemClickListener { menu ->
                if (parentFragment is PreviewCombineFragment) {
                    (parentFragment as PreviewCombineFragment).onMenuItemSelected(menu)
                } else {
                    onMenuItemSelected(menu)
                }
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar): String {
        val isSelectAll = (mMainRecentViewModel!!.uiState.value?.mAllEntityList?.size == mMainRecentViewModel!!.uiState.value?.selectedList?.size)
        val title = if (isSelectAll) {
            appContext.resources.getString(com.filemanager.common.R.string.unselect_all)
        } else {
            appContext.resources.getString(com.filemanager.common.R.string.file_list_editor_select_all)
        }
        toolbar.menu.findItem(com.filemanager.common.R.id.action_select_all)?.title = title

        val checkedCount = mMainRecentViewModel!!.uiState.value?.selectedList?.size
            ?: 0
        var tempTitle = baseVMActivity?.getString(com.filemanager.common.R.string.mark_selected_no_items)
        if (checkedCount > 0) {
            tempTitle = appContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.mark_selected_items_new,
                checkedCount,
                checkedCount
            )
        }
        var isEnable = mMainRecentViewModel!!.uiState.value?.selectedList?.isNotEmpty() ?: false
        isEnable = isEnable && !DragUtils.isDragging
        if (baseVMActivity is NavigationInterfaceForMain) {
            (baseVMActivity as NavigationInterfaceForMain).setNavigateItemAble(
                isEnable, hasDrmFileRecentEntity(mMainRecentViewModel!!.getSelectItems())
            )
        }
        return tempTitle ?: ""
    }

    override fun pressBack(): Boolean {
        return mMainRecentViewModel?.pressBack() ?: false
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart dragging:${DragUtils.isDragging}")
        if (DragUtils.isDragging) return false
        baseVMActivity?.let {
            val view = mRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
            Log.d(TAG, "onDragStart view:$view")
            val dragHoldDownFile = RecentFileEntity()
            var dragHoldDrawable: Drawable? = null
            var dragHoldDetail: String? = null
            var dragHoldTitle: String? = null
            val listIconView = view.findViewById<ImageView>(com.filemanager.common.R.id.file_list_item_icon)
            val listTitleView = view.findViewById<TextViewSnippet>(com.filemanager.common.R.id.file_list_item_title)
            val listDetailView = view.findViewById<TextView>(com.filemanager.common.R.id.file_list_item_detail)
            if (listIconView != null && listTitleView != null && listDetailView !== null) {
                dragHoldDrawable = listIconView.drawable
                dragHoldDetail = listDetailView.text.toString()
                dragHoldTitle = handleTitle(listTitleView.text.toString())
                dragHoldDownFile.mViewType = BaseRecentEntity.VIEW_TYPE_ITEM_FILE
            }

            val gridIconView = view.findViewById<ImageView>(com.filemanager.common.R.id.file_grid_item_icon)
            val gridTitleView = view.findViewById<MiddleMultilineTextView>(com.filemanager.common.R.id.title_tv)
            val gridDetailView = view.findViewById<TextView>(com.filemanager.common.R.id.detail_tv)
            if (gridIconView != null && gridTitleView != null && gridDetailView != null) {
                dragHoldDrawable = gridIconView.drawable ?: return false
                Log.d(TAG, "onDragStart grid $dragHoldDrawable size:${Size(dragHoldDrawable.intrinsicWidth, dragHoldDrawable.intrinsicHeight)}")
                dragHoldTitle = handleTitle(gridTitleView.text.toString())
                dragHoldDetail = gridDetailView.text.toString()
                dragHoldDownFile.mViewType = BaseRecentEntity.VIEW_TYPE_ITEM_FILE
                dragHoldDownFile.mLocalType = MimeTypeHelper.IMAGE_TYPE
            }

            Log.d(TAG, "onDragStart view tag = ${view.tag}")
            val itemViewList = ArrayList<View>()
            val selectList = mMainRecentViewModel?.getSelectItems()
            mRecyclerView?.post {
                selectList?.forEach { baseFileBean ->
                    val fileList = mMainRecentViewModel?.uiState?.value?.fileList
                    val indexOf = fileList?.indexOf(baseFileBean)
                    if (indexOf != null && indexOf >= 0 && indexOf < fileList.size) {
                        val viewHolder = mRecyclerView?.findViewHolderForAdapterPosition(indexOf)
                        if (viewHolder != null) {
                            itemViewList.add(viewHolder.itemView)
                        }
                    }
                }
            }
            selectList?.let { DragUtils.createSelectedFileList(selectList) }
            (baseVMActivity as? NavigationInterface)?.let { mMainRecentViewModel?.setNavigateItemAble(it) }
            if (((view.tag as? Int) ?: 0) > 0) {
                mDragScanner?.cancel(true)
                mDragScanner = RecentFileDragDropScanner(
                    it,
                    RecentDragListener(
                        it,
                        view,
                        dragHoldDrawable,
                        dragHoldTitle,
                        dragHoldDetail,
                        e,
                        mMainRecentViewModel?.getRecyclerViewScanMode()
                    ).addSelectedView(itemViewList),
                    null,
                    dragHoldDownFile
                )
                mDragScanner?.let { scanner ->
                    if (scanner.addData(selectList)) {
                        scanner.execute()
                    }
                }
            }
            Log.d(TAG, "onDragStart end")
        }
        return true
    }

    private fun handleTitle(title: String): String {
        if (title.contains(NEWLINE)) {
            val index = title.indexOf(NEWLINE)
            return title.substring(0, index) + title.substring(index + 1)
        }
        return title
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = mMainRecentViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = mMainRecentViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = mMainRecentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.e(TAG, "onDestroyView")
        mEmptyLayout = null
        permissionEmptyView = null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e(TAG, "onDestroy")
        mHandler.removeCallbacksAndMessages(null)
        mMainRecentHandler.removeCallbacksAndMessages(null)
        mMainRecentViewModel?.releaseObserver()
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        mCompressDialog?.dismiss()
        mCompressDialog = null
        mMainRecentViewModel?.releaseDecompressDialog()
    }

    override fun isRefresh(): Boolean {
        val isRefresh = (isPreRefreshing() || isRefreshLayoutDragY()) && isScrollDrag()
        Log.d(TAG, "isRefresh() = $isRefresh")
        return isRefresh
    }

    override fun onClickCompress(baseVMActivity: ComponentActivity) {
        Log.w(TAG, "Recent view not support compress operation")
        mCompressDialog?.dismiss()
        mCompressDialog = CompressConfirmDialog(baseVMActivity, com.support.panel.R.style.COUIFitContentBottomSheetDialog).apply {
            setSavePath(VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) + DEFAULT_SAVE_PATH)
            setModifySavePathListener {
                if (activity is TransformNextFragmentListener) {
                    (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_EDITOR_COMPRESS)
                }
            }
            mMainRecentViewModel?.getSelectItems()?.let {
                if (it.size > 0) {
                    setFileName(it[0].mDisplayName?.substringBeforeLast(".") ?: "")
                }
            }
            setClickButtonListener { name, path ->
                if (mMainRecentViewModel?.doCompress(baseVMActivity, path, name) == true) {
                    mCompressDialog?.dismiss()
                }
            }
            show()
        }
    }

    override fun onClickDeCompress(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.showDeCompressDialog(baseVMActivity) { path, name ->
            operateInterceptor.onDecompress(baseVMActivity, path, name)
        }
    }

    override fun onClickRename(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.doRename(baseVMActivity)
        OptimizeStatisticsUtil.onRename(RECENT)
    }

    override fun onClickEncryption(baseVMActivity: ComponentActivity): Unit? {
        mMainRecentViewModel?.doEncryption(baseVMActivity)
        OptimizeStatisticsUtil.onEncrypt(RECENT)
        return null
    }

    override fun onClickDetail(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.doShowDetail(baseVMActivity)
        OptimizeStatisticsUtil.onDetail(RECENT)
    }

    override fun onClickOtherOpen(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.doOtherOpen(baseVMActivity)
        OptimizeStatisticsUtil.onOpenWith(RECENT)
    }

    override fun onClickCloudDisk(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.doClickCloudDisk(baseVMActivity)
        OptimizeStatisticsUtil.onUploadCloud(RECENT)
    }

    override fun onCopy(baseVMActivity: ComponentActivity) {
        if (baseVMActivity is TransformNextFragmentListener) {
            (baseVMActivity as TransformNextFragmentListener).showSelectPathFragmentDialog(
                MessageConstant.MSG_EDITOR_COPY
            )
        }
        OptimizeStatisticsUtil.onCopy(RECENT)
    }

    override fun createShortCut(baseVMActivity: ComponentActivity) {
        mMainRecentViewModel?.createShortCut(baseVMActivity)
    }

    fun isChildDisplay(): Boolean {
        return isChildDisplay
    }

    fun updateLabels() {
        mMainRecentViewModel?.loadRecentData(RecentDataHelper.TYPE_MEDIA)
    }

    fun forceLoadFromMedia() {
        if (PermissionUtils.hasStoragePermission().not() || !PrivacyPolicyController.hasAgreePrivacy()) {
            setPermissionEmptyVisible(View.VISIBLE)
            return
        }
        setPermissionEmptyVisible(View.GONE)
        mRecyclerView?.visibility = View.VISIBLE
        mMainRecentViewModel?.loadRecentData(RecentDataHelper.TYPE_MEDIA)
    }

    private fun checkIsSelectedMode() : Boolean {
        return (baseVMActivity as? MainActivity)?.checkIsFromLabelFileList() == true
    }

    fun resetIsChangeToSelectedMode() {
        Log.d(TAG, "resetIsChangeToSelectedMode ")
        mMainRecentViewModel?.resetIsChangeToSelectedMode()
    }
    override fun backToTop() {
        getRecyclerView()?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {
    }

    override fun permissionSuccess() {
    }

    override fun setCurrentFromOtherSide(currentPath: String) {
    }

    override fun fromSelectPathResult(code: Int, paths: List<String>?) {
        fromSelectPathResult(code, paths?.getOrNull(0))
    }

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return mMainRecentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        mMainRecentViewModel?.setScanMode(mode)
    }

    override fun isSelectionMode(): Boolean {
        return mMainRecentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.RecentOperatorListener? =
        mMainRecentViewModel?.obtainOperatorResultListener(recentOperateBridge)

    override fun getOperateInterceptor(): IFileOperate? = operateInterceptor

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            mMainRecentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (mMainRecentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val slideWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            slideWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            mRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        toolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
        }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        updatePermissionEmptyLayoutParams()
    }

    private fun updatePermissionEmptyLayoutParams() {
        if (isShowPermissionEmptyView.not()) {
            return
        }
        val emptyLayout = permissionEmptyView ?: return
        val layoutParams = emptyLayout.layoutParams
        //小屏
        if (!isChildDisplay) {
            val navigationViewY = (activity as MainActivity).getNavigationTabView().y.toInt()
            if (layoutParams is MarginLayoutParams) {
                layoutParams.topMargin = stickyViewInitMarginTop
                layoutParams.height = navigationViewY - stickyViewInitMarginTop
                emptyLayout.layoutParams = layoutParams
            }
            //大屏
        } else {
            appBarLayout?.post {
                val emptyMarginTop = appBarLayout?.measuredHeight ?: 0
                val appBarLayoutBottom = (appBarLayout?.y?.toInt() ?: 0) + (appBarLayout?.measuredHeight ?: 0)
                val rootViewHeight = rootView?.measuredHeight ?: 0
                if (layoutParams is MarginLayoutParams) {
                    layoutParams.topMargin = emptyMarginTop
                    layoutParams.height = rootViewHeight - appBarLayoutBottom
                    emptyLayout.layoutParams = layoutParams
                }
            }
        }
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    fun isNavigationTabShow(): Boolean {
        return (mMainRecentViewModel?.mModeState?.listModel?.value
            ?: KtConstants.LIST_NORMAL_MODE) == KtConstants.LIST_NORMAL_MODE
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        this.toolbar = toolbar
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    private inner class RecentOperateInterceptor : IFileOperate {
        override fun onDelete(activity: ComponentActivity): Boolean {
            (baseVMActivity as MainActivity).resetRefreshTime()
            return false
        }

        override fun onCut(activity: ComponentActivity, destPath: String): Boolean {
            (baseVMActivity as MainActivity).resetRefreshTime()
            return false
        }

        override fun onCopy(activity: ComponentActivity, destPath: String): Boolean {
            (baseVMActivity as MainActivity).resetRefreshTime()
            return false
        }

        override fun onDecompress(activity: ComponentActivity, destPath: String?, fileName: String?): Boolean {
            (baseVMActivity as MainActivity).resetRefreshTime()
            return false
        }
    }

    fun resetViewModel() {
        mRecyclerView?.postDelayed({
            mMainRecentViewModel?.reset()
        }, DELAY_UPDATE)
    }
}

interface RefreshLayoutInterface {
    fun isRefresh(): Boolean
}