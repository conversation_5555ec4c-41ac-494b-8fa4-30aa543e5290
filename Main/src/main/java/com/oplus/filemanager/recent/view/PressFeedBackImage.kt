/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PressFeedBackImage
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/7/12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/7/12       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.recent.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import com.filemanager.common.utils.Log

class PressFeedBackImage @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    init {
        Log.d("PressFeedBackImage", "init")
        // 设置点击反馈
        initPressFeedback(this)
    }

    private fun initPressFeedback(view: View) {
        val feedbackUtils =
            COUIPressFeedbackHelperUtil(view, COUIPressFeedbackHelperUtil.CARD_PRESS_FEEDBACK)
        view.setOnTouchListener { v, event ->
            when (event?.action) {
                MotionEvent.ACTION_DOWN ->
                    feedbackUtils.executeFeedbackAnimator(true)

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL ->
                    feedbackUtils.executeFeedbackAnimator(false)
            }
            return@setOnTouchListener false
        }
    }
}