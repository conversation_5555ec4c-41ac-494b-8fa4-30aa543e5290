/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BounceCallBack.kt
 ** Description: Pull-down refresh and pull-up loaded callback interface
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON><PERSON>.<EMAIL>              2019-04-30   1.1         Convert this demo into Kotlin
 ********************************************************************************/
package com.oplus.filemanager.recent.view.refresh

interface BounceCallBack {
    fun startRefresh()
    fun startLoadingMore()
}