package com.oplus.filemanager.recent.entity.recent

import android.text.TextUtils
import com.oplus.filemanager.room.model.RecentFilesEntity
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.getTypeFromPath
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import org.apache.commons.io.FilenameUtils

class RecentFileEntity : BaseRecentEntity {
    var mAbsolutePath: String = ""
        set(path) {
            field = path
            setSuperProperties()
        }

    var mRelativePath: String = ""

    var mVolumeName: String = ""

    var mMediaType = 0

    /**
     * @apiNote The minimum precision of this value is seconds
     */
    var mLastModified: Long = 0
        set(value) {
            field = value
            setSuperProperties()
        }

    var mId: Long? = null

    var mType = 0
        set(value) {
            field = value
            setSuperProperties()
        }

    var mParentDate: String = ""
    var mIsParentExpand: Boolean = true

    var mDbActionType = ACTION_ADD
    var mIsFiltered = false
    var mOrientation = 0
    var mDuration: Long = 0L

    constructor() {}

    /**
     * @param mRelativePath
     * @param mVolumeName
     * @param mLastModified The minimum precision of this value is seconds,
     * Please confirm the accuracy of the input parameter value.
     * @param mSize
     * @param mDisplayName
     * @param mAbsolutePath
     * @param orientation
     */
    @Suppress("LongParameterList")
    constructor(
        mRelativePath: String?,
        mVolumeName: String?,
        mLastModified: Long,
        mSize: Long,
        mDisplayName: String?,
        mAbsolutePath: String?,
        orientation: Int,
        mediaType: Int,
        duration: Long = 0L
    ) {
        this.mAbsolutePath = mAbsolutePath ?: ""
        this.mVolumeName = mVolumeName ?: ""
        this.mRelativePath = mRelativePath ?: ""
        this.mLastModified = mLastModified
        super.mSize = mSize
        super.mDisplayName = mDisplayName
        mOrientation = orientation
        mMediaType = mediaType
        mType = when (mediaType) {
            FileMediaHelper.MEDIA_TYPE_AUDIO -> {
                MimeTypeHelper.AUDIO_TYPE
            }
            FileMediaHelper.MEDIA_TYPE_VIDEO -> {
                MimeTypeHelper.VIDEO_TYPE
            }
            else -> {
                getTypeFromPath(mDisplayName)
            }
        }
        // os13 need show video duration in recent file
        if (FileMediaHelper.MEDIA_TYPE_VIDEO == mediaType) {
            this.mDuration = duration
        }
        setSuperProperties()
    }

    constructor(data: RecentFilesEntity) {
        mId = data.mId
        mAbsolutePath = data.mAbsolutePath ?: ""
        mVolumeName = data.mVolumeName ?: ""
        mRelativePath = data.mRelativePath ?: ""
        if (data.mLastModified != null) {
            mLastModified = data.mLastModified!!
        }
        if (data.mSize != null) {
            super.mSize = data.mSize!!
        }
        super.mDisplayName = data.mDisplayName
        if (data.mType != null) {
            mType = data.mType!!
        }
        mAnotherName = data.mAnotherName ?: ""
        mParentDate = data.mParentDate ?: ""
        setSuperProperties()
    }

    /**
     * @param mRelativePath
     * @param mVolumeName
     * @param mLastModified The minimum precision of this value is seconds,Please confirm the accuracy of the input parameter value.
     * pay attention to whether it is used in seconds or milliseconds
     * @param mSize
     * @param mDisplayName
     * @param mAbsolutePath
     * @param mAnotherName
     * @param mParentDate
     * @param mType
     */
    constructor(mRelativePath: String?, mVolumeName: String?, mLastModified: Long, mSize: Long, mDisplayName: String?, mAbsolutePath: String?, mAnotherName: String?, mParentDate: String?, mType: Int) {
        this.mAbsolutePath = mAbsolutePath ?: ""
        this.mVolumeName = mVolumeName ?: ""
        this.mRelativePath = mRelativePath ?: ""
        this.mLastModified = mLastModified
        super.mSize = mSize
        super.mDisplayName = mDisplayName
        this.mType = mType
        this.mAnotherName = mAnotherName ?: ""
        this.mParentDate = mParentDate ?: ""
        setSuperProperties()
    }

    fun clone(entity: RecentFileEntity) {
        if (!TextUtils.isEmpty(entity.mAbsolutePath)) {
            mAbsolutePath = entity.mAbsolutePath
            super.mDisplayName = FilenameUtils.getName(mAbsolutePath)
        }
        mLastModified = entity.mLastModified
        mDbActionType = entity.mDbActionType
    }

    override fun toString(): String {
        return StringBuilder("mAbsolutePath =").append(if (TextUtils.isEmpty(mAbsolutePath)) "" else mAbsolutePath).append(";")
                .append("mVolumeName =").append(if (TextUtils.isEmpty(mVolumeName)) "" else mVolumeName).append(";")
                .append("mRelativePath =").append(if (TextUtils.isEmpty(mRelativePath)) "" else mVolumeName).append(";")
                .append("mLastModified =").append(mLastModified).append(";")
                .append("mSize =").append(mSize).append(";")
                .append("mDisplayName =").append(if (TextUtils.isEmpty(mDisplayName)) "" else mVolumeName).toString()
    }

    private fun setSuperProperties() {
        super.mData = mAbsolutePath
        super.mLocalType = mType
        super.mIsDirectory = false
        super.mDateModified = mLastModified * SECONDS_TO_MILLISECONDS
    }

    companion object {
        const val ACTION_DELETE = 0
        const val ACTION_UPDATE = 1
        const val ACTION_ADD = 2
    }

    override fun equals(other: Any?): Boolean {
        if (super.equals(other).not()) {
            return false
        }
        if (javaClass != other?.javaClass) return false
        other as RecentFileEntity
        if (mAbsolutePath != other.mAbsolutePath) return false
        if (mRelativePath != other.mRelativePath) return false
        if (mVolumeName != other.mVolumeName) return false
        if (mMediaType != other.mMediaType) return false
        if (mLastModified != other.mLastModified) return false
        if (mId != other.mId) return false
        if (mType != other.mType) return false
        if (mAnotherName != other.mAnotherName) return false
        if (mDuration != other.mDuration) return false
        if (mParentDate != other.mParentDate) return false
        if (mIsParentExpand != other.mIsParentExpand) return false
        return true
    }
}