/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:com.coloros.filemanager.view.main.recent/RecentContentEntity.java
 * * Description:
 * * Version:
 * * Date :2019/7/15
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version>    <desc>
 * *
</desc></version></data></author> */
package com.oplus.filemanager.recent.entity.recent

import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.main.R
import com.oplus.filemanager.recent.task.BaseMediaDBTask.Companion.dateToTimeStamp
import com.oplus.filemanager.recent.task.BaseMediaDBTask.Companion.getTodayEndTime

class RecentGroupEntity : BaseRecentEntity() {
    companion object {
        const val RECENT_DAYS = 3
        const val ONE_DAY_MIN = 24 * 60 * 60
    }

    var mId: String? = null

    /**
     * Group Date, format:yyyy-MM-dd
     */
    var mGroupDate: String = ""

    var mChildSize: Int = 0

    /**
     * Group Show date
     */
    val groupShowDateAndSize: CharSequence
        get() {
            val groupTimeStamp = dateToTimeStamp(mGroupDate)
            val todayEndTime = getTodayEndTime() / KtConstants.SECONDS_TO_MILLISECONDS
            val diffTime = todayEndTime - groupTimeStamp
            val date = (diffTime / ONE_DAY_MIN).toInt()
            var dateDisplay =  if ((diffTime >= 0) && (date in 0..RECENT_DAYS)) {
                if (date == 0) {
                    MyApplication.appContext.getString(com.filemanager.common.R.string.string_today)
                } else {
                    val res = MyApplication.appContext.resources
                    Utils.formatMessage(
                        res.getQuantityString(
                            com.filemanager.common.R.plurals.string_x_days_ago,
                            date,
                            date
                        ), Utils.RTL_POSITION_DOUBLE
                    )
                }
            } else {
                Utils.getDateMDFormat(
                    MyApplication.appContext,
                    groupTimeStamp * KtConstants.SECONDS_TO_MILLISECONDS
                )
            }
            var counts = MyApplication.appContext.resources.getQuantityString(com.filemanager.common.R.plurals.text_x_items, mChildSize, mChildSize)
            return Utils.formatRecentGroup(dateDisplay, counts)
        }

    init {
        setmViewType(VIEW_TYPE_ITEM_GROUP)
        mIsExpand = true
        mIsCanExpand = true
    }

    override fun equals(other: Any?): Boolean {
        if (super.equals(other).not()) {
            return false
        }
        if (javaClass != other?.javaClass) return false
        other as RecentGroupEntity
        return mGroupDate == other.mGroupDate
    }
}