/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PackageNameUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/10/8
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  wanghonglei     2024/10/8    1.0        create
 ***********************************************************************/
package com.oplus.filemanager.recent.utils

import android.content.Context
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import java.io.File

object PackageNameUtil {
    val context: Context by lazy { MyApplication.appContext }
    private const val TAG = "PackageNameUtil"
    var phoneStoragePath: String? = null
    var storagePath999: String? = null
    private var androidDirPath: String? = null
    private var sdPath: String? = null
    private var otgPaths: List<String>? = null
    private const val LENGTH_OTHER_FILE = 6
    private const val LENGTH_ROOT_FILE = 4
    private const val POS_ONE = 0
    private const val POS_TWO = 1
    private const val POS_THR = 2
    private const val POS_FOUR = 3
    private const val POS_FIVE = 4
    private const val POS_SIX = 5

    init {
        initVariables()
    }

    @JvmStatic
    private fun initVariables() {
        if (phoneStoragePath == null) {
            phoneStoragePath = VolumeEnvironment.getInternalSdPath(context)
        }
        if (sdPath == null) {
            sdPath = VolumeEnvironment.getExternalSdPath(context)
        }
        if (otgPaths == null) {
            otgPaths = VolumeEnvironment.getOTGPath(context)
        }
        if (storagePath999 == null) {
            storagePath999 = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
        }
        if (androidDirPath == null) {
            androidDirPath = phoneStoragePath?.let {
                StringBuilder(it).append(File.separator)
                    .append("Android").toString()
            }
        }
    }

    @JvmStatic
    private fun checkIsRootPathFile(pathChilds: Array<String>): String {
        val curPathBuilder: StringBuilder = if (pathChilds[POS_ONE].isEmpty()) {
            StringBuilder(pathChilds[POS_ONE])
        } else {
            StringBuilder(File.separator).append(pathChilds[POS_ONE])
        }
        curPathBuilder.append(File.separator).append(pathChilds[POS_TWO]).append(File.separator)
            .append(pathChilds[POS_THR])
        if (pathChilds.size > LENGTH_ROOT_FILE) {
            curPathBuilder.append(File.separator).append(pathChilds[POS_FOUR])
        }
        val curPath = curPathBuilder.toString()
        Log.d(TAG, "curPath = $curPath ")
        if (curPath == phoneStoragePath) {
            return context.getString(com.filemanager.common.R.string.string_all_files)
        } else if (curPath == sdPath) {
            return context.getString(com.filemanager.common.R.string.storage_external)
        } else if (storagePath999.isNullOrEmpty().not() && curPath.startsWith(storagePath999!!)) {
            Log.w(TAG, "Multi App Path don't be supported")
        } else if ((pathChilds.size == LENGTH_ROOT_FILE) && otgPaths.isNullOrEmpty().not()) {
            otgPaths?.forEach {
                if (curPath.startsWith(it)) {
                    return context.getString(com.filemanager.common.R.string.storage_otg)
                }
            }
        }
        return pathChilds[POS_FOUR]
    }

    @JvmStatic
    fun getPackageName(absolutePath: String): String {
        val result = ""
        if (TextUtils.isEmpty(absolutePath)) {
            return result
        }
        val pathChilds = absolutePath.split(File.separator.toRegex()).toTypedArray()
        //other path exp:"/storage/emulated/0/dir name/filename.file"
        if (pathChilds.size < LENGTH_OTHER_FILE) {
            //root path exp:"/storage/emulated/0/filename.file" or "/storage/XXXX-XXXX/filename.file"
            if (pathChilds.size >= LENGTH_ROOT_FILE) {
                return checkIsRootPathFile(pathChilds)
            }
        } else {
            val firstPath =
                StringBuilder(File.separator).append(pathChilds[POS_TWO]).append(File.separator)
                    .append(pathChilds[POS_THR]).append(File.separator).append(pathChilds[POS_FOUR])
                    .append(File.separator).append(pathChilds[POS_FIVE]).toString()
            val secondPath =
                StringBuilder(firstPath).append(File.separator).append(pathChilds[POS_SIX])
                    .toString()
            Log.d(TAG, "firstPath  =$firstPath; secondPath =$secondPath")
            val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
            var alias: String = cloudConfigApi?.getAlias(secondPath) ?: ""
            if (alias.isNotEmpty()) {
                return alias
            }
            alias = cloudConfigApi?.getAlias(firstPath) ?: ""
            if (alias.isNotEmpty()) {
                return alias
            }
            return pathChilds[POS_FIVE].ifEmpty {
                checkIsRootPathFile(pathChilds)
            }
        }
        return result
    }
}