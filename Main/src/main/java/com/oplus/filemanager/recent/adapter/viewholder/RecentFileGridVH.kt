/***********************************************************
 * * Copyright (C), 2011-2021, Oplus. All rights reserved
 * *
 * * File:/RecentNormalGridVH.kt
 * * Description:
 * * Version:
 * * Date :2021/3/25
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2021/3/25        1.0           Create
 ****************************************************************/
package com.oplus.filemanager.recent.adapter.viewholder

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.MiddleMultilineTextView
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity


open class RecentFileGridVH(
    context: Activity? = null,
    convertView: View,
    listener: OnRecentItemClickListener? = null
) : BaseRecentFileVH(convertView) {
    companion object {
        const val TAG = "RecentNormalGridVH"
        private const val SECOND = 1000L
        fun getLayoutId(): Int {
            return R.layout.recent_file_grid_img_item
        }
    }
    private val mRootView: RelativeLayout = convertView.findViewById(R.id.file_grid_item_layout)
    /*private*/ val mImg: FileThumbView = convertView.findViewById(com.filemanager.common.R.id.file_grid_item_icon)
    private val mTitle: MiddleMultilineTextView = convertView.findViewById(R.id.title_tv)
    private val mDetail: TextView = convertView.findViewById(R.id.detail_tv)
    private var mDuration: TextView = convertView.findViewById(R.id.file_duration_tv)
    private val mSource: TextView = convertView.findViewById(R.id.source_tv)
    private var mImgWith: Int = 0
    private val docRadius = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_4dp)
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.scan_grid_bg_radius)


    /**
     * 判断当前item是否有下一行
     */
    var hasNextRow: Boolean = false

    init {
        mCheckBox = convertView.findViewById(R.id.gridview_scrollchoice_checkbox)
        initImgSize(context)
        mClickListener = listener
    }

    open fun initImgSize(context: Activity?) {
        mImgWith = ItemDecorationFactory.getRecentItemImageWidth(context)
    }

    override fun updateViewHolder(
        context: Context,
        key: Int?,
        file: RecentFileEntity,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        roundConnerType: Int
    ) {
        initImgSize(context as Activity)
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        if (choiceMode) {
            mRootView.setBackgroundResource(R.drawable.file_browser_scan_grid_item_bg)
        } else {
            mRootView.background = null
        }
        mTitle.tag = path
        mDetail.tag = path
        file.mDisplayName?.apply {
            mTitle.text = this
            mTitle.contentDescription = if (file.mIsDirectory) {
                stringResource(R.string.folder)
            } else {
                stringResource(R.string.file)
            } + "\u200B" + "\u3000" + file.mDisplayName
        }
        itemView.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
            override fun onLayoutChange(v: View?, l: Int, t: Int, r: Int, b: Int, oL: Int, oT: Int, oR: Int, oB: Int) {
                itemView.removeOnLayoutChangeListener(this)
                file.mDisplayName?.apply {
                    mTitle.setMultiText(this)
                }
            }
        })
        LabelVHUtils.displayLabelFlag(file, context, mTitle)
        FileImageVHUtils.updateFileGridImgSize(context, mImg, file)
        mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        if (type == MimeTypeHelper.DRM_TYPE) {
            mImg.setDrmState(true)
            val typeString = MimeTypeHelper.getDrmMimeType(context, path)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith("image/"))) {
                mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                mImg.scaleType = ImageView.ScaleType.FIT_XY
            } else {
                mImg.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        } else {
            mImg.setDrmState(false)
            when {
                (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) -> {
                    mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                    mImg.scaleType = ImageView.ScaleType.FIT_XY
                }

                (MimeTypeHelper.isDocType(type)) -> {
                    if (MimeTypeHelper.PPT_TYPE == type || type == MimeTypeHelper.PPTX_TYPE) {
                        mImg.scaleType = ImageView.ScaleType.FIT_END
                    } else {
                        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                    }
                }

                (type == MimeTypeHelper.APPLICATION_TYPE) -> mImg.scaleType = ImageView.ScaleType.FIT_CENTER

                else -> mImg.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        }
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            FileImageLoader.sInstance.clear(context, mImg)
            mImg.mErrorLoadTimes = 0
            mImg.setCallBack(object : FileThumbView.LoadCallBack {
                override fun onError() {
                    displayApplicationWithDetail(context, file, path, sizeCache, threadManager)
                }
            })
            displayApplicationWithDetail(context, file, path, sizeCache, threadManager)
        } else {
            val radius = ViewUtils.getIconRadius(type, docRadius, mImgRadius)
            FileImageLoader.sInstance.clear(context, mImg)
            FileImageLoader.sInstance.displayDefault(
                file,
                mImg,
                0,
                radius,
                FileImageLoader.THUMBNAIL_TYPE_SQUARE,
                true,
                isCoverError = true,
                isSmallDoc = true
            )
            mDetail.visibility = View.VISIBLE
            showDetail(context, file, mDetail, path, sizeCache, threadManager, false) {}
        }
        mSource.text = file.mAnotherName
        mCheckBox?.let {
            if (choiceMode) {
                if (selectionArray.contains(key)) {
                    it.state = COUICheckBox.SELECT_ALL
                    it.visibility = View.VISIBLE
                } else {
                    it.isEnabled = false
                    it.state = COUICheckBox.SELECT_NONE
                    it.visibility = View.VISIBLE
                    it.isEnabled = true
                }
            } else {
                it.state = COUICheckBox.SELECT_NONE
                it.jumpDrawablesToCurrentState()
                it.visibility = View.GONE
            }
        }
        val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
        mSource.alpha = alpha
        showDurationIfNeed(file, mDuration)
    }

    private fun showDurationIfNeed(file: RecentFileEntity, mDuration: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mDuration != 0L) {
            mDuration.visibility = View.VISIBLE
            mDuration.text = KtUtils.formatVideoTime(file.mDuration / SECOND)
        } else {
            mDuration.visibility = View.GONE
        }
    }

    private fun displayApplicationWithDetail(
        context: Context,
        file: BaseFileBean,
        path: String,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager
    ) {
        FileImageLoader.sInstance.displayApplicationWithDetail(file, mImg,
            { applicationInfoDetail ->
                val tagDetail = mDetail.tag as? String
                if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(applicationInfoDetail?.mApkVersion)) {
                    mDetail.visibility = View.VISIBLE
                    mDetail.text = KtUtils.formatSize(file)
                } else {
                    showDetail(context, file, mDetail, path, sizeCache, threadManager, false, {})
                }
            }, isGridMode = true
        )
    }

    override fun getPerformChildClickWidget(): View? {
        return null
    }

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        itemView.getGlobalVisibleRect(checkBoxRect)
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }
}