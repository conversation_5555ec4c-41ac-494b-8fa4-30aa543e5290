<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_divider_height"
        android:layout_gravity="center_horizontal"
        android:layout_marginVertical="8dp"
        android:layout_marginLeft="@dimen/common_margin"
        android:layout_marginRight="@dimen/common_margin"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false" />

    <include
        android:id="@+id/side_private_safe"
        layout="@layout/item_side_options"
        android:contentDescription="@string/string_encrypt_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_recycle_bin"
        layout="@layout/item_side_options"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>