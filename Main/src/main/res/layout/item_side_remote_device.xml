<?xml version="1.0" encoding="utf-8"?>
<com.oplus.filemanager.parentchild.view.SideNavigationItemContainer xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/option_wrapper"
    android:theme="@style/COUIMaskEffectDrawableTheme"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:paddingVertical="@dimen/oplus_doc_sidebar_list_item_wrapper_padding_horizontal"
    android:paddingHorizontal="@dimen/oplus_doc_sidebar_list_item_wrapper_padding_horizontal"
    android:focusable="true"
    android:clickable="true"
    android:forceDarkAllowed="false">

    <ImageView
        android:visibility="gone"
        android:id="@+id/drag_view"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_sidebar_sort" />

    <com.oplus.filemanager.main.view.CropImageView
        android:id="@+id/option_icon"
        android:layout_width="24dp"
        android:layout_height="24dp" />

    <TextView
        android:id="@+id/option_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:layout_marginHorizontal="8dp"
        android:textFontWeight="500"
        android:textAppearance="?attr/couiTextAppearanceHeadline6"
        android:maxLines="2"
        android:ellipsize="end" />

    <TextView
        android:id="@+id/option_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textFontWeight="400"
        android:textAppearance="?attr/couiTextAppearanceBody"
        android:textAlignment="viewEnd"
        android:maxLines="1"
        android:ellipsize="end" />

    <ImageView
        android:visibility="gone"
        android:id="@+id/lock_icon"
        android:layout_width="24dp"
        android:layout_height="24dp" />

    <LinearLayout
        android:visibility="gone"
        android:id="@+id/option_widget"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clipChildren="false" />
</com.oplus.filemanager.parentchild.view.SideNavigationItemContainer>