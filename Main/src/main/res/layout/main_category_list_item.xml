<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.SmoothRoundedCornersConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_category_item"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_main_card"
    app:corner_radius="?attr/couiRoundCornerM"
    app:default_background="true">

    <ImageView
        android:visibility="gone"
        android:id="@+id/drag_view"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="-4dp"
        android:src="@drawable/ic_sidebar_sort" />

    <ImageView
        android:id="@+id/list_item_icon"
        android:layout_width="@dimen/main_category_item_icon_size_width"
        android:layout_height="@dimen/main_category_item_icon_size_height"
        android:forceDarkAllowed="false"
        android:layout_marginTop="-10dp"
        android:importantForAccessibility="no"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/drag_view" />

    <TextView
        android:id="@+id/list_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/font_line_space_1"
        android:minHeight="@dimen/main_category_item_title_size_height"
        android:textAppearance="@style/couiTextButtonM"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/list_item_icon"
        app:layout_constraintWidth_default="spread" />

    <TextView
        android:id="@+id/list_item_sub_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_2dp"
        android:gravity="center"
        android:minHeight="@dimen/main_category_item_title_size_height"
        android:textAppearance="@style/couiTextBodyXS"
        android:textColor="?attr/couiColorLabelSecondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/list_item_title"
        app:layout_constraintStart_toStartOf="@id/list_item_title"
        app:layout_constraintTop_toBottomOf="@id/list_item_title"
        app:layout_constraintWidth_default="spread" />

    <ViewStub
        android:id="@+id/vs_red_dot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/app_market_manager_hot_dot"
        android:layout_marginTop="@dimen/dimen_6dp_half"
        android:layout_marginEnd="@dimen/dimen_8dp"
        app:layout_constraintEnd_toEndOf="@id/list_item_icon"
        app:layout_constraintTop_toTopOf="@id/list_item_icon"/>
</com.filemanager.common.view.SmoothRoundedCornersConstraintLayout>