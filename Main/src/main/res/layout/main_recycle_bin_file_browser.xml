<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_recycle_bin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:minHeight="@dimen/dimen_50dp"
    android:paddingVertical="10dp"
    android:paddingStart="@dimen/dimen_32dp"
    android:paddingEnd="@dimen/dimen_32dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_recycle_bin_cons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/icon_recycle_bin"
            android:layout_width="@dimen/dimen_24dp"
            android:layout_height="@dimen/dimen_24dp"
            android:contentDescription="@null"
            android:src="@drawable/ic_recently_deleted_category"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/recycle_bin_title"
            style="?android:attr/textAppearanceLarge"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_18dp"
            android:text="@string/text_recycle_bin"
            android:textAlignment="viewStart"
            android:includeFontPadding="false"
            app:layout_constraintBottom_toTopOf="@id/recycle_bin_desc"
            app:layout_constraintEnd_toStartOf="@id/arrow_container"
            app:layout_constraintStart_toEndOf="@id/icon_recycle_bin"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/recycle_bin_desc"
            style="@style/COUIPreferenceSummaryStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/support_preference_margin_between_line"
            android:textAlignment="viewStart"
            android:includeFontPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/recycle_bin_title"
            app:layout_constraintStart_toStartOf="@id/recycle_bin_title"
            app:layout_constraintTop_toBottomOf="@id/recycle_bin_title" />

        <LinearLayout
            android:id="@+id/arrow_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/privacy_lock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:scaleType="fitCenter"
                android:src="@drawable/privacy_psd_lock"
                android:layout_marginEnd="@dimen/dimen_4dp" />

            <ImageView
                android:id="@+id/arrow_recycle_bin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:scaleType="fitCenter"
                android:src="@drawable/coui_btn_next" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>