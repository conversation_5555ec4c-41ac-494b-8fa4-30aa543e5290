<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/item_container"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:paddingStart="0dp"
    android:paddingEnd="@dimen/dimen_16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/action_download"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dimen_50dp">

            <ImageView
                android:id="@+id/list_item_drag"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_marginStart="-8dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_drag_view_small"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.oplus.filemanager.main.view.CropImageView
                android:id="@+id/list_item_icon"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_marginStart="@dimen/dimen_16dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_download"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/list_item_drag"
                app:layout_constraintTop_toTopOf="parent" />

            <com.oplus.filemanager.main.view.SideEditText
                android:id="@+id/item_title"
                style="@style/Widget.COUI.EditTextView"
                android:textAppearance="?attr/couiTextAppearanceHeadline6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_16dp"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:imeOptions="actionDone"
                android:focusable="false"
                android:clickable="false"
                android:focusableInTouchMode="false"
                android:inputType="text|textMultiLine"
                android:layout_marginVertical="@dimen/dimen_10dp"
                android:maxLines="2"
                android:text="@string/download"
                android:textAlignment="viewStart"
                app:layout_constraintBottom_toTopOf="@+id/item_desc"
                app:layout_constraintEnd_toStartOf="@id/list_item_num"
                app:layout_constraintStart_toEndOf="@id/list_item_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/item_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/cloud_docs"
                android:textAlignment="viewStart"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/item_title"
                app:layout_constraintStart_toStartOf="@+id/item_title"
                app:layout_constraintTop_toBottomOf="@+id/item_title" />

            <ImageView
                android:id="@+id/list_item_delete"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_marginEnd="@dimen/main_list_delete_view_normal_margin_end"
                android:contentDescription="@string/menu_file_list_delete"
                android:src="@drawable/ic_edit_delete"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.coui.appcompat.couiswitch.COUISwitch
                android:id="@+id/list_item_switch"
                android:layout_width="@dimen/dimen_38dp"
                android:layout_height="@dimen/dimen_24dp"
                android:layout_marginEnd="@dimen/main_list_switch_view_normal_margin_end"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:checked="false"
                android:clickable="true"/>

            <TextView
                android:id="@+id/list_item_num"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dimen_4dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewEnd"
                android:textColor="@color/coui_color_label_secondary"
                android:textFontWeight="400"
                android:textSize="@dimen/font_size_14"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/arrow_icon"
                app:layout_constraintTop_toTopOf="parent" />

            <include
                android:id="@+id/arrow_icon"
                layout="@layout/main_arrow_image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:layout_marginStart="@dimen/dimen_72dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:background="?attr/couiColorDivider" />
    </LinearLayout>
</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>
