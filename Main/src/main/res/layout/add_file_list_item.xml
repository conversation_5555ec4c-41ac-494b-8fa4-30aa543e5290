<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/add_file_list_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:background="@drawable/select_list_item_background_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/file_list_item_icon"
        android:layout_width="@dimen/main_image_width"
        android:layout_height="@dimen/main_image_height"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:id="@+id/rl_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/listview_scrollchoice_checkbox"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon"
        app:layout_constraintTop_toTopOf="parent">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:maxLines="2"
            android:includeFontPadding="false"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textAppearance="?attr/couiTextHeadlineXS"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/mark_file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:textSize="@dimen/file_list_item_detail_text_size" />
    </RelativeLayout>


    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="23dp"/>
</androidx.constraintlayout.widget.ConstraintLayout >