/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryFragmentFactoryTest
 ** Description : CategoryFragmentFactory Unit Test
 ** Version     : 1.0
 ** Date        : 2022/10/24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/10/24      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.main.utils

import android.os.Looper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.oplus.filemanager.main.ui.category.MainCategoryFragment
import com.oplus.filemanager.parentchild.ui.MainCombineFragment
import com.oplus.filemanager.parentchild.ui.MainParentFragment
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class CategoryFragmentFactoryTest {

    @Before
    fun setup() {
        mockkObject(CategoryFragmentFactory)
        mockkObject(UIConfigMonitor)
        mockkStatic(Looper::class)
        val mainLooper = mockk<Looper>()
        every { Looper.getMainLooper() }.returns(mainLooper)
        every { mainLooper.thread }.returns(Thread.currentThread())
    }

    @After
    fun teardown() {
        unmockkObject(CategoryFragmentFactory)
        unmockkObject(UIConfigMonitor)
        unmockkStatic(Looper::class)
    }

    @Ignore
    @Test
    fun should_notNull_when_create() {
        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_LAUNCH_FROM_SMALL)
        var fragment = CategoryFragmentFactory.create()
        Assert.assertTrue(fragment is MainCategoryFragment)

        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE)
        fragment = CategoryFragmentFactory.create()
        Assert.assertTrue(fragment is MainParentFragment)

        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_DEFAULT)
        fragment = CategoryFragmentFactory.create()
        Assert.assertNotNull(fragment)
    }

    @Ignore
    @Test
    fun should_when_switch() {
        val fragment = mockk<MainCombineFragment>()
        justRun { CategoryFragmentFactory.switchToCategoryFragment(any(), true) }
        justRun { CategoryFragmentFactory.switchToMainRecentFragment(any()) }
        justRun { fragment.setContainerVisible(any(), any()) }
        justRun { fragment.ensureParentFragment() }
        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_LARGE_TO_SMALL)
        CategoryFragmentFactory.switch(fragment, 0, false, false)
        verify { fragment.setContainerVisible(false, true) }

        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_SMALL_TO_LARGE)
        CategoryFragmentFactory.switch(fragment, 0, false, false)
        verify { fragment.setContainerVisible(true, true) }

        every { UIConfigMonitor.getCurrentScreenState() }.returns(UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE)
        CategoryFragmentFactory.switch(fragment, 0, false, false)
        verify { fragment.setContainerVisible(true, true) }
    }

    @Ignore
    @Test
    fun should_when_switchToRecentFragment() {
        val fragment = mockk<MainCombineFragment>()
        every { fragment.secondFragment }.returns(mockk<MainCategoryFragment>())
        justRun { fragment.setCurrentChildFragment(any(), any()) }
        justRun { fragment.secondFragment = any() }
        CategoryFragmentFactory.switchToMainRecentFragment(fragment)
        verify { fragment.setCurrentChildFragment(CategoryHelper.CATEGORY_RECENT, any()) }
    }

    @Test
    fun should_when_switchToCategoryFragment() {
/*        val fragment = mockk<MainCompainFragment>()
        val fragmentMgr = mockk<FragmentManager>()
        val transaction = mockk<FragmentTransaction>()
        every { fragment.mSecondFragment }.returns(mockk<MainCategoryFragment>())
        justRun { fragment.mSecondFragment = any() }
        every { fragment.childFragmentManager }.returns(fragmentMgr)
        every { fragmentMgr.beginTransaction() }.returns(transaction)
        every { transaction.replace(any(), any(), any<String>()) }.returns(transaction)
        every { transaction.commitAllowingStateLoss() }.returns(1)
        every { fragment.getFragmentTag(any()) }.returns("test")
        CategoryFragmentFactory.switchToCategoryFragment(fragment)
        verify { transaction.replace(any(), any<MainCategoryFragment>(), any()) }*/
    }
}