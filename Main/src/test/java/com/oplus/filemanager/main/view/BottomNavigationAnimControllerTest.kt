/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.main.view
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/4/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.main.view

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationType
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class BottomNavigationAnimControllerTest {

    @MockK
    private lateinit var navigationView: COUINavigationView
    private lateinit var animController: BottomNavigationAnimController
    private lateinit var navigationController: NavigationController
    private lateinit var layoutParams: MarginLayoutParams
    private lateinit var activity: Activity
    private var needShowTab = false

    @Before
    fun before() {
        MockKAnnotations.init(this)
        justRun { navigationView.setOnAnimatorShowHideListener(any()) }
        animController = spyk(BottomNavigationAnimController(navigationView) {
            needShowTab
        })
        navigationController = mockk {
            every { type } returns NavigationType.DEFAULT
            justRun { setAnimEndListener(any()) }
        }
        layoutParams = MarginLayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        every { navigationView.layoutParams }.returns(layoutParams)
        activity = mockk()

        mockkStatic(FeatureCompat::class)
    }

    @After
    fun teardown() {
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should execute setAnimEndListener when setSubNavigationTool`() {
        animController.setSubNavigationTool(navigationController)
        verify { navigationController.setAnimEndListener(any()) }
    }

    @Test
    fun `should not execute showNavigation when showToolNavDirectly if nav controller is null`() {
        val activity = mockk<Activity>()
        justRun { navigationController.showNavigation(activity, 100) }
        animController.showToolNavDirectly(activity, 100)
        verify(inverse = true) { navigationController.showNavigation(activity, 100) }
    }

    @Test
    fun `should execute showNavigation when showToolNavDirectly`() {
        val activity = mockk<Activity>()
        justRun { navigationController.showNavigation(activity, 100) }
        animController.setSubNavigationTool(navigationController)
        animController.showToolNavDirectly(activity, 100)
        verify { navigationController.showNavigation(activity, 100) }
    }

    @Test
    fun `should true when isHide if view is gone`() {
        every { navigationView.visibility } returns View.GONE
        assertTrue(animController.isHide(navigationView))

        every { navigationView.visibility }.returns(View.VISIBLE)
        layoutParams.bottomMargin = -156

        Assert.assertTrue(animController.isHide(navigationView))

        layoutParams.bottomMargin = 0
        Assert.assertFalse(animController.isHide(navigationView))
    }

    @Test
    fun `should execute show when showMainTabNav if is hide`() {
        every { navigationView.visibility }.returns(View.VISIBLE)
        every { animController.isHide(navigationView) } returns false
        justRun { navigationView.show(any()) }
        animController.showMainTabNav()
        verify(inverse = true) { navigationView.show(false) }

        every { navigationView.visibility } returns View.GONE
        every { animController.isHide(navigationView) } returns true
        justRun { navigationView.show(false) }
        animController.showMainTabNav()
        verify { navigationView.show(false) }
    }


    @Test
    fun should_call_isHide_when_showToolNav() {
        justRun { navigationController.prepare(any(), any()) }
        justRun { navigationController.showNavigation(any(), any()) }
        animController.setSubNavigationTool(navigationController)
        every { navigationView.visibility }.returns(View.GONE)
        animController.mIsAnimating = true
        animController.showToolNav(activity, 0)
        Assert.assertEquals(true, animController.mIsAnimating)
        verify(inverse = true) { animController.isHide(navigationView) }

        animController.mIsAnimating = false
        animController.showToolNav(activity, 0)
        Assert.assertEquals(true, animController.mIsAnimating)
        verify { animController.isHide(navigationView) }
    }

    @Test
    fun should_call_hideToolNav_when_hideToolNavDirectly() {
        justRun { navigationController.hideNavigationDirectly(any()) }
        animController.setSubNavigationTool(navigationController)
        animController.hideToolNavDirectly(activity)
        verify { navigationController.hideNavigationDirectly(activity) }
    }

    @Test
    fun should_call_isShowNavigation_when_hideToolNav() {
        every { navigationController.isShowNavigation() }.returns(false)
        justRun { navigationController.hideNavigation(any()) }
        animController.setSubNavigationTool(navigationController)
        animController.mIsAnimating = true
        animController.hideToolNav(activity)
        verify(inverse = true) { navigationController.isShowNavigation() }

        animController.mIsAnimating = false
        animController.hideToolNav(activity)
        verify { navigationController.isShowNavigation() }
    }


    @Test
    fun should_call_hideNavigation_when_hideToolNav() {
        every { navigationController.isShowNavigation() }.returns(false)
        justRun { navigationController.hideNavigation(any()) }
        animController.setSubNavigationTool(navigationController)
        animController.mIsAnimating = false
        animController.hideToolNav(activity)
        verify(inverse = true) { navigationController.hideNavigation(activity) }

        every { navigationController.isShowNavigation() }.returns(true)
        animController.mIsAnimating = false
        animController.hideToolNav(activity)
        verify { navigationController.hideNavigation(activity) }
    }

    @Test
    fun should_call_onDestroy_when_release() {
        justRun { navigationController.onDestroy() }
        animController.setSubNavigationTool(navigationController)
        animController.release()
        verify { navigationController.onDestroy() }
    }

    @Test
    fun should_call_run_when_OnceAnimConsumer_accept() {
        var count = 0
        val consumer = object : BottomNavigationAnimController.OnceAnimConsumer() {
            override fun run(t: Boolean) {
                count++
            }
        }

        consumer.accept(true)
        consumer.accept(true)
        consumer.accept(true)

        Assert.assertEquals(1, count)
    }
}