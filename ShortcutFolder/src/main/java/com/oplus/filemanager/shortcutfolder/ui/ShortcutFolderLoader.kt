/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.list.LabelListLoader
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.shortcutfolder.ui

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.selectdir.SelectPathLoader

class ShortcutFolderLoader(context: Context, filePath: String) :
    SelectPathLoader(context, filePath) {

    companion object {
        private const val TAG = "ShortcutFolderLoader"
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): ArrayList<BaseFileBean> {
        val currentSort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getShortcutFolderKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getShortcutFolderKey())
        val mLastSort = SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        Injector.injectFactory<IDocumentExtensionType>()?.sortFiles(list, currentSort, mLastSort, true, isDesc)
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(list)
        return list as ArrayList<BaseFileBean>
    }
}