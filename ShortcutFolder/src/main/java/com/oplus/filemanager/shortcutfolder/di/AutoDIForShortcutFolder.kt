/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: AutoDIForShortFolder
 * * Description: AutoDIForQuickFolder
 * * Version: 1.0
 * * Date : 2024/10/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/22      1.0            create
 ****************************************************************/
package com.oplus.filemanager.shortcutfolder.di

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.filemanager.shortcutfolder.ShortcutFolderApi
import org.koin.dsl.module

@Keep
object AutoDIForShortcutFolder {

    val shortcutFolderApi = module {
        single<IShortcutFolderApi>(createdAtStart = true) {
            ShortcutFolderApi()
        }
    }
}