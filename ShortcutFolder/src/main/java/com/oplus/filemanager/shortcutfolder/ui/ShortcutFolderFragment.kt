/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ShortcutFolderFragment
 * * Description: 快捷文件夹 详情Fragment
 * * Version: 1.0
 * * Date : 2024/10/23
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/23      1.0            create
 ****************************************************************/
package com.oplus.filemanager.shortcutfolder.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import androidx.core.view.doOnNextLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.coui.responsiveui.config.UIConfig.WindowType
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.FeedbackFloatingButton
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.addfilepanel.AddFileClickListener
import com.oplus.filemanager.addfilepanel.AddFileController
import com.oplus.filemanager.addfilepanel.bean.AddFileBean
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.filemanager.shortcutfolder.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class ShortcutFolderFragment : RecyclerSelectionVMFragment<ShortcutFolderViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener, IPreviewListFragment, IRefreshFragmentDataForDir, AddFileClickListener {

    companion object {
        private const val TAG = "ShortcutFolderFragment"
        private const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
        private const val LOAD_DATA_DELAYED_TIME = 500L
        private const val ID_ADD_FOLDER = 0
        private const val ID_ADD_FILE = 1
        private const val OPERATE_SUCCESS = 200
    }

    private var mTitle: String? = null
    private var path: String = ""
    private var currentFileBrowserPath: String = ""
    private var dbID: Long = 0L
    private var mIsFilter: Boolean = false
    private var mIsFromSearch: Boolean = false
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mAdapter: ShortcutFolderAdapter? = null
    private var mLayoutManager: GridLayoutManager? = null
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
    }
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController: FileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    private var needLoadData = false
    private var isChildDisplay = false
    private var bySideRefreshScanMode = false
    private var hasShowEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /** 是否显示底部工具栏，父子级时用于父级Fragment判断 */
    var isShowNav = false
    private var mAddFileDialogShow: Boolean = false

    /**
     * 触发 navigation 显示和隐藏
     */
    var triggerNavigation: Boolean = false

    private var previewOperate: IPreviewOperate? = null

    private var mAddFab: FeedbackFloatingButton? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    private var mAddPopupWindow: COUIPopupListWindow? = null
    private val mAddPopupItemList: MutableList<PopupListItem> = mutableListOf()
    private val mAddArrayMap: ArrayMap<Int, String> = arrayMapOf(
        ID_ADD_FOLDER to appContext.resources.getString(com.filemanager.common.R.string.menu_file_list_new_folder),
        ID_ADD_FILE to appContext.resources.getString(com.filemanager.common.R.string.label_files_add_file)
    )
    private var mAddFileController: AddFileController? = null
    private var mFileServiceAction: IFileService? = null
    private var sideCategoryType = -1
    override fun getLayoutResId(): Int {
        return R.layout.fragment_shortcut_folder
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        rootView = view.findViewById(R.id.coordinator_layout_label_file)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        initToolbar()
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getShortcutFolderKey())
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onMenuItemSelected(menu)
        }
        // mAddFab = view.findViewById(R.id.add_folder_fab)
        mAddFab?.mainFloatingButton?.contentDescription = stringResource(com.filemanager.common.R.string.string_add_fast_folder)
        updateFloatButtonMargin()
        mAddFab?.setFloatingButtonClickListener {
            showAddFolderDialog()
        }
        scrollHelper = DragScrollHelper(getRecyclerView())
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let { recyclerView ->
            mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_3).apply {
            }
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = mFolderTransformAnimator
            recyclerView.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            resetRecyclerViewHoriontalPadding(recyclerView)

            mAdapter?.let {
                recyclerView.adapter = it
            }
            appBarLayout?.doOnNextLayout {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(
                        recyclerView.paddingLeft,
                        KtViewUtils.getRecyclerViewTopPadding(appBarLayout, 0),
                        recyclerView.paddingRight, paddingBottom
                    )
                }
            }
            recyclerView.setLoadStateForScroll(this)
        }
        if (needLoadData) {
            onResumeLoadData()
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun resetRecyclerViewHoriontalPadding(recyclerView: FileManagerRecyclerView) {
        (activity as? ShortcutFolderActivity)?.let {
            if (UIConfigMonitor.getWindowType() == UIConfig.WindowType.LARGE) {
                recyclerView.setPadding(
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp),
                    recyclerView.paddingTop,
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp),
                    recyclerView.paddingBottom
                )
            } else {
                recyclerView.setPadding(0, recyclerView.paddingTop, 0, recyclerView.paddingBottom)
            }
        }
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        if (isChildDisplay) {
            toolbar.navigationIcon = null
        } else {
            toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            toolbar.setNavigationOnClickListener {
                clickNavigationIcon()
            }
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(R.menu.shortcut_folder_menu)
        displayActionByIsChild(toolbar)
        setToolbarEditIcon(toolbar, isChildDisplay)
        updateEditAndSortMenu(toolbar)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun displayActionByIsChild(toolbar: COUIToolbar) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = !isChildDisplay
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            it.setIcon(null)
            it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        }
    }

    private fun setSearchMenuStatus(status: Int?, isChildDisplay: Boolean) {
        toolbar?.menu?.findItem(R.id.actionbar_search)?.apply {
            if (status == KtConstants.LIST_SELECTED_MODE
                && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST
                && isChildDisplay) {
                icon = null
                setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_search)
                setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                setToolbarEditIcon(it, isChildDisplay)
                setSearchMenuStatus(status, isChildDisplay)
                if (bySideRefreshScanMode) {
                    refreshScanModeItemIcon(it, isChildDisplay)
                }
                bySideRefreshScanMode = true
            }
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        if (!isChildDisplay) {
            toolbar.navigationIcon = null
            toolbar.setNavigationOnClickListener(null)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            startListModelObserver(viewModule)
            startUIStateObserver(viewModule)
            startScanModeObserver()
            startObserveLoadState()
            startSideNavigationStatusObserver()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun startListModelObserver(viewModule: ShortcutFolderViewModel) {
        viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.mModeState.initState) {
                    mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    return
                }
                Log.d(TAG, "mListModel=$value")
                if (value == KtConstants.LIST_SELECTED_MODE) {
                    previewEditedFiles(fragmentViewModel?.getSelectItems())
                    isShowNav = true
                    mAdapter?.setSelectEnabled(true)
                    fragmentRecyclerView?.let {
                        val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                        val paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView) +
                                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_22dp)
                        it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                    }
                    mToolbar?.let {
                        changeActionModeAnim(it, {
                            initToolbarWithEditMode(it)
                            refreshSelectToolbar(it)
                        })
                        it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    }
                    triggerNavigation {
                        (baseVMActivity as? NavigationInterface)?.let {
                            it.showNavigation()
                            fragmentViewModel?.setNavigateItemAble(it)
                        }
                    }
                    mAddFab?.visibility = View.GONE
                } else {
                    mAdapter?.setSelectEnabled(false)
                    fragmentRecyclerView?.let {
                        it.setPadding(
                            it.paddingLeft,
                            it.paddingTop,
                            it.paddingRight,
                            appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_52dp)
                        )
                    }
                    mToolbar?.let {
                        changeActionModeAnim(it, {
                            initToolbarNormalMode(it)
                            refreshScanModeItemIcon(it)
                        }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                        it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    }
                    isShowNav = false
                    triggerNavigation {
                        (baseVMActivity as? NavigationInterface)?.hideNavigation()
                    }
                    mAddFab?.visibility = View.VISIBLE
                }
            }
        })
    }

    private fun startUIStateObserver(viewModule: ShortcutFolderViewModel) {
        viewModule.uiState.observe(this) { fileUiModel ->
            Log.d(TAG, "UiModel mUiState =" + fileUiModel.fileList.size + "," + fileUiModel.selectedList.size + "," + fileUiModel.keyWord)
            sortEntryView?.setFileCount(viewModule.getRealFileSize())
            if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                mToolbar?.let {
                    refreshSelectToolbar(it)
                }
                if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                    mFolderTransformAnimator.mIsFolderInAnimation = true
                    mAdapter?.checkComputingAndExecute {
                        mAdapter?.setData(
                            fileUiModel.fileList as ArrayList<BaseFileBean>,
                            fileUiModel.selectedList
                        )
                        previewEditedFiles(fragmentViewModel?.getSelectItems())
                    }
                }
            } else {
                previewClickedFile(fragmentViewModel?.previewClickedFileLiveData?.value, fragmentViewModel?.previewClickedFileLiveData)
                if (fileUiModel.fileList.isEmpty()) {
                    showEmptyView()
                } else {
                    hideEmptyView()
                }
                mToolbar?.let {
                    refreshScanModeItemIcon(it)
                    updateEditAndSortMenu(it)
                }
                if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                    mAdapter?.checkComputingAndExecute {
                        mAdapter?.let {
                            it.setKeyWord(fileUiModel.keyWord)
                            mFolderTransformAnimator.mIsFolderInAnimation = true
                            it.setData(
                                fileUiModel.fileList as ArrayList<BaseFileBean>,
                                fileUiModel.selectedList
                            )
                        }
                    }
                }
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
                bgColor?.let { color ->
                    setBackgroundColor(color)
                }
                setDeleyShowTime(LOAD_DATA_DELAYED_TIME)
                setShowAinimate(true)
                setDissapearAnimate(true)
                setShowLoadingTips(false)
                //这里蒋LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            if (isChildDisplay) {
                if (fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
                    || (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
                            && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST)) {
                    it.icon = null
                    it.title = desc
                    it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
                } else {
                    it.title = null
                    if (needSkipAnimation) {
                        it.setIcon(resId)
                    } else {
                        KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
                    }
                    it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
                }
            } else {
                it.icon = null
                it.title = desc
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        if (baseVMActivity is NavigationInterface) {
            val isEnable = fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() ?: false
            triggerNavigation {
                (baseVMActivity as NavigationInterface).setNavigateItemAble(
                    isEnable, hasDrmFile(fragmentViewModel?.getSelectItems())
                )
            }
        }
    }

    private fun triggerNavigation(callback: () -> Unit) {
        triggerNavigation = true
        callback.invoke()
        triggerNavigation = false
    }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private fun startScanModeObserver() {
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode, ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            mAdapter?.checkComputingAndExecute {
                notifyDataSetChanged()
            }
        }
        setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
        toolbar?.let { setToolbarEditIcon(it, isChildDisplay) }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.mModeState?.initState == true && fragmentViewModel?.uiState?.value?.fileList?.isEmpty() != false) {
            showEmptyView()
        }
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getShortcutFolderKey())
        if (mAddFileDialogShow) {
            mAddFileDialogShow = false
            rootView?.post {
                showAddFolderDialog()
            }
        }
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }


    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        mAddFileController = null
        mFileServiceAction = null
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            if (PathUtils.isDocumentsUIPath(path)) { // 访问受限的页面
                val emptyMarginTop = toolbar?.let { toolbar -> (toolbar.y + toolbar.measuredHeight).toInt() } ?: 0
                mFileEmptyController.showGuideDocumentsUIView(baseVMActivity!!, rootView!!, path, emptyMarginTop)
            } else {
                // 普通的空页面
                mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
                mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
                mFileEmptyController.setEmptySummaryVisibilityAndContent(
                    View.VISIBLE,
                    appContext.resources.getString(com.filemanager.common.R.string.add_content_tips)
                )
            }
            sortEntryView?.visibility = View.INVISIBLE
            fragmentRecyclerView?.visibility = View.INVISIBLE
            mAddFab?.visibility = View.GONE
            hasShowEmpty = true
            listEmptyFile()
            Log.d(TAG, "showEmptyView")
        }
    }

    private fun hideEmptyView() {
        mFileEmptyController.hideFileEmptyView()
        sortEntryView?.visibility = View.VISIBLE
        fragmentRecyclerView?.visibility = View.VISIBLE
        mAddFab?.visibility = View.VISIBLE
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            return
        }
        val bundle = arguments ?: return
        mTitle = bundle.getString(KtConstants.P_TITLE) ?: ""
        path = bundle.getString(KtConstants.FILE_PATH) ?: ""
        dbID = bundle.getLong(Constants.DB_ID)
        handleSideCategoryType(bundle)
        currentFileBrowserPath = ""
        Log.d(TAG, "onResumeLoadData title:$mTitle path:$path dbID:$dbID")
        if (fragmentViewModel?.mModeState?.listModel?.value != KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.title = mTitle
        }
        if (PermissionUtils.hasStoragePermission().not()) {
            return
        }
        if (dbID == 0L) {
            Log.w(TAG, "onResumeLoadData dbID is zero")
            showEmptyView()
        } else {
            fragmentViewModel?.initLoader(path, LoaderViewModel.getLoaderController(this))
        }
    }

    override fun createViewModel(): ShortcutFolderViewModel {
        val vm = ViewModelProvider(this)[ShortcutFolderViewModel::class.java]
        val sortMode = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getShortcutFolderKey())
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_FILE_BROWSER, vm, sortMode).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey] ?: return true
            activity?.let {
                if (baseFile.mIsDirectory) {
                    val fileBrowser = Injector.injectFactory<IFileBrowser>()
                    currentFileBrowserPath = baseFile.mData ?: ""
                    if (WindowUtils.isSmallScreen(it)) {
                        fileBrowser?.startFileBrowserActivity(it, baseFile.mData, fromDetail = true, isFromShortcutFolder = true)
                    } else {
                        fileBrowser?.startFileBrowserFragment(it, baseFile.mData, true, true)
                    }
                } else {
                    val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                    if (!previewResult) {
                        mFileOperateController?.onFileClick(it, baseFile, e)
                    }
                }
                fragmentViewModel?.viewModelScope?.launch(Dispatchers.IO) {
                    if (!baseFile.checkExist()) {
                        fragmentViewModel?.cleanAndLoadData()
                    }
                }
            }
        }
        return true
    }

    override fun onClickDir(
        path: String
    ) {
        if (currentFileBrowserPath.isNotEmpty() && currentFileBrowserPath != path) return
        activity?.let {
            val fileBrowser = Injector.injectFactory<IFileBrowser>()
            if (WindowUtils.isSmallScreen(it)) {
                fileBrowser?.startFileBrowserActivity(it, path, fromDetail = true, isFromShortcutFolder = true)
            } else {
                fileBrowser?.startFileBrowserFragment(it, path, true, true)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mTitle = bundle.getString(KtConstants.P_TITLE) ?: ""
            path = bundle.getString(KtConstants.FILE_PATH) ?: ""
            dbID = bundle.getLong(Constants.DB_ID)
            mIsFilter = bundle.getBoolean(Constants.IS_FILTER_SEARCH_RESULT)
            mIsFromSearch = bundle.getBoolean(Constants.IS_FROM_SEARCH)
            mAddFileDialogShow = bundle.getBoolean(Constants.SHOW_ADD_FILE_DIALOG)
            mAdapter = ShortcutFolderAdapter(it, this.lifecycle)
            mAdapter!!.setHasStableIds(true)
            mAdapter?.mAddFileDialogListener = object : ShortcutFolderAdapter.AddFileDialogShowListener {
                /**标签列表二级页面，有文件时，点击添加文件拉起添加文件面板*/
                override fun showAddFileDialog() {
                    showAddFolderDialog()
                }
            }
            needLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            handleSideCategoryType(bundle)
        }
    }

    private fun handleSideCategoryType(bundle: Bundle) {
        val bundleCategoryType = bundle.getInt(Constants.SIDE_CATEGORY_TYPE, -1)
        if (sideCategoryType != -1 && bundleCategoryType != sideCategoryType) {
            rootView?.apply {
                super.setFragmentViewDragTag(this)
            }
        }
        sideCategoryType = bundleCategoryType
    }

    /**弹出新建文件夹弹窗*/
    private fun showAddFolderDialog() {
        activity?.let { ac -> mFileOperateController?.onCreateFolder(ac, path) }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.shortcut_folder_menu)
            updateToolbarHeight(this)
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
            initPopupWindow()
            setToolbarEditIcon(this, isChildDisplay)
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            if (previewOperate?.isSupportPreview() != true) {
                setOnMenuItemClickListener {
                    return@setOnMenuItemClickListener onMenuItemSelected(it)
                }
            }
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(
                    paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom
                )
            }
        }
        displayToolbarNavigationIcon()
    }

    private fun displayToolbarNavigationIcon() {
        mToolbar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                Log.d(TAG, "current is in select mode")
            } else {
                if (isChildDisplay) {
                    navigationIcon = null
                    setNavigationOnClickListener(null)
                } else {
                    setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
                    setNavigationOnClickListener {
                        clickNavigationIcon()
                    }
                }
            }
        }
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(baseVMActivity)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.SHORTCUT_FOLDER)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_SHORTCUT_FOLDER)
                true
            }

            R.id.actionbar_edit -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_EDIT)
                    OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.SHORTCUT_FOLDER)
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
                true
            }

            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.SHORTCUT_FOLDER)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            0,
                            anchorView,
                            SortRecordModeFactory.getShortcutFolderKey(), object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            })
                    }
                }
                true
            }

            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }

            R.id.action_setting -> {
                StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.SHORTCUT_FOLDER)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_SHORTCUT_FOLDER)
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                true
            }

            com.filemanager.common.R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }

            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }

            R.id.action_add -> {
                mAddPopupWindow?.show(mToolbar?.menuView)
                true
            }

            else -> false
        }
    }

    private fun clickNavigationIcon() {
        if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        } else {
            baseVMActivity?.onBackPressed()
        }
    }

    override fun pressBack(): Boolean {
        val result = fragmentViewModel?.pressBack() ?: false
        if (result) {
            previewEditedFiles(null)
        }
        return result
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.shortcut_folder_menu, menu)
        mToolbar?.apply {
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            refreshScanModeAdapter(scanMode)
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
                if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
                    showEmptyView()
                }
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            updatePermissionView()
            fragmentRecyclerView?.let {
                resetRecyclerViewHoriontalPadding(it)
            }
            updateFloatButtonMargin()
            updateLeftRightMargin()
        }
    }


    private fun updateFloatButtonMargin() {
        val activity = activity ?: return
        mAddFab?.updateLayoutParams<MarginLayoutParams> {
            marginEnd = if (UIConfigMonitor.getWindowType() == WindowType.LARGE) {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_40dp)
            } else {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp)
            }
        }
    }

    override fun updateLeftRightMargin() {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = fragmentViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fragmentViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun fromSelectPathResult(code: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult $code paths:$paths")
        activity?.let { mFileOperateController?.onSelectPathReturn(it, code, paths) }
        if (code != MessageConstant.MSG_EDITOR_COMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            fragmentViewModel?.loadData()
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        displayToolbarNavigationIcon()
        mToolbar?.apply {
            displayActionByIsChild(this)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    private fun updatePermissionView() {
        if (WindowUtils.isMiddleAndLargeScreen(context).not() && PermissionUtils.hasStoragePermission().not()) {
            setPermissionEmptyVisible(View.VISIBLE)
            return
        }
        setPermissionEmptyVisible(View.GONE)
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    fun getSelectItemCount(): Int {
        return fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
    }

    fun changeListMode() {
        if (KtConstants.LIST_SELECTED_MODE == fragmentViewModel?.mModeState?.listModel?.value) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return path
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getInstallPerMission() {
        super.getInstallPerMission()
        Log.d(TAG, "getInstallPerMission")
        (baseVMActivity as BaseVMActivity).checkGetInstalledAppsPermission()
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        return true
    }

    override fun getFragmentCategoryType(): Int {
        if (sideCategoryType == -1) {
            return CategoryHelper.CATEGORY_FOLDER_GROUP
        } else {
            return sideCategoryType
        }
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun refreshDataForDir(path: String, category: Int) {
        if (path == getCurrentPath() && CategoryHelper.isShortcutFolderType(category)) {
            onResumeLoadData()
        }
    }

    override fun renameToShortCutFolder(newPath: String, file: BaseFileBean): String {
        val oldPath = file.mData
        if (oldPath?.let { getCurrentPath().contains(it) && newPath != oldPath } == true) {
            path = path.replace(oldPath, newPath)
            if (File(path).exists()) {
                arguments?.putString(KtConstants.FILE_PATH, path)
                arguments?.putString(KtConstants.P_TITLE, File(path).name)
                onResumeLoadData()
            }
        }
        return path
    }

    override fun renameToLabel(newName: String, labelId: Long) {}

    private fun initPopupWindow() {
        fun createListItems(): List<PopupListItem> {
            val builder = PopupListItem.Builder()
            val list = java.util.ArrayList<PopupListItem>()
            for (map in mAddArrayMap) {
                val item = builder.reset().setTitle(map.value).setId(map.key).build()
                list.add(item)
            }
            return list
        }

        mAddPopupWindow = COUIPopupListWindow(context).apply {
            val list: List<PopupListItem> = createListItems()
            mAddPopupItemList.clear()
            mAddPopupItemList.addAll(list)
            itemList = list
            setDismissTouchOutside(true)
            setOnDismissListener {
                Log.d(TAG, "mAddPopupWindow OnDismissListener")
            }
            setOnItemClickListener { _, _, position, _ ->
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    mAddPopupWindow?.dismiss()
                    return@setOnItemClickListener
                }
                when (mAddPopupItemList[position].id) {
                    ID_ADD_FOLDER -> showAddFolderDialog()
                    ID_ADD_FILE -> showAddFileDialog()
                }
                mAddPopupWindow?.dismiss()
            }
        }

        if (mAddFileController == null) {
            mAddFileController = activity?.let { AddFileController() }
            mAddFileController?.setOnAddFileClickListener(this)
        }

        if (mFileServiceAction == null) {
            mFileServiceAction = Injector.injectFactory<IFileService>()
        }
    }

    /**弹出添加文件面板*/
    private fun showAddFileDialog() {
        if (mTitle == null) return
        mAddFileController?.mPath = path
        activity?.supportFragmentManager?.let { mAddFileController?.showAddFileDialog(mTitle!!, it, lifecycle) }
    }

    /**
     * 点击添加文件
     */
    override fun onAddFileClick(selectData: List<AddFileBean>) {
        if (Utils.isQuickClick()) {
            Log.w(TAG, "click too fast, try later")
            return
        }
        mAddFileController?.dismissAddFileDialog()
        lifecycleScope.launch(context = Dispatchers.IO) {
            val oldFiles = mutableListOf<File>()
            val newFile = File(path)
            selectData.forEach {
                oldFiles.add(File(it.mData))
            }
            val code = mFileServiceAction?.cutTo(requireContext(), oldFiles, newFile)
            Log.d(TAG, "cut file result code=$code")

            withContext(context = Dispatchers.Main) {
                Handler(Looper.getMainLooper()).postDelayed({ onResumeLoadData() }, LOAD_DATA_DELAYED_TIME)
                if (code == OPERATE_SUCCESS) {
                    CustomToast.showShort(com.filemanager.common.R.string.label_add_files_successed)
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.drag_privacy_add_failed)
                }
            }
        }
    }
}