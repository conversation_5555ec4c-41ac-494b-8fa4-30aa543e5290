<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="84dp"
    android:height="84dp"
    android:viewportWidth="84"
    android:viewportHeight="84">
    <path
        android:fillColor="#0062FF"
        android:pathData="M27.87,50.116L41.64,58.85V76.453L12.775,59.873C12.46,59.693 12.352,59.291 12.533,58.977C12.591,58.876 12.675,58.792 12.776,58.734L27.87,50.116Z" />
    <group>
        <clip-path android:pathData="M27.87,50.116L41.64,58.85V76.453L12.775,59.873C12.46,59.693 12.352,59.291 12.533,58.977C12.591,58.876 12.675,58.792 12.776,58.734L27.87,50.116Z" />
        <path
            android:fillAlpha="0.6"
            android:fillType="evenOdd"
            android:pathData="M41.64,76.59V59.063L26.853,50.667L11.875,59.35L41.64,76.59Z"
            android:strokeAlpha="0.6">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="2274.05"
                    android:endY="2044.55"
                    android:startX="2984.21"
                    android:startY="1021.39"
                    android:type="linear">
                    <item
                        android:color="#FF0044B1"
                        android:offset="0" />
                    <item
                        android:color="#000062FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path
        android:fillType="evenOdd"
        android:pathData="M71.828,41.834V59.391L41.645,76.453V59.257L71.828,41.834Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="2060.14"
                android:endY="1569.61"
                android:startX="403.602"
                android:startY="2607.69"
                android:type="linear">
                <item
                    android:color="#FF1A4ACC"
                    android:offset="0" />
                <item
                    android:color="#FF0854E4"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillType="evenOdd"
        android:pathData="M71.832,59.391V24.609L56.738,30.74V50.592L71.832,59.391Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="811.426"
                android:endY="457.336"
                android:startX="811.426"
                android:startY="3021.57"
                android:type="linear">
                <item
                    android:color="#FF0A5DF6"
                    android:offset="0" />
                <item
                    android:color="#FF124CDC"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:fillColor="#0082FF"
        android:pathData="M27.177,33.24L41.645,40.668V59.379L12.462,42.578C12.148,42.397 12.04,41.996 12.22,41.681C12.28,41.578 12.366,41.494 12.47,41.436L27.177,33.24Z" />
    <group>
        <clip-path android:pathData="M27.177,33.24L41.645,40.668V59.379L12.462,42.578C12.148,42.397 12.04,41.995 12.22,41.681C12.28,41.578 12.366,41.493 12.47,41.436L27.177,33.24Z" />
        <path
            android:fillAlpha="0.600679"
            android:fillType="evenOdd"
            android:pathData="M41.646,59.062V41.671L26.858,33.24L13.354,41.069C12.884,41.342 12.724,41.944 12.996,42.414C13.083,42.564 13.207,42.688 13.357,42.774L41.646,59.062Z"
            android:strokeAlpha="0.600679">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="2172.52"
                    android:endY="1995.39"
                    android:startX="2891.11"
                    android:startY="998.155"
                    android:type="linear">
                    <item
                        android:color="#FF0044B1"
                        android:offset="0" />
                    <item
                        android:color="#000062FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path
        android:fillType="evenOdd"
        android:pathData="M41.039,7.6L12.819,23.981C12.506,24.163 12.399,24.565 12.581,24.878C12.639,24.978 12.722,25.06 12.822,25.118L41.645,41.672L71.832,24.609L42.354,7.598C41.946,7.364 41.445,7.364 41.039,7.6Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="2979.51"
                android:endY="3432.34"
                android:startX="2979.51"
                android:startY="7.423"
                android:type="linear">
                <item
                    android:color="#FF4AC3FF"
                    android:offset="0" />
                <item
                    android:color="#FF0E93F8"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
