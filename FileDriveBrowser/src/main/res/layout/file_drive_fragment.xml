<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard">

    <RelativeLayout
        android:id="@+id/file_drive_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:splitMotionEvents="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
            android:id="@+id/fastScroller"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:common_track_marginBottom="@dimen/ftp_text_margin_bottom"
            app:common_track_marginEnd="@dimen/base_album_fastscroller_margin_end"
            app:common_track_marginTop="@dimen/base_album_recyclerview_padding_top">

            <com.filemanager.common.view.FileManagerRecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false" />

            <ViewStub
                android:id="@+id/cloud_drive_unauthorized"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/cloud_drive_unauthorized_layout" />
        </com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller>
    </RelativeLayout>

    <include layout="@layout/appbar_with_pathbar_sort_layout_secondary" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>