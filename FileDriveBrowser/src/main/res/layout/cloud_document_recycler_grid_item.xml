<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/file_grid_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorCard">

    <RelativeLayout
        android:id="@+id/file_grid_item_icon_container"
        android:layout_width="@dimen/file_grid_frame_size"
        android:layout_height="@dimen/file_grid_frame_size"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_centerHorizontal="true">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_grid_item_icon"
            android:layout_width="@dimen/file_browser_img_size"
            android:layout_height="@dimen/file_browser_img_size"
            android:layout_marginBottom="@dimen/file_grid_icon_margin_bottom"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:forceDarkAllowed="false"
            android:scaleType="fitEnd" />
    </RelativeLayout>

    <com.filemanager.common.view.MiddleMultilineTextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/file_grid_item_icon_container"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/dimen_4dp"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_4dp"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:singleLine="false"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="@dimen/grid_recent_item_text_size" />

    <TextView
        android:id="@+id/detail_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_tv"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/dimen_6dp"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_6dp"
        android:layout_marginBottom="@dimen/dimen_6dp"
        android:adjustViewBounds="true"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:paddingStart="@dimen/dimen_18dp"
        android:paddingEnd="@dimen/dimen_18dp"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/grid_recent_item_sub_text_size" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/gridview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:background="@null"
        android:clickable="false"
        android:focusable="false"
        android:paddingStart="0dp" />
</RelativeLayout>