/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileDriveActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/11 14:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import android.content.Intent
import android.os.Build
import android.os.Build.VERSION
import android.os.Bundle
import android.os.Environment
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.core.view.WindowInsetsCompat
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NavigationBarHelper
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.drivebrowser.R
import com.oplus.selectdir.SelectPathController

class FileDriveActivity : BaseVMActivity(), NavigationInterfaceForMain, NavigationBarView.OnItemSelectedListener,
    BaseVMActivity.PermissonCallBack, TransformNextFragmentListener, DragDropInterface {

    private var actionActivityResultListener: ActionActivityResultListener? = null
    private var fileDriveFragment: FileDriveFragment? = null
    private val navigationController by lazy { NavigationController(lifecycle, type = NavigationType.FILE_DRIVE, id = R.id.navigation_tool) }
    private val selectPathController by lazy { SelectPathController(lifecycle) }
    private val navigationBarHelper: NavigationBarHelper by lazy { NavigationBarHelper() }

    override fun getLayoutResId(): Int {
        return R.layout.file_drive_activity
    }

    override fun initView() {
        val type = intent.getIntExtra(Constants.KEY_FILE_DRIVE_TYPE, 0)
        Log.d(TAG, "initView -> type = $type")
        registerVmChangedReceiver(null)
        fileDriveFragment = (supportFragmentManager.findFragmentByTag(TAG_FILE_DRIVE_FRAGMENT) as? FileDriveFragment)
            ?: FileDriveFragment.newInstance(type)
        supportFragmentManager.beginTransaction().replace(
            R.id.fragment_container_view, fileDriveFragment!!, TAG_FILE_DRIVE_FRAGMENT
        ).commit()

        updateWindowInsets()
    }

    /**
     * 15的不再这里处理，14及以下的在这里处理
     */
    private fun updateWindowInsets() {
        if (VERSION.SDK_INT <= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            findViewById<View>(android.R.id.content).let {
                navigationBarHelper.addInsetsCallback(it, window) { _, insets, showNavigationBar ->
                    val systemBarInsetsBottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
                    Log.d(TAG, "onApplyInsets showNavigationBar: $showNavigationBar, systemBarInsetsBottom = $systemBarInsetsBottom")
                    fileDriveFragment?.updatePadding(systemBarInsetsBottom)
                }
            }
        }
    }

    override fun initData() {
        val type = intent.getIntExtra(Constants.KEY_FILE_DRIVE_TYPE, 0)
        val isAuth = IntentUtils.getBoolean(intent, CommonConstants.KEY_IS_AUTHORIZING, false)
        Log.d(TAG, "initData -> isAuth = $isAuth")
        val bundle = fileDriveFragment?.arguments ?: Bundle()
        bundle.putInt(Constants.KEY_FILE_DRIVE_TYPE, type)
        bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, isAuth)
        fileDriveFragment?.arguments = bundle
        intent.putExtra(CommonConstants.KEY_IS_AUTHORIZING, false)
        Log.d(TAG, "initData -> action = ${intent.action} type:$type")
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        fileDriveFragment?.refreshCurrentFragment()
    }

    override fun showNavigation() {
        navigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun showNavigation(type: NavigationType, showTab: Boolean) {
    }

    override fun hideNavigation(after: Runnable?) {
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        Log.d(TAG, "setNavigateItemAble -> isEnable = $isEnable")
        navigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean,
        mHasSelectedLabelsAllPin: Boolean,
        mHasSelectedFileEmpty: Boolean
    ) {
        Log.d(TAG, "setNavigateItemAble -> isEnable = $isEnable,selectMulti:$mHasSelectedMultiLabels fileEmpty:$mHasSelectedFileEmpty")
        navigationController.setNavigateItemAble(
            isEnable,
            mHasDrm,
            mHasSelectedMultiLabels,
            mHasSelectedLabelsAllPin,
            mHasSelectedFileEmpty
        )
    }

    override fun hideNavigation() {
        navigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        this.actionActivityResultListener = actionActivityResultListener
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        fileDriveFragment?.let {
            it.onCreateOptionsMenu(menu, menuInflater)
            return true
        }
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        fileDriveFragment?.let {
            return it.onOptionsItemSelected(item)
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onBackPressed() {
        if ((fileDriveFragment as? OnBackPressed)?.pressBack() == true) {
            return
        }
        super.onBackPressed()
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return fileDriveFragment?.onNavigationItemSelected(item) ?: false
    }

    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission")
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        selectPathController.updateDialogHeightIfNeed(supportFragmentManager)
        fileDriveFragment?.onUIConfigChanged(configList)
    }

    override fun transformToNextFragment(path: String?) {
        // do nothing
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path = downloadPath)
    }

    override fun showSelectPathFragmentDialog(code: Int, path: String?) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path = path)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        selectPathController.onDestroy()
        fileDriveFragment?.onSelect(code, paths?.getOrNull(0))
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        // do nothing
    }

    override fun onUpdatedLabel() {
        // do nothing
    }

    override fun hasShowPanel(): Boolean {
        return selectPathController.hasShowPanel()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        selectPathController.onDestroy()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
    }

    override fun onResume() {
        super.onResume()
        val bundle = Bundle()
        val itemType = IntentUtils.getInt(intent, Constants.KEY_FILE_DRIVE_TYPE, 0)
        val isAuthorizing = IntentUtils.getBoolean(intent, CommonConstants.KEY_IS_AUTHORIZING, false)
        Log.d(TAG, "onResume -> itemType = $itemType ; isAuthorizing = $isAuthorizing")
        bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, isAuthorizing)
        bundle.putInt(Constants.KEY_FILE_DRIVE_TYPE, itemType)
        intent.putExtra(CommonConstants.KEY_IS_AUTHORIZING, false)
        fileDriveFragment?.arguments = bundle
    }

    companion object {
        private const val TAG = "FileDriveActivity"

        const val TAG_FILE_DRIVE_FRAGMENT = "fileDriveFragment"

        val downloadPath: String by lazy {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
        }
    }

    override fun updateNavigationToolPadding() {
        fileDriveFragment?.updatePadding(navPaddingBottom)
        navigationController.updateNavigationToolPadding(navPaddingBottom)
    }
}