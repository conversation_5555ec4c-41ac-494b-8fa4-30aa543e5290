/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DeleteFileRequest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/5 11:02
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/5       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model.tencent

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DeleteFileRequest(
    /**
     * 文件id
     */
    @SerializedName("fileId") var fileId: String,
    /**
     * 文件所属的类别类型，（origin:源文件，shared:共享文件，recent:浏览记录，trash:回收站文件），默认是origin
     */
    @SerializedName("type") var type: String,
    /**
     * 是否删除到回收站，（0：彻底删除，1：删除到回收站），默认是彻底删除。删除回收站，浏览记录的文件时，结果都是彻底删除
     */
    @SerializedName("recoverable") var recoverable: String
) : Parcelable {

    companion object {
        /**
         * 源文件
         */
        const val TYPE_ORIGIN = "origin"

        /**
         * 共享文件
         */
        const val TYPE_SHARED = "shared"

        /**
         * 浏览记录
         */
        const val TYPE_RECENT = "recent"

        /**
         * 回收站文件
         */
        const val TYPE_TRASH = "trash"

        /**
         * 彻底删除
         */
        const val RECOVERABLE_DELETE_COMPLETE = 0

        /**
         * 删除到回收站
         */
        const val RECOVERABLE_DELETE_TO_TRASH = 1
    }
}