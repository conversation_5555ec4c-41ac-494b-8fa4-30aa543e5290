/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BatchDeleteFileItem
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/6 10:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/6       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model.tencent

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class BaseFileItem(
    @SerializedName("fileId") var fileId: String = "",
) : Parcelable