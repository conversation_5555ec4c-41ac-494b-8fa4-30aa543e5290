/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchListResp
 ** Description : Search List Request
 ** Version     : 1.0
 ** Date        : 2024/04/24 15:54
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/24       1.0      create
 ***********************************************************************/

package com.oplus.filemanager.drivebrowser.data.model.tencent

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
@Parcelize
data class SearchListResp(
    @SerializedName("next") var next: Int = 0,
    @Deprecated("this value always is 0, Replace with field hasMore")
    @SerializedName("total") var total: Int = 0,
    @SerializedName("hasMore") var hasMore: Boolean = false,
    @SerializedName("list") var list: List<SearchListItem> = emptyList()
) : Parcelable
