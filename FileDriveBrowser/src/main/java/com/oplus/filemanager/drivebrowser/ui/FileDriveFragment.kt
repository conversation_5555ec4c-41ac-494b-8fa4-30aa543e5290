/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileDriveFragment
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 14:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewStub
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.ActionBar
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.SystemBarUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.BrowserPathBar
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.drivebrowser.R
import com.oplus.filemanager.drivebrowser.data.utils.CloudFileSortTypeUtils
import com.oplus.filemanager.drivebrowser.ui.adapter.FileDriveAdapter
import com.oplus.filemanager.drivebrowser.utils.AuthorizationWebViewClient
import com.oplus.filemanager.drivebrowser.utils.ExceptionHandler
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import com.oplus.filemanager.drivebrowser.utils.UniqueIdUtils
import com.oplus.filemanager.drivebrowser.utils.getFileCloudDriveTitle
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.filemanager.interfaze.touchshare.TouchShareNotSupportSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.filemanager.interfaze.wechat.IWechat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FileDriveFragment : RecyclerSelectionVMFragment<FileDriveFragmentViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener {

    private var category: Int = CategoryHelper.CATEGORY_TENCENT_DOCS
    private var unauthorizedVS: ViewStub? = null
    private var unauthorizedLayout: View? = null
    private var authorizationWebView: WebView? = null
    private var authorizationTitle: TextView? = null
    private var unauthorizedLayoutTips: TextView? = null
    private var unauthorizedSpace: TextView? = null
    private var unauthorizedLayoutIcon: ImageView? = null
    private var unauthorizedLayoutButton: COUIButton? = null
    private var fileDriveLayout: ViewGroup? = null
    private var sortView: SortEntryView? = null
    private var pathBar: BrowserPathBar? = null
    private var mToolbar: COUIToolbar? = null
    private var mAppBarLayout: COUIDividerAppBarLayout? = null

    private var viewModel: FileDriveFragmentViewModel? = null
    private var currentPath: String? = null
    private var isChildDisplay = false
    private var pageAdapter: FileDriveAdapter? = null
    private var gridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var layoutManager: FileGridLayoutManager? = null
    private var verticalButtonWrap: SingleButtonWrap? = null
    private val spacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
    }

    private var selectDocumentTitle: String = ""

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [needSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var needSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    private val sortPopupController by lazy { SortPopupController(lifecycle) }
    private val fileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val noConnectController by lazy { FileEmptyController(lifecycle) }
    private var authorizationController: AuthorizationController? = null
    private var openKDocs = false
    private var loadingController: LoadingController? = null
    private var forceUpdateData = true


    private var selectPath: String? = null
    private var selectCode: Int = 0
    private var permissionLauncher: ActivityResultLauncher<String> =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) {
            Log.d(TAG, "onSelect -> request notification permission $it")
            fromSelectPathResult(selectCode, selectPath)
        }

    private val heytapAccountAction: IHeytapAccount? by lazy {
        Injector.injectFactory<IHeytapAccount>()
    }

    private val wechat: IWechat? by lazy {
        Injector.injectFactory<IWechat>()
    }

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    private var isClickedSelectAllMenu = false
    private var rootWindowInsetsListener: View.OnApplyWindowInsetsListener? = null

    override fun createViewModel(): FileDriveFragmentViewModel? {
        category = arguments?.getInt(Constants.KEY_FILE_DRIVE_TYPE) ?: CategoryHelper.CATEGORY_TENCENT_DOCS
        currentPath = getFileCloudDriveTitle(category)
        viewModel = ViewModelProvider(this)[category.toString(), FileDriveFragmentViewModel::class.java]
        Log.e(TAG, "createViewModel $viewModel ; category = $category")
        return viewModel
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val bundle = arguments ?: return
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
    }

    private fun initArguments() {
        val bundle = arguments ?: return
        category = bundle.getInt(Constants.KEY_FILE_DRIVE_TYPE)
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        currentPath = getFileCloudDriveTitle(category)
        Log.d(TAG, "initArguments category:$category ; isChildDisplay = $isChildDisplay")
    }

    override fun getLayoutResId(): Int {
        return R.layout.file_drive_fragment
    }

    override fun initView(view: View) {
        Log.d(TAG, "initView")
        fileDriveLayout = view.findViewById(R.id.file_drive_layout)
        unauthorizedVS = view.findViewById(R.id.cloud_drive_unauthorized)

        initToolbar(view)
        toolbar = mToolbar
        initRecyclerView(view)
        initPathBar(view)
        initSortEntryView(view)
        if (!FileDriveStateUtils.isAuthed(category)) {
            showUnauthorizedLayout()
        }
        setWindowInsetsListener()
    }

    private fun setWindowInsetsListener() {
        if (rootWindowInsetsListener == null) {
            rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
                updateButtonBottomMargin(unauthorizedLayoutButton)
                insets
            }
        }
        rootView?.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
    }

    override fun initData(savedInstanceState: Bundle?) {
        registerWechatSdk()
        onResumeLoadData()
        TouchShareSupplier.attach(this, object : TouchShareNotSupportSupplier(category) {
            override fun notSupportTips() {
                CustomToast.showLong(com.filemanager.common.R.string.touch_share_not_support_non_local_file_toast)
            }
        })
    }

    private fun registerWechatSdk() {
        if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            wechat?.register(appContext)
        }
    }

    private fun unregisterWechatSdk() {
        if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            wechat?.unregister(appContext)
        }
    }

    override fun startObserve() {
        startDriveUIObserver()
        startUIStateObserver()
        startScanModeObserver()
        startListSelectObserver()
        startPositionObserver()
        startKDocsUrlObserver()
        startLoadingObserver()
        startAuthObserver()
        startForceReloadObserver()
    }

    fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        baseVMActivity?.supportActionBar?.apply {
            if (viewModel?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(false)
            } else {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            }
        }
    }

    private fun startDriveUIObserver() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel?.driveUiState?.collect {
                    Log.d(TAG, "startDriveUIObserver -> uiState = $it")
                    handleUiState(it)
                }
            }
        }
    }

    /**
     * 监听UIState
     */
    private fun startUIStateObserver() {
        viewModel?.uiState?.observe(this) { fileUiModel ->
            Log.d(TAG, "startUIStateObserver mUiState = ${fileUiModel.fileList.size} , ${fileUiModel.selectedList.size}")
            if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                mToolbar?.let { refreshSelectToolbar(it) }
                (fileUiModel.fileList as? ArrayList<CloudDocsItem>)?.let {
                    val newFileList = ArrayList(it)
                    if (isClickedSelectAllMenu) {
                        setData(newFileList, needInvalid = true)
                        isClickedSelectAllMenu = false
                    } else {
                        setData(newFileList, needInvalid = false)
                        notifyItemRangeChanged()
                    }
                }
            } else {
                if (fileUiModel.fileList.isEmpty()) {
                    handleEmptyData()
                } else {
                    handleNoneEmptyData()
                }
                mToolbar?.let {
                    refreshScanModeItemIcon(it)
                    updateEditMenuVisible(it)
                    setToolbarEditIcon(it, isChildDisplay)
                }
                (fileUiModel.fileList as? ArrayList<CloudDocsItem>)?.let {
                    val newFileList = ArrayList(it)
                    setData(newFileList)
                }
            }
        }
    }


    /**
     * 监听宫格模式，列表模式切换
     */
    private fun startScanModeObserver() {
        needSkipAnimation = true
        viewModel?.browseModeState?.observe(this@FileDriveFragment) { scanMode ->
            Log.d(TAG, "startObserve scanMode=$scanMode")
            val skipAnim = needSkipAnimation
            if (skipAnim) {
                refreshScanModeAdapter(scanMode)
            } else {
                fragmentRecyclerView?.let { recyclerView ->
                    recyclerView.mTouchable = false
                    recyclerView.stopScroll()
                }
                gridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                    override fun onSpanChangeCallback() {
                        layoutManager?.scrollToPosition(0)
                        refreshScanModeAdapter(scanMode)
                    }
                }, object : OnAnimatorEndListener {
                    override fun onAnimatorEnd() {
                        fragmentRecyclerView?.mTouchable = true
                    }
                })
            }
            mToolbar?.let {
                delay { refreshScanModeItemIcon(it) }
            }
        }
    }

    /**
     * 监听选中
     */
    private fun startListSelectObserver() {
        viewModel?.selectModeState?.listModel?.observe(this) { listModel ->
            if (viewModel?.selectModeState?.initState != true) {
                mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                return@observe
            }
            Log.d(TAG, "startListSelectObserver select=$listModel")
            if (listModel == KtConstants.LIST_SELECTED_MODE) {
                HighlightUtil.endAnimation()
                (baseVMActivity as? NavigationInterface)?.showNavigation()
                pageAdapter?.setSelectEnabled(enabled = true, notifyData = false)
                pageAdapter?.invalidate()
                setRecyclerViewPaddingBottom(true)
                mToolbar?.let {
                    changeActionModeAnim(it, {
                        initToolbarSelectedMode(it)
                        refreshSelectToolbar(it)
                    })
                    it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                }
            } else {
                pageAdapter?.setSelectEnabled(enabled = false, notifyData = false)
                pageAdapter?.invalidate()
                setRecyclerViewPaddingBottom(false)
                mToolbar?.let {
                    changeActionModeAnim(it, {
                        initToolbarNormalMode(it)
                        refreshScanModeItemIcon(it)
                    }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                    it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                }
                (baseVMActivity as? NavigationInterface)?.hideNavigation()
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            Log.d(TAG, "onUIConfigChanged -> should update ui")
            val scanMode = fragmentViewModel?.browseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            refreshScanModeAdapter(scanMode)
            fileEmptyController.changeEmptyFileIcon()
            sortPopupController.hideSortPopUp()
            updateAuthorizationSpaceVisible()
            if (viewModel?.selectModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                viewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    private fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.browseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            pageAdapter?.notifyDataSetChanged()
        }
        pathBar?.updateLeftRightMargin()
        sortView?.updateLeftRightMargin()
    }

    private fun fromSelectPathResult(code: Int, path: String?) {
        val activity = activity ?: return
        fragmentViewModel?.fromSelectPathResult(activity, code, path)
    }

    /**
     * 监听PositionModel
     */
    private fun startPositionObserver() {
        fragmentViewModel?.positionModel?.observe(this, object : Observer<FileDriveFragmentViewModel.PositionModel> {
            override fun onChanged(value: FileDriveFragmentViewModel.PositionModel) {
                Log.d(TAG, "startPositionObserver $value")
                if (fragmentViewModel?.selectModeState?.initState != true) {
                    return
                }
                HighlightUtil.cancelAnimation()
                pathBar?.let {
                    if (it.getCurrentPath() != value.currentPath) {
                        it.setCurrentPath(value.currentPath)
                    }
                }
                appBarLayout?.postDelayed({
                    layoutManager?.scrollToPositionWithOffset(value.position, value.offset)
                    fragmentViewModel?.positionModel?.value?.position = 0
                    fragmentViewModel?.positionModel?.value?.offset = 0
                    fragmentViewModel?.needScroll = false
                }, BaseFolderAnimAdapter.FILE_BROWSER_FOLDER_ANIM_DELAY)
            }
        })
    }

    /**
     * 监听金山文档打开的url
     */
    private fun startKDocsUrlObserver() {
        viewModel?.kdocsFileUrl?.observe(this) {
            val intent = Intent(baseVMActivity, DocumentPreviewActivity::class.java)
            intent.putExtra(DocumentPreviewActivity.KEY_DOCUMENT_URL, it)
            intent.putExtra(DocumentPreviewActivity.KEY_DOCUMENT_TITLE, selectDocumentTitle)
            startActivity(intent)
        }
    }

    /**
     * 监听数据加载状态，显示loading弹窗
     */
    private fun startLoadingObserver() {
        val activity = activity ?: return
        loadingController = LoadingController(activity, this).apply {
            observe(fragmentViewModel?.dataLoadState, rootView) {
                (fragmentViewModel?.getRealFileSize() ?: 0) > 0
            }
        }
    }

    /**
     * 监听存在这个界面的时候，从接口中获取无授权的情况
     */
    private fun startAuthObserver() {
        viewModel?.authState?.observe(this) {
            val authState = it
            Log.i(TAG, "startAuthObserver authState = $authState")
            if (authState.not()) {
                showUnauthorizedLayout()
                sortPopupController.hideSortPopUp()
            }
        }
    }

    private fun startForceReloadObserver() {
        viewModel?.forceReload?.observe(this) {
            val isForceReload = it
            Log.i(TAG, "startDeleteFilesObserver isForceReload = $isForceReload")
            if (isForceReload) {
                pageAdapter?.refresh()
                viewModel?.forceReload?.value = false
            }
        }
    }


    /**
     * 显示、隐藏云文档列表界面：列表界面，排序，menu
     * @param show true:显示，false:隐藏
     */
    private fun showCloudFileListUI(show: Boolean) {
        fragmentRecyclerView?.isVisible = show
        sortView?.isVisible = show
        pathBar?.isVisible = show
        val toolbar = mToolbar ?: return
        setToolbarMenuVisible(toolbar, show)
    }

    /**
     * 设置RecyclerView 底部padding，让底部工具栏显示出来
     * @param isSelect 是否选中进入编辑模式
     */
    private fun setRecyclerViewPaddingBottom(isSelect: Boolean) {
        fragmentRecyclerView?.let {
            val paddingBottom = if (isSelect) {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
            } else {
                appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            }
            it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
            fragmentFastScroller?.apply { trackMarginBottom = paddingBottom }
        }
    }

    override fun onResume() {
        super.onResume()
        val isAuth = arguments?.getBoolean(CommonConstants.KEY_IS_AUTHORIZING, false)
        Log.d(TAG, "onResume -> isAuth = $isAuth")
        if (isAuth == true) {
            updateIsAuthorizing()
            viewModel?.loadData()
            arguments?.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, false)
        }
    }

    override fun onResumeLoadData() {
        initArguments()
        currentPath?.let {
            viewModel?.initUiState(category, it)
        }
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                displayActionIcon(supportActionBar)
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
        val isAuth = bundle.getBoolean(CommonConstants.KEY_IS_AUTHORIZING, false)
        Log.d(TAG, "onResumeLoadData -> isAuth = $isAuth")
        if (isAuth) {
            updateIsAuthorizing()
            bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, false)
        }
        mToolbar?.let {
            it.title = getFileCloudDriveTitle(category)
        }
        viewModel?.loadData()
    }

    private fun initToolbar(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        mToolbar = view.findViewById(R.id.toolbar)
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mToolbar?.apply {
            isTitleCenterStyle = false
            title = getFileCloudDriveTitle(category)
            updateLayoutParams<ViewGroup.LayoutParams> {
                height = appContext.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.toolbar_min_height)
            }
        }
        rootView?.apply {
            setPadding(
                paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom
            )
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            Log.d(TAG, "initToolbar -> isChildDisplay = $isChildDisplay")
            displayActionIcon(supportActionBar)
        }
    }

    private fun initRecyclerView(view: View) {
        val activity = activity ?: return
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView?.let {
            gridSpanAnimationHelper = GridSpanAnimationHelper(it)
            layoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = pageAdapter?.getItemViewType(position)
                        val isSingleLine = viewType == KtConstants.SCAN_MODE_LIST
                        return if (isSingleLine) spanCount else 1
                    }
                }
            }
            it.layoutManager = layoutManager
            it.addItemDecoration(spacesItemDecoration)
            pageAdapter = FileDriveAdapter(activity, <EMAIL>) {
                val files = pageAdapter?.getCloudDocsFiles() ?: mutableListOf()
                Log.d(TAG, "allFiles = ${files.size}")
                viewModel?.setDocumentsList(files, forceUpdateData)
                forceUpdateData = false
                //如果是授权异常，则要加载出授权界面
                if (pageAdapter?.isAuthError() == true) {
                    viewModel?.checkAuthState()
                }
            }
            it.adapter = pageAdapter
            //刷新的时候图标闪烁，加上此逻辑，去除change的时候的动画
            (it.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations = false
            setRecyclerViewPaddingBottom(false)
            it.setOnScrollChangeListener { _, _, _, _, _ ->
                //列表滑动后让列表切回可以加载更多的模式
                viewModel?.queryLiveData?.value?.userRemote?.let { userRemote ->
                    Log.d(TAG, "OnScrollChange userRemote:$userRemote")
                    if (!userRemote) {
                        viewModel?.setCanLoadMore()
                    }
                }
            }
        }
    }

    @SuppressLint("RestrictedApi")
    private fun initSortEntryView(view: View) {
        sortView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortView?.setDefaultOrder(SortRecordModeFactory.getCloudFileKey(category))
        sortView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onOptionsItemSelected(menu)
        }
    }

    private fun initPathBar(view: View) {
        pathBar = view.findViewById(com.oplus.selectdir.R.id.path_bar)
        pathBar?.let {
            val path = currentPath ?: return
            if (!TextUtils.isEmpty(path)) {
                viewModel?.initPathHelper(path)
                it.setPathHelper(viewModel?.pathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        viewModel?.clickPathBar(index)
                        pageAdapter?.refresh()
                        //如果二级页面为空列表, 则先隐藏空界面，如果一级页面也为空了，那么就依赖数据刷新了
                        handleNoneEmptyData()
                    }
                }).setTextFocusChangeListener(object : BrowserPathBar.OnTextFocusColorChangeListener {
                    override fun onFocusChange(currentFocusText: String) {
                        if (viewModel?.needScroll == true) {
                            KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, currentFocusText)
                        } else {
                            mToolbar?.title = currentFocusText
                        }
                    }
                }).show()
                it.setCurrentPath(path)
            }
        }
    }

    private fun handleUiState(fileDriveUiState: FileDriveUiState) {
        Log.d(TAG, "handleUiState -> fileDriveUiState = $fileDriveUiState")
        if (fileDriveUiState.isAuthorization) {
            showDocsList(fileDriveUiState)
        } else {
            handleAuthFailLayout(fileDriveUiState.exception)
        }
    }

    private fun displayActionIcon(bar: ActionBar?) {
        bar?.let {
            it.setDisplayHomeAsUpEnabled(!isChildDisplay)
            it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
    }

    fun refreshCurrentFragment() {
        Log.d(TAG, "refreshCurrentFragment")
    }

    private fun updateIsAuthorizing() {
        viewModel?.setAuthorizing()
    }

    private fun updateDocsList(fileDriveUiState: FileDriveUiState) {
        Log.d(TAG, "updateDocsList -> fileDriveUiState = $fileDriveUiState")
        mToolbar?.apply {
            setToolbarMenuVisible(this, true)
        }
        fragmentRecyclerView?.isVisible = true
        getDocumentsList()
    }

    private fun getDocumentsList() {
        lifecycleScope.launch {
            viewModel?.getDocumentsList()?.collectLatest {
                pageAdapter?.submitData(it)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(
            activity,
            scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        layoutManager?.spanCount = spanCount
        spacesItemDecoration.mSpanCount = spanCount
        pageAdapter?.mScanViewModel = scanMode
        pageAdapter?.invalidate()
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        val scanModeMenu = toolbar.menu.findItem(R.id.actionbar_scan_mode)
        scanModeMenu?.let {
            val desc: String
            val resId: Int = if (viewModel?.browseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = stringResource(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = stringResource(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            if (needSkipAnimation) {
                it.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
            }
        }
    }


    override fun onCreateOptionsMenu(menu: Menu, menuInflater: MenuInflater) {
        val toolbar = mToolbar ?: return
        if (fragmentViewModel?.selectModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
            initToolbarSelectedMode(toolbar)
        } else {
            initToolbarNormalMode(toolbar)
        }
        mToolbar?.apply {
            refreshScanModeItemIcon(this)
            updateEditMenuVisible(this)
            setToolbarEditIcon(this, isChildDisplay)
            val isAuthed = FileDriveStateUtils.isAuthed(category)
            if (!isAuthed) {
                setToolbarMenuVisible(this, false)
            }
        }
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            displayActionIcon(this)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = currentPath
        toolbar.inflateMenu(R.menu.file_cloud_drive_menu)
        updateEditMenuVisible(toolbar)
        setToolbarEditIcon(toolbar, isChildDisplay)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun updateEditMenuVisible(toolbar: COUIToolbar?) {
        val editMenu = toolbar?.menu?.findItem(R.id.actionbar_edit)
        editMenu?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (isChildDisplay) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun initToolbarSelectedMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = true
        toolbar.inflateMenu(R.menu.file_cloud_drive_selected_menu)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val selectedList = fragmentViewModel?.uiState?.value?.selectedList
        val checkedCount = selectedList?.size ?: 0
        toolbar.menu.findItem(R.id.actionbar_select_all)?.apply {
            this.title = if (fragmentViewModel?.getRealFileSize() == checkedCount) {
                stringResource(com.filemanager.common.R.string.unselect_all)
            } else {
                stringResource(com.filemanager.common.R.string.file_list_editor_select_all)
            }

            val tempTitle = if (checkedCount > 0) {
                appContext.resources.getQuantityString(
                    com.filemanager.common.R.plurals.mark_selected_items_new,
                    checkedCount,
                    checkedCount
                )
            } else {
                appContext.resources.getString(com.filemanager.common.R.string.mark_selected_no_items)
            }
            toolbar.title = tempTitle
        }
        (baseVMActivity as? NavigationInterfaceForMain)?.setNavigateItemAble(
            isEnable = checkedCount > 0,
            hasDrmFile(viewModel?.getSelectItems()),
            mHasSelectedMultiLabels = (checkedCount > 1),
            mHasSelectedFileEmpty = (viewModel?.supportDownload() == false)
        )
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home, R.id.actionbar_cancel -> {
                if (viewModel?.selectModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    viewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                } else {
                    activity?.onBackPressed()
                }
                return true
            }

            R.id.actionbar_edit -> { //编辑
                if (viewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    viewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
                return true
            }

            R.id.navigation_sort -> {
                if (viewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        sortPopupController.showSortPopUp(
                            it,
                            0,
                            anchorView,
                            SortRecordModeFactory.getCloudFileKey(category),
                            object : SelectItemListener {

                                override fun onDismiss() {
                                    sortView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortView?.setSortOrder(sortMode, isDesc)
                                        val sortType = CloudFileSortTypeUtils.getSortType(category, sortMode)
                                        // 1：正序，0：倒序
                                        val asc = if (isDesc) {
                                            0
                                        } else {
                                            1
                                        }
                                        viewModel?.updateSortType(sortType, asc)
                                        pageAdapter?.refresh()
                                    }
                                }
                            })
                    }
                }
                return true
            }

            R.id.actionbar_scan_mode -> {
                viewModel?.clickScanModeItem(baseVMActivity)
                return true
            }

            R.id.actionbar_cancel_auth -> {
                cancelAuth()
                return true
            }

            R.id.actionbar_select_all -> {
                isClickedSelectAllMenu = true
                viewModel?.toggleSelectAll()
                return true
            }
        }
        return true
    }

    override fun pressBack(): Boolean {
        Log.d(TAG, "pressBack")
        val activity = activity ?: return false
        if (isAuthFalse()) {
            Log.d(TAG, "isAuthFalse return false")
            return false
        }
        if (handleWebViewPressBack()) {
            return true
        }
        val result = fragmentViewModel?.pressBack {
            pageAdapter?.refresh()
            //如果二级页面为空列表, 则先隐藏空界面，如果一级页面也为空了，那么就依赖数据刷新了
            handleNoneEmptyData()
        } ?: false
        if (activity is FileDriveActivity) {
            return result
        }
        return result
    }

    private fun isAuthFalse(): Boolean {
        return (unauthorizedLayout?.visibility == View.VISIBLE) && (viewModel?.authState?.value == false)
    }

    /**
     * 处理预览金山文档时，webview的返回事件
     */
    private fun handleWebViewPressBack(): Boolean {
        if (authorizationWebView == null) {
            return false
        }
        if (openKDocs) {
            openKDocs = false
            fragmentFastScroller?.removeView(authorizationWebView)
            showCloudFileListUI(true)
            return true
        }
        return false
    }

    /**
     * 单击事件
     */
    override fun onItemClick(
        item: ItemDetailsLookup.ItemDetails<Int>,
        e: MotionEvent
    ): Boolean {
        Log.d(TAG, "onItemClick $item")
        viewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true

            if (baseFile.mIsDirectory) {
                // 下一级目录
                fragmentRecyclerView?.paddingTop?.let { paddingTop ->
                    val viewPosition = layoutManager?.findFirstVisibleItemPosition() ?: 0
                    val offset = layoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
                    fragmentViewModel?.onClickDir(baseVMActivity, baseFile, viewPosition, offset - paddingTop)
                    pageAdapter?.refresh()
                }
            } else {
                if (category == CategoryHelper.CATEGORY_TENCENT_DOCS && (wechat?.isWxInstalled(appContext) == true).not()) {
                    Log.d(TAG, "onItemClick -> not install wechat")
                    if (authorizationController == null) {
                        authorizationController = AuthorizationController()
                    }
                    baseVMActivity?.let { authorizationController?.showUninstallWeChatPreviewDialog(it) }
                    return true
                }
                selectDocumentTitle = baseFile.title
                fragmentViewModel?.onClickFile(baseFile)
            }
        }
        return true
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        val activity = activity ?: return false
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return fragmentViewModel?.handleSelectFiles(activity, item.itemId) ?: false
    }

    /**
     * 处理空数据
     */
    private fun handleEmptyData() {
        if (!FileDriveStateUtils.isAuthed(category)) { //取消授权，清空数据导致显示空页面
            Log.d(TAG, "handleEmptyData not auth")
            return
        }
        val networkAvailable = isNetworkAvailable(appContext)
        val isServerError = pageAdapter?.isServerError() ?: false
        Log.d(TAG, "handleEmptyData network:$networkAvailable server:$isServerError")
        hideUnauthorizedLayout()
        if (networkAvailable) {
            if (isServerError) { // 服务端出现问题
                showServerErrorView()
            } else { // 空页面
                showEmptyView()
            }
        } else { // 无网络
            showNoConnectView()
        }
    }

    /**
     * 显示空页面
     */
    private fun showEmptyView() {
        Log.d(TAG, "showEmptyView")
        val activity = activity ?: return
        val rootView = rootView ?: return
        pathBar?.isVisible = true
        sortView?.isVisible = true
        //隐藏recyclerView
        fragmentRecyclerView?.isVisible = false
        noConnectController.hideFileEmptyView()
        fileEmptyController.showFileEmptyView(activity, rootView)
        fileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
    }

    /**
     * 显示服务异常的界面
     */
    private fun showServerErrorView() {
        Log.d(TAG, "showServerErrorView")
        val activity = activity ?: return
        val rootView = rootView ?: return
        pathBar?.isVisible = true
        sortView?.isVisible = false
        //隐藏recyclerView
        fragmentRecyclerView?.isVisible = false
        fileEmptyController.hideFileEmptyView()
        noConnectController.showFileEmptyView(activity, rootView, FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON)
        noConnectController.setFileEmptyTitle(com.filemanager.common.R.string.server_error)
        noConnectController.setEmptySummaryVisibilityAndContent(
            View.VISIBLE,
            activity.getString(com.filemanager.common.R.string.try_again_later)
        )
    }

    /**
     * 显示无网络页面
     */
    private fun showNoConnectView() {
        Log.d(TAG, "showNoConnectView")
        val activity = activity ?: return
        val rootView = rootView ?: return
        pathBar?.isVisible = true
        sortView?.isVisible = false
        //隐藏recyclerView
        fragmentRecyclerView?.isVisible = false
        fileEmptyController.hideFileEmptyView()
        noConnectController.showFileEmptyView(activity, rootView, FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON)
        noConnectController.setFileEmptyTitle(com.filemanager.common.R.string.no_internet_connection)
        noConnectController.setEmptySummaryVisibilityAndContent(
            View.VISIBLE,
            activity.getString(com.filemanager.common.R.string.check_network_setting)
        )
    }

    /**
     * 先隐藏异常界面，再显示列表界面
     */
    private fun handleNoneEmptyData() {
        showCloudFileListUI(true)
        hideExceptionPage()
    }

    /**
     * 隐藏空页面或者无网络界面
     */
    private fun hideExceptionPage() {
        pathBar?.isVisible = true
        sortView?.isVisible = true
        noConnectController.hideFileEmptyView()
        fileEmptyController.hideFileEmptyView()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setData(data: MutableList<CloudDocsItem>, needInvalid: Boolean = false) {
        val selectList = viewModel?.uiState?.value?.selectedList ?: mutableListOf()
        Log.d(TAG, "setData selectList = ${selectList.size}")
        pageAdapter?.setData(data, selectList, doAnimation = false, notifyData = false)
        //编辑模式下还是需要刷新
        if (needInvalid) {
            pageAdapter?.invalidate()
        }
        //这里显示的已加载多少项，而不是界面上多少条，所以要用itemCount
        val itemCount = data.size
        Log.d(TAG, "setData itemCount = $itemCount, data = ${data.size}")
        sortView?.setFileCount(itemCount, com.filemanager.common.R.plurals.loaded_file_count)
        updateEditMenuVisible(mToolbar)
        mToolbar?.let {
            setToolbarEditIcon(it, isChildDisplay)
        }
    }

    private fun notifyItemRangeChanged() {
        val layoutManager = fragmentRecyclerView?.layoutManager as? LinearLayoutManager
        val firstPosition = layoutManager?.findFirstVisibleItemPosition() ?: RecyclerView.NO_POSITION
        val lastPosition = layoutManager?.findLastVisibleItemPosition() ?: RecyclerView.NO_POSITION
        if (firstPosition == RecyclerView.NO_POSITION || lastPosition == RecyclerView.NO_POSITION) {
            Log.d(TAG, "notifyItemRangeChanged first or last position is invalid")
            return
        }

        val count = lastPosition - firstPosition + 1
        pageAdapter?.checkComputingAndExecute {
            Log.d(TAG, "notifyItemRangeChanged first=$firstPosition, last=$lastPosition, count=$count")
            pageAdapter?.notifyItemRangeChanged(firstPosition, count)
        }
    }

    private fun requestAuthorization() {
        if (!isNetworkAvailable(appContext)) {
            Log.d(TAG, "requestAuthorization -> no network")
            CustomToast.showShort(stringResource(com.filemanager.common.R.string.no_internet_connection))
            return
        }
        lifecycleScope.launch {
            val accountLoginState = getAccountLoginState()
            Log.d(TAG, "requestAuthorization -> accountLoginState = $accountLoginState")
            if (!accountLoginState) {
                withContext(Dispatchers.Main) {
                    if (authorizationController == null) {
                        authorizationController = AuthorizationController()
                    }
                    baseVMActivity?.let {
                        authorizationController?.showAccountLoginDialog(it) {
                            loginAccount()
                        }
                    }
                }
            } else {
                applyAuthorization()
            }
        }
    }

    private suspend fun getAccountLoginState(): Boolean =
        withContext(Dispatchers.IO) {
            heytapAccountAction?.isLogin(appContext) ?: false
        }

    private fun loginAccount() {
        heytapAccountAction?.login(appContext, true) {
            Log.d(TAG, "login result = $it")
            lifecycleScope.launch {
                if (it) {
                    applyAuthorization()
                } else {
                    authorizationController?.dismissAccountLoginDialog()
                }
            }
        }
    }

    private suspend fun applyAuthorization() {
        CollectPrivacyUtils.collectDUID(UniqueIdUtils.getDeviceId())
        if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            authorizationByTencentDocs()
        } else {
            authorizedByKDocs()
        }
    }

    private suspend fun authorizationByTencentDocs() {
        val isInstalledWeChat = wechat?.isWxInstalled(appContext) ?: false
        Log.d(TAG, "authorizationByTencentDocs -> isInstalledWeChat = $isInstalledWeChat")
        if (isInstalledWeChat) {
            withContext(Dispatchers.IO) {
                val authUrl = viewModel?.getAuthUrl()
                if (authUrl.isNullOrEmpty()) {
                    withContext(Dispatchers.Main) {
                        CustomToast.showShort(com.filemanager.common.R.string.try_again_later)
                    }
                } else {
                    wechat?.authorization(authUrl)
                }
            }
        } else {
            if (authorizationController == null) {
                authorizationController = AuthorizationController()
            }
            baseVMActivity?.let {
                authorizationController?.showWeChatUninstalledDialog(it)
            }
        }
    }

    private suspend fun authorizedByKDocs() {
        loadAuthorizationView()
    }

    private suspend fun loadAuthorizationView() {
        Log.d(TAG, "loadAuthorizationView")
        val webViewAuthUrl = viewModel?.getWebViewAuthUrl()
        if (webViewAuthUrl.isNullOrEmpty()) {
            withContext(Dispatchers.Main) {
                CustomToast.showShort(com.filemanager.common.R.string.try_again_later)
            }
        } else {
            Log.d(TAG, "loadAuthorizationView -> url = $webViewAuthUrl")
            showAuthorizationLayout(webViewAuthUrl)
        }
    }

    private fun showUnauthorizedLayout() {
        Log.d(TAG, "showUnauthorizedLayout")
        forceUpdateData = true
        initUnauthorizedLayout()
        mToolbar?.apply {
            setToolbarMenuVisible(this, false)
        }
        fragmentRecyclerView?.isVisible = false
        hideExceptionPage()
        sortView?.isVisible = false
        pathBar?.isVisible = false
    }

    private fun handleAuthFailLayout(exception: Throwable?) {
        forceUpdateData = true
        val networkAvailable = isNetworkAvailable(appContext)
        val isServerError = if (exception != null) {
            ExceptionHandler.isSeverError(exception)
        } else {
            false
        }
        Log.d(TAG, "handleAuthFailLayout network:$networkAvailable server:$isServerError")
        if (isServerError) {
            hideUnauthorizedLayout()
            if (networkAvailable) { // 服务端出现问题
                showServerErrorView()
            } else { // 无网络
                showNoConnectView()
            }
            return
        }
        // 未授权页面
        showUnauthorizedLayout()
    }

    private fun hideUnauthorizedLayout() {
        if (unauthorizedLayout != null) {
            unauthorizedLayout?.isVisible = false
        }
    }

    private fun showAuthorizationLayout(url: String) {
        Log.d(TAG, "showAuthorizationLayout")
        initAuthorizationLayout()
        unauthorizedLayout?.isVisible = false
        initWebView(url)
    }

    private fun initUnauthorizedLayout() {
        if (unauthorizedLayout != null) {
            Log.d(TAG, "initUnauthorizedLayout had initialized")
            unauthorizedLayout?.isVisible = true
            return
        }
        val view = unauthorizedVS?.inflate()
        unauthorizedLayout = view?.findViewById(R.id.unauthorized_layout)
        authorizationTitle = unauthorizedLayout?.findViewById(R.id.cloud_drive_title)
        authorizationTitle?.text = getFileCloudDriveTitle(category)
        unauthorizedLayoutTips = unauthorizedLayout?.findViewById(R.id.cloud_drive_unauthorized_tips)
        unauthorizedSpace = unauthorizedLayout?.findViewById(R.id.space)
        updateAuthorizationSpaceVisible()
        unauthorizedLayoutTips?.text = if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            stringResource(com.filemanager.common.R.string.tencent_docs_authorization_desc, getFileCloudDriveTitle(category))
        } else {
            stringResource(com.filemanager.common.R.string.kdocs_authorization_title)
        }
        unauthorizedLayoutButton = unauthorizedLayout?.findViewById(R.id.authorization_button)
        unauthorizedLayoutIcon = unauthorizedLayout?.findViewById(R.id.cloud_drive_icon)
        unauthorizedLayoutIcon?.setImageDrawable(if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            ContextCompat.getDrawable(appContext, R.drawable.tencent_docsauthorization_icon)
        } else {
            ContextCompat.getDrawable(appContext, R.drawable.ic_kdocs_authorization)
        })
        unauthorizedLayoutButton?.setOnClickListener { requestAuthorization() }
        updateLPForButton(unauthorizedLayoutButton)
        updateButtonBottomMargin(unauthorizedLayoutButton)
    }

    private fun updateLPForButton(btn: COUIButton?) {
        btn?.let {
            val context = it.context
            it.maxWidth =
                context.resources.getDimension(com.support.appcompat.R.dimen.coui_single_larger_btn_width)
                    .toInt()
            val margin =
                context.resources.getDimension(com.support.button.R.dimen.coui_horizontal_single_btn_margin)
                    .toInt()
            verticalButtonWrap = SingleButtonWrap(it, SingleButtonWrap.Type.Large)
            it.updateLayoutParams<ConstraintLayout.LayoutParams> {
                val layoutLp = this as? MarginLayoutParams
                layoutLp?.marginStart = margin
                layoutLp?.marginEnd = margin
            }
        }
    }

    private fun updateButtonBottomMargin(btn: COUIButton?) {
        btn?.let {
            //如果导航手势模式下没有taskbar，按钮的底部需要24dp+16dp(导航条空间)，其他情况为24dp
            val isNavGestureWithoutTaskBar = SystemBarUtils.isNavGestureWithoutTaskBar(rootView)
            val bottomMargin = if (isNavGestureWithoutTaskBar) {
                it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_40dp).toInt()
            } else {
                it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_24dp).toInt()
            }
            it.updateLayoutParams<ConstraintLayout.LayoutParams> {
                val layoutLp = this as? ViewGroup.MarginLayoutParams
                layoutLp?.bottomMargin = bottomMargin
            }
        }
    }

    private fun initAuthorizationLayout() {
        if (authorizationWebView != null) {
            Log.d(TAG, "initAuthorizationLayout -> webView had initialized.")
            return
        }
        authorizationWebView = WebView(appContext)
        authorizationWebView?.apply {
            setBackgroundColor(Color.TRANSPARENT)
        }
        fragmentFastScroller?.apply {
            addView(authorizationWebView, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView(url: String) {
        val webSettings = authorizationWebView?.settings
        webSettings?.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                isAlgorithmicDarkeningAllowed = true
            }
        }
        authorizationWebView?.webChromeClient = WebChromeClient()
        baseVMActivity?.let {
            authorizationWebView?.webViewClient = AuthorizationWebViewClient { code, state ->
                Log.d(TAG, "initWebView -> code = $code ; state = $state")
                handleAuthorizationResult(code, state)
            }
        }
        Log.d(TAG, "initWebView -> url = $url")
        authorizationWebView?.loadUrl(url)
    }

    private fun handleAuthorizationResult(code: String, state: String) {
        if (code.isEmpty() || state.isEmpty()) {
            Log.d(TAG, "handleAuthorizationResult -> code or state is empty.")
            showUnauthorizedLayout()
            CustomToast.showShort(stringResource(com.filemanager.common.R.string.authorization_fail))
            fragmentFastScroller?.removeView(authorizationWebView)
        } else {
            lifecycleScope.launch(Dispatchers.IO) {
                val result = viewModel?.saveAuthorizationResult(code, state) ?: false
                withContext(Dispatchers.Main) {
                    if (result) {
                        viewModel?.loadData()
                        CustomToast.showShort(stringResource(com.filemanager.common.R.string.authorization_success))
                    } else {
                        showUnauthorizedLayout()
                        fragmentFastScroller?.removeView(authorizationWebView)
                        CustomToast.showShort(stringResource(com.filemanager.common.R.string.authorization_fail))
                    }
                }
            }
        }
    }

    private fun showDocsList(uiState: FileDriveUiState) {
        Log.d(TAG, "showDocsList")
        hideUnauthorizedLayout()
        destroyWebView()
        updateDocsList(uiState)
        sortView?.isVisible = true
        pathBar?.isVisible = true
    }

    private fun cancelAuth() {
        if (authorizationController == null) {
            authorizationController = AuthorizationController()
        }
        baseVMActivity?.let {
            authorizationController?.showCancelAuthorizeConfirmDialog(it, category) {
                lifecycleScope.launch {
                    val result = viewModel?.cancelAuth() ?: false
                    if (result) {
                        if (it is FileDriveActivity) {
                            val intent = Intent().apply {
                                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            }
                            Injector.injectFactory<IMain>()?.startMainActivity(it, intent)
                            it.finish()
                        } else {
                            Log.d(TAG, "cancel isChildDisplay:$isChildDisplay")
                            /*
                            cancelAuth方法成功后删除数据库会导致延迟执行FileDriveAdapter的onDataLoad，可能会将当前ui状态刷异常（显示sortView和pathBar），这里延迟200毫秒后处理。
                             */
                            fileDriveLayout?.postDelayed({
                                if (isChildDisplay) {
                                    showUnauthorizedLayout()
                                    mToolbar?.title = getFileCloudDriveTitle(category)
                                } else {
                                    viewModel?.authState?.value = false
                                    baseVMActivity?.onBackPressed()
                                }
                            }, CANCEL_AUTHORIZE_UPDATE_DELAY_TIME)
                        }
                        CustomToast.showShort(stringResource(com.filemanager.common.R.string.cancel_auth_success))
                    } else {
                        CustomToast.showShort(stringResource(com.filemanager.common.R.string.cancel_auth_fail))
                    }
                }
            }
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        Log.d(TAG, "setToolbarMenuVisible -> visible = $visible")
        toolbar.menu.findItem(R.id.actionbar_edit)?.isVisible = visible
        toolbar.menu.findItem(R.id.actionbar_cancel_auth)?.isVisible = visible
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.isVisible = visible
    }

    private fun updateAuthorizationSpaceVisible() {
        val isSmallScreen = UIConfigMonitor.isCurrentSmallScreen()
        val isMultiWindow = UIConfigMonitor.isMultiWindow()
        unauthorizedSpace?.isVisible = !(isSmallScreen && isMultiWindow)
    }

    override fun onDestroyView() {
        authorizationWebView?.destroy()
        authorizationWebView = null
        authorizationController?.release()
        authorizationController = null
        super.onDestroyView()
        pageAdapter?.onDestroy()
        unregisterWechatSdk()
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        verticalButtonWrap?.release()
        rootView?.setOnApplyWindowInsetsListener(null)
    }

    private fun destroyWebView() {
        fragmentFastScroller?.removeView(authorizationWebView)
        authorizationWebView?.destroy()
        authorizationWebView = null
    }

    fun onSelect(code: Int, path: String?) {
        selectCode = code
        selectPath = path
        if (PermissionUtils.hasNotificationPermission(appContext)) {
            fromSelectPathResult(code, path)
        } else {
            if (SdkUtils.isAtLeastT()) {
                permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            } else {
                fromSelectPathResult(selectCode, selectPath)
            }
        }
    }

    fun updatePadding(systemBarInsetsBottom: Int) {
        rootView?.updatePadding(bottom = systemBarInsetsBottom)
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_TENCENT_DOCS
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        baseVMActivity?.let {
            FileImageVHUtils.changedListMargin(
                it,
                it.sideNavigationContainer?.drawerViewWidth ?: 0,
                if (isOpen) KtConstants.SIDE_NAVIGATION_OPEN else KtConstants.SIDE_NAVIGATION_CLOSE
            )
            updateLeftRightMargin()
        }
        if (viewModel?.browseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    companion object {
        private const val TAG = "FileDriveFragment"
        private const val CANCEL_AUTHORIZE_UPDATE_DELAY_TIME = 200L

        fun newInstance(type: Int): FileDriveFragment {
            val fragment = FileDriveFragment()
            fragment.arguments = Bundle().apply {
                putInt(Constants.KEY_FILE_DRIVE_TYPE, type)
            }
            return fragment
        }
    }
}