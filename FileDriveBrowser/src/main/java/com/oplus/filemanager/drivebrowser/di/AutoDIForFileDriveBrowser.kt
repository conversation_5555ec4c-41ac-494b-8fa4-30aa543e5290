/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.di

import androidx.annotation.Keep
import com.oplus.filemanager.drivebrowser.FileCloudBrowserApi
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import org.koin.dsl.module

@Keep
class AutoDIForFileDriveBrowser {

    val fileCloudBrowserModule = module {
        single<IFileCloudBrowser>(createdAtStart = true) {
            FileCloudBrowserApi
        }
    }
}