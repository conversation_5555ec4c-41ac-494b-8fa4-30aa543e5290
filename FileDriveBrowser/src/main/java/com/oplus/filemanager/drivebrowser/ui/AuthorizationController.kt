/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AuthorizationController
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/28 15:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/28       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.drivebrowser.R

class AuthorizationController {

    private var authorizationDialog: AlertDialog? = null
    private var accountLoginDialog: AlertDialog? = null
    private var cancelAuthorizeConfirmDialog: AlertDialog? = null
    private var uninstallWeChatPreviewDialog: AlertDialog? = null

    fun showWeChatUninstalledDialog(context: Context) {
        if (authorizationDialog == null) {
            authorizationDialog = createUninstallWeChatTipsDialog(context)
        }
        authorizationDialog?.show()
    }

    fun showUninstallWeChatPreviewDialog(context: Context) {
        if (uninstallWeChatPreviewDialog == null) {
            uninstallWeChatPreviewDialog = createUninstallWeChatPreviewDialog(context)
        }
        uninstallWeChatPreviewDialog?.show()
    }

    fun showAccountLoginDialog(context: Context, login: () -> Unit) {
        if (accountLoginDialog == null) {
            accountLoginDialog = createAccountLoginDialog(context, login)
        }
        accountLoginDialog?.show()
    }

    fun showCancelAuthorizeConfirmDialog(context: Context, category: Int, confirm: () -> Unit) {
        if (cancelAuthorizeConfirmDialog == null) {
            cancelAuthorizeConfirmDialog = createCancelAuthorizeConfirmDialog(context, category, confirm)
        }
        cancelAuthorizeConfirmDialog?.show()
    }

    fun dismissAccountLoginDialog() {
        accountLoginDialog?.dismiss()
    }

    fun release() {
        authorizationDialog?.dismiss()
        authorizationDialog = null
        uninstallWeChatPreviewDialog?.dismiss()
        uninstallWeChatPreviewDialog = null
        accountLoginDialog?.dismiss()
        accountLoginDialog = null
        cancelAuthorizeConfirmDialog?.dismiss()
        cancelAuthorizeConfirmDialog = null
    }

    private fun createAccountLoginDialog(context: Context, login: () -> Unit): AlertDialog {
        val builder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Center)
            .setTitle(context.getString(com.filemanager.common.R.string.login_account_to_authorization, getBrandString(context)))
            .setPositiveButton(context.getString(com.filemanager.common.R.string.sing_in)) { _, _ ->
                login.invoke()
            }
            .setNegativeButton(context.getString(com.filemanager.common.R.string.dialog_cancel), null)
        return builder.create()
    }

    private fun createUninstallWeChatPreviewDialog(context: Context): AlertDialog {
        val builder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Center)
            .setTitle(context.getString(com.filemanager.common.R.string.wechat_uninstall_preview_title))
            .setPositiveButton(com.filemanager.common.R.string.positive_ok, null)
        return builder.create()
    }

    private fun createCancelAuthorizeConfirmDialog(context: Context, category: Int, confirm: () -> Unit): AlertDialog {
        val categoryTitle = if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            context.getString(com.filemanager.common.R.string.tencent_docs)
        } else {
            context.getString(com.filemanager.common.R.string.kdocs)
        }
        val builder = COUIAlertDialogBuilder(context)
            .setTitle(context.getString(com.filemanager.common.R.string.cancel_auth_confirm_title))
            .setMessage(context.getString(com.filemanager.common.R.string.cancel_auth_confirm_desc, categoryTitle))
            .setNeutralButton(context.getString(com.filemanager.common.R.string.cancel_auth)) { _, _ ->
                confirm.invoke()
            }
            .setNegativeButton(context.getString(com.filemanager.common.R.string.dialog_cancel), null)
        return builder.create()
    }

    private fun createUninstallWeChatTipsDialog(context: Context): AlertDialog {
        val builder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Center)
            .setTitle(context.getString(com.filemanager.common.R.string.wechat_uninstall_authorization_title))
            .setPositiveButton(com.filemanager.common.R.string.positive_ok, null)
        return builder.create()
    }

    private fun getBrandString(context: Context): String {
        return if (Utils.isRealmePhone()) {
            context.getString(com.filemanager.common.R.string.brand_realme)
        } else if (KtAppUtils.isOnePlus) {
            context.getString(com.filemanager.common.R.string.brand_oneplus)
        } else {
            context.getString(com.filemanager.common.R.string.brand_oppo)
        }
    }

    companion object {
        private const val TAG = "AuthorizationController"
    }
}