/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentListsRequest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/5 14:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/5       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model.kdocs

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class DocumentListsRequest(
    /**
     * 获取数量
     */
    @SerializedName("count") var count: Int,
    @SerializedName("offset") var offset: Int,
    /**
     * 排序规则，asc 或者 desc
     */
    @SerializedName("order") var order: String,
    /**
     * 排序字段，枚举值：mtime/fname/fsize
     */
    @SerializedName("order_by") var orderBy: String,
    @SerializedName("parent_id") var parentId: String,
    /**
     * 传递folder或者file
     */
    @SerializedName("filter") var filter: String
) : Parcelable {
    companion object {
        /**
         * 升序
         */
        const val ORDER_ASC = "asc"

        /**
         * 降序
         */
        const val ORDER_DESC = "desc"

        fun orderBy(asc: Int): String {
            return if (asc == 1) {
                ORDER_ASC
            } else {
                ORDER_DESC
            }
        }
    }
}