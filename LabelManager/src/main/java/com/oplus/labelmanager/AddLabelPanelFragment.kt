/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.AddLabelPanelFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Message
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.progressbar.COUICompProgressIndicator
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMPanelFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StaticHandler
import com.google.android.material.chip.ChipGroup
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.labelmanager.KeyboardUtils.hideSoftInput
import com.oplus.labelmanager.chip.ChipEditText

class AddLabelPanelFragment : BaseVMPanelFragment<LabelManagerViewModel>() {

    companion object {
        private const val TAG = "AddLabelPanelFragment"
        private const val DOUBLE_ACTION_INTERVAL = 2000L
        private const val MSG_DIALOG_DELAY = 500L
        private const val MSG_DIALOG_SHOW = 1
        private const val MSG_DIALOG_DISMISS = 2
        private const val KEY_INPUT_LABELS_NAMES = "InputLabelsDisplayNames"
        private const val KEY_IS_NEED_CLEAR_INPUT = "isNeedClearInput"
        private const val HAS_RESTORED = "HAS_RESTORED"
    }

    var mFileList: List<BaseFileBean> = arrayListOf()

    private var mHasRestored = false
    private var mLastClickTime = 0L
    private var mChipEditText: ChipEditText? = null
    private var mChipEditTextMinHeight: ChipEditText? = null
    private lateinit var mRecommendLabels: TextView
    private lateinit var mOtherLabels: TextView
    private var mRecommendLabelsChipsGroup: ChipGroup? = null
    private var mOtherLabelsChipsGroup: ChipGroup? = null
    private var mRootView: ViewGroup? = null
    private var mRotatingAlertDialog: AlertDialog? = null
    private var mDisplayDialogTime = 0L
    private var mLoadingView: COUICompProgressIndicator? = null
    private var mLoadingLayout: RelativeLayout? = null
    private var mShowLabelsLayout: LinearLayout? = null

    private var mAddLabelViewModel: AddLabelViewModel? = null
    private var mRecommendLabelsDisplayNames = mutableListOf<String>()
    private var mOtherLabelsDisplayNames = mutableListOf<String>()
    private var mInputLabelsDisplayNames = mutableListOf<String>()

    private var mIgnoreInputChange: Boolean = false

    private val mHandler by lazy { AddLabelHandler(this) }
    /** 已经选择过的选中标签，会在onDestroy后清除，需要在onSaveInstanceState记录下，重建时不clear mInputLabelsDisplayNames  */
    private var isNeedClearInput = true

    override fun initContentView(view: View) {
        findViews(view)
        dragView.visibility = View.INVISIBLE
        PreferencesUtils.put(key = Constants.KEY_HAS_CLICK_LABEL_NAVIGATION_MENU, value = true)
        initOutSideViewClickListener()
        initOnBackKeyListener()
        initToolbar()
        initChips()
    }

    private fun findViews(view: View) {
        mRootView = view.findViewById(R.id.root_view)
        mRecommendLabelsChipsGroup = mBaseRootView?.findViewById(R.id.recommend_label_group)
        mOtherLabelsChipsGroup = mBaseRootView?.findViewById(R.id.other_label_group)
        mChipEditText = view.findViewById(R.id.editTextEditLabel)
        mChipEditTextMinHeight = view.findViewById(R.id.editTextEditLabelMinHeight)
        mRecommendLabels = view.findViewById(R.id.recommend_label_tv)
        mOtherLabels = view.findViewById(R.id.other_label_tv)
        mLoadingView = view.findViewById(R.id.loadingView_eav)
        mShowLabelsLayout = view.findViewById(R.id.show_labels_ll)
        mLoadingLayout = view.findViewById(R.id.loading_layout_ll)
        startLoadingViewAnim()
    }

    private fun startLoadingViewAnim() {
        mLoadingView?.visibility = View.VISIBLE
    }

    private fun hideInputKeyBoard() {
        mChipEditText?.apply {
            hideSoftInput()
        }
    }

    private fun initToolbar() {
        toolbar = toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(com.filemanager.common.R.string.string_add_label)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_panel_edit)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    dismissPanel()
                    true
                }
            }
            menu.findItem(R.id.save).apply {
                setOnMenuItemClickListener {
                    onSaveClick()
                    true
                }
            }
        }
    }

    private fun onSaveClick() {
        mChipEditText?.let {
            it.commitDefault(it.text)
        }
        val inputContent = mChipEditText?.text.toString().trim()
        if (inputContent.isEmpty()) {
            Log.v(TAG, "inputContent is empty, do unmapping")
            val mainAction = Injector.injectFactory<IMain>()
            val isFromLabelList = mainAction?.isLabelListActivity(mActivity) ?: false
            mAddLabelViewModel?.unMappingFileToLabel(mFileList, isFromLabelList)
        } else {
            val labelNames = arrayListOf<String>()
            inputContent.split(ChipEditText.COMMIT_CHAR_COMMA).filter { it != "" }.forEach {
                val chipText = mChipEditText?.getChipText(it)
                if (chipText != null) {
                    labelNames.add(chipText)
                }
            }
            removeDuplicateLabelName(labelNames)
            mAddLabelViewModel?.mappingFileToLabel(labelNames, mFileList)
        }
    }

    /**
     * 判断标签面板的输入框中是否有内容
     * 无-退出时：直接退出
     * 有-退出时：保持二次确认的逻辑不变
     */
    private fun isInputContentEmpty(): Boolean {
        val inputContent = mChipEditText?.text.toString().trim()
        return inputContent.isEmpty()
    }

    private fun removeDuplicateLabelName(labelNames: ArrayList<String>) {
        val distinct = labelNames.toSet().toList()
        labelNames.clear()
        labelNames.addAll(distinct)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initOutSideViewClickListener() {
        setOutSideViewOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                if (!isInputContentEmpty() && System.currentTimeMillis() - mLastClickTime > DOUBLE_ACTION_INTERVAL) {
                    Toast.makeText(context, getString(com.filemanager.common.R.string.panel_click_outside_view_toast), Toast.LENGTH_SHORT).show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    mLastClickTime = System.currentTimeMillis()
                } else {
                    dismissPanel()
                }
            }
            true
        }
    }

    private fun initOnBackKeyListener() {
        setDialogOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                if (!isInputContentEmpty() && System.currentTimeMillis() - mLastClickTime > DOUBLE_ACTION_INTERVAL) {
                    (parentFragment as? COUIBottomSheetDialogFragment)?.isCancelable = false
                    Toast.makeText(context, getString(com.filemanager.common.R.string.panel_back_toast), Toast.LENGTH_SHORT).show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    mLastClickTime = System.currentTimeMillis()
                } else {
                    (parentFragment as? COUIBottomSheetDialogFragment)?.isCancelable = true
                }
            }
            false
        }
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
        hideInputKeyBoard()
    }

    override fun getLayoutResId(): Int = R.layout.fragment_add_label_panel

    override fun onResume() {
        super.onResume()
        if (mHasRestored) {
            mChipEditText?.post { mChipEditText?.refresh() }
            if (mFileList.isEmpty()) {
                mAddLabelViewModel?.mFileList?.let {
                    if (it.isNotEmpty()) {
                        Log.i(TAG, "mFileList is null and resetdata")
                        mFileList = it
                    }
                }
            }
            mHasRestored = false
        }
        mChipEditTextMinHeight?.apply {
            //此处给占位用的ChipEditText设置默认文本和标签状态来计算出最小高度，以备实际的mChipEditText使用
            setText("a")
            commitDefault(text)
            val minHeight = this.measuredHeight
            mChipEditText?.minHeight = minHeight
        }
    }

    override fun initData() {
        if (mAddLabelViewModel == null) {
            mAddLabelViewModel = ViewModelProvider(this)[AddLabelViewModel::class.java]
        }
        mAddLabelViewModel?.initData(mFileList)
    }

    private fun initChips() {
        mChipEditText?.apply {
            hint = resources.getString(com.filemanager.common.R.string.label_choice_add_label)
            setTokenizer(ChipEditText.ChipTokenizer())
            setChipDeleteListener(object : ChipEditText.OnChipDeleteListener {
                override fun onDelete(originText: CharSequence) {
                    val childView = if (originText in mRecommendLabelsDisplayNames) {
                        val position = mRecommendLabelsDisplayNames.toList().indexOf(originText)
                        mRecommendLabelsChipsGroup?.getChildAt(position)
                    } else {
                        val position = mOtherLabelsDisplayNames.indexOf(originText)
                        mOtherLabelsChipsGroup?.getChildAt(position)
                    }
                    childView.apply {
                        if (this is COUIChip) {
                            setOnCheckedChangeListener(null)
                            isChecked = false
                            setOnCheckedChangeListener(getChipListener(originText.toString()))
                        }
                    }
                }
            })
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    if (mIgnoreInputChange) return
                }

                override fun onTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                    if (mIgnoreInputChange) return
                    if (s.isNullOrBlank()) {
                        mAddLabelViewModel?.showAllLabelsForOther()
                    } else {
                        val noneChipChar = getNoneChipChar(s.toString())
                        if (noneChipChar.isBlank()) {
                            mAddLabelViewModel?.showAllLabelsForOther()
                        } else {
                            //判断在选中 取消chip时 不去执行其他标签查询筛选 bug4839193
                            val needSetCusorVisibleAndLast = mChipEditText?.mNeedSetCusorVisibleAndLast ?: false
                            if (!needSetCusorVisibleAndLast) {
                                mAddLabelViewModel?.showFuzzyMatchedLabelsForOther(noneChipChar)
                            }
                        }
                    }
                }

                override fun afterTextChanged(s: Editable?) {
                    if (mIgnoreInputChange) return
                }
            })
        }
        mChipEditTextMinHeight?.apply {
            setTokenizer(ChipEditText.ChipTokenizer())
        }
    }

    private fun handleChipOnClick(chip: COUIChip, chipName: String) {
        chip.setOnCheckedChangeListener(getChipListener(chipName))
    }

    private fun getChipListener(chipName: String): CompoundButton.OnCheckedChangeListener {
        return CompoundButton.OnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                mChipEditText?.apply {
                    val noneChipChar = getNoneChipChar(text.toString())
                    val first = text.toString().indexOf(noneChipChar)
                    text?.delete(first, first + noneChipChar.length)
                    commitDefault(text?.append(chipName))
                }
            } else {
                mChipEditText?.apply {
                    val editable = text
                    var displayText = createAddressText(chipName)
                    var first = editable.toString().indexOf(displayText)
                    if (first == -1) {
                        first = editable.toString().indexOf(chipName + ChipEditText.COMMIT_CHAR_SPACE)
                        displayText = chipName + ChipEditText.COMMIT_CHAR_SPACE
                    }
                    //找不到需要删除的displayText时 返回处理
                    if (first == -1) {
                        return@apply
                    }
                    editable?.delete(first, first + displayText.length)
                    //取消选中的标签如果是输入框中选中的标签 把mSelectedChip置为空
                    mSelectedChip?.originalText?.let {
                        if (it == chipName) {
                            mSelectedChip = null
                        }
                    }
                }
            }
        }
    }

    override fun startObserve() {
        mRootView?.post {
            if (isAdded) {
                mAddLabelViewModel?.uiState?.observe(this) {
                    showInputLabelsUi(it)
                    showRecommendLabelsUi(it)
                    showOthersLabelsUi(it)
                    hideLoadingView()
                }
                mAddLabelViewModel?.mUpdateLabels?.observe(this) {
                    showSavingDialog(it)
                }
            }
        }
    }

    private fun showSavingDialog(updateStatus: Int?) {
        when (updateStatus) {
            AddLabelViewModel.STATUS_LABELS_UPDATING -> mHandler.sendEmptyMessageDelayed(MSG_DIALOG_SHOW, MSG_DIALOG_DELAY)
            AddLabelViewModel.STATUS_LABELS_DONE -> {
                if (mActivity is TransformNextFragmentListener) {
                    (mActivity as? TransformNextFragmentListener)?.onUpdatedLabel()
                }
                mHandler.removeMessages(MSG_DIALOG_SHOW)
                if (mDisplayDialogTime != 0L) {
                    val timeStamp = System.currentTimeMillis() - mDisplayDialogTime
                    if (timeStamp > MSG_DIALOG_DELAY) {
                        dismissSavingDialog()
                        dismissPanel()
                    } else {
                        mHandler.sendEmptyMessageDelayed(MSG_DIALOG_DISMISS, MSG_DIALOG_DELAY)
                    }
                } else {
                    dismissSavingDialog()
                    dismissPanel()
                }
            }
            AddLabelViewModel.STATUS_LABELS_FAIL_REACH_MAX -> {
                dismissSavingDialog()
                CustomToast.showShort(com.filemanager.common.R.string.exceed_label_counts)
            }
            AddLabelViewModel.STATUS_LABELS_FAIL_TOO_LONG -> {
                dismissSavingDialog()
                CustomToast.showShort(com.filemanager.common.R.string.label_input_over_upper_limit)
            }
            AddLabelViewModel.STATUS_LABELS_FAIL_INVALID_SYMBOL -> {
                dismissSavingDialog()
                CustomToast.showShort(
                    MyApplication.appContext.getString(
                        com.filemanager.common.R.string.error_symbol_in_input_text,
                        " \\ / : * ? \" < > |"
                    )
                )
            }
            AddLabelViewModel.STATUS_LABELS_FAIL_ADD_ERROR -> {
                dismissSavingDialog()
                CustomToast.showShort(com.filemanager.common.R.string.phone_storage_can_not_save)
            }
        }
    }

    private fun dismissSavingDialog() {
        mHandler.removeMessages(MSG_DIALOG_SHOW)
        mRotatingAlertDialog?.let { dialog ->
            mDisplayDialogTime = 0
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }
    }

    @Suppress("UnsafeCallOnNullableType")
    private fun createLoadingDialog() {
        val activity = mActivity ?: return
        mRotatingAlertDialog = COUIRotatingDialogBuilder(activity, activity.getString(com.filemanager.common.R.string.label_tip_saving))
            .show()
        mRotatingAlertDialog?.setCancelable(false)
    }

    private fun showInputLabelsUi(addLabelUiModel: AddLabelViewModel.AddLabelUiModel) {
        mIgnoreInputChange = true
        if (isNeedClearInput) {
            //会从addLabelUiModel.inputLabels中更新，先清空
            mInputLabelsDisplayNames.clear()
        }
        isNeedClearInput = true
        if (addLabelUiModel.isFromInit) {
            addLabelUiModel.inputLabels.forEach { mInputLabelsDisplayNames.add(it.label.name) }
            mInputLabelsDisplayNames.forEach { name -> mChipEditText?.let { it.commitDefault(it.editableText?.append(name)) } }
        } else {
            mInputLabelsDisplayNames.addAll(mChipEditText?.getChipCharList() ?: arrayListOf())
        }
        mIgnoreInputChange = false
    }

    private fun hideLoadingView() {
        mLoadingView?.apply {
            post {
                mChipEditText?.isEnabled = true
                visibility = View.GONE
                mShowLabelsLayout?.visibility = View.VISIBLE
            }
        }
    }

    private fun showRecommendLabelsUi(addLabelUiModel: AddLabelViewModel.AddLabelUiModel) {
        mRecommendLabelsDisplayNames.clear()
        if (addLabelUiModel.recommendedLabels.isEmpty()) {
            mRecommendLabels.visibility = View.GONE
        } else {
            mRecommendLabels.visibility = View.VISIBLE
            addLabelUiModel.recommendedLabels.forEach { mRecommendLabelsDisplayNames.add(it.label.name) }
            Log.w(TAG, "showRecommendLabelsUi mRecommendLabelsDisplayNames : $mRecommendLabelsDisplayNames")
            mRecommendLabelsChipsGroup?.removeAllViews()
            mRecommendLabelsDisplayNames.forEach { addChipToChipGroup(it, mRecommendLabelsChipsGroup) }
        }
    }

    private fun showOthersLabelsUi(addLabelUiModel: AddLabelViewModel.AddLabelUiModel) {
        mOtherLabelsChipsGroup?.removeAllViews()
        mOtherLabelsDisplayNames.clear()
        if (addLabelUiModel.otherLabels.isEmpty()) {
            mOtherLabels.visibility = View.GONE
        } else {
            mOtherLabels.visibility = View.GONE
            addLabelUiModel.otherLabels.forEach { mOtherLabelsDisplayNames.add(it.label.name) }
            mOtherLabelsDisplayNames.forEach { name ->
                if (name in mRecommendLabelsDisplayNames) return@forEach
                mOtherLabels.visibility = View.VISIBLE
                addChipToChipGroup(name, mOtherLabelsChipsGroup)
            }
        }
    }

    private fun addChipToChipGroup(name: String, chipGroup: ChipGroup?) {
        val chip: COUIChip = LayoutInflater.from(mActivity).inflate(R.layout.item_label_edit, chipGroup, false) as COUIChip
        COUIChangeTextUtil.adaptFontSize(chip, COUIChangeTextUtil.G4)
        chip.text = name
        chipGroup?.addView(chip)
        chip.isChecked = mInputLabelsDisplayNames.contains(name)
        handleChipOnClick(chip, name)
    }

    override fun onResumeLoadData() {
        // do nothing
    }

    class AddLabelHandler(fragment: AddLabelPanelFragment) : StaticHandler<AddLabelPanelFragment>(fragment) {
        override fun handleMessage(msg: Message?, fragment: AddLabelPanelFragment?) {
            super.handleMessage(msg, fragment)
            msg ?: return
            when (msg.what) {
                MSG_DIALOG_SHOW -> fragment?.createLoadingDialog()
                MSG_DIALOG_DISMISS -> {
                    fragment?.dismissSavingDialog()
                    fragment?.dismissPanel()
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val arrayList = arrayListOf<String>()
        arrayList.addAll(mInputLabelsDisplayNames)
        outState.putStringArrayList(KEY_INPUT_LABELS_NAMES, arrayList)
        outState.putBoolean(KEY_IS_NEED_CLEAR_INPUT, false)
        outState.putBoolean(HAS_RESTORED, true)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val arrayList = savedInstanceState?.getStringArrayList(KEY_INPUT_LABELS_NAMES)
        arrayList?.let {
            mInputLabelsDisplayNames.addAll(it.toMutableList())
            isNeedClearInput = savedInstanceState.getBoolean(KEY_IS_NEED_CLEAR_INPUT)
        }
        mHasRestored = savedInstanceState?.getBoolean(HAS_RESTORED) ?: false
    }

    override fun onDestroy() {
        super.onDestroy()
        mHandler.removeCallbacksAndMessages(null)
    }
}