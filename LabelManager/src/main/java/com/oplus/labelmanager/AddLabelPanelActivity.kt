/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddLabelPanelActivity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/02/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   keweiwei                 2024/02/29       1      create
 ***********************************************************************/
package com.oplus.labelmanager

import android.content.pm.ActivityInfo
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils

class AddLabelPanelActivity : BaseVMActivity(), BaseVMActivity.PermissonCallBack {

    companion object {
        private const val TAG = "AddLabelPanelActivityTag"
        private const val FROM_SAVE_INSTANCE = "fromSaveInstance"
        private const val FILES_TAG = "files"
    }
    @VisibleForTesting
    var fileUris: ArrayList<String>? = null
    @VisibleForTesting
    var hasShowPanel: Boolean = false
    @VisibleForTesting
    var hasPermissionReject = false
    @VisibleForTesting
    var fromSaveInstance = false
    @VisibleForTesting
    val addFileLabelController: AddFileLabelController by lazy {
        AddFileLabelController(
            lifecycle
        )
    }
    @VisibleForTesting
    val addLabelPanelModel: AddLabelPanelModel by lazy {
        AddLabelPanelModel()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(TAG, "onCreate $this")
        loadData(savedInstanceState)
        super.onCreate(savedInstanceState)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        Log.d(TAG, "onSaveInstanceState $this, $fileUris")
        super.onSaveInstanceState(outState)
        outState.putBoolean(FROM_SAVE_INSTANCE, true)
        outState.putStringArrayList(FILES_TAG, fileUris)
    }

    override fun getLayoutResId(): Int {
        return R.layout.add_label_activity
    }

    @VisibleForTesting
    fun canShowAddLabelPanel(): Boolean {
        Log.d(
            TAG,
            "canShowAddLabelPanel ${PrivacyPolicyController.hasAgreePrivacy()}, ${
                PermissionUtils.hasStoragePermission(this)
            }, ${hasShowPanel.not()}"
        )
        return PrivacyPolicyController.hasAgreePrivacy() && PermissionUtils.hasStoragePermission(
            this
        ) && hasShowPanel.not()
    }

    override fun initView() {
        showAddLabelPanel()
    }

    @VisibleForTesting
    fun loadData(savedInstanceState: Bundle?) {
        fileUris = if (savedInstanceState?.getBoolean(FROM_SAVE_INSTANCE) == true) {
            fromSaveInstance = true
            savedInstanceState.getStringArrayList(FILES_TAG)
        } else {
            fromSaveInstance = false
            IntentUtils.getStringArrayList(intent, FILES_TAG)
        }
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
    }

    override fun initData() {
        Log.d(TAG, "initData")
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage")
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        addFileLabelController.onDestroy()
        super.onDestroy()
    }

    override fun onPermissionSuccess() {
        Log.d(TAG, "onPermissionSuccess hasShowPanel = ${hasShowPanel.not()}")
        showAddLabelPanel()
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        Log.d(TAG, "onPermissionReject")
        hasPermissionReject = true
        finish()
    }

    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission")
        if (!hasPermissionReject) {
            showSettingGuildDialog()
        }
    }

    @VisibleForTesting
    fun showAddLabelPanel() {
        if (fromSaveInstance.not()) {
            if (canShowAddLabelPanel().not()) {
                Log.d(TAG, "can not show add Label panel")
                return
            }
            val files = fileUris
            if (files.isNullOrEmpty()) {
                Log.d(TAG, "files is null")
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                finish()
                return
            }
            val fileList = addLabelPanelModel.getFileList(files)
            if (fileList.isEmpty()) {
                Log.d(TAG, "fileBean is null")
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                finish()
                return
            }
            Log.d(TAG, "showAddLabelPanel")
            addFileLabelController.showAddLabelFragment(supportFragmentManager, fileList) {
                Log.d(TAG, "onDismiss, ${<EMAIL>()}")
                if (<EMAIL>()) {
                    finish()
                }
            }
            hasShowPanel = true
        } else {
            Log.d(TAG, "showAddLabelPanel reset DismissListener")
            addFileLabelController.setOnDismissListener(supportFragmentManager) {
                Log.d(TAG, "onDismiss, ${<EMAIL>()}")
                if (<EMAIL>()) {
                    finish()
                }
            }
            hasShowPanel = addFileLabelController.hasShowPanel(supportFragmentManager)
            Log.d(TAG, "showAddLabelPanel hasShowPanel = $hasShowPanel")
        }
    }

    override fun getScreenOrientation(): Int? {
        return ActivityInfo.SCREEN_ORIENTATION_USER
    }
}