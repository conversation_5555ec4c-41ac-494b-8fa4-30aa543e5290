/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Recommend.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/8/15      1.0        create
 ***********************************************************************/
package com.oplus.labelmanager.recommend

import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.labelmanager.recommend.aiunit.AiUnitUtils

class Recommend {

    companion object {
        private const val TAG = "Recommend"
        private const val TOP_3 = 3
        private const val TOP_5 = 5

        private const val SOURCE_AI = 0
        private const val SOURCE_USED = 1
        private const val SOURCE_MAPPING_COUNT = 2
        private const val SOURCE_ACT_VIEW = 3
        private const val SOURCE_ACT_PIN = 4

        private const val WEIGHT_INDEX_AI = 0.4F
        private const val WEIGHT_INDEX_USED = 0.25F
        private const val WEIGHT_INDEX_MAPPING_COUNT = 0.15F
        private const val WEIGHT_INDEX_OTHER_ACT_VIEW = 0.14F
        private const val WEIGHT_INDEX_OTHER_ACT_PIN = 0.06F

        private const val INDEX_0 = 0
        private const val INDEX_1 = 1
        private const val INDEX_2 = 2
        private const val INDEX_3 = 3
        private const val INDEX_4 = 4

        private const val INDEX_0_PERCENT = 1.0F
        private const val INDEX_1_PERCENT = 0.9F
        private const val INDEX_2_PERCENT = 0.75F
        private const val INDEX_3_PERCENT = 0.55F
        private const val INDEX_4_PERCENT = 0.3F
    }

    /** 计算出的AIUnit智能标签，选中的标签中有AI标签，触发选中智能标签埋点 */
    private val aiRecommendLabels = mutableListOf<RecommendLabel>()

    fun getTopRecommendLabels(fileList: List<BaseFileBean>): List<FileLabelEntity> {
        val t = System.currentTimeMillis()
        val result: List<FileLabelEntity>?

        val topAiUnit = getTopAiUnitLabels(fileList)
        val topUsed = getTopUsedLabels()
        val topMappingCount = getTopMappingFileCountLabels()
        val topOtherUserAction = getTopOtherUserActionLabels()

        result = mergeAllSources(topAiUnit, topUsed, topMappingCount, topOtherUserAction)

        return result.also {
            it.forEachIndexed { index, fileLabelEntity ->
                Log.v(TAG, "getTopRecommendLabels index = $index, label = $fileLabelEntity")
            }
            Log.v(TAG, "getTopRecommendLabels cost = ${System.currentTimeMillis() - t}")
        }
    }

    private fun getTopAiUnitLabels(fileList: List<BaseFileBean>): List<RecommendLabel> {
        aiRecommendLabels.clear()
        val aiUnitResult = AiUnitUtils.getInstance().getAIUnitResult(fileList)
        aiUnitResult?.let { aiLabelList ->
            val top5List = aiLabelList.take(TOP_5)
            val labelsInLocal = FileLabelDBHelper.getFileLabelByNames(top5List.map { it.label })
            top5List.forEachIndexed { index, label ->
                val labelInLocal = labelsInLocal?.find { it.name == label.label }
                val labelId = findLabelIdByLabelInLocal(labelInLocal)
                val recommendLabel = RecommendLabel(labelId, label.label, WEIGHT_INDEX_AI * getPercentByIndex(index), SOURCE_AI)
                aiRecommendLabels.add(recommendLabel)
            }
        }
        return aiRecommendLabels
    }

    /**
     * 选择的标签中，包含AIUnit计算出来的标签，触发埋点
     */
    fun checkAiLabelClick(labelNames: List<String>): Boolean {
        val selectedAiLabel = aiRecommendLabels.find { recommendLabel -> labelNames.contains(recommendLabel.labelName) }
        return selectedAiLabel != null
    }

    /**
     * 根据标签名称，从本地数据库中获取Id值，若没有ID值的，给-1.
     */
    private fun findLabelIdByLabelInLocal(labelInLocal: FileLabelEntity?): Long {
        if (labelInLocal != null) {
            return labelInLocal.id
        }
        return -1L
    }

    private fun getTopUsedLabels(): List<RecommendLabel> {
        val result = mutableListOf<RecommendLabel>()
        FileLabelDBHelper.getTopUsedLabels(TOP_5)?.mapIndexed { index, fileLabelEntity ->
            Log.d(TAG, "getTopUsedLabels index = $index, label = $fileLabelEntity")
            result.add(
                RecommendLabel(
                    labelId = fileLabelEntity.id,
                    labelName = fileLabelEntity.name,
                    score = WEIGHT_INDEX_USED * getPercentByIndex(index),
                    source = SOURCE_USED
                )
            )
        }
        return result.also {
            it.forEachIndexed { index, recommendLabel ->
                Log.v(TAG, "getTopUsedLabels index = $index, recommendLabel = $recommendLabel")
            }
        }
    }

    private fun getTopMappingFileCountLabels(): List<RecommendLabel> {
        val result = mutableListOf<RecommendLabel>()
        val ids = FileLabelMappingDBHelper.getTopMappingFileCountLabelIds().take(TOP_5)
        Log.d(TAG, "getTopMappingFileCountLabels ids = $ids")
        ids.forEachIndexed { index, id ->
            val label = FileLabelDBHelper.getFileLabelById(id)
            result.add(
                RecommendLabel(
                    labelId = id,
                    labelName = label?.name ?: "",
                    score = WEIGHT_INDEX_MAPPING_COUNT * getPercentByIndex(index),
                    source = SOURCE_MAPPING_COUNT
                )
            )
        }

        return result.also {
            it.forEachIndexed { index, recommendLabel ->
                Log.d(TAG, "getTopMappingFileCountLabels index = $index, recommendLabel = $recommendLabel")
            }
        }
    }

    private fun getTopOtherUserActionLabels(): List<RecommendLabel> {
        val result: List<RecommendLabel>
        val topViewLabels = FileLabelDBHelper.getTopViewedLabels(TOP_5).also {
            it?.forEachIndexed { index, fileLabelEntity ->
                Log.i(TAG, "getTopOtherUserActionLabels topViewLabels index = $index, label = $fileLabelEntity")
            }
        }
        val topRecentPinnedLabels = FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5).also {
            it?.forEachIndexed { index, fileLabelEntity ->
                Log.d(TAG, "getTopOtherUserActionLabels topRecentPinnedLabels index = $index, label = $fileLabelEntity")
            }
        }
        result = mergeUserActionLabels(topViewLabels, topRecentPinnedLabels)
        return result
    }

    private fun mergeUserActionLabels(topViewLabels: List<FileLabelEntity>?, topRecentPinnedLabels: List<FileLabelEntity>?): List<RecommendLabel> {
        val result = mutableListOf<RecommendLabel>()
        topViewLabels?.forEachIndexed { index, fileLabelEntity ->
            result.add(
                RecommendLabel(
                    labelId = fileLabelEntity.id,
                    labelName = fileLabelEntity.name,
                    score = WEIGHT_INDEX_OTHER_ACT_VIEW * getPercentByIndex(index),
                    source = SOURCE_ACT_VIEW
                )
            )
        }
        topRecentPinnedLabels?.forEachIndexed { index, fileLabelEntity ->
            result.add(
                RecommendLabel(
                    labelId = fileLabelEntity.id,
                    labelName = fileLabelEntity.name,
                    score = WEIGHT_INDEX_OTHER_ACT_PIN * getPercentByIndex(index),
                    source = SOURCE_ACT_PIN
                )
            )
        }
        return result
    }

    private fun getPercentByIndex(index: Int): Float {
        return when (index) {
            INDEX_0 -> INDEX_0_PERCENT
            INDEX_1 -> INDEX_1_PERCENT
            INDEX_2 -> INDEX_2_PERCENT
            INDEX_3 -> INDEX_3_PERCENT
            INDEX_4 -> INDEX_4_PERCENT
            else -> INDEX_0_PERCENT
        }
    }

    private fun mergeAllSources(
        topAiUnit: List<RecommendLabel>,
        topUsed: List<RecommendLabel>,
        topMappingCount: List<RecommendLabel>,
        topOtherUserAction: List<RecommendLabel>
    ): List<FileLabelEntity> {
        val sourceAll = mutableListOf<RecommendLabel>()
        addToAllByUnique(topAiUnit, sourceAll)
        addToAllByUnique(topUsed, sourceAll)
        addToAllByUnique(topMappingCount, sourceAll)
        addToAllByUnique(topOtherUserAction, sourceAll)
        val result = mutableListOf<FileLabelEntity>()
        val sortedList = sourceAll.sortedByDescending { it.score }
        var isShowAiLabel = false
        sortedList.take(TOP_3).forEachIndexed { index, recommendLabel ->
            Log.d(TAG, "mergeAllSources TOP_3 = $index - $recommendLabel")
            val fileLabel =  FileLabelDBHelper.getFileLabelById(recommendLabel.labelId)
            if (fileLabel != null) {
                result.add(fileLabel)
            } else {
                result.add(FileLabelEntity(-1L, recommendLabel.labelName, 0, 0, 0L, 0L))
            }
            if (!isShowAiLabel) {
                //最终显示的3个推荐标签中，有AI标签，即AI标签显示给了用户
                isShowAiLabel = topAiUnit.contains(recommendLabel)
            }
        }
        if (isShowAiLabel) {
            StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.LABEL_AI_SHOW)
        }
        return result
    }

    /**
     * 将子集的推荐标签，合入到最终的推荐标签，去重
     * 有重复的，排序分累加
     */
    private fun addToAllByUnique(subList: List<RecommendLabel>, sourceAll: MutableList<RecommendLabel>) {
        subList.forEach { recommend ->
            var isInAll = false
            run outsize@{
                sourceAll.forEach { label ->
                    if (label.labelName == recommend.labelName) {
                        recommend.score += label.score
                        isInAll = true
                        return@outsize
                    }
                }
            }
            if (!isInAll) {
                sourceAll.add(recommend)
            }
        }
    }
}