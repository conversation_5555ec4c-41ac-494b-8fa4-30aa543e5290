<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.scrollview.COUIScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fadingEdgeLength="@dimen/coui_full_page_statement_scroll_fade_length"
    android:forceDarkAllowed="false"
    android:requiresFadingEdge="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:id="@+id/coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:id="@+id/card_view_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dialog_name_top_padding"
            android:layout_marginBottom="@dimen/dialog_name_button_padding"
            android:layout_marginEnd="@dimen/dimen_24dp"
            app:cardCornerRadius="@dimen/dimen_12dp"
            app:cardElevation="0dp"
            app:cardBackgroundColor="?attr/couiColorContainer4">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dialog_name_tv_margin_top"
                android:layout_marginBottom="@dimen/dialog_name_tv_margin_button"
                android:paddingStart="@dimen/dimen_16dp"
                android:paddingEnd="@dimen/dimen_16dp"
                android:gravity="center"
                android:textColor="?attr/couiColorLabelPrimary"
                android:textAppearance="?attr/couiTextHeadlineXS"
                android:fontFamily="sans-serif-medium"
                android:maxLines="2"
                android:ellipsize="middle"
                android:singleLine="false"
                android:text="@string/abc" />


        </androidx.cardview.widget.CardView>


        <TextView
            android:id="@+id/source_tv"
            android:layout_marginStart="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dialog_padding_between_name_and_source"
            android:layout_marginBottom="@dimen/dialog_name_bottom_padding"
            android:layout_marginEnd="@dimen/dimen_24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="?attr/couiColorLabelTertiary"
            android:textAppearance="?attr/couiTextBodyS"
            android:fontFamily="sans-serif-regular"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:visibility="gone"
            android:text="@string/abc" />


    </LinearLayout>
</com.coui.appcompat.scrollview.COUIScrollView>