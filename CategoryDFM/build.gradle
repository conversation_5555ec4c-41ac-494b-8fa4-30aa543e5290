plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.categorydfm'
}

dependencies {
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.apache.commons.io
    implementation libs.google.gson

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.tablayout
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appprovider.settings
    implementation libs.oplus.onet.sdk
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':FileOperate')
    implementation project(':SelectDir')
    implementation project(':framework:DFM')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}