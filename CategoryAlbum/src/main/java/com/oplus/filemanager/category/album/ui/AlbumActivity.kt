/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.ui

import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_COVER_PATH
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_RELATIVE_PATH
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.category.album.R
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController

class AlbumActivity : EncryptActivity(), NavigationInterface,
    NavigationBarView.OnItemSelectedListener, TransformNextFragmentListener,
    BaseVMActivity.PermissonCallBack, DragDropInterface, IDraggingActionOperate {

    companion object {
        const val TAG = "AlbumActivity"
        const val TAG_CATEGORY_IMAGE = "category_images_tag"
    }

    private var mTitle: String? = ""
    private var mRootView: ViewGroup? = null
    private var mAlbumFragment: AlbumFragment? = null
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mBucketDate: String = ""
    private var coverPath: String = ""
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }
    override fun getLayoutResId(): Int {
        return R.layout.album_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        val intent = intent
        if (null == intent) {
            Log.v(TAG, "intent null")
            finish()
            return
        }
        mRootView = findViewById(R.id.coordinator_layout)
        mBucketDate = IntentUtils.getString(intent, Constants.BUCKETDATA) ?: IntentUtils.getString(intent, KEY_IMAGE_RELATIVE_PATH) ?: ""
        coverPath = IntentUtils.getString(intent, KEY_IMAGE_COVER_PATH) ?: ""
        mTitle = IntentUtils.getString(intent, Constants.TITLE)
        setCategoryFileFragment()
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mAlbumFragment?.let { LoaderViewModel.getLoaderController(it) }
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mAlbumFragment?.onNavigationItemSelected(menuItem) ?: false
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    private fun setCategoryFileFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_CATEGORY_IMAGE)
        if ((fragment == null) || (fragment !is AlbumFragment)) {
            fragment = AlbumFragment()
        }
        val bundle = Bundle()
        bundle.putString(KEY_IMAGE_RELATIVE_PATH, mBucketDate)
        bundle.putString(KEY_IMAGE_COVER_PATH, coverPath)
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.main_frame, fragment, TAG_CATEGORY_IMAGE)
        ft.show(fragment)
        ft.commitAllowingStateLoss()

        (fragment as? AlbumFragment)?.setTitle(mTitle)

        mAlbumFragment = fragment
    }

    override fun onPermissionSuccess() {
        super.onPermissionSuccess()
        mRootView?.post {
            mAlbumFragment?.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        mAlbumFragment?.onCreateOptionsMenu(menu, menuInflater)
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (mAlbumFragment != null) {
            mAlbumFragment!!.onMenuItemSelected(item)
        } else {
            super.onOptionsItemSelected(item)
        }
    }

    override fun onBackPressed() {
        if ((mAlbumFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mAlbumFragment?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mAlbumFragment?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        mSelectPathController.onDestroy()
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mAlbumFragment?.onResumeLoadData()
    }


    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mAlbumFragment?.fromSelectPathResult(code, paths)
    }

    override fun onRefreshData() {
        mAlbumFragment?.onResumeLoadData()
    }

    override fun handleNoStoragePermission() {
        mAlbumFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mAlbumFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mAlbumFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mAlbumFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }
}