/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FootViewManager
import com.filemanager.common.view.FootViewOperation
import com.filemanager.common.viewholder.BaseNormalVH
import com.filemanager.common.viewholder.NormalFooterVH
import com.filemanager.common.viewholder.NormalGridVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.wrapper.ImageFileWrapper
import com.oplus.filemanager.category.album.R

class AlbumAdapter(context: Context, lifecycle: Lifecycle)
    : BaseSelectionRecycleAdapter<BaseNormalVH, ImageFileWrapper>(context), LifecycleObserver, FootViewOperation {

    companion object {
        const val TAG = "AlbumAdapter"
    }

    var mScanViewModel = KtConstants.SCAN_MODE_GRID
        set(value) {
            field = value
            if (value == KtConstants.SCAN_MODE_GRID) {
                (mContext as? Activity)?.let {
                    mItemWith = ItemDecorationFactory.getGridItemWidth(it, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM)
                }
            }
        }
    private var mItemWith: Int = 0
    private var mThreadManager = ThreadManager(lifecycle)
    private var mFootViewManager = FootViewManager(this)
    private val mUiHandler: Handler = Handler(Looper.getMainLooper())
    private val mSizeCache = HashMap<String, String>()
    private var mIsRtl = false
    private val mImgRadius = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    init {
        lifecycle.addObserver(this)
    }

    fun setItemWith(with: Int) {
        mItemWith = with
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: ArrayList<ImageFileWrapper>, selectionArray: ArrayList<Int>) {
        mFiles = data
        mSelectionArray = selectionArray
        mFootViewManager.addFootView(mFiles)
        mIsRtl = Utils.isRtl()
        Log.d(TAG, "setData")
        notifyDataSetChanged()
    }

    override fun getItemKey(item: ImageFileWrapper, position: Int): Int {
        return item.getId()
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseNormalVH {
        return when (viewType) {
            BaseFileBean.TYPE_FILE_LIST_FOOTER -> {
                NormalFooterVH(
                    LayoutInflater.from(parent.context).inflate(R.layout.album_gire_footer_item, parent, false)
                )
            }
            KtConstants.SCAN_MODE_GRID -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalGridVH.getLayoutId(), parent, false))
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
            KtConstants.SCAN_MODE_LIST -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false), mImgRadius)
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
            else -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalGridVH.getLayoutId(),
                    parent, false)).apply { setBorderRoundCornerType(true) }
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
        }
    }

    override fun getItemCount(): Int {
        if (!needShowFootView() && (mFiles.size > 0) && (mFiles.last().mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)) {
            return mFiles.size - 1
        }
        return mFiles.size
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mUiHandler.removeCallbacksAndMessages(null)
    }

    override fun onBindViewHolder(holder: BaseNormalVH, position: Int) {
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if (holder is NormalGridVH) {
            holder.setItemWidth(mItemWith)
        }
        (holder as? NormalListVH)?.updateDividerVisible(mFiles.size - 2, position)
        val file = mFiles[position]
        holder.loadData(
            mContext,
            getItemKey(file, position),
            file,
            mChoiceMode,
            mSelectionArray,
            mSizeCache,
            mThreadManager,
            this
        )
        (holder as? NormalListVH)?.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        if (position < mFiles.size) {
            return if (mFiles[position].mFileWrapperViewType == null) {
                mScanViewModel
            } else {
                mFiles[position].mFileWrapperViewType ?: 0
            }
        }
        return super.getItemViewType(position)
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    override fun operation(): FootViewManager<ImageFileWrapper> {
        return mFootViewManager
    }

    override fun hasFootView(): Boolean {
        return mFootViewManager.hasFootView()
    }

    override fun getFootViewPosition(): Int {
        return mFootViewManager.getFootViewPosition()
    }

    override fun needShowFootView(): Boolean {
        return mScanViewModel == KtConstants.SCAN_MODE_GRID
    }

    override fun isFootView(position: Int): Boolean {
        return getItemViewType(position) == BaseFileBean.TYPE_FILE_LIST_FOOTER
    }

    override fun getFootString(count: Int): String {
        return Utils.formatMessage(
            MyApplication.sAppContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.scan_pic_grid_num_des, count, count
            ), Utils.RTL_POSITION_DOUBLE
        )
    }
}