/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : KoinRegister
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/11 10:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/6/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager

import com.filemanager.categorycompress.di.AutoDIForCategoryCompress
import com.filemanager.common.utils.Log
import com.filemanager.compresspreview.di.AutoDIForCompressPreview
import com.filemanager.fileoperate.di.AutoDIForFileOperate
import com.filemanager.recyclebin.di.AutoDIForRecycleBin
import com.filemanager.setting.di.AutoDIForSetting
import com.filemanager.superapp.ui.di.AutoDIForSuperApp
import com.oplus.filebrowser.di.AutoDIForFileBrowser
import com.oplus.filemanager.addfilepanel.di.AutoDIForAddFileService
import com.oplus.filemanager.cardwidget.di.AutoDIForWidget
import com.oplus.filemanager.category.album.di.AutoDIForCategoryAlbum
import com.oplus.filemanager.category.albumset.di.AutoDIForCategoryAlbumSet
import com.oplus.filemanager.category.apk.di.AutoDIForCategoryApk
import com.oplus.filemanager.category.audiovideo.di.AutoDIForCategoryAudioVideo
import com.oplus.filemanager.category.document.di.AutoDIForCategoryDocument
import com.oplus.filemanager.category.globalsearch.di.AutoDIForGlobalSearch
import com.oplus.filemanager.category.remotedevice.di.AutoDIForCategoryRemoteDevice
import com.oplus.filemanager.categorydfm.di.AutoDIForCategoryDFM
import com.oplus.filemanager.di.AutoDIForMain
import com.oplus.filemanager.di.AutoDIForProvider
import com.oplus.filemanager.filechoose.di.AutoDIForFileChoose
import com.oplus.filemanager.filelabel.di.AutoDIForLabel
import com.oplus.filemanager.pcconnect.di.AutoDIForPCConnect
import com.oplus.filemanager.preview.di.AutoDIForFilePreview
import com.oplus.filemanager.recent.di.AutoDIForRecent
import com.oplus.filemanager.remotedevice.di.AutoDIForRemoteDevice
import com.oplus.filemanager.shortcutfolder.di.AutoDIForShortcutFolder
import com.oplus.fileopentime.di.AutoDIForFileOpenTime
import org.koin.core.module.Module
import java.lang.reflect.Field

object KoinRegister {

    /**
     * 全渠道的module注册
     */
    private val MODULE_OMNICHANNEL_REGISTER = mutableListOf(
        AutoDIForFileOperate.fileOperateModule,
        AutoDIForFileBrowser.fileBrowserModule,
        AutoDIForRecycleBin.recycleBinModule,
        AutoDIForProvider.providerModule,
        AutoDIForSuperApp.superAppModule,
        AutoDIForCategoryAlbum.categoryAlbumApi,
        AutoDIForCategoryAlbumSet.categoryAlbumSetApi,
        AutoDIForMain.mainModule,
        AutoDIForCategoryCompress.categoryCompressApi,
        AutoDIForCompressPreview.compressPreviewModule,
        AutoDIForGlobalSearch.categoryGlobalSearchApi,
        AutoDIForCategoryAudioVideo.categoryAudioVideoApi,
        AutoDIForCategoryApk.categoryApkApi,
        AutoDIForCategoryDocument.categoryDocApi,
        AutoDIForCategoryDocument.categoryExtensionApi,
        AutoDIForSetting.settingModule,
        AutoDIForSetting.collectPrivacyApi,
        AutoDIForFileChoose.fileChooseModule,
        AutoDIForCategoryDFM.categoryDFMApi,
        AutoDIForPCConnect.pcConnectModule,
        AutoDIForWidget.widgetAlbumApi,
        AutoDIForFileOpenTime.fileOpenTimeModule,
        AutoDIForRecent.recentModule,
        AutoDIForLabel.labelModule,
        AutoDIForFilePreview.filePreviewApi,
        AutoDIForShortcutFolder.shortcutFolderApi,
        AutoDIForCategoryRemoteDevice.remoteDeviceApi,
        AutoDIForRemoteDevice.remoteDeviceModule,
        AutoDIForRemoteDevice.remoteThumbnailModule,
        AutoDIForAddFileService.fileServiceModule
    )

    /**
     * 分渠道的注册，要利用反射，对应的类必须不进行混淆
     */
    private val MODULE_DIVIDED_CHANNELS_REGISTER = HashMap<String, String>().apply {
        put("com.filemanager.feedback.AutoDIForFeedBack", "feedBackModule")
        put("com.oplus.filemanager.clouddrive.di.AutoDIForCloudDrive", "cloudDriveModule")
        put("com.oplus.filemanager.account.di.AutoDIForHeytapAccount", "heytapAccountModule")
        put("com.oplus.fileservice.di.AutoDIForFileService", "fileServiceModule")
        put("com.oplus.cloudconfig.di.AutoDIForCloudConfig", "cloudConfigApi")
        put("com.oplus.filemanager.appmarketmanager.di.AutoDIForCategoryAppMarketManager", "categoryAppMarketManagerApi")
        put("com.oplus.filemanager.questionnaire.di.AutoDIForQuestionnaire", "questionnaireModule")
        put("com.oplus.filemanager.drivebrowser.di.AutoDIForFileDriveBrowser", "fileCloudBrowserModule")
        put("com.oplus.filemanager.wechatsdk.di.AutoDIForWechat", "wechatModule")
        put("com.oplus.filemanager.di.AutoDIForAd", "advertSwitchHelp")
        put("com.oplus.filemanager.appmanager.di.AutoDIForCategoryAppManager", "categoryAppManagerApi")
        put("com.oplus.filemanager.di.AutoDIForIcpLicense", "icpLicenseModule")
        put("com.oplus.filemanager.oaps.di.AutoDIForOapsLib", "oapsLibModule")
        put("com.coloros.filemanager.appswitch.di.AppSwitchLibDI", "appSwitchLibModule")
        put("com.oplus.filemanager.dump.di.DmpLibDI", "dmpLibModule")
        put("com.oplus.filemanager.exportdmp.di.DmpLibDI", "dmpLibModule")
        put("com.oplus.filemanager.exportdmp.di.ExportAppSwitchLibDI", "appSwitchLibModule")
        put("com.oplus.filemanager.simulateclick.di.AutoDIForSimulateClick", "simulateClickModule")
        put("com.oplus.filemanager.remotedevice.di.AutoDIForRemoteDevice", "remoteDeviceModule")
        put("com.oplus.filemanager.remotedevice.di.AutoDIForRemoteDevice", "remoteThumbnailModule")
        put("com.oplus.filemanager.clouddrivekit.di.AutoDiForCloudKitDrive", "cloudDriveKitModule")
    }

    @JvmStatic
    fun getKoinModules(): List<Module> {
        val modules: MutableList<Module> = mutableListOf()
        //1,先添加全渠道module
        modules.addAll(MODULE_OMNICHANNEL_REGISTER)

        //2,根据反射获取分渠道module
        MODULE_DIVIDED_CHANNELS_REGISTER.forEach { moduleRegister ->
            val className = moduleRegister.key
            val moduleName = moduleRegister.value

            val module = runCatching {
                val clazz = Class.forName(className)
                val instance = clazz.getDeclaredConstructor().newInstance()
                val api: Field? = clazz.getDeclaredField(moduleName)
                api?.isAccessible = true
                api?.get(instance) as? Module
            }.onFailure { error ->
                error.printStackTrace()
            }.getOrDefault(null)

            Log.i("KoinRegister", "module = $module, className = $className")
            module?.let {
                modules.add(module)
            }
        }
        return modules
    }
}
