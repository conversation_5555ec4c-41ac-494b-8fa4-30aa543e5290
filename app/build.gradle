plugins {
    id 'com.android.application'
    id 'onative' //必须gradle7才能使用该插件，否则会报错
    id 'oapm-perf'
    id 'com.autotest.opasm.CoverageInstPlugin'
}
apply from: rootProject.file('scripts/appCompile.gradle')
apply from: rootProject.file("scripts/variant.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")
// 引入apk自动签名插件
apply from: rootProject.file("scripts/apk_sign.gradle")

OApmConfig {
    defaultConfig {
        iconEnabled true
        logLevel 5
    }
    startupSpeed {
        enabled true
        type 1
        launchActivity 'com.oplus.filemanager.main.ui.MainActivity'
    }
    memory {
        // 内存监控开关，默认开启
        enabled true
        leak {
            // 内存泄漏监控开关，默认开启
            enabled true
            // 内存泄漏是否在服务端分析，默认关闭
            analysisOnServer true
        }
    }
}

OBuildConfig {
    buildTask = "OppoPall,RealmePall,OneplusPall"
    codeScanVariants = "OppoPallDomesticApilevelallDebug"
    androidTestVariants = "OppoPallDomesticApilevelallDebug"
    outputType = "aab,apk"
}

CoverageConfig {
    ignoreDirs = ['com/heytap/nearx', 'com/oplus/nearx', 'com/heytap/cloud']
}

android {
    defaultConfig {
        namespace "com.coloros.filemanager"
        testNamespace "com.android.filemanager.test"
        archivesBaseName = prop_archivesBaseName
        vectorDrawables.useSupportLibrary = true

        if (project.hasProperty('prop_disableSubPackage') && prop_disableSubPackage.toBoolean()) {
            println("app disable resource subpacakge")
        } else {
            if (project.hasProperty('prop_resConfigs') && !prop_resConfigs.toString().isEmpty()) {
                resConfigs prop_resConfigs
            } else {
                println("subpacakge config is empty, no subpackage")
            }
        }
        manifestPlaceholders = [versionCommit: "${prop_versionCommit}",
                                versionDate  : "${prop_versionDate}"]

        testInstrumentationRunner "com.oplus.autotest.olt.testlib.common.OplusRunner"
    }

    buildTypes {


        release {

            //config enable proGuard
            minifyEnabled true

            //config enable shrink unused resources
            shrinkResources true

            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'

        }

        debug {
            //config enable proGuard
            minifyEnabled false

            //config enable shrink unused resources
            shrinkResources false

            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'

            //for oapm, In order to distinguish the test version
            versionNameSuffix "_debug"
        }

        oapm {
            initWith(buildTypes.release)
            matchingFallbacks = ['release']
            //config enable proGuard
            minifyEnabled true

            shrinkResources true
            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }

        coverage {
            //按照 release 的配置构建
            initWith(buildTypes.release)
            //其他模块没有配置 coverage 的情况下默认 release
            matchingFallbacks = ['release']

            //需要去掉该配置，避免重复插桩
            //testCoverageEnabled = true
            //避免混淆，以免影响覆盖率执行率
            minifyEnabled false
            shrinkResources false

            //此处影响流水线出包
            manifestPlaceholders = [ area:"" ]
        }
    }

    sourceSets {

        // Move the build types to build-types/<type>
        // For instance, build-types/debug/java, build-types/debug/AndroidManifest.xml, ...
        // This moves them out of them default location under src/<type>/... which would
        // conflict with src/ being used by the main source set.
        // Adding new build types or product flavors should be accompanied
        // by a similar customization.
        domestic.setRoot('build-types/debug')

        realme {
        }
        oneplus {
        }
        gdpr {
        }
        oneplusPallExportApilevelall {
            //manifest.srcFile '/oneplus/AndroidManifest.xml'
            res.srcDirs += ['src/main/res-oneplusExp']
        }
        domestic {
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
    aaptOptions {
        if (project.hasProperty('prop_disableSubPackage') && prop_disableSubPackage.toBoolean()) {
            println("app disable density resource subpacakge")
        } else {
            if (project.hasProperty('prop_densityResConfigs') && !prop_densityResConfigs.toString().isEmpty()) {
                additionalParameters "--preferred-density", prop_densityResConfigs.toString()
                println("additional density Parameters " + prop_densityResConfigs.toString())
            } else {
                println("density subpacakge config is empty, no subpackage")
            }
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/*.version'
        exclude 'javax/**'
        dex {
            useLegacyPackaging true
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

android.variantFilter { variant ->
    def buildTypeName = variant.buildType.name
    def flavor = variant.getFlavors().name
    def fullFlavorName = flavor + buildTypeName
    println("fullFlavorName : $fullFlavorName")

    //只输出oapm OPPO内外销，一加外销版本
    if (fullFlavorName.contains("oapm")) {
        if (fullFlavorName.contains("oppo") && fullFlavorName.contains("pall")
                && fullFlavorName.contains("export")) {
            variant.setIgnore(false)
        } else if (fullFlavorName.contains("oppo") && fullFlavorName.contains("pall")
                && fullFlavorName.contains("domestic")) {
            variant.setIgnore(false)
        } else if (fullFlavorName.contains("oneplus") && fullFlavorName.contains("pall")
                && fullFlavorName.contains("export")) {
            variant.setIgnore(false)
        } else {
            variant.setIgnore(true)
        }
    }

    if (fullFlavorName.contains("realme") && (fullFlavorName.contains("export") || fullFlavorName.contains("gdpr"))) {
        variant.setIgnore(true)
    }

    //Jacoco覆盖率插桩预编译coverage（暂时）仅出包oppo内外销
    if (fullFlavorName.contains("coverage")) {
        if (fullFlavorName.contains("oppo")) {
            if (fullFlavorName.contains("gdpr")) {
                variant.setIgnore(true)
            }
        } else {
            variant.setIgnore(true)
        }
    }

    // 外销共包, 所以不需要编译欧盟
    if (fullFlavorName.contains("gdpr")) {
        variant.setIgnore(true)
    }
}

android.applicationVariants.configureEach { variant ->
    def name = variant.name.toLowerCase()
    def mergeFlavor = variant.mergedFlavor
    if (name.contains("oneplus") && (name.contains("export") || name.contains("gdpr"))) {
        mergeFlavor.setApplicationId("com.oneplus.filemanager")
        variant.outputs.each { output ->
            output.versionCodeOverride = (4 + mainVersionCode).toInteger()
        }
    } else {
        mergeFlavor.setApplicationId("com.coloros.filemanager")
    }
    // 升级广告sdk后usesCleartextTraffic会为true, 这里再修改usesCleartextTraffic为fasle
    if (name.contains("export")) {
        variant.outputs.each { output ->
            output.getProcessManifestProvider().get().doFirst {
                String manifestPath = "${buildDir}/intermediates/merged_manifest/${variant.name}/AndroidManifest.xml"
                def manifestContent = file(manifestPath).getText()
                manifestContent = manifestContent.replace('android:usesCleartextTraffic="true"', 'android:usesCleartextTraffic="false"')
                file(manifestPath).write(manifestContent)
            }
        }
    }
}

android.sourceSets.configureEach { sourceSet ->
    if(sourceSet.name.contains('androidTest')){
        return
    }
    if (sourceSet.name.contains('oneplus') && (sourceSet.name.contains('Export') || sourceSet.name.contains('Gdpr'))) {
        sourceSet.manifest.srcFile 'src/main/oneplus/AndroidManifest.xml'
    }else if ((sourceSet.name.toLowerCase().contains('oppo') || (sourceSet.name.toLowerCase().contains('realme'))) && sourceSet.name.toLowerCase().contains('export')) {
        sourceSet.manifest.srcFile 'src/oppo_realme_exp/AndroidManifest.xml'
    }
}

configurations {
    oppoPallExportApilevelallImplementation
    oneplusExportApilevelallImplementation
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }
    implementation libs.heytap.addon.adapter
    implementation libs.androidx.constraintlayout
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':Encrypt')
    implementation project(':FileOperate')
    implementation project(':FileBrowser')
    implementation project(':SelectDir')
    implementation project(':RecycleBin')
    implementation project(':Provider')
    implementation project(':SuperApp')
    implementation project(':CategoryAlbum')
    implementation project(':CategoryAlbumSet')
    implementation project(':Main')
    implementation project(':CategoryCompress')
    implementation project(':CompressPreview')
    implementation project(':CategoryGlobalSearch')
    implementation project(':CategoryAudioVideo')
    implementation project(':CategoryApk')
    implementation project(':CategoryDocument')
    implementation project(':OTA')
    implementation project(':Setting')
    implementation project(':FileChoose')
    implementation project(':KeyMove')
    implementation project(':CategoryDFM')
    implementation project(':framework:DFM')
    implementation project(':BackupRestore')
    implementation project(':PCConnect')
    implementation project(':CardWidget')
    implementation project(':FileOpenTime')
    implementation project(':Oaps:OapsLib')
    implementation project(':framework:CloudDrive')
    implementation project(':framework:HeytapAccount')
    implementation project(':framework:AddFilePanel')
    implementation project(':FilePreview')
    implementation project(':ShortcutFolder')
    implementation project(':framework:RemoteDevice')
    implementation project(':CategoryRemoteDevice')
    implementation project(':framework:TouchShare')

    if (prop_use_prebuilt_thumbnail_wps_lib.toBoolean()) {
        implementation libs.oplus.filemanager.thumbnailWpsCompat
    } else {
        implementation project(':exportedLibs:ThumbnailWpsCompat')
    }

    domesticImplementation project(':FeedBack')
    implementation project(':fileservice')
    domesticImplementation project(':CloudConfig')
    domesticImplementation project(':AppMarketManager')
    domesticImplementation project(':Questionnaire')
    domesticImplementation project(':DomesticInfo')
    domesticImplementation project(':FileDriveBrowser')
    domesticImplementation project(":framework:WeChatSdk")
    domesticImplementation project(':ICPLicense:License')
    domesticImplementation project(':framework:AppSwitch')
    domesticImplementation project(':framework:Dmp')
    domesticImplementation project(':framework:SimulateClickEngine')
    domesticImplementation project(':ApiProxyServices')
    domesticImplementation project(':framework:CloudDriveKit')
    exportImplementation project(':framework:ExportDmp')
    gdprImplementation project(':framework:ExportDmp')
    oppoPallExportApilevelallImplementation project(':Oaps:MultiChannel')
    oppoPallExportApilevelallImplementation project(':AppManager')
    exportImplementation project(':exportUserAgreement')

    // --Config for realme ad function--
    domesticImplementation project(':Ad:AdNull')
    oppoPallExportApilevelallImplementation project(':Ad:Overseas')
    // --Config for realme ad function end--

    // --Config for oneplus different--
    oneplusImplementation project(':Ad:AdNull')
    coverageImplementation "otestPlatform:coverageLibDisk:2.1.6@aar"
    oapmImplementation "otestPlatform:coverageLibDisk:2.1.6@aar"
}

// Test code dependencies:
dependencies {
    testImplementation libs.apache.commons.io
    testImplementation libs.bumptech.glide.base
    testImplementation libs.innosystec.unrar.java
    testImplementation libs.lingala.zip4j

    testImplementation libs.oplus.api.adapter.compat
    testImplementation libs.oplus.appcompat.panel
    testImplementation libs.oplus.appcompat.responsiveui
    testImplementation libs.oplus.statistics.track
    testImplementation libs.oplus.stdid.sdk
    testImplementation libs.oppo.marker.oaps.download

    testImplementation project(':Oaps:OapsLib')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        testImplementation libs.oplus.filemanager.dragDrop
    } else {
        testImplementation project(':exportedLibs:DragDropSelection')
    }

    androidTestImplementation libs.heytap.addon.adapter
}
